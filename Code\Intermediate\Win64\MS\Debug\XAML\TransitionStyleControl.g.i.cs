﻿#pragma checksum "TransitionStyleControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "33E40E3439B8E86A715E1E50606DF37DE91178F6A31E9127ACDA0DE37339C0F4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using Microsoft.Expression.Interactivity.Core;
using Microsoft.Expression.Interactivity.Input;
using Microsoft.Expression.Interactivity.Layout;
using Microsoft.Expression.Interactivity.Media;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// TransitionStyleControl
    /// </summary>
    public partial class TransitionStyleControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 26 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualStateGroup Slides1;
        
        #line default
        #line hidden
        
        
        #line 30 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState GoLeft;
        
        #line default
        #line hidden
        
        
        #line 46 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState GoBackFromLeft;
        
        #line default
        #line hidden
        
        
        #line 53 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState GoRight;
        
        #line default
        #line hidden
        
        
        #line 69 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState GoBackFromRight;
        
        #line default
        #line hidden
        
        
        #line 78 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualStateGroup Zooms;
        
        #line default
        #line hidden
        
        
        #line 82 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState ZoomIn;
        
        #line default
        #line hidden
        
        
        #line 107 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Slides;
        
        #line default
        #line hidden
        
        
        #line 117 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Left;
        
        #line default
        #line hidden
        
        
        #line 130 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LeftBackBtn;
        
        #line default
        #line hidden
        
        
        #line 140 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Center;
        
        #line default
        #line hidden
        
        
        #line 145 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GoLeftBtn;
        
        #line default
        #line hidden
        
        
        #line 153 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GoRightBtn;
        
        #line default
        #line hidden
        
        
        #line 170 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Right;
        
        #line default
        #line hidden
        
        
        #line 184 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RightBackBtn;
        
        #line default
        #line hidden
        
        
        #line 193 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Zoom;
        
        #line default
        #line hidden
        
        
        #line 207 "TransitionStyleControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomBackBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/styleguide/transitionstylecontrol.xaml", System.UriKind.Relative);
            
            #line 1 "TransitionStyleControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Slides1 = ((System.Windows.VisualStateGroup)(target));
            return;
            case 2:
            this.GoLeft = ((System.Windows.VisualState)(target));
            return;
            case 3:
            this.GoBackFromLeft = ((System.Windows.VisualState)(target));
            return;
            case 4:
            this.GoRight = ((System.Windows.VisualState)(target));
            return;
            case 5:
            this.GoBackFromRight = ((System.Windows.VisualState)(target));
            return;
            case 6:
            this.Zooms = ((System.Windows.VisualStateGroup)(target));
            return;
            case 7:
            this.ZoomIn = ((System.Windows.VisualState)(target));
            return;
            case 8:
            this.Slides = ((System.Windows.Controls.Grid)(target));
            return;
            case 9:
            this.Left = ((System.Windows.Controls.Grid)(target));
            return;
            case 10:
            this.LeftBackBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 11:
            this.Center = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.GoLeftBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 13:
            this.GoRightBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 14:
            this.Right = ((System.Windows.Controls.Grid)(target));
            return;
            case 15:
            this.RightBackBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 16:
            this.Zoom = ((System.Windows.Controls.Grid)(target));
            return;
            case 17:
            this.ZoomBackBtn = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

