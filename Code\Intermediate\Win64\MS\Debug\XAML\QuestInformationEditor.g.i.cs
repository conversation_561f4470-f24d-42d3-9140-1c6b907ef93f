﻿#pragma checksum "QuestInformationEditor.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "8A9676612B1B076DDD65D669859F44683BC70A82AC9F8751041E56F23AD1C26B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// QuestInformationEditorControl
    /// </summary>
    public partial class QuestInformationEditorControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 21 "QuestInformationEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer QuestInformationScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 57 "QuestInformationEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuestDefinitionTitle;
        
        #line default
        #line hidden
        
        
        #line 66 "QuestInformationEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ImagePlaceholder;
        
        #line default
        #line hidden
        
        
        #line 75 "QuestInformationEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CustomImage;
        
        #line default
        #line hidden
        
        
        #line 83 "QuestInformationEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UploadQuestDefintionImageBtn;
        
        #line default
        #line hidden
        
        
        #line 222 "QuestInformationEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteQuestDefinitionBtn;
        
        #line default
        #line hidden
        
        
        #line 242 "QuestInformationEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelEditQuestDefinitionBtn;
        
        #line default
        #line hidden
        
        
        #line 249 "QuestInformationEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveQuestDefinitionBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/quests/questcreator/questinformationeditor.xaml", System.UriKind.Relative);
            
            #line 1 "QuestInformationEditor.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.QuestInformationScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 2:
            this.QuestDefinitionTitle = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.ImagePlaceholder = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.CustomImage = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.UploadQuestDefintionImageBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 6:
            this.DeleteQuestDefinitionBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.CancelEditQuestDefinitionBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.SaveQuestDefinitionBtn = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

