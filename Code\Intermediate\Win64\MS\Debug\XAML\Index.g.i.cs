﻿#pragma checksum "Index.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "B9430DE0D44F4977937AD93130007470DF6B4C556480B0DF07B9E2A2EF8F4A7C"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// SansarStyleguide
    /// </summary>
    public partial class SansarStyleguide : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 24 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid StyleTesterLayout;
        
        #line default
        #line hidden
        
        
        #line 33 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel Menu;
        
        #line default
        #line hidden
        
        
        #line 35 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrushBtn;
        
        #line default
        #line hidden
        
        
        #line 39 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ButtonsBtn;
        
        #line default
        #line hidden
        
        
        #line 43 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CheckBoxBtn;
        
        #line default
        #line hidden
        
        
        #line 47 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ComboBoxBtn;
        
        #line default
        #line hidden
        
        
        #line 51 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConverterBtn;
        
        #line default
        #line hidden
        
        
        #line 55 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DragDropBtn;
        
        #line default
        #line hidden
        
        
        #line 59 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExpanderBtn;
        
        #line default
        #line hidden
        
        
        #line 63 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GroupBoxBtn;
        
        #line default
        #line hidden
        
        
        #line 67 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ItemsControlBtn;
        
        #line default
        #line hidden
        
        
        #line 71 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LabelsBtn;
        
        #line default
        #line hidden
        
        
        #line 75 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ListBoxBtn;
        
        #line default
        #line hidden
        
        
        #line 79 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MenuBtn;
        
        #line default
        #line hidden
        
        
        #line 83 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ProgressBarBtn;
        
        #line default
        #line hidden
        
        
        #line 87 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RibbonsBtn;
        
        #line default
        #line hidden
        
        
        #line 91 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SlidersBtn;
        
        #line default
        #line hidden
        
        
        #line 95 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SwitchesBtn;
        
        #line default
        #line hidden
        
        
        #line 99 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TabsBtn;
        
        #line default
        #line hidden
        
        
        #line 103 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TextBlockBtn;
        
        #line default
        #line hidden
        
        
        #line 107 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TextBoxBtn;
        
        #line default
        #line hidden
        
        
        #line 111 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ToolTipBtn;
        
        #line default
        #line hidden
        
        
        #line 115 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TransitionBtn;
        
        #line default
        #line hidden
        
        
        #line 119 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TreeViewBtn;
        
        #line default
        #line hidden
        
        
        #line 127 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QuestBtn;
        
        #line default
        #line hidden
        
        
        #line 131 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BringItemBtn;
        
        #line default
        #line hidden
        
        
        #line 138 "Index.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Main;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/styleguide/index.xaml", System.UriKind.Relative);
            
            #line 1 "Index.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StyleTesterLayout = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.Menu = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.BrushBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.ButtonsBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 5:
            this.CheckBoxBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 6:
            this.ComboBoxBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.ConverterBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.DragDropBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 9:
            this.ExpanderBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 10:
            this.GroupBoxBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 11:
            this.ItemsControlBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 12:
            this.LabelsBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 13:
            this.ListBoxBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 14:
            this.MenuBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 15:
            this.ProgressBarBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 16:
            this.RibbonsBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 17:
            this.SlidersBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 18:
            this.SwitchesBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 19:
            this.TabsBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 20:
            this.TextBlockBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 21:
            this.TextBoxBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 22:
            this.ToolTipBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 23:
            this.TransitionBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 24:
            this.TreeViewBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 25:
            this.QuestBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 26:
            this.BringItemBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 27:
            this.Main = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

