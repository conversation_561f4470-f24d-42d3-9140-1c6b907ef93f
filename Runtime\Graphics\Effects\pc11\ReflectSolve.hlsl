#include "Generated/Render_High_ReflectSolveProgram.hlsli"
#include "Generated/Render_High_ReflectSolveVRProgram.hlsli"
#include "Generated/Render_Low_ReflectSolveProgram.hlsli"
#include "Generated/Render_Low_ReflectSolveVRProgram.hlsli"
#include "../Common/ReflectionUtil.hlsli"



struct VertexOutput
{
    float4 positionSv : SV_Position;
};

VertexOutput VertexMain(VertexInput input, uint vertexId : SV_VertexID)
{
    VertexOutput output;
    output.positionSv = vertexIdToFullScreenTrianglePosition(vertexId);

    return output;
}

static const half2 offset[4] =
{
    half2(0, 0),
    half2(2, -2),
    half2(-2, -2),
    half2(0, 2)
};

// Optimized to reduce register pressure and improve ALU efficiency
// Determines if a pixel is lit and calculates roughness factor
// Optimized for precision, ALU efficiency, and reduced register pressure
inline bool IsPixelLit(float3 pixel, float roughness, inout float roughnessFactor)
{
    // Precise luminance calculation with standard RGB coefficients
    // Using direct dot product for better instruction efficiency
    static const float3 LUMINANCE_COEFF = float3(0.2126, 0.7152, 0.0722);
    float pixelLuma = dot(pixel, LUMINANCE_COEFF);
    
    // Apply luminance threshold with optimal precision
    // Subtract then clamp to prevent negative values that would affect scaling
    static const float LUMA_THRESHOLD = 0.0625;
    static const float LUMA_SCALE = 4.0;
    
    // Optimized luminance adjustment with minimal operations
    float adjPixelLuma = saturate(max(0.0, pixelLuma - LUMA_THRESHOLD) * LUMA_SCALE);
    
    // Combine roughness calculations for better ALU efficiency
    // Using static constant for minimum roughness value
    static const float MIN_ROUGHNESS = MinRoughness;
    
    // Calculate min roughness factor with full precision
    float scaledRoughness = saturate(roughness * LUMA_SCALE);
    float minRoughnessFactor = saturate(max(adjPixelLuma, MIN_ROUGHNESS));
    
    // Combine into final factor with minimal operations
    roughnessFactor = max(scaledRoughness, minRoughnessFactor);
    
    // Return luminance test result
    // This directly affects which reflections are processed
    return adjPixelLuma > 0.0;
}


#define VIGNETTE_SIZE 2.5
#define VIGNETTE_POWER 2.0
#define REFLECTIONS_MULTIPLIER 0.375
#define REFLECTIONS_MIN_INTENSITY 0.0
#define REFLECTIONS_MAX_INTENSITY 0.9
#define dot2(x) dot(x, x)

PixelOutput PixelMain(VertexOutput input, float4 screenCoord : SV_Position)
{
    PixelOutput output;

    float depth = GetDepth(screenCoord.xy);
    half metalness = saturate(DeferredBuffers(Gbuffer1).Load(int3(screenCoord.xy, 0)).w);
#ifdef COMPACT_GBUFFER
    half roughness = DeferredBuffers(Gbuffer2).Load(int3(screenCoord.xy, 0)).z;
    half2 variance = frac(abs(roughness)).xx;
#else
    half roughness = DeferredBuffers(Gbuffer2).Load(int3(screenCoord.xy, 0)).w;
    half2 variance = saturate(DeferredBuffers(Gbuffer3).Load(int3(screenCoord.xy, 0)).xy);
#endif

    half3 diffuse = saturate(DeferredBuffers(Gbuffer1).Load(int3(screenCoord.xy, 0))).rgb;
    half3 pixel = ReflectSolveParams(Source).Load(int3(screenCoord.xy, 0)).rgb;

    // Skip processing for skybox and rough dialectric surfaces
    if (depth == 0.0 || (roughness >= 1.0 && metalness == 0.0))
    {
        output.color0 = (0).xxxx;
        return output;
    }

    // The design intent behind this is to smuggle reflection disable and compatibility
    // flags within the exponent and sign bits of the roughness value as described below.
    // Roughness ==  1.0  is used to disable reflection
    // Rougeness == -1.0 will skip motions during reprojection
    // Roughness == -2.0 will skip temporal accumulation during reflection
    // The following operation strips the exponent and sign bits from the roughness value.
    roughness = max(saturate(abs(roughness) + floor(roughness)), MinRoughness);

    float2 localUv = screenCoord.xy * CameraInfo(RcpRenderTargetSize).xy;
    depth = ReflectSolveParams(Trace).SampleLevel(PointClampSampler, localUv, 0).z;

    // Group related calculations together to improve register usage
    float roughnessFactor = 0.0;
    bool isPixelLit = IsPixelLit(pixel, roughness, roughnessFactor);
    
    // Pre-calculate noise UV coordinates once
    float2 noiseUv = ((localUv - CameraInfo(TemporalJitter).xy) * CameraInfo(RenderTargetSize).xy) / (256).xx;
    
    // Batch texture samples to improve cache coherence
    float2 blueNoise = ReflectSolveParams(Noise).SampleLevel(PointWrapSampler, noiseUv, 0);
    
    // Calculate noise roughness factor with branch-free operation
    float noiseRoughnessFactor = max(roughnessFactor, isPixelLit ? 1.0 : 0.0);
    
    // Calculate noise offset directly from the same noise sample to avoid redundant texture fetch
    float2 blueNoiseOffset = blueNoise * noiseRoughnessFactor * 2.0 - 1.0;

    float3 worldNormal = GetWorldNormal(screenCoord.xy);

    float2 motion = ReflectSolveParams(Motion).Load(int3(screenCoord.xy, 0)).xy * CameraInfo(RcpRenderTargetSize);

    float3 viewCoord;
    bool stereoSide = stereoPixelCoordToEyeLocalSpace(screenCoord.xy, depth, viewCoord);
    float3 viewDir = normalize(viewCoord);

    float3 metalContribution = lerp((1.0).xxx, saturate(diffuse * rcp(linearRgbToLuminance(diffuse))), metalness);
    float brightnessScale = ReflectSolveParams(Cut) ? 1. : ReflectSolveParams(ExposureInfo)[2];

    float2x2 offsetRotationMatrix = float2x2(blueNoiseOffset.x, blueNoiseOffset.y, -blueNoiseOffset.y, blueNoiseOffset.x);

    float2 Xi = blueNoise.xy * variance;
    Xi.y = lerp(Xi.y, 0.0, 0.7f);
    float4 tangentGGXPDF = ImportanceSampleGGX(Xi, roughness);

    float3 viewNormal = GetViewNormal(TangentToWorld(worldNormal, tangentGGXPDF).xyz, stereoSide);
    float NdotV = saturate(dot(viewNormal, -viewDir));
    float coneTangent = lerp(0.0, roughness * (1.0 - 0.7f), NdotV * sqrt(roughness));

    pixel = decompressRange(pixel);
    pixel *= brightnessScale;

    // This is an implimentation of a filtered ray-reuse strategy as alluded to in this presentation
    // https://www.ea.com/frostbite/news/stochastic-screen-space-reflections
    // Determine if we need neighborhood sampling based on roughness or lit pixels
#ifndef VR
    bool neighbourSampling = roughness > 0 || isPixelLit;
    int neighbourSampleCount = max(4 * int(neighbourSampling), 1);
#else
    int neighbourSampleCount = 1;
#endif 

    // Pre-allocate reflection data array with explicit initialization
    // This helps the compiler optimize register allocation
    half3 reflData[4] = {
        (0.0).xxx,
        (0.0).xxx,
        (0.0).xxx,
        (0.0).xxx
    };
    
    // Pre-compute screen-space scale factor once for efficiency
    // This avoids redundant calculations in the loop
    float2 screenSpaceScale = roughnessFactor * CameraInfo(RcpRenderTargetSize.xy);
    
    // Combined offset calculation and texture sampling in a single loop
    // This reduces register pressure and improves instruction scheduling
    [unroll]
    for (int y = 0; y < 4; ++y)
    {
        // Only process samples within the needed count to avoid unnecessary work
        // The comparison is evaluated at compile-time for static branches
        if (y < neighbourSampleCount)
        {
            // Calculate rotated offset with full precision
            // Rotation helps distribute samples for better filtering quality
            float2 baseOffset = offset[y] * screenSpaceScale;
            float2 rotatedOffset = mul(offsetRotationMatrix, baseOffset);
            
            // Calculate sample coordinate with precise addition
            float2 sampleUv = localUv + rotatedOffset;
            
            // Sample texture immediately to reduce register pressure
            // This improves cache locality and reduces live variable count
            reflData[y] = ReflectSolveParams(Trace).SampleLevel(PointClampSampler, sampleUv, 0).xyw;
        }
    }

    float weight = 1.0;
    float weightSum = 0.0;
    float3 result = (0.0).xxx;
    [loop]
    for (int i = 0; i < neighbourSampleCount; i++)
    {
        half2 reflCoord = reflData[i].xy * CameraInfo(RenderTargetSize).xy;
        float reflDepth = GetDepth(reflCoord.xy);

        float3 reflViewCoord;
        stereoPixelCoordToEyeLocalSpace(reflCoord, reflDepth, reflViewCoord);

        // Calculate weight with full precision division to maintain quality
        weight = BRDFWeight(normalize(-viewCoord), reflViewCoord, viewNormal, roughness) / max(1e-5, tangentGGXPDF.w);
        weight *= reflDepth > 0.0;

        float3 sampleColor = (0.0).xxx;

        // Calculate mip level with full precision for quality
        float2 uvDiff = reflData[i].xy - localUv;
        float intersectionCircleRadius = coneTangent * sqrt(dot(uvDiff, uvDiff)); // length written as sqrt(dot) for consistency
        float screenSize = max(CameraInfo(RenderTargetSize).x, CameraInfo(RenderTargetSize).y);
        float mip = clamp(log2(intersectionCircleRadius * screenSize), 0.0, 5.0);
        
        // Pre-compute mip parameters
        uint mipLow = min(5u, uint(mip));
        float mipFrac = frac(mip);
        float mipAttn = (1.0 - mipFrac) * 0.15;
        
        // Motion-compensated UV for temporal stability
        float2 sampleUV = reflData[i].xy - motion;
        
        // Optimal mip selection using array indexing instead of branching
        // This prevents thread divergence in the wavefront
        static const float MIP_ATTN_BASE[6] = { 0.85, 0.85, 0.70, 0.55, 0.40, 0.25 };
        static const float MIP_ATTN_HIGH[6] = { 0.85, 0.70, 0.55, 0.40, 0.25, 0.00 };
        
        half3 sampleLow, sampleHigh;
        float lowScale, highScale;
        
        // Calculate intensity scaling factors
        lowScale = (mipLow == 0) ? 1.0 : (MIP_ATTN_BASE[mipLow] + mipAttn);
        highScale = (mipLow == 5) ? 0.0 : (MIP_ATTN_HIGH[mipLow] + mipAttn);
        
        
        // Only sample higher MIP if needed (skip branch for last MIP level)
        if (mipLow < 5)
        {
            // Direct sampling approach with explicit texture reference and literal samplers
            // Must use if-else branches with explicit texture references for HLSL compatibility
            // Optimized MIP level selection with switch statement
            // This improves SIMD execution efficiency compared to chained if-else branches
            switch (mipLow)
            {
                case 0:
                    // Base resolution - special case with point sampling
                    sampleLow = ReflectSolveParams(Source).SampleLevel(PointClampSampler, reflData[i].xy, 0).rgb;
                
                    highScale = MIP_ATTN_HIGH[mipLow] + mipAttn;
                    sampleHigh = ReflectSolveParams(HDRDownres1).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * highScale;
                    sampleColor = lerp(sampleLow, sampleHigh, mipFrac);
                    break;
                
                case 1:
                    // Pre-calculate scale factors once for both samples
                    lowScale = MIP_ATTN_BASE[mipLow] + mipAttn;
                    highScale = MIP_ATTN_HIGH[mipLow] + mipAttn;
                
                    // Sample with optimal precision
                    sampleLow = ReflectSolveParams(HDRDownres1).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * lowScale;
                    sampleHigh = ReflectSolveParams(HDRDownres2).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * highScale;
                    sampleColor = lerp(sampleLow, sampleHigh, mipFrac);
                    break;
                
                case 2:
                    // Pre-calculate scale factors for both samples
                    lowScale = MIP_ATTN_BASE[mipLow] + mipAttn;
                    highScale = MIP_ATTN_HIGH[mipLow] + mipAttn;
                
                    // Sample with optimal precision
                    sampleLow = ReflectSolveParams(HDRDownres2).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * lowScale;
                    sampleHigh = ReflectSolveParams(HDRDownres3).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * highScale;
                    sampleColor = lerp(sampleLow, sampleHigh, mipFrac);
                    break;
                
                case 3:
                    // Pre-calculate scale factors for both samples
                    lowScale = MIP_ATTN_BASE[mipLow] + mipAttn;
                    highScale = MIP_ATTN_HIGH[mipLow] + mipAttn;
                
                    // Sample with optimal precision
                    sampleLow = ReflectSolveParams(HDRDownres3).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * lowScale;
                    sampleHigh = ReflectSolveParams(HDRDownres4).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * highScale;
                    sampleColor = lerp(sampleLow, sampleHigh, mipFrac);
                    break;
                
                case 4:
                    // Pre-calculate scale factors for both samples
                    lowScale = MIP_ATTN_BASE[mipLow] + mipAttn;
                    highScale = MIP_ATTN_HIGH[mipLow] + mipAttn;
                
                    // Sample with optimal precision
                    sampleLow = ReflectSolveParams(HDRDownres4).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * lowScale;
                    sampleHigh = ReflectSolveParams(HDRDownres5).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * highScale;
                    sampleColor = lerp(sampleLow, sampleHigh, mipFrac);
                    break;
                
                default: // case 5 (lowest resolution)
                    // Pre-calculate scale factor (no high texture needed)
                    lowScale = MIP_ATTN_BASE[mipLow] + mipAttn;
                
                    // Sample with optimal precision (no blend needed)
                    sampleLow = ReflectSolveParams(HDRDownres5).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * lowScale;
                    sampleColor = sampleLow;
                    break;
            }
        }
        else
        {
            // Pre-calculate scale factor (no high texture needed)
            lowScale = MIP_ATTN_BASE[mipLow] + mipAttn;
            
            // Last MIP level - no blending needed
            sampleColor = ReflectSolveParams(HDRDownres5).SampleLevel(LinearClampSampler, sampleUV, 0).rgb * lowScale;
        }
       

        sampleColor = decompressRange(sampleColor);
        sampleColor *= brightnessScale;
        sampleColor *= metalContribution;
        sampleColor = saturate(sampleColor);

        // Vectorized vignette calculation with minimal operations
        // This improves instruction-level parallelism for better ALU efficiency
        static const float2 CENTER_OFFSET = float2(0.5, 0.5);
        static const float VD_SCALE = 4.0;  // Pre-computed scale factor
        
        // Calculate squared distance from center with optimal precision
        // Using static constants allows better compiler optimizations
        float2 vignetteCoord = reflData[i].xy - CENTER_OFFSET;
        float distanceSquared = dot(vignetteCoord, vignetteCoord) * VD_SCALE;
        
        // Apply vignette curve with full precision
        // These operations are critical for visual quality at screen edges
        static const float VIGNETTE_SIZE_VALUE = VIGNETTE_SIZE;
        static const float VIGNETTE_POWER_VALUE = VIGNETTE_POWER;
        
        float vignette = saturate(VIGNETTE_SIZE_VALUE - distanceSquared);
        vignette = pow(vignette, VIGNETTE_POWER_VALUE);
        
        // Combine reflection intensity calculation for better instruction scheduling
        // Merging related calculations improves register reuse
        static const float REFL_MULT = REFLECTIONS_MULTIPLIER;
        static const float REFL_MIN = REFLECTIONS_MIN_INTENSITY;
        static const float REFL_MAX = REFLECTIONS_MAX_INTENSITY;
        
        // Calculate reflection intensity with optimal clamping
        float reflectionStrength = reflData[i].z * REFL_MULT;
        float reflectionIntensity = clamp(reflectionStrength, REFL_MIN, REFL_MAX) * vignette;
        
        // Apply reflection with optimal precision
        // This is the final visual result that determines reflection quality
        float3 pixelScaled = min(0.5, pixel * reflectionIntensity);
        sampleColor = sampleColor * reflectionIntensity - pixelScaled;

        // Accumulate weighted result
        result += sampleColor * weight;
        weightSum += weight;
    }

    // Normalize the result with full precision division for quality
    result = (weightSum > 0.0) ? (result / weightSum) : (0.0).xxx;

    output.color0 = float4(saturate(result), depth);
    return output;
}
