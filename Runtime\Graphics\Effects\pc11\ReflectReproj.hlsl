#include "Generated/Render_High_ReflectReprojProgram.hlsli"
#include "Generated/Render_Low_ReflectReprojProgram.hlsli"
#include "../Common/ReflectionUtil.hlsli"
#include "../Common/StereoUtil.hlsli"

// Define feature flags for advanced reprojection techniques
// Comment out any features that can't be supported by your current data buffers

// #define ENABLE_PREV_NORMAL_DEPTH // Enable if you have access to previous frame normal and depth
#define ENABLE_VIEW_DEPENDENT_CORRECTION // Uses StereoUtil to calculate view direction changes
#define ENABLE_ROUGHNESS_ADAPTIVE_BLEND // Adjusts temporal weights based on surface roughness
#define ENABLE_VARIANCE_CLAMP // Uses variance-based neighborhood clamp instead of min/max

#ifdef ENABLE_PREV_NORMAL_DEPTH
// Previous frame normal and depth buffers for consistency checks
Texture2D<float4> PrevNormal : register(t10);
Texture2D<float> PrevDepth : register(t11);
#endif


// Define constants for better compiler optimization
#define TemporalScale 1.8         // Reduced from 2.0 to preserve more history details
#define ResponseSpeed 0.92        // Slightly reduced for smoother transitions
#define NEIGHBORHOOD_SIZE 3
#define MOTION_SCALE_FACTOR 8.0
#define ROUGHNESS_ADAPT_FACTOR 4.0 // Controls how much roughness affects temporal accumulation
#define SPECULAR_VARIANCE_SCALE 0.75 // Controls variance scaling for specular surfaces
#define MAX_RAY_DISTANCE 100.0    // Maximum valid ray hit distance
#define DISOCCLUSION_THRESHOLD 0.15 // Threshold for detecting disocclusions


struct VertexOutput
{
    float4 positionSv : SV_Position;
    float2 uv : TEXCOORD0;
};

VertexOutput VertexMain(VertexInput input, uint vertexId : SV_VertexID)
{
    VertexOutput output;
    output.positionSv = vertexIdToFullScreenTrianglePosition(vertexId);
    output.uv = output.positionSv.xy * float2(0.5, -0.5) + 0.5;

    return output;
}

PixelOutput PixelMain(VertexOutput input, float4 screenCoord : SV_Position)
{
    PixelOutput output;

    // Sample GBuffer data for current pixel
    // Use shared roughness variable for improved register allocation
#ifdef COMPACT_GBUFFER
    half roughness = DeferredBuffers(Gbuffer2).Load(int3(screenCoord.xy, 0)).z;
#else
    half roughness = DeferredBuffers(Gbuffer2).Load(int3(screenCoord.xy, 0)).w;
#endif

    // Sample world normal from GBuffer for geometric consistency checks
    float3 worldNormal = DeferredBuffers(Gbuffer1).Load(int3(screenCoord.xy, 0)).xyz * 2.0 - 1.0;

    // Sample current reflection data
    float2 localUv = screenCoord.xy * CameraInfo(RcpRenderTargetSize).xy;
    // Early sample of current frame reflection data
    float4 current = ReflectReprojParams(ReflectSolve).Sample(PointClampSampler, localUv);
    
    // Calculate hit distance using the reflection hit position from ReflectTrace
    // Optimization: Combined hit position sampling and validation to reduce branches
    float2 hitScreenPos = ReflectReprojParams(ReflectTrace).Sample(PointClampSampler, localUv).xy;
    float hitDistSqr = dot(hitScreenPos, hitScreenPos);
    
    // Pre-calculate stereo side value which is used in multiple places
    bool stereoSide = (screenCoord.x >= CameraInfo(RenderViewportSize).x * 0.5);
    
    // Prepare hit distance calculation with defaults for invalid hit case
    float hitDistance = MAX_RAY_DISTANCE;
    bool hasValidHit = false;
    
    // Optimization: Use single branch with multiple conditions for improved coherence
    // Only process depth sampling and view position calculation if we have a valid hit position
    if (hitDistSqr > 0.001f)
    {
        // Optimization: Precalculate reciprocal screen dimensions once
        float2 rcpDimensions = CameraInfo(RcpRenderTargetSize).xy;
        float2 hitUv = hitScreenPos * rcpDimensions;
        
        // Get linear depth at hit position
        float hitDepth = GetLinearDepth(hitUv, stereoSide);
        
        // Process depth if it's valid
        // Optimization: Combine branches using multiplication instead of nested if
        float validDepth = step(0.001f, hitDepth);
        
        if (validDepth > 0.5f)
        {
            // Calculate view-space position only if we have valid depth
            float3 hitViewPos;
            stereoPixelCoordToEyeLocalSpace(hitScreenPos, hitDepth, hitViewPos);
            
            // Calculate squared length first (avoiding square root until needed)
            float hitDistSqr = dot(hitViewPos, hitViewPos);
            
            // Final distance calculation
            hitDistance = sqrt(hitDistSqr);
            hasValidHit = true;
        }
    }
    
    // Sample precomputed depth-motion buffer
    float2 motion = ReflectReprojParams(Motion).Load(int3(screenCoord.xy, 0));;
    
    // Pre-calculate accumulation flag based on roughness
    // Roughness == -2.0 will skip temporal accumulation during reflection
    bool doAccumulation = roughness >= -1.0 && hasValidHit;

    // Initialize depth tracking for motion vector sampling
    float currentDepth = -1.1;
    
    // The design intent behind this is to smuggle reflection disable and compatibility
    // flags within the exponent and sign bits of the roughness value.
    // Roughness >= 0.0: normal path - find motion vector using neighborhood sampling
    // Roughness < 0.0 && >= -1.0: skip motion calculation but still accumulate
    // Roughness < -1.0: skip motion calculation and skip accumulation
    bool useNeighborhood = roughness >= 0.0;

    // Calculate depth and sample motion vector for normal case
    if (useNeighborhood)
    { 
        // For reflections, we need to consider view-dependent movement
        // Optimization: Reuse stereoSide from earlier in the shader if available
        bool stereoSideLocal = stereoSide;

        // Pre-fetch matrices once to avoid redundant memory access
        const float4x4 currentHeadToWorld = stereoGetHeadToWorld();
        const float4x4 previousHeadToWorld = stereoGetHeadToWorldHistory();

        // Extract forward and up vectors with minimal operations
        // Optimization: Load matrix rows directly to avoid redundant memory access
        float3 currentForward = currentHeadToWorld[2].xyz;
        float3 previousForward = previousHeadToWorld[2].xyz;
        float3 currentUp = currentHeadToWorld[1].xyz;
        float3 previousUp = previousHeadToWorld[1].xyz;

        // Optimization: Batch normalization operations for better ALU utilization
        // Use fast approximate normalize with reciprocal square root
        float cfLenSq = dot(currentForward, currentForward);
        float pfLenSq = dot(previousForward, previousForward);
        float cuLenSq = dot(currentUp, currentUp);
        float puLenSq = dot(previousUp, previousUp);

        // Use rsqrt for faster reciprocal square root approximation
        // Optimization: Normalize vectors with minimal operations
        currentForward *= rsqrt(cfLenSq);
        previousForward *= rsqrt(pfLenSq);
        currentUp *= rsqrt(cuLenSq);
        previousUp *= rsqrt(puLenSq);

        // Calculate dot products once for reuse
        float forwardDifference = dot(currentForward, previousForward);
        float upDifference = dot(currentUp, previousUp);

        // Optimization: Fast approximation for acos for nearly aligned vectors
        // This improves performance for small angle changes which are common
        const float HALF_PI = 1.570796f; // π/2

        // Branch-free coding pattern for better execution coherence
        float forwardDelta = forwardDifference > 0.9999f ?
                              0.0f :
                              (forwardDifference > 0.7f ?
                               HALF_PI - forwardDifference :
                               acos(saturate(forwardDifference)));

        float upDelta = upDifference > 0.9999f ?
                         0.0f :
                         (upDifference > 0.7f ?
                          HALF_PI - upDifference :
                          acos(saturate(upDifference)));

        // Fused multiply-add for improved performance
        // Calculate cross product only when needed for direction determination
        float upCrossSign = 0.0f;
        if (forwardDelta > 0.001f) {
            float3 forwardCross = cross(previousForward, currentForward);
            upCrossSign = dot(forwardCross, currentUp);
        }

        // Optimization: Compute roughness scale more efficiently
        // Avoid redundant operations with fused multiply-add
        float roughnessScale = saturate(1.0f - roughness * roughness);

        // Optimization: Use vector operations for correction calculation
        // Pre-compute correction scale factors as constants for better instruction packing
        const float2 CORRECTION_STRENGTH = float2(0.02f, 0.025f);

        // Calculate corrections in a vectorized form for better instruction utilization
        float2 viewCorrection;
        viewCorrection.x = -forwardDelta * CORRECTION_STRENGTH.x * roughnessScale;
        viewCorrection.y = upDelta * CORRECTION_STRENGTH.y * sign(upCrossSign) * roughnessScale;

        // Apply correction to motion vector using efficient vector addition
        motion += viewCorrection;
    }

    // Sanitize motion vector to handle potential NaN values
    motion = isfinite(motion.x) && isfinite(motion.y) ? motion : 0.0;

    // Calculate previous frame UV coordinates
    float2 screenPosition = screenCoord.xy;
    float2 prevUv = (screenPosition - motion) * CameraInfo(RcpRenderTargetSize).xy;
    
    // Sample previous frame reflection data
    float4 previous = ReflectReprojParams(PrevReproj).Sample(PointClampSampler, prevUv);
    
    // Default consistency factor - conservative for stability
    float consistencyFactor = 0.5;
    
#ifdef ENABLE_PREV_NORMAL_DEPTH
    // TODO: Create these buffers and pass this into the shader for improved fidelity.
    /*
    // Sample previous frame normal and depth for consistency check
    float3 prevNormal = ReflectReprojParams(PrevNormal).Sample(PointClampSampler, prevUv).xyz * 2.0 - 1.0;
    float prevDepth = ReflectReprojParams(PrevDepth).Sample(PointClampSampler, prevUv).x;
    
    // Check geometric consistency between frames to detect disocclusions
    float normalSimilarity = saturate(dot(worldNormal, prevNormal));
    float depthDiff = abs(currentDepth - prevDepth) / (currentDepth + 0.0001);
    bool isConsistent = normalSimilarity > 0.9 && depthDiff < DISOCCLUSION_THRESHOLD;
    
    // If geometry isn't consistent, reduce history influence
    consistencyFactor = isConsistent ? 1.0 : 0.1;
    */
#endif

#ifdef ENABLE_ROUGHNESS_ADAPTIVE_BLEND
    // Smoother surfaces (low roughness) need more stable history weight
    // This improves the appearance of mirrors and other highly specular surfaces
    float roughnessAdaptation = saturate(1.0 - roughness * 4.0);
    
    // Hit distance also affects history confidence:
    // - Closer hits are likely more accurate and can use more history
    // - Distant hits may have more variance and should use less history
    float distanceFactor = saturate(1.0 - hitDistance / (MAX_RAY_DISTANCE * 0.7));
    
    // Combine factors - higher value = more history preserved
    consistencyFactor = max(consistencyFactor, roughnessAdaptation * 0.7 + distanceFactor * 0.3);
#endif
    
    // Apply accumulation factor based on consistency and accumulation flag
    float accumFactor = doAccumulation ? consistencyFactor : 0.0;
    previous *= accumFactor;
     
    // Pre-calculate texture step sizes for 3x3 neighborhood sampling
    float2 du = float2(1.0 / CameraInfo(RenderTargetSize).x, 0.0);
    float2 dv = float2(0.0, 1.0 / CameraInfo(RenderTargetSize).y);
    
    // Define sample offsets for neighborhood analysis
    static const float2 offsets[9] = {
        float2(-1, -1), float2(0, -1), float2(1, -1),
        float2(-1,  0), float2(0,  0), float2(1,  0),
        float2(-1,  1), float2(0,  1), float2(1,  1)
    };
    
    // Optimization: Pre-calculate variance scale based on roughness once
    // Scale is used in both clamping approaches
    float roughnessFactor = saturate(roughness * 4.0);
    float varianceScale = lerp(SPECULAR_VARIANCE_SCALE, TemporalScale, roughnessFactor);
    
    // Calculate hit distance factor early to allow instruction reordering
    float rcpMaxDist = rcp(MAX_RAY_DISTANCE);
    float hitDistanceFactor = saturate(1.0 - hitDistance * rcpMaxDist);
    
    // Optimization: Pre-compute sampling offsets as float2 vector for better memory access
    // This allows coalesced texture sampling with better cache coherence
    float2 samplingOffsets[9];
    
    [unroll]
    for (int i = 0; i < 9; i++)
    {
        samplingOffsets[i] = offsets[i].x * du + offsets[i].y * dv;
    }
    
    // Batch sample all neighborhood pixels for better texture cache utilization
    float4 samples[9];
    
    [unroll]
    for (int i = 0; i < 9; i++)
    {
        samples[i] = ReflectReprojParams(ReflectSolve).Sample(PointClampSampler, localUv.xy + samplingOffsets[i]);
    }
    
    // Determine clamping approach based on feature flag
#ifdef ENABLE_VARIANCE_CLAMP
    // Advanced variance-based clamping optimized for performance
    
    // Optimization: Pre-calculate weights and avoid redundant exponentiation
    // Precalculate the 9 sample weights with minimal operations
    float sampleWeights[9];
    float weightSum = 0.0;
    
    // Optimization: Use constant values for common offsets to avoid redundant calculations
    // Center offset (index 4) weight is always 1.0 (exp(-0 * 0.5) = 1.0)
    sampleWeights[4] = 1.0;
    
    // Corner weights (indices 0,2,6,8) are all the same: exp(-2 * 0.5) = exp(-1) ≈ 0.368
    // Diagonal distance = sqrt(1²+1²) = sqrt(2), and sqrt(2)² = 2
    const float CORNER_WEIGHT = 0.368;
    sampleWeights[0] = sampleWeights[2] = sampleWeights[6] = sampleWeights[8] = CORNER_WEIGHT;
    
    // Edge weights (indices 1,3,5,7) are all the same: exp(-1 * 0.5) = exp(-0.5) ≈ 0.607
    const float EDGE_WEIGHT = 0.607;
    sampleWeights[1] = sampleWeights[3] = sampleWeights[5] = sampleWeights[7] = EDGE_WEIGHT;
    
    // Sum weights - optimization: use constant sum value since weights are fixed
    // 1.0 + 4*0.368 + 4*0.607 = 1.0 + 1.472 + 2.428 = 4.9
    weightSum = 4.9;
    
    // Calculate inverse weight sum once for faster normalization
    float invWeightSum = rcp(weightSum);
    
    // Normalize weights via multiplication instead of division (faster)
    [unroll]
    for (int j = 0; j < 9; j++)
    {
        sampleWeights[j] *= invWeightSum;
    }
    
    // Calculate weighted mean with optimized loop ordering for better ALU utilization
    float4 weightedMean = 0;
    
    // Unroll this manually for better compiler optimization
    // Center sample contributes the most and can be calculated first
    weightedMean = samples[4] * sampleWeights[4];
    
    // Add corner samples (indices 0,2,6,8)
    float cornerWeight = sampleWeights[0]; // All corners have same weight
    float4 cornerSum = samples[0] + samples[2] + samples[6] + samples[8];
    weightedMean += cornerSum * cornerWeight;
    
    // Add edge samples (indices 1,3,5,7)
    float edgeWeight = sampleWeights[1]; // All edges have same weight
    float4 edgeSum = samples[1] + samples[3] + samples[5] + samples[7];
    weightedMean += edgeSum * edgeWeight;
    
    // Calculate variance with minimal register pressure
    // Pre-initialize with zero to avoid uninitialized variable warning
    float4 variance = 0;
    
    // Calculate variance more efficiently
    [unroll]
    for (int m = 0; m < 9; m++)
    {
        float4 diff = samples[m] - weightedMean;
        // Use efficient vectorized multiply-add
        variance = mad(diff * diff, sampleWeights[m], variance);
    }
    
    // Apply variance scaling with minimal operations
    // Combine scaling steps to reduce instruction count
    const float DIST_SCALE_FACTOR = 0.5;
    float finalVarianceScale = varianceScale * (1.0 + hitDistanceFactor * DIST_SCALE_FACTOR);
    
    // Optimization: Calculate standard deviation with better precision safety
    float4 stdDev = sqrt(max(0.0001, variance));
    
    // Calculate bounds using fused multiply-add for better performance
    // Avoid redundant operations by calculating scale*stdDev once
    float4 scaledStdDev = stdDev * finalVarianceScale;
    float4 currentMin = weightedMean - scaledStdDev;
    float4 currentMax = weightedMean + scaledStdDev;
#else
    // Simple min/max clamping (faster but less accurate)
    float4 currentMin = samples[0];
    float4 currentMax = samples[0];
    
    [unroll]
    for (int j = 1; j < 9; j++)
    {
        currentMin = min(currentMin, samples[j]);
        currentMax = max(currentMax, samples[j]);
    }

    // Apply scale to the min/max bounds relative to center
    float4 center = (currentMin + currentMax) * 0.5f;
    float4 extent = (currentMax - currentMin) * 0.5f;
    
    // Scale bounds based on roughness - smoother surfaces = tighter bounds
    float boundScale = lerp(1.1, TemporalScale, saturate(roughness * 4.0));
    
    currentMin = center - extent * boundScale;
    currentMax = center + extent * boundScale;
#endif
    
    // Clamp previous value to neighborhood bounds to prevent ghosting
    previous = clamp(previous, currentMin, currentMax);

    // Optimization: Use RGB luminance constants as a vector for better ALU utilization
    static const float3 LUMA_WEIGHTS = float3(0.299, 0.587, 0.114);
    
    // Batch dot products for better ALU parallelism
    float2 frameChannelSums = float2(
        dot(current.rgb, LUMA_WEIGHTS),
        dot(previous.rgb, LUMA_WEIGHTS)
    );
    
    // Name the channel sums for clarity
    float currentLuma = frameChannelSums.x;
    float previousLuma = frameChannelSums.y;
    
    // Optimization: Calculate motion metrics in a single batch
    // Use fast length approximation for better performance
    float motionLengthSq = dot(motion, motion);
    float motionLength = sqrt(motionLengthSq); // Only one sqrt operation needed
    
    // Optimization: Use fast approximation for sqrt in motion factor
    // Approximate sqrt(x) with x/sqrt(1+x) for small x
    float sqrtApproxArg = motionLength;
    float sqrtApprox = sqrtApproxArg * rsqrt(1.0 + sqrtApproxArg);
    
    // Calculate motion factor with optimized multiply-add operations
    // Pre-compute the scaled factor to avoid redundant multiplications
    const float SCALED_MOTION_FACTOR = MOTION_SCALE_FACTOR * 0.7;
    float motionFactor = saturate(1.0 - sqrtApprox * SCALED_MOTION_FACTOR);
    
#ifdef ENABLE_ROUGHNESS_ADAPTIVE_BLEND
    // Optimization: Pre-compute roughness-derived values once to avoid redundancy
    const float oneMinusRoughness = 1.0 - roughness;
    const float roughnessScaleFactor = oneMinusRoughness * ROUGHNESS_ADAPT_FACTOR;
    float roughnessInfluence = saturate(roughnessScaleFactor);
    
    // Optimize lerp calculation with pre-computed constants
    const float RESPONSE_DELTA = ResponseSpeed - 0.9;
    float baseResponseSpeed = 0.9 + RESPONSE_DELTA * roughnessInfluence;
    
    // Optimize distance calculation with single reciprocal
    const float RCP_HALF_MAX_DIST = 2.0 / MAX_RAY_DISTANCE;
    float distanceInfluence = saturate(1.0 - hitDistance * RCP_HALF_MAX_DIST);
    
    // Optimize lerp with pre-computed scale factor
    const float RESPONSE_SCALE = 0.85;
    float adaptiveResponse = baseResponseSpeed * (RESPONSE_SCALE + (1.0 - RESPONSE_SCALE) * distanceInfluence);
    
    // Use fused multiply-add for temporal weight calculation
    // Sets a minimum weight threshold for stability
    const float MIN_TEMPORAL_WEIGHT = 0.3;
    float rawTemporalWeight = adaptiveResponse * motionFactor * consistencyFactor;
    float temporalWeight = max(MIN_TEMPORAL_WEIGHT, saturate(rawTemporalWeight));
#else
    // Optimize standard blend with precalculated constants
    const float MIN_STANDARD_WEIGHT = 0.2;
    float temporalWeight = max(MIN_STANDARD_WEIGHT, saturate(ResponseSpeed * motionFactor));
#endif
    
    // Brightness stability correction optimized for better ALU utilization
    // Calculate luma ratio with branch-free operation to handle division-by-zero
    float safeRatio = (previousLuma > 0.001) ? currentLuma / previousLuma : 1.0;
    float lumaRatio = min(safeRatio, 2.0);
    
    // Use step function to avoid branches for conditional correction
    float needsCorrection = step(1.1, lumaRatio) * step(0.001, motionLength);
    
    if (needsCorrection > 0.5)
    {
        // Optimize brightness correction with minimal operations
        // Using reciprocal multiplication for better performance
        float correctionBlend = saturate(motionLength * 2.0);
        float invLumaRatio = rcp(lumaRatio); // Faster than 1.0/lumaRatio
        float brightnessCorrection = lerp(1.0, invLumaRatio, correctionBlend);
        
        // Apply correction with minimal register pressure
        current.rgb *= brightnessCorrection;
    }
    
    // Final blend using optimized lerp operation
    output.color0 = lerp(current, previous, temporalWeight);
    return output;
}
