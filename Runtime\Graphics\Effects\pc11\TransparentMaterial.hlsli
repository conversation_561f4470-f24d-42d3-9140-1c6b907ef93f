
#include "Generated/Materials_High_TransparentGbufferStaticProgram.hlsli"
#include "Generated/Materials_High_TransparentGbufferSkinnedProgram.hlsli"
#include "Generated/Materials_High_TransparentMultibumpGbufferStaticProgram.hlsli"
#include "Generated/Materials_High_TransparentMultibumpGbufferSkinnedProgram.hlsli"
#include "Generated/Materials_High_TransparentMultibumpDiffractGbufferStaticProgram.hlsli"
#include "Generated/Materials_High_TransparentMultibumpDiffractGbufferSkinnedProgram.hlsli"
#include "Generated/Materials_High_TransparentMultibumpEnvironmentDiffractGbufferStaticProgram.hlsli"
#include "Generated/Materials_High_TransparentMultibumpEnvironmentDiffractGbufferSkinnedProgram.hlsli"

#include "Generated/Materials_Low_TransparentGbufferStaticProgram.hlsli"
#include "Generated/Materials_Low_TransparentGbufferSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TransparentMultibumpGbufferStaticProgram.hlsli"
#include "Generated/Materials_Low_TransparentMultibumpGbufferSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TransparentMultibumpDiffractGbufferStaticProgram.hlsli"
#include "Generated/Materials_Low_TransparentMultibumpDiffractGbufferSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TransparentMultibumpEnvironmentDiffractGbufferStaticProgram.hlsli"
#include "Generated/Materials_Low_TransparentMultibumpEnvironmentDiffractGbufferSkinnedProgram.hlsli"

#include "../Common/MathUtil.hlsli"
#include "../Common/StereoUtil.hlsli"
#include "../Common/MotionUtil.hlsli"
#include "ScrollingUtil.hlsli"
#include "RenderUtil.hlsli"

static const uint cMultibumpOctaves = 5;
static uint cBlendFlag_AdvAlbedoBlend = 1 << 0;


struct NormalRoughnessInfo
{
#ifdef COMPACT_GBUFFER
    float2 normalOctUnorm;
    float roughness;
#else
    float2 normalOct;
    float3 varCovar;
    float2 dInvW_dXY;
    float visibleReflectance;
#endif
};


#ifdef SCROLLING
float2 CalcualteFluidDistortion(float2 uv)
{
    if (MaterialInfo(UvFluidDistortion))
    {
        float localTimeSeconds = (WorldInfo(WorldTimeSeconds) - ModelInfo(InitialTime)) * MaterialInfo(UvFluidDistortion) * 0.25;

        float X = uv.x * MaterialInfo(UvFluidDistortionScale) + localTimeSeconds;
        float Y = uv.y * MaterialInfo(UvFluidDistortionScale) + localTimeSeconds;
        uv.y += cos(X + Y) * MaterialInfo(UvFluidDistortionAmp) * 0.025 * cos(Y);
        uv.x += sin(X - Y) * MaterialInfo(UvFluidDistortionAmp) * 0.025 * sin(Y);
    }
    return uv;
}
#endif

float2 nth_weyl(float2 p, float n)
{
    return frac(p + n * (float2(4651903., 2588041.) * exp2(-23.)));
}

float2 SampleBump(float2 uv, float2 offset)
{
#ifdef BUMPLESS
    return 0.;
#elif defined(MULTIBUMP)
    float2 bump = 0.;
    float amp = MaterialInfo(BumpScale);
    float k = 1.;
    [unroll]
    for (uint i = 0; i < cMultibumpOctaves; i++)
    {
        bump += slopeFromUnorm8(MaterialInfo(NormalMap).SampleBias(AnisoSampler, (uv + nth_weyl(0., i))/k + frac(rsqrt(k)*offset), CameraInfo(GlobalMipBias)).xy)*amp;
        k *= MaterialInfo(WavelengthDecay);
        amp *= MaterialInfo(BumpScaleDecay);
    }
    return bump;
#else
    return slopeFromUnorm8(MaterialInfo(NormalMap).SampleBias(AnisoSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias)).xy);
#endif
}

float2 SampleVariance(float2 uv, float2 offset)
{
#ifdef BUMPLESS
    return square(1./32768.);
#elif defined(MULTIBUMP)
    float2 variance = 0.;
    float amp = MaterialInfo(BumpScale);
    float k = 1.;
    [unroll]
    for (uint i = 0; i < cMultibumpOctaves; i++)
    {
        variance += square(MaterialInfo(RoughnessMap).Sample(AnisoSampler, (uv + nth_weyl(0., i))/k + frac(rsqrt(k)*offset)).xy*amp);
        k *= MaterialInfo(WavelengthDecay);
        amp *= MaterialInfo(BumpScaleDecay);
    }
    return max(variance, square(1./32768.));
#else
    float2 roughness = MaterialInfo(RoughnessMap).Sample(AnisoSampler, uv * MaterialInfo(UvScale)).xy;
    roughness += 1. / 32768.;
    return roughness * roughness;
#endif
}

struct VertexOutput
{
    float4 position : SV_Position;
    float4 history : TEXCOORD0;
    half4 tangent_u : TEXCOORD1;
    half4 bitangent_v : TEXCOORD2;
    half4 view_handedness : TEXCOORD3;
    nointerpolation half handedness : TEXCOORD4;
#if defined(MULTIBUMP)
    float2 uvOffset : TEXCOORD5;
#endif
    float stereoClip : SV_ClipDistance;
};

struct PixelInput
{
    float4 position : SV_Position;
    float4 history : TEXCOORD0;
    half4 tangent_u : TEXCOORD1;
    half4 bitangent_v : TEXCOORD2;
    half4 view_handedness : TEXCOORD3;
    nointerpolation half handedness : TEXCOORD4;
#if defined(MULTIBUMP)
    float2 uvOffset : TEXCOORD5;
#endif
};

float2 EncodeNormal(float3 normal, bool isFrontFace)
{
    normal = isFrontFace ? normal : -normal;
    float2 encoded = float3ToOctahedral2(normal);
    return encoded;
}

NormalRoughnessInfo GenerateNormalRoughnessInfo(float2 bump, float2 variance, float3 tangent, float3 bitangent, float3 view, float handedness, bool isFrontFace, float positionClipW, inout float3 normal)
{
    NormalRoughnessInfo result;

    float3 geoNormal = normalize(cross(tangent, bitangent));

    // build surface slope basis as reciprocal of tangent basis (important for correct stretching/shearing)
    float3 du = cross(bitangent, geoNormal);
    float3 dv = cross(geoNormal, tangent);
    du /= dot(du, tangent);
    dv /= dot(dv, bitangent);

    normal = geoNormal * handedness + bump.x * du + bump.y * dv;

#ifdef COMPACT_GBUFFER
    result.normalOctUnorm = snormToUnorm(EncodeNormal(normal, isFrontFace));
    result.roughness = sqrt(0.5 * (variance.x + variance.y));
#else

    normal = normalize(normal);
    float rcpw = rcp(positionClipW);

    result.normalOct = EncodeNormal(normal, isFrontFace);
    result.dInvW_dXY = float2(ddx(rcpw), ddy(rcpw));

    float3 viewtangent = normalize(view - normal * dot(view, normal));
    float3 viewbitangent = cross(normal, viewtangent);
    float2 roughness = sqrt(variance);

    // pixel footprint spec filter
    float3 geoDeviationBounds = abs(ddx_fine(geoNormal)) + abs(ddy_fine(geoNormal));

    roughness.x = max(roughness.x, 0.5 * dot(abs(tangent), geoDeviationBounds));
    roughness.y = max(roughness.y, 0.5 * dot(abs(bitangent), geoDeviationBounds));

    float apparentRoughnessSinTheta = roughness.x * abs(dot(view, du)) + roughness.y * abs(dot(view, dv));
    result.visibleReflectance = square(TransparentStageParams(DielectricLookup).SampleLevel(LinearClampSampler, float2(apparentRoughnessSinTheta, dot(view, normal) * 0.5 + 0.5), 0).r);

    // project into view-tangent covariance
    float2x2 covar = mul(float2x3(du, dv), transpose(float2x3(viewtangent, viewbitangent)));
    covar[0] *= roughness.x;
    covar[1] *= roughness.y;

    covar = mul(transpose(covar), covar);

    result.varCovar.x = sqrt(covar[0].x); // roughness
    result.varCovar.y = sqrt(covar[1].y); // roughness
    result.varCovar.z = covar[0].y * rsqrt(covar[0].x * covar[1].y); // correlation coefficient
#endif

    return result;
}


#ifdef DIFFRACTION
void takeNearestZ(float2 loc, in out float nearestZ, in out float2 locOfNearest)
{
    float z = DiffractionAlbedoParams(OpaqueDepth)[loc].x;
    nearestZ = max(nearestZ, z);
    locOfNearest = (nearestZ == z ? loc : locOfNearest);
}

float3 SampleDiffractedClamp(float2 uv, float2 clampedUv, float2 clampedUvMotion, float2 uvOffset)
{
    float2 uvAdj = clampedUv + uvOffset;
    float2 uvAdjMotion = clampedUvMotion + uvOffset;
    float opaqueDepth = DiffractionAlbedoParams(OpaqueDepth).Sample(PointClampSampler, uvAdj).r;
    float diffractDepth = DiffractionAlbedoParams(DiffractDepth).Sample(PointClampSampler, uvAdjMotion).r;

    if (opaqueDepth > diffractDepth)
    {
        uvAdj = uv;
    }

    return DiffractionAlbedoParams(Albedo).Sample(PointClampSampler, uvAdj).rgb;
}

float3 SampleDiffractedAlbedo(float2 screenPosition, float3 normal, float3 view, float2 materialUv)
{
    float2 motion = DiffractionAlbedoParams(Motion)[screenPosition].xy;

    float2 uv = screenPosition / CameraInfo(RenderTargetSize).xy;
    float2 uvMotion = (screenPosition - motion) / CameraInfo(RenderTargetSize).xy;
    float2 pixel = 1 / CameraInfo(RenderTargetSize).xy;
    bool stereoSide = (screenPosition.x >= CameraInfo(RenderTargetSize).x * 0.5);
    float3 viewNorm = mul((float3x3)stereoGetWorldToEye(stereoSide), normal);
#ifdef SCROLLING
    float2 diffractedUv = CalcualteFluidDistortion(uv) + (viewNorm.xy * MaterialInfo(DiffractiveIndex) * 0.01);
    float2 diffractedMaterialUv = CalcualteFluidDistortion(materialUv) + (viewNorm.xy * MaterialInfo(DiffractiveIndex) * 0.01);
#else
    float2 diffractedUv = uv + (viewNorm.xy * MaterialInfo(DiffractiveIndex) * 0.01);
    float2 diffractedMaterialUv = materialUv + (viewNorm.xy * MaterialInfo(DiffractiveIndex) * 0.01);
#endif
    float2 diffractedUvMotion = uvMotion + (viewNorm.xy * MaterialInfo(DiffractiveIndex) * 0.01);
    float2 clampedDiffractUv = clamp(diffractedUv.xy, 0, 1 - pixel);
    float2 clampedDiffractUvMotion = clamp(diffractedUvMotion.xy, 0, 1 - pixel);

    float opaqueDepth = DiffractionAlbedoParams(OpaqueDepth).Sample(PointClampSampler, uv).r;
    float diffractDepth = DiffractionAlbedoParams(DiffractDepth).Sample(PointClampSampler, uvMotion).r;

    float3 color;
    if (opaqueDepth > diffractDepth || diffractDepth == 0)
    {
        return DiffractionAlbedoParams(Albedo).Sample(PointClampSampler, uv).rgb;
    }
    else if (clampedDiffractUv.x != diffractedUv.x || clampedDiffractUv.y != diffractedUv.y)
    {
        color = DiffractionAlbedoParams(Albedo).Sample(PointClampSampler, uv).rgb;
    }
    else
    {
        opaqueDepth = DiffractionAlbedoParams(OpaqueDepth).Sample(PointClampSampler, clampedDiffractUv).r;
        diffractDepth = DiffractionAlbedoParams(DiffractDepth).Sample(PointClampSampler, clampedDiffractUvMotion).r;

        if (opaqueDepth > diffractDepth)
        {
            float2 width = pixel * 2.5;
            color = SampleDiffractedClamp(uv, clampedDiffractUv, clampedDiffractUvMotion, float2(width.x, 0));
            color += SampleDiffractedClamp(uv, clampedDiffractUv, clampedDiffractUvMotion, float2(-width.x, 0));
            color += SampleDiffractedClamp(uv, clampedDiffractUv, clampedDiffractUvMotion, float2(0, -width.y));
            color += SampleDiffractedClamp(uv, clampedDiffractUv, clampedDiffractUvMotion, float2(0, width.y));
            color /= 4;
        }
        else
        {
            color = DiffractionAlbedoParams(Albedo).Sample(PointClampSampler, clampedDiffractUv).rgb;
        }
    }

    color *= MaterialInfo(AlbedoMap).Sample(AnisoSampler, diffractedMaterialUv * MaterialInfo(UvScale)).rgb;
    color *= MaterialInfo(Tint);
    color = saturate(color.rgb + 1. / 255.);

    return color;
}
#endif

PixelOutput PopulateGbuffer(float4 color, NormalRoughnessInfo normalRoughness)
{
    PixelOutput output;

#ifdef COMPACT_GBUFFER
    {
        output.color0.rgb = color.rgb;
        output.color0.w = color.a;
        output.color1.xy = normalRoughness.normalOctUnorm;
        output.color1.z = normalRoughness.roughness;
        output.color1.w = 0.;
    }
#else
    {
        output.color0.rgb = color.rgb;
        output.color0.w = color.a;
        output.color1.xy = normalRoughness.normalOct;
        output.color1.zw = normalRoughness.dInvW_dXY;
        output.color2.xyz = normalRoughness.varCovar;
#ifndef DIFFRACTION
        output.color2.w = -normalRoughness.visibleReflectance;
#endif
#ifdef REFLECTIONFLAGS
        // The design intent behind this is to smuggle reflection disable and compatibility
        // flags within the exponent and sign bits of the roughness value as described below.
        // Roughness ==  1.0  is used to disable reflection
        // Rougeness == -1.0 will skip motions during reprojection
        // Roughness == -2.0 will skip temporal accumulation during reflection
        output.color1.w = reflectionSkipMotionVectors() ? -1.0 :  output.color1.w;
        output.color1.w = reflectionSkipAccumulation() ? -2.0 :  output.color1.w;
        output.color1.w += reflectionReceiver() ? 0.0 : 1.0 + floor(abs(output.color1.w));
#endif
    }
#endif

    return output;
}

PixelOutput PixelMain(PixelInput input, bool isFrontFace : SV_IsFrontFace)
{
    float2 uv = float2(input.tangent_u.w, input.bitangent_v.w);

#ifdef SCROLLING
    uv = CalcualteFluidDistortion(uv);
#endif
#if defined(MULTIBUMP)
    float2 uvOffset = input.uvOffset;
#else
    float2 uvOffset = 0.;
#endif

    float3 view = normalize(input.view_handedness.xyz);

    float2 bump = SampleBump(uv, uvOffset);
    float2 variance = SampleVariance(uv, uvOffset);
    float3 normal;

    NormalRoughnessInfo normalRoughness = GenerateNormalRoughnessInfo(bump, variance, input.tangent_u.xyz, input.bitangent_v.xyz, view, input.view_handedness.w, isFrontFace, input.position.w, normal);

#ifdef DIFFRACTION
    float4 color = float4(SampleDiffractedAlbedo(input.position.xy, normal, view, uv), 1);
    return PopulateGbuffer(color, normalRoughness);
#else
    if ((uint(hsum(input.position.xy)) & 1) == (CameraInfo(FrameNumber) & 1))
    {
        discard;
    }
    float4 color = (0).xxxx;
    uint blendFlags = MaterialInfo(BlendFlags);
    bool advAlbedoBlend = blendFlags & cBlendFlag_AdvAlbedoBlend;
    if (advAlbedoBlend)
    {
        float4 color = MaterialInfo(AlbedoMap).Sample(AnisoSampler, uv * MaterialInfo(UvScale));
        color.rgb *= MaterialInfo(Tint);
        color.rgb = saturate(color.rgb + 1. / 255.);
    }
#endif

    return PopulateGbuffer(color, normalRoughness);
}
