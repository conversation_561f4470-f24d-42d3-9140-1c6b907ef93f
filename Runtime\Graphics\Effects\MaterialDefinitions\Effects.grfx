local core = Common.Core
local targets = Common.Targets
local blendSettings = Common.BlendSettings
local samplers = Common.Samplers

local variant = current_variant()

Highlight_Depth               = target_signature { Colors = targets.RasterLDR_Depth.Colors, Depth = targets.JustDepth.Depth }
Gbuffer1Variant_Depth         = target_signature { Colors = (variant == 'High' and targets.Gbuffer1High_Depth.Colors         or targets.Gbuffer1Low_Depth.Colors), Depth = targets.JustDepth.Depth }
GbufferVariant_Motion_Depth   = target_signature { Colors = (variant == 'High' and targets.GbufferHigh_Motion_Depth.Colors   or targets.GbufferLow_Motion_Depth.Colors), Depth = targets.JustDepth.Depth }
GbufferVariant_Depth          = target_signature { Colors = (variant == 'High' and targets.GbufferHigh_Depth.Colors          or targets.GbufferLow_Depth.Colors), Depth = targets.JustDepth.Depth }
GbufferVariant_Emissive_Depth = target_signature { Colors = targets.Gbuffer_Emissive_Depth.Colors, Depth = targets.JustDepth.Depth }
DiffractAlbedoVariant_Depth   = target_signature { Colors = (variant == 'High' and targets.DiffractAlbedoHigh.Colors         or targets.DiffractAlbedoLow.Colors), Depth = targets.JustDepth.Depth }

if variant == 'High' then
    DeferredBuffers = frame_parameters {
        Gbuffer1 = texture2d(float4),
        Gbuffer2 = texture2d(float4),
        Gbuffer3 = texture2d(float4),
        Depth = texture2d(float),
        Visibility = texture2d(float4),
        DielectricLookup = texture2d(float4),
        Noise = texture2d(float4),
        ProbeCoeffsL1 = buffer(float4),
        SkyCoeffsL2 = buffer(float4),
    }
else
    DeferredBuffers = frame_parameters {
        Gbuffer1 = texture2d(float4),
        Gbuffer2 = texture2d(float4),
        Depth = texture2d(float),
        Visibility = texture2d(float4),
        DielectricLookup = texture2d(float4),
        Noise = texture2d(float4),
        ProbeCoeffsL1 = buffer(float4),
        SkyCoeffsL2 = buffer(float4),
    }
end

DisplacementDepthStageParams = frame_parameters {
    UvFrames = float2(1.0, 1.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    ScrollRate = float2(0.0, 0.0),
    ScrollFlags = uint(0),
    Frame = float(0.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

VATDepthStageParams = frame_parameters {
    VatFlags = uint(0),
    VATMap = texture2d(float4),
    VATScale = float3(1,1,1),
    StepRate = float(0.0),
    Frame = float(0.0),
}

DisplacementDepthOnlyEffectType = graphics_effect_type {
    TargetSignature = targets.JustDepth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, DisplacementDepthStageParams =  DisplacementDepthStageParams },
}

VATDepthOnlyEffectType = graphics_effect_type {
    TargetSignature = targets.JustDepth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, VATDepthStageParams =  VATDepthStageParams },
}

DepthOnlyEffectType = graphics_effect_type {
    TargetSignature = targets.JustDepth,
    StageParameters = { CameraInfo = core.CameraInfo },
}

TransDiffractDepthOnlyEffectType = graphics_effect_type {
    TargetSignature = targets.JustDepth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
}

ShadowDepthOnlyEffectType = graphics_effect_type {
    TargetSignature = targets.JustShadowDepth,
    StageParameters = { ShadowProjectionInfo = core.ShadowProjectionInfo, WorldInfo = core.WorldInfo },
}

ShadowCascadeDepthOnlyEffectType = graphics_effect_type {
    TargetSignature = targets.JustShadowDepth,
    StageParameters = { ShadowProjectionInfo = core.ShadowProjectionInfo, WorldInfo = core.WorldInfo },
}
ShadowCascadeDepthOnlyFadingEffectType = graphics_effect_type {
    TargetSignature = targets.JustShadowDepth,
    StageParameters = { ShadowProjectionInfo = core.ShadowProjectionInfo, WorldInfo = core.WorldInfo },
}

GbufferMotionEffectType = graphics_effect_type {
    TargetSignature = GbufferVariant_Motion_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, MediaParams = core.MediaParams },
}
GbufferMotionFadingEffectType = graphics_effect_type {
    TargetSignature = GbufferVariant_Motion_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, MediaParams = core.MediaParams },
}

VRVisDepthEffectType = graphics_effect_type {
    TargetSignature = targets.JustDepth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, MediaParams = core.MediaParams },
}

DiffractionAlbedoParams = frame_parameters {
    Albedo = texture2d(float4),
    Motion = texture2d(float2),
    OpaqueDepth = texture2d(float),
    DiffractDepth = texture2d(float),
}

GbufferMotionDiffractionEffectType = graphics_effect_type {
    TargetSignature = targets.DiffractAlbedo,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, DiffractionAlbedoParams = DiffractionAlbedoParams },
}

GbufferEffectType = graphics_effect_type {
    TargetSignature = GbufferVariant_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
}

TransparentStageParams = frame_parameters {
    DielectricLookup = texture2d(float4),
}

TransparentGbufferEffectType = graphics_effect_type {
    TargetSignature = GbufferVariant_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, TransparentStageParams = TransparentStageParams },
}

TransparentDiffractGbufferEffectType = graphics_effect_type {
    TargetSignature = GbufferVariant_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, TransparentStageParams = TransparentStageParams, DiffractionAlbedoParams = DiffractionAlbedoParams },
}

TransmittanceEffectType = graphics_effect_type {
    TargetSignature = targets.RasterLDR_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
}

EmissiveEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, MediaParams = core.MediaParams },
}

EnvironmentEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, SkyParams = core.SkyParams, DeferredBuffers = DeferredBuffers },
}

SubsurfaceGatherEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, SubsurfaceParams = core.SubsurfaceParams },
}

UnlitAbsorptionEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
}

UnlitAbsorptionEmissiveEffectType = graphics_effect_type {
    TargetSignature = GbufferVariant_Emissive_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
}

UnlitEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
}

UnlitEmissiveEffectType = graphics_effect_type {
    TargetSignature = GbufferVariant_Emissive_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
}

TransmissiveEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
}

MediaAddEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, MediaParams = core.MediaParams, WorldInfo = core.WorldInfo },
}

MediaGbufferMotionEffectType = graphics_effect_type {
    TargetSignature = GbufferVariant_Motion_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, MediaParams = core.MediaParams, WorldInfo = core.WorldInfo },
}

TriangleDensityEffectType = graphics_effect_type {
    TargetSignature = targets.RasterLDR_Motion_Depth,
    StageParameters = { CameraInfo = core.CameraInfo, DiagnosticsInfo = core.DiagnosticsInfo },
}

OverdrawEffectType = graphics_effect_type {
    TargetSignature = targets.RasterLDR_Motion_Depth,
    StageParameters = { CameraInfo = core.CameraInfo },
}

WireframeEffectType = graphics_effect_type {
    TargetSignature = targets.RasterLDR_Motion_Depth,
    StageParameters = { CameraInfo = core.CameraInfo },
}

BackfacesEffectType = graphics_effect_type {
    TargetSignature = targets.RasterLDR_Motion_Depth,
    StageParameters = { CameraInfo = core.CameraInfo },
}

DiagnosticFillDepthEffectType = graphics_effect_type {
    TargetSignature = targets.JustDepth,
    StageParameters = { CameraInfo = core.CameraInfo },
}

DisplacementPreZBin = render_bin {
    RenderOrder = RenderOrder.NearToFar,
    GraphicsEffectTypes = { DisplacementDepthOnlyEffectType },
}

VATPreZBin = render_bin {
    RenderOrder = RenderOrder.NearToFar,
    GraphicsEffectTypes = { VATDepthOnlyEffectType },
}

TransDiffractPreZBin = render_bin {
    RenderOrder = RenderOrder.NearToFar,
    GraphicsEffectTypes = { TransDiffractDepthOnlyEffectType },
}

PreZBin = render_bin {
    RenderOrder = RenderOrder.NearToFar,
    GraphicsEffectTypes = { DepthOnlyEffectType },
}

StaticCasterBin = render_bin {
    RenderOrder = RenderOrder.Performance,  -- not using NearToFar because trying to preserve texture sorting as much as possible (TODO separate bin for alpha mask shadows?)
    GraphicsEffectTypes = { ShadowDepthOnlyEffectType, ShadowCascadeDepthOnlyEffectType },
}
StaticCasterFadingBin = render_bin {
    RenderOrder = RenderOrder.Performance,  -- not using NearToFar because trying to preserve texture sorting as much as possible (TODO separate bin for alpha mask shadows?)
    GraphicsEffectTypes = { ShadowDepthOnlyEffectType, ShadowCascadeDepthOnlyFadingEffectType },
}

DynamicCasterBin = render_bin {
    RenderOrder = RenderOrder.Performance,  -- not using NearToFar because trying to preserve texture sorting as much as possible (TODO separate bin for alpha mask shadows?)
    GraphicsEffectTypes = { ShadowDepthOnlyEffectType, ShadowCascadeDepthOnlyEffectType },
}
DynamicCasterFadingBin = render_bin {
    RenderOrder = RenderOrder.Performance,  -- not using NearToFar because trying to preserve texture sorting as much as possible (TODO separate bin for alpha mask shadows?)
    GraphicsEffectTypes = { ShadowDepthOnlyEffectType, ShadowCascadeDepthOnlyFadingEffectType },
}

local effectTypesCommon = { core.DebugGeometryEffectType, core.HighlightMaskEffectType, core.HighlightEffectType, TriangleDensityEffectType, OverdrawEffectType, WireframeEffectType, BackfacesEffectType, DiagnosticFillDepthEffectType }

LitOpaqueBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, GbufferMotionEffectType },
}
LitOpaqueFadingBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, GbufferMotionFadingEffectType },
}

UnlitOpaqueDiffractionBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, GbufferMotionDiffractionEffectType },
}

EmissiveOpaqueBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, GbufferMotionEffectType, EmissiveEffectType },
}

VRVisDepthBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { VRVisDepthEffectType },
}

EnvironmentOpaqueBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, EnvironmentEffectType },
}

SubsurfaceBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, GbufferMotionEffectType, SubsurfaceGatherEffectType },
}

UnlitAlphaBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { UnlitAbsorptionEffectType, UnlitEffectType },
}

UnlitAlphaEmissiveBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { UnlitAbsorptionEmissiveEffectType, UnlitEmissiveEffectType },
}

EmissiveTransmissiveBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, TransmissiveEffectType, EmissiveEffectType },
}

PureEmissiveBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, EmissiveEffectType },
}

TransparentBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, TransparentGbufferEffectType, TransmittanceEffectType },
}

TransparentDiffractBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, TransparentDiffractGbufferEffectType, TransmittanceEffectType },
}

MediaBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, GbufferMotionEffectType, MediaAddEffectType },
}

ChromaKeyMediaBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = flatten { effectTypesCommon, MediaGbufferMotionEffectType, MediaAddEffectType },
}

SolidCasterCommon = partial(graphics_program) {
    Parameters = {ShadowProjectionInfo = core.ShadowProjectionInfo},
    PixelOutput = targets.JustShadowDepth,
    BlendState = blendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Greater },
}

SolidCasterStaticProgram = graphics_program {
    SolidCasterCommon,
    CullMode = CullMode.None,
    Parameters = {ModelInfo = core.StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "SolidCasterStatic",
}
SolidCasterCascadeStaticProgram = graphics_program {
    SolidCasterCommon,
    CullMode = CullMode.None,
    Parameters = {ModelInfo = core.StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "CascadedCaster",
}
SolidCasterCascadeFadingStaticProgram = graphics_program {
    SolidCasterCommon,
    CullMode = CullMode.None,
    Parameters = {ModelInfo = core.StaticModelInfo, WorldInfo = core.WorldInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "DISTANCE_FADE" },
}

SolidCasterSkinnedProgram = graphics_program {
    SolidCasterCommon,
    CullMode = CullMode.Back,
    Parameters = {ModelInfo = core.RiggedModelInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "SolidCasterSkinned",
}
SolidCasterCascadeSkinnedProgram = graphics_program {
    SolidCasterCommon,
    CullMode = CullMode.Back,
    Parameters = {ModelInfo = core.RiggedModelInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "CascadedCaster",
    Macros = { "SKINNED" },
}
SolidCasterCascadeFadingSkinnedProgram = graphics_program {
    SolidCasterCommon,
    CullMode = CullMode.Back,
    Parameters = {ModelInfo = core.RiggedModelInfo, WorldInfo = core.WorldInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "DISTANCE_FADE", "SKINNED" },
}

SolidCasterStaticEffect = graphics_effect {
    Type = ShadowDepthOnlyEffectType,
    Program = SolidCasterStaticProgram,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo'},
}
SolidCasterCascadeStaticEffect = graphics_effect {
    Type = ShadowCascadeDepthOnlyEffectType,
    Program = SolidCasterCascadeStaticProgram,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo'},
}
SolidCasterCascadeFadingStaticEffect = graphics_effect {
    Type = ShadowCascadeDepthOnlyFadingEffectType,
    Program = SolidCasterCascadeFadingStaticProgram,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo', WorldInfo = 'WorldInfo'},
}

SolidCasterSkinnedEffect = graphics_effect {
    Type = ShadowDepthOnlyEffectType,
    Program = SolidCasterSkinnedProgram,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo'},
}
SolidCasterCascadeSkinnedEffect = graphics_effect {
    Type = ShadowCascadeDepthOnlyEffectType,
    Program = SolidCasterCascadeSkinnedProgram,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo'},
}
SolidCasterCascadeFadingSkinnedEffect = graphics_effect {
    Type = ShadowCascadeDepthOnlyFadingEffectType,
    Program = SolidCasterCascadeFadingSkinnedProgram,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo', WorldInfo = 'WorldInfo'},
}