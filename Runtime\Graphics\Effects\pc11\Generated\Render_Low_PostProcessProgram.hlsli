//
// Generated by GraphicsBuild. Do not modify.
//

#if !defined(INCLUDED_Render_Low_PostProcessProgram) && defined(COMPILING_Render_Low_PostProcessProgram)
#define INCLUDED_Render_Low_PostProcessProgram


// Effect-provided macros
//

#define CHEAPER

// Accessor macros (for HLSL/GLSL/Metal shader source compatibility)
//

#define BloomParams(member)                         BloomParams_##member
#define CameraInfo(member)                          CameraInfo_##member
#define PostEffectParams(member)                    PostEffectParams_##member
#define PostProcessParams(member)                   PostProcessParams_##member
#define WorldInfo(member)                           WorldInfo_##member
#define VertexInput(member)                         VertexInput_.member
#define PixelOutput(member)                         PixelOutput_.member

// Samplers
//

SamplerState                        LinearSampler                       : register(s0);

SamplerState                        PointSampler                        : register(s1);

// Parameter blocks
//

//     Type Render::BloomParams
//

cbuffer                             BloomParams                         : register(b0)
{
    float                           BloomParams(BloomStrength);
    float                           BloomParams(BloomWidth);
    float                           BloomParams(BloomWarmth);
};

//     Type Core::CameraInfo
//

cbuffer                             CameraInfo                          : register(b1)
{
    float4x4                        CameraInfo(HeadToWorld);
    float4x4                        CameraInfo(HeadToWorldHistory);
    float4x4                        CameraInfo(WorldToHead);
    float4x4                        CameraInfo(LeftEyeToHead);
    float4x4                        CameraInfo(RightEyeToHead);
    float4x4                        CameraInfo(LeftHeadToEye);
    float4x4                        CameraInfo(RightHeadToEye);
    float4x4                        CameraInfo(ClipToLeftEye);
    float4x4                        CameraInfo(ClipToRightEye);
    float4x4                        CameraInfo(LeftEyeToClip);
    float4x4                        CameraInfo(RightEyeToClip);
    float4x4                        CameraInfo(LeftEyeToClipHistory);
    float4x4                        CameraInfo(RightEyeToClipHistory);
    float                           CameraInfo(LeftEyeToHeadOffset);
    float                           CameraInfo(RightEyeToHeadOffset);
    float2                          CameraInfo(RenderViewportOffset);
    float2                          CameraInfo(RenderViewportSize);
    float2                          CameraInfo(RcpRenderViewportSize);
    float2                          CameraInfo(DisplayViewportOffset);
    float2                          CameraInfo(DisplayViewportSize);
    float2                          CameraInfo(RcpDisplayViewportSize);
    float2                          CameraInfo(RenderTargetSize);
    float2                          CameraInfo(RcpRenderTargetSize);
    float2                          CameraInfo(DisplayTargetSize);
    float2                          CameraInfo(RcpDisplayTargetSize);
    float2                          CameraInfo(TemporalJitter);
    float                           CameraInfo(GlobalMipBias);
    uint                            CameraInfo(FrameNumber);
};

//     Type Render::PostEffectParams
//

cbuffer                             PostEffectParams                    : register(b2)
{
    float                           PostEffectParams(ShadingStratification);
    float                           PostEffectParams(ShadingBandSeparation);
    float                           PostEffectParams(ShadingBandShift);
    float                           PostEffectParams(OutlineWeight);
    float                           PostEffectParams(OutlineInset);
    float                           PostEffectParams(SaturationBoost);
    float                           PostEffectParams(BrightnessAdjustment);
};

//     Type Render::PostProcessParams
//

cbuffer                             PostProcessParams                   : register(b3)
{
    float3                          PostProcessParams(PostTint);
    float                           PostProcessParams(Sharpen);
    float                           PostProcessParams(SharpenShockAllowance);
    float                           PostProcessParams(VignetteStrength);
    float                           PostProcessParams(SeparatorWidth);
    float                           PostProcessParams(NoiseResolution);
    float                           PostProcessParams(BlueNoiseResolution);
    float                           PostProcessParams(HdrMaxFALL);
    float                           PostProcessParams(HdrMaxLuminance);
    float                           PostProcessParams(HdrWhitePoint);
    float                           PostProcessParams(HdrPqNormalizationRcp);
    uint                            PostProcessParams(vrDisplay);
    uint                            PostProcessParams(WaterEffect);
};
Buffer<float4>                      PostProcessParams(OutputStatistics) : register(t0);
Buffer<float>                       PostProcessParams(ExposureInfo)     : register(t1);
Buffer<uint>                        PostProcessParams(HDRExposureInfo)  : register(t2);
Texture2D<float4>                   PostProcessParams(Source)           : register(t3);
Texture2D<float4>                   PostProcessParams(Trails)           : register(t4);
Texture2D<float4>                   PostProcessParams(Bloom)            : register(t5);
Texture2D<float4>                   PostProcessParams(Noise)            : register(t6);
Texture2D<float4>                   PostProcessParams(BlueNoise)        : register(t7);
Texture2D<float>                    PostProcessParams(Depth)            : register(t8);

//     Type Core::WorldInfo
//

cbuffer                             WorldInfo                           : register(b4)
{
    float                           WorldInfo(WorldTimeSeconds);
    float                           WorldInfo(WorldDeltaSeconds);
    bool                            WorldInfo(EnableDistanceFading);
    uint                            WorldInfo(FrameNumber);
};

// Vertex inputs
//

struct                              VertexInput
{
};

// Pixel outputs
//

struct                              PixelOutput
{
    float4                          color0          : SV_Target0;
    float4                          color1          : SV_Target1;
};


#endif
