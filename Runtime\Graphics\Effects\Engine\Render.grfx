local variant = current_variant()

local core = Common.Core
local targets = Common.Targets
local samplers = Common.Samplers
local blendSettings = Common.BlendSettings
local effects = MaterialDefinitions[variant].Effects

local add_NoAlphaWrite = blend_state {
    TargetSettings = {
        blend_setting {
            BlendEnable = true,
            BlendColor = blend_add(BlendCoefficient.SourceAlpha, BlendCoefficient.One),
            BlendAlpha = blend_add(BlendCoefficient.One, BlendCoefficient.One),
            WriteMask = rgba_mask(true, true, true, false),
        }
    }
}

--------------------------
-- PostEffect
--------------------------

PostEffectParams = frame_parameters {
    ShadingStratification = float(0.0),
    ShadingBandSeparation = float(4.),
    ShadingBandShift = float(0.0),

    OutlineWeight = float(0.0),
    OutlineInset = float(0.0),

    SaturationBoost = float(0.0),
    BrightnessAdjustment = float(0.0),
}

--------------------------
-- Lighting
--------------------------

LightData = frame_parameters {
    SunDirection = float3(),
    SunShadowEnable = bool(1),
    SunLuminance = float3(1.0, 1.0, 1.0),

    SpatialIndexOrigin = float3(),
    SpatialIndexRootCellExtent = float(1.0),
    SpatialIndexRootGridDimensions = uint3(),
    SpatialIndexRootCellInverseExtent = float(1.0),
    SpatialIndexPopulationMasks = buffer(uint2),
    SpatialIndexChildOffsets = buffer(uint),
    FullNeighborhoodBitfield = buffer(uint),

    SunShadowmap = texture2d(float),
    SpotShadowmaps = texture2d(float),
    PointSourcesCount = uint(0),
    SpotSourcesCount = uint(0),
    SpotShadowsCount = uint(124),
    PointSources = buffer(float4),
    SpotSources = buffer(float4),
    SpotShadowTransforms = array(124, float4x4),

    AtmosphereSkyColor = float3(0),
    AtmosphereLightScale = float(1),
    AtmosphereMieAlbedo = float3(1,1,1),
    AtmosphereMieGradient = float(0),
    AtmosphereMieGradientOffset = float(0),
    AtmosphereMieBaseDensity = float(0),
    AtmosphereMieFarDistance = float(999999),
    AtmosphereMieAmbient = float(1),
    AtmosphereMieAnisotropy = float(0),
    AtmosphereRayleighFarDistance = float(999999),
    AtmosphereSecondaryScatter = float(1),
}

-- Deferred Light

DeferredBuffersTarget = frame_parameters {
    Target = texture2d_rw(float4),
    Subsurface = texture2d_rw(float4),
    VisualizeRadiance = uint(0),
}

if variant == 'High' then
    DeferredLightProgram = compute_program {
        Samplers = { DepthCompareSampler = samplers.LinearCompareGreater, LinearSampler = samplers.LinearClamp },
        Parameters = { LightData = LightData, DeferredBuffers = effects.DeferredBuffers, DeferredBuffersTarget = DeferredBuffersTarget, SunProjectionInfo = core.ShadowProjectionInfo, PostEffectParams = PostEffectParams, CameraInfo = core.CameraInfo },
        Shader = "DeferredLight",
        Macros = {"OCCLUSION"},
    }

    DeferredLightEffect = compute_effect {
        Type = compute_effect_type {
            StageParameters = { LightData = LightData, DeferredBuffers = effects.DeferredBuffers, DeferredBuffersTarget = DeferredBuffersTarget, SunProjectionInfo = core.ShadowProjectionInfo, PostEffectParams = PostEffectParams, CameraInfo = core.CameraInfo },
        },
        Arguments = {LightData = 'LightData', DeferredBuffers = 'DeferredBuffers', DeferredBuffersTarget = 'DeferredBuffersTarget', SunProjectionInfo = 'SunProjectionInfo', PostEffectParams = 'PostEffectParams', CameraInfo = 'CameraInfo'},
        Program = DeferredLightProgram,
    }

    DeferredLight = compute_stage {
        EffectType = DeferredLightEffect.Type,
    }
end

-- Deferred Unoccluded Light
local maybeCompactGbuffer = (variant == 'Low' and "COMPACT_GBUFFER" or "FAT_GBUFFER")

DeferredUnoccludedLightProgram = compute_program {
    Samplers = { DepthCompareSampler = samplers.LinearCompareGreater, LinearSampler = samplers.LinearClamp },
    Parameters = { LightData = LightData, DeferredBuffers = effects.DeferredBuffers, DeferredBuffersTarget = DeferredBuffersTarget, SunProjectionInfo = core.ShadowProjectionInfo, PostEffectParams = PostEffectParams, CameraInfo = core.CameraInfo },
    Shader = "DeferredLight",
    Macros = {maybeCompactGbuffer},
}

DeferredUnoccludedLightEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { LightData = LightData, DeferredBuffers = effects.DeferredBuffers, DeferredBuffersTarget = DeferredBuffersTarget, SunProjectionInfo = core.ShadowProjectionInfo, PostEffectParams = PostEffectParams, CameraInfo = core.CameraInfo },
    },
    Arguments = {LightData = 'LightData', DeferredBuffers = 'DeferredBuffers', DeferredBuffersTarget = 'DeferredBuffersTarget', SunProjectionInfo = 'SunProjectionInfo', PostEffectParams = 'PostEffectParams', CameraInfo = 'CameraInfo'},
    Program = DeferredUnoccludedLightProgram,
}

DeferredUnoccludedLight = compute_stage {
    EffectType = DeferredUnoccludedLightEffect.Type,
}

--------------------------
-- Lighting Complexity
--------------------------

LightingComplexityData = frame_parameters {
    Depth = texture2d(float),
    Target = texture2d_rw(float4_unorm),
}

LightingComplexityProgram = compute_program {
    Samplers = { },
    Parameters = { DiagnosticsInfo = core.DiagnosticsInfo, LightData = LightData, LightingComplexityData = LightingComplexityData, CameraInfo = core.CameraInfo },
    Shader = "LightingComplexity",
}

LightingComplexityEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { DiagnosticsInfo = core.DiagnosticsInfo, LightData = LightData, LightingComplexityData = LightingComplexityData, CameraInfo = core.CameraInfo },
    },
    Arguments = { DiagnosticsInfo = 'DiagnosticsInfo', LightData = 'LightData', LightingComplexityData = 'LightingComplexityData', CameraInfo = 'CameraInfo'},
    Program = LightingComplexityProgram,
}

LightingComplexity = compute_stage {
    EffectType = LightingComplexityEffect.Type,
}

--------------------------
-- Probes
--------------------------

-- Shade Probes

SkyHarmonicsParams = frame_parameters {
    RgbCoeffs = buffer(float4),
}

ShadeProbesParams = frame_parameters {
    SkyTransferL1 = buffer(float4),
    ProbeEmissionL1a = buffer(float4),
    ProbeEmissionL1b = buffer(float4),
    SkyTransferProbeIds = buffer(uint),
    VideoTexture = texture2d(float3),
    ProbeVideoEmission = buffer(float4),
    ProbeCoeffsL1Rw = buffer_rw(uint),
}

ShadeProbesProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearBlackBorder },
    Parameters = { LightData = LightData, ShadeProbesParams = ShadeProbesParams, SkyHarmonicsParams = SkyHarmonicsParams },
    Shader = "ShadeProbes",
}

ShadeProbesEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { LightData = LightData, ShadeProbesParams = ShadeProbesParams, SkyHarmonicsParams = SkyHarmonicsParams },
    },
    Arguments = {LightData = 'LightData', ShadeProbesParams = 'ShadeProbesParams', SkyHarmonicsParams = 'SkyHarmonicsParams'},
    Program = ShadeProbesProgram,
}

ShadeProbesStage = compute_stage {
    EffectType = ShadeProbesEffect.Type,
}

-- Infill Probes

InfillProbesParams = frame_parameters {
    PhaseStart = uint(0),
    PhaseEnd = uint(0),
    InfillCount = uint(0),
    InfillProbeIds = buffer(uint),
    InfillNeighborIds = buffer(uint),
    ProbeCoeffsL1Rw = buffer_rw(uint),
}

InfillProbesProgram = compute_program {
    Parameters = { InfillProbesParams = InfillProbesParams, LightData = LightData },
    Shader = "InfillProbes",
}

InfillProbesEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { InfillProbesParams = InfillProbesParams, LightData = LightData },
    },
    Arguments = {InfillProbesParams = 'InfillProbesParams', LightData = 'LightData'},
    Program = InfillProbesProgram,
}

InfillProbesStage = compute_stage {
    EffectType = InfillProbesEffect.Type,
}

-- Blend Fringe Probes

BlendFringeProbesParams = frame_parameters {
    ProbeCount = uint(0),
    ProbeLocs = buffer(uint4),
    SkyCoeffsL2 = buffer(float4),
    ProbeCoeffsL1Rw = buffer_rw(uint),
}

BlendFringeProbesProgram = compute_program {
    Parameters = { BlendFringeProbesParams = BlendFringeProbesParams, LightData = LightData },
    Shader = "BlendFringeProbes",
}

BlendFringeProbesEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { BlendFringeProbesParams = BlendFringeProbesParams, LightData = LightData },
    },
    Arguments = {BlendFringeProbesParams = 'BlendFringeProbesParams', LightData = 'LightData'},
    Program = BlendFringeProbesProgram,
}

BlendFringeProbesStage = compute_stage {
    EffectType = BlendFringeProbesEffect.Type,
}

--------------------------
-- Occlusion
--------------------------

OcclusionBuffers = frame_parameters {
    DepthMinMax = texture2d(float2),
    Depth = texture2d(float),
    Noise = texture2d(float),
    NoiseResolution = float(1),
    VisibilityRw1 = texture2d_rw(float4_snorm),
    VisibilityRw2 = texture2d_rw(float4_snorm),
}

OcclusionProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearClamp },
    Parameters = { OcclusionBuffers = OcclusionBuffers, CameraInfo = core.CameraInfo },
    Shader = "Occlusion",
}

OcclusionEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { OcclusionBuffers = OcclusionBuffers, CameraInfo = core.CameraInfo },
    },
    Arguments = {OcclusionBuffers = 'OcclusionBuffers', CameraInfo = 'CameraInfo'},
    Program = OcclusionProgram,
}

Occlusion = compute_stage {
    EffectType = OcclusionEffect.Type,
}

-- Filter Occlusion

FilterOcclusionBuffers = frame_parameters {
    DepthMinMax = texture2d(float2),
    Visibility1 = texture2d(float4),
    Visibility2 = texture2d(float4),
    VisibilityRw1 = texture2d_rw(float4_snorm),
    VisibilityRw2 = texture2d_rw(float4_snorm),
}

FilterOcclusionProgram = compute_program {
    Parameters = { FilterOcclusionBuffers = FilterOcclusionBuffers, CameraInfo = core.CameraInfo },
    Shader = "FilterOcclusion",
}

FilterOcclusionEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { FilterOcclusionBuffers = FilterOcclusionBuffers, CameraInfo = core.CameraInfo },
    },
    Arguments = {FilterOcclusionBuffers = 'FilterOcclusionBuffers', CameraInfo = 'CameraInfo'},
    Program = FilterOcclusionProgram,
}

FilterOcclusion = compute_stage {
    EffectType = FilterOcclusionEffect.Type,
}

-- Resolve Occlusion

ResolveOcclusionBuffers = frame_parameters {
    Depth = texture2d(float),
    DepthMinMax = texture2d(float2),
    Visibility1 = texture2d(float4),
    Visibility2 = texture2d(float4),
    Noise = texture2d(float),
    NoiseResolution = float(1),
    VisibilityRw = texture2d_rw(float4_snorm),
}

ResolveOcclusionProgram = compute_program {
    Parameters = { ResolveOcclusionBuffers = ResolveOcclusionBuffers, CameraInfo = core.CameraInfo },
    Shader = "ResolveOcclusion",
}

ResolveOcclusionEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { ResolveOcclusionBuffers = ResolveOcclusionBuffers, CameraInfo = core.CameraInfo },
    },
    Arguments = {ResolveOcclusionBuffers = 'ResolveOcclusionBuffers', CameraInfo = 'CameraInfo'},
    Program = ResolveOcclusionProgram,
}

ResolveOcclusion = compute_stage {
    EffectType = ResolveOcclusionEffect.Type,
}

--------------------------
-- FSR
--------------------------

--- Tonemap

FSRTonemapParams = frame_parameters {
    Source = texture2d(float4),
}

FSRTonemapProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { DeferredBuffers = effects.DeferredBuffers, FSRTonemapParams = FSRTonemapParams, CameraInfo = core.CameraInfo },
    Samplers = { PointWrapSampler = samplers.PointWrap, LinearClampSampler = samplers.LinearClamp },
    VertexShader = "FSRTonemap",
    PixelShader = "FSRTonemap",
    PixelOutput = targets.FSR,
}

local fsrTonemapEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.FSR,
        StageParameters = { DeferredBuffers = effects.DeferredBuffers, FSRTonemapParams = FSRTonemapParams, CameraInfo = core.CameraInfo },
    },
    Program = FSRTonemapProgram
}

FSRTonemap = graphics_stage {
    EffectType = fsrTonemapEffect.Type,
    FixedDraws = { fixed_draw { Effect = fsrTonemapEffect } }
}

--- Upscale

FSRUpscaleData = frame_parameters {
    Source = texture2d(float4),
    Target = texture2d_rw(float4),
    sharpness = float(1),
}

FSRUpscaleProgram = compute_program {
    Samplers = { LinearClampSampler = samplers.LinearClamp },
    Parameters = { DeferredBuffers = effects.DeferredBuffers, FSRUpscaleData = FSRUpscaleData, CameraInfo = core.CameraInfo },
    Shader = "FSRUpscale",
    Macros = { "SAMPLE_EASU" },
}

FSRUpscaleEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { DeferredBuffers = effects.DeferredBuffers, FSRUpscaleData = FSRUpscaleData, CameraInfo = core.CameraInfo },
    },
    Arguments = { DeferredBuffers = 'DeferredBuffers', FSRUpscaleData = 'FSRUpscaleData', CameraInfo = 'CameraInfo' },
    Program = FSRUpscaleProgram,
}

FSRUpscale = compute_stage {
    EffectType = FSRUpscaleEffect.Type,
}

--- Sharpen

FSRSharpenProgram = compute_program {
    Samplers = { LinearClampSampler = samplers.LinearClamp },
    Parameters = { DeferredBuffers = effects.DeferredBuffers, FSRUpscaleData = FSRUpscaleData, CameraInfo = core.CameraInfo },
    Shader = "FSRUpscale",
    Macros = { "SAMPLE_RCAS" },
}

FSRSharpenEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { DeferredBuffers = effects.DeferredBuffers, FSRUpscaleData = FSRUpscaleData, CameraInfo = core.CameraInfo },
    },
    Arguments = { DeferredBuffers = 'DeferredBuffers', FSRUpscaleData = 'FSRUpscaleData', CameraInfo = 'CameraInfo' },
    Program = FSRSharpenProgram,
}

FSRSharpen = compute_stage {
    EffectType = FSRSharpenEffect.Type,
}

--------------------------------------------
-- Temporal Resolve
--------------------------------------------
TemporalResolveParams = frame_parameters {
    History = texture2d(float4),
    Source = texture2d(float4),
    ExposureInfo = buffer(float),
    Motion = texture2d(float2),
    Depth = texture2d(float2),
    Cut = bool(0),
}

TemporalResolveProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { TemporalResolveParams = TemporalResolveParams, CameraInfo = core.CameraInfo },
    Samplers = { LinearSampler = samplers.LinearClamp, LinearWrapSampler = samplers.LinearWrap },
    VertexShader = "TemporalResolveGraphics",
    PixelShader = "TemporalResolveGraphics",
    PixelOutput = targets.Luminance,
    Macros = {maybeCompactGbuffer},
}
TemporalResolveEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance,
        StageParameters = { TemporalResolveParams = TemporalResolveParams, CameraInfo = core.CameraInfo },
    },
    Program = TemporalResolveProgram
}
TemporalResolve = graphics_stage {
    EffectType = TemporalResolveEffect.Type,
    FixedDraws = { fixed_draw { Effect = TemporalResolveEffect } }
}


--------------------------
-- Reflection
--------------------------

--- Trace

ReflectTraceParams = frame_parameters {
    Source = texture2d(float4),
    Noise = texture2d(float2),
    ExposureInfo = buffer(float),
    RaySamples = uint(1),
    Cut = bool(0)
}

ReflectTraceProgram = graphics_program {
        CullMode = CullMode.None,
        DepthStencilState = depth_stencil_state { DepthEnable = false },
        Parameters = { ReflectTraceParams = ReflectTraceParams, DeferredBuffers = effects.DeferredBuffers, CameraInfo = core.CameraInfo },
        Samplers = { PointClampSampler = samplers.PointClamp, PointWrapSampler = samplers.PointWrap},
        VertexShader = "ReflectTrace",
        PixelShader = "ReflectTrace",
        PixelOutput = targets.ReflectTrace,
        Macros = {maybeCompactGbuffer},
}

local reflectTraceEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.ReflectTrace,
        StageParameters = { ReflectTraceParams = ReflectTraceParams, DeferredBuffers = effects.DeferredBuffers, CameraInfo = core.CameraInfo },
    },
    Program = ReflectTraceProgram,
}

ReflectTrace = graphics_stage {
    EffectType = reflectTraceEffect.Type,
    FixedDraws = { fixed_draw { Effect = reflectTraceEffect } }
}

--- Solve

ReflectSolveParams = frame_parameters {
    Source = texture2d(float4),
    Trace = texture2d(float4),
    Motion = texture2d(float2),
    Noise = texture2d(float2),
    HDRDownres1 = texture2d(float4),
    HDRDownres2 = texture2d(float4),
    HDRDownres3 = texture2d(float4),
    HDRDownres4 = texture2d(float4),
    HDRDownres5 = texture2d(float4),
    ExposureInfo = buffer(float),
    Cut = bool(0)
}

ReflectSolveProgram = graphics_program {
        CullMode = CullMode.None,
        DepthStencilState = depth_stencil_state { DepthEnable = false },
        Parameters = { ReflectSolveParams = ReflectSolveParams, DeferredBuffers = effects.DeferredBuffers, CameraInfo = core.CameraInfo },
        Samplers = { PointClampSampler = samplers.PointClamp, PointWrapSampler = samplers.PointWrap, LinearClampSampler = samplers.LinearClamp },
        VertexShader = "ReflectSolve",
        PixelShader = "ReflectSolve",
        PixelOutput = targets.ReflectSolve,
        Macros = {maybeCompactGbuffer},
}

local reflectSolveEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.ReflectSolve,
        StageParameters = { ReflectSolveParams = ReflectSolveParams, DeferredBuffers = effects.DeferredBuffers, CameraInfo = core.CameraInfo },
    },
    Program = ReflectSolveProgram,
}

ReflectSolve = graphics_stage {
    EffectType = reflectSolveEffect.Type,
    FixedDraws = { fixed_draw { Effect = reflectSolveEffect } }
}

--- Solve VR

ReflectSolveVRProgram = graphics_program {
        CullMode = CullMode.None,
        DepthStencilState = depth_stencil_state { DepthEnable = false },
        Parameters = { ReflectSolveParams = ReflectSolveParams, DeferredBuffers = effects.DeferredBuffers, CameraInfo = core.CameraInfo },
        Samplers = { PointClampSampler = samplers.PointClamp, PointWrapSampler = samplers.PointWrap, LinearClampSampler = samplers.LinearClamp },
        VertexShader = "ReflectSolve",
        PixelShader = "ReflectSolve",
        PixelOutput = targets.ReflectSolve,
        Macros = {maybeCompactGbuffer, "VR"},
}

local reflectSolveVREffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.ReflectSolve,
        StageParameters = { ReflectSolveParams = ReflectSolveParams, DeferredBuffers = effects.DeferredBuffers, CameraInfo = core.CameraInfo },
    },
    Program = ReflectSolveVRProgram,
}

ReflectSolveVR = graphics_stage {
    EffectType = reflectSolveVREffect.Type,
    FixedDraws = { fixed_draw { Effect = reflectSolveVREffect } }
}

--- Reprojection

ReflectReprojParams = frame_parameters {
    PrevReproj = texture2d(float4),
    ReflectTrace = texture2d(float4),
    ReflectSolve = texture2d(float4),
    Motion = texture2d(float2)
}

ReflectReprojProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { DeferredBuffers = effects.DeferredBuffers, ReflectReprojParams = ReflectReprojParams, CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
    Samplers = { PointClampSampler = samplers.PointClamp, LinearClampSampler = samplers.LinearClamp },
    VertexShader = "ReflectReproj",
    PixelShader = "ReflectReproj",
    PixelOutput = targets.ReflectReproj,
    Macros = {maybeCompactGbuffer},
}

local reflectReprojEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.ReflectReproj,
        StageParameters = { DeferredBuffers = effects.DeferredBuffers, ReflectReprojParams = ReflectReprojParams, CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
    },
    Program = ReflectReprojProgram,
}

ReflectReproj = graphics_stage {
    EffectType = reflectReprojEffect.Type,
    FixedDraws = { fixed_draw { Effect = reflectReprojEffect } }
}


--- Blit reprojection history

ReflectBlitPrevReprojParams = frame_parameters {
    ReflectReproj = texture2d(float4),
}

ReflectBlitPrevReprojProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { ReflectBlitPrevReprojParams = ReflectBlitPrevReprojParams, CameraInfo = core.CameraInfo },
    Samplers = { LinearSampler = samplers.LinearClamp },
    VertexShader = "ReflectBlitPrevReproj",
    PixelShader = "ReflectBlitPrevReproj",
    PixelOutput = targets.ReflectPrevReproj,
}

local reflectBlitPrevReprojEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.ReflectPrevReproj,
        StageParameters = { ReflectBlitPrevReprojParams = ReflectBlitPrevReprojParams, CameraInfo = core.CameraInfo },
    },
    Program = ReflectBlitPrevReprojProgram
}

ReflectBlitPrevReproj = graphics_stage {
    EffectType = reflectBlitPrevReprojEffect.Type,
    FixedDraws = { fixed_draw { Effect = reflectBlitPrevReprojEffect } }
}

--- Blend

ReflectBlendParams = frame_parameters {
    ReflectTrace = texture2d(float4),
    ReflectMip0  = texture2d(float4),
    ReflectMip1  = texture2d(float4),
    ReflectMip2  = texture2d(float4),
    ReflectMip3  = texture2d(float4),
    ReflectMip4  = texture2d(float4),
    ReflectMip5  = texture2d(float4),
    ExposureInfo = buffer(float),
    Cut = bool(0)
}

ReflectBlendProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { DeferredBuffers = effects.DeferredBuffers, ReflectBlendParams = ReflectBlendParams, CameraInfo = core.CameraInfo },
    Samplers = { PointClampSampler = samplers.PointClamp, LinearClampSampler = samplers.LinearClamp },
    VertexShader = "ReflectBlend",
    PixelShader = "ReflectBlend",
    PixelOutput = targets.Luminance,
    BlendState = add_NoAlphaWrite,
    Macros = {maybeCompactGbuffer},
}

local reflectBlendEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance,
        StageParameters = { DeferredBuffers = effects.DeferredBuffers, ReflectBlendParams = ReflectBlendParams, CameraInfo = core.CameraInfo },
    },
    Program = ReflectBlendProgram
}

ReflectBlend = graphics_stage {
    EffectType = reflectBlendEffect.Type,
    FixedDraws = { fixed_draw { Effect = reflectBlendEffect } }
}

--------------------------
-- Atmosphere
--------------------------

AtmosphereBuffers = frame_parameters {
    DepthMinMax = texture2d(float2),
    --Depth = texture2d(float),
    Noise = texture2d(float),
    NoiseResolution = float(1),
    SkyCoeffsL2 = buffer(float4),
}

AtmosphereProgram = graphics_program {
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    --BlendState = blendSettings.States.PremultipliedOver,
    Parameters = { AtmosphereBuffers = AtmosphereBuffers, LightData = LightData, SunProjectionInfo = core.ShadowProjectionInfo, CameraInfo = core.CameraInfo },
    Samplers = { DepthCompareSampler = samplers.LinearCompareGreater },
    VertexShader = "Atmosphere",
    PixelShader = "Atmosphere",
    PixelOutput = targets.Luminance_Luminance,
    --PixelOutput = targets.Luminance,
}

AtmosphereEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance_Luminance,
        --TargetSignature = targets.Luminance,
        StageParameters = { LightData = LightData, AtmosphereBuffers = AtmosphereBuffers, SunProjectionInfo = core.ShadowProjectionInfo, CameraInfo = core.CameraInfo },
    },
    Arguments = {LightData = 'LightData', AtmosphereBuffers = 'AtmosphereBuffers', SunProjectionInfo = 'SunProjectionInfo', CameraInfo = 'CameraInfo'},
    Program = AtmosphereProgram,
}

LightAtmosphere = graphics_stage {
    EffectType = AtmosphereEffect.Type,
    FixedDraws = { fixed_draw { Effect = AtmosphereEffect } }
}

-- Resolve Atmosphere

ResolveAtmosphereBuffers = frame_parameters {
    Depth = texture2d(float),
    DepthMinMax = texture2d(float2),
    Inscatter1 = texture2d(float4),
    Inscatter2 = texture2d(float4),
    Noise = texture2d(float),
    NoiseResolution = float(1),
    SkyCoeffsL2 = buffer(float4),
}

local premultipliedOver_NoAlphaWrite = blend_state {
    TargetSettings = {
        blend_setting {
            BlendEnable = true,
            BlendColor = blend_add(BlendCoefficient.One, BlendCoefficient.SourceAlphaComplement),
            BlendAlpha = blend_add(BlendCoefficient.One, BlendCoefficient.SourceAlphaComplement),
            WriteMask = rgba_mask(true, true, true, false),
        }
    }
}

local resolveAtmosphereProgramCommon = partial(graphics_program) {
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    BlendState = premultipliedOver_NoAlphaWrite, --blendSettings.States.PremultipliedOver,
    Parameters = { ResolveAtmosphereBuffers = ResolveAtmosphereBuffers, LightData = LightData, SunProjectionInfo = core.ShadowProjectionInfo, CameraInfo = core.CameraInfo },
    Samplers = { LinearSampler = samplers.LinearClamp },
    VertexShader = "ResolveAtmosphere",
    PixelShader = "ResolveAtmosphere",
    PixelOutput = targets.Luminance,
}

ResolveAtmosphereProgram = graphics_program {
    resolveAtmosphereProgramCommon,
}

ResolveAtmosphereEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance,
        StageParameters = { LightData = LightData, ResolveAtmosphereBuffers = ResolveAtmosphereBuffers, SunProjectionInfo = core.ShadowProjectionInfo, CameraInfo = core.CameraInfo },
    },
    Arguments = {LightData = 'LightData', ResolveAtmosphereBuffers = 'ResolveAtmosphereBuffers', SunProjectionInfo = 'SunProjectionInfo', CameraInfo = 'CameraInfo'},
    Program = ResolveAtmosphereProgram,
}

ResolveAtmosphere = graphics_stage {
    EffectType = ResolveAtmosphereEffect.Type,
    FixedDraws = { fixed_draw { Effect = ResolveAtmosphereEffect } }
}

-- Unshadowed Atmosphere

UnshadowedAtmosphereProgram = graphics_program {
    resolveAtmosphereProgramCommon,
    Macros = { "UNSHADOWED" },
}

UnshadowedAtmosphereEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance,
        StageParameters = { LightData = LightData, ResolveAtmosphereBuffers = ResolveAtmosphereBuffers, SunProjectionInfo = core.ShadowProjectionInfo, CameraInfo = core.CameraInfo },
    },
    Arguments = {LightData = 'LightData', ResolveAtmosphereBuffers = 'ResolveAtmosphereBuffers', SunProjectionInfo = 'SunProjectionInfo', CameraInfo = 'CameraInfo'},
    Program = UnshadowedAtmosphereProgram,
}

UnshadowedAtmosphere = graphics_stage {
    EffectType = UnshadowedAtmosphereEffect.Type,
    FixedDraws = { fixed_draw { Effect = UnshadowedAtmosphereEffect } }
}

--------------------------
-- Filter Video
--------------------------

FilterVideoEmissionParams = frame_parameters {
    VideoEmissionTextureRw = texture2d_rw(float3_unorm),
}

FilterVideoEmissionProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearBlackBorder },
    Parameters = { FilterVideoEmissionParams = FilterVideoEmissionParams, MediaParams = core.MediaParams },
    Shader = "FilterVideoEmission",
}

FilterVideoEmissionEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { FilterVideoEmissionParams = FilterVideoEmissionParams, MediaParams = core.MediaParams },
    },
    Arguments = {FilterVideoEmissionParams = 'FilterVideoEmissionParams', MediaParams = 'MediaParams'},
    Program = FilterVideoEmissionProgram,
}

FilterVideoEmissionStage = compute_stage {
    EffectType = FilterVideoEmissionEffect.Type,
}

--------------------------
-- Editor Lighting
--------------------------

EditorLightingProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearClamp },
    Parameters = { DeferredBuffers = effects.DeferredBuffers, DeferredBuffersTarget = DeferredBuffersTarget, CameraInfo = core.CameraInfo },
    Shader = "EditorLighting",
}

EditorLightingEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { DeferredBuffers = effects.DeferredBuffers, DeferredBuffersTarget = DeferredBuffersTarget, CameraInfo = core.CameraInfo },
    },
    Arguments = {DeferredBuffers = 'DeferredBuffers', DeferredBuffersTarget = 'DeferredBuffersTarget', CameraInfo = 'CameraInfo'},
    Program = EditorLightingProgram,
}

EditorLighting = compute_stage {
    EffectType = EditorLightingEffect.Type,
}

--------------------------
-- Visualize Normals
--------------------------

VisualizeNormalsProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearClamp },
    Parameters = { DeferredBuffers = effects.DeferredBuffers, DeferredBuffersTarget = DeferredBuffersTarget, CameraInfo = core.CameraInfo },
    Shader = "VisualizeNormals",
    Macros = {maybeCompactGbuffer},
}

VisualizeNormalsEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { LightData = LightData, DeferredBuffers = effects.DeferredBuffers, DeferredBuffersTarget = DeferredBuffersTarget, CameraInfo = core.CameraInfo },
    },
    Arguments = {DeferredBuffers = 'DeferredBuffers', DeferredBuffersTarget = 'DeferredBuffersTarget', CameraInfo = 'CameraInfo'},
    Program = VisualizeNormalsProgram,
}

VisualizeNormals = compute_stage {
    EffectType = VisualizeNormalsEffect.Type,
}

--------------------------
-- Populate Directional Shadowmap
--------------------------

ShadowmapTargetType = target_signature { Depth = targets.JustShadowDepth.Depth }

PopulateDirectionalShadowmapStage = graphics_stage {
    EffectType = effects.ShadowCascadeDepthOnlyEffectType,
    DepthStencilClear = { Enable = true },
    Bins = { effects.StaticCasterBin, effects.DynamicCasterBin },
}
PopulateDirectionalShadowmapFadingStage = graphics_stage {
    EffectType = effects.ShadowCascadeDepthOnlyFadingEffectType,
    DepthStencilClear = { Enable = false },
    Bins = { effects.StaticCasterFadingBin, effects.DynamicCasterFadingBin },
}

PopulateSpotShadowmapStaticOnlyStage = graphics_stage {
    EffectType = effects.ShadowDepthOnlyEffectType,
    DepthStencilClear = { Enable = true },
    Bins = { effects.StaticCasterBin, effects.StaticCasterFadingBin },
}

PopulateSpotShadowmapStage = graphics_stage {
    EffectType = effects.ShadowDepthOnlyEffectType,
    DepthStencilClear = { Enable = true },
    Bins = { effects.StaticCasterBin, effects.StaticCasterFadingBin, effects.DynamicCasterBin, effects.DynamicCasterFadingBin },
}

CopyShadowmapParams = frame_parameters {
    Source = texture2d(float),
}

CopyShadowmapCellProgram = graphics_program {
    Parameters = { CopyShadowmapParams = CopyShadowmapParams },
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Always, DepthWriteEnable = true },
    VertexShader = "CopyShadowmapCell",
    PixelShader = "CopyShadowmapCell",
    PixelOutput = targets.JustShadowDepth,
}

local copyShadowmapCellEffect = graphics_effect {
    Type = graphics_effect_type {
        StageParameters = { CopyShadowmapParams = CopyShadowmapParams },
        TargetSignature = targets.JustShadowDepth,
    },
    Arguments = {CopyShadowmapParams = 'CopyShadowmapParams'},
    Program = CopyShadowmapCellProgram,
}

CopyShadowmapCellStage = graphics_stage {
    EffectType = copyShadowmapCellEffect.Type,
    FixedDraws = { fixed_draw { Effect = copyShadowmapCellEffect } }
}

--------------------------
-- Toon outlines
--------------------------

ToonOutlineBuffers = frame_parameters {
    Opacity = texture2d(float4),
    Depth = texture2d(float),
}
ToonOutlineProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { ToonOutlineBuffers = ToonOutlineBuffers, PostEffectParams = PostEffectParams, CameraInfo = core.CameraInfo },
    Samplers = { LinearSampler = samplers.LinearClamp },
    VertexShader = "ToonOutline",
    PixelShader = "ToonOutline",
    PixelOutput = targets.Luminance,
    BlendState = blendSettings.States.Multiply,
}
ToonOutlineEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance,
        StageParameters = { ToonOutlineBuffers = ToonOutlineBuffers, PostEffectParams = PostEffectParams, CameraInfo = core.CameraInfo },
    },
    Program = ToonOutlineProgram
}
ToonOutlineStage = graphics_stage {
    EffectType = ToonOutlineEffect.Type,
    FixedDraws = { fixed_draw { Effect = ToonOutlineEffect } }
}

--------------------------
-- Sky
--------------------------

SkyProgram = graphics_program {
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    Parameters = { CameraInfo = core.CameraInfo, SkyParams = core.SkyParams, SkyHarmonicsParams = SkyHarmonicsParams },
    Samplers = { LinearSampler = samplers.LinearClamp },
    VertexShader = "Sky",
    PixelShader = "Sky",
    PixelOutput = targets.Luminance_Motion_Depth,
}

local renderSkyEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance_Motion_Depth,
        StageParameters = { CameraInfo = core.CameraInfo, SkyParams = core.SkyParams, SkyHarmonicsParams = SkyHarmonicsParams },
    },
    Program = SkyProgram
}

RenderSky = graphics_stage {
    EffectType = renderSkyEffect.Type,
    FixedDraws = { fixed_draw { Effect = renderSkyEffect } }
}

-- Project Sky Harmonics

ProjectSkyHarmonicsParams = frame_parameters {
    RgbCoeffsRw = buffer_rw(float4),
    FrameNumber = uint(0),
}

ProjectSkyHarmonicsProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearBlackBorder },
    Parameters = { ProjectSkyHarmonicsParams = ProjectSkyHarmonicsParams, SkyParams = core.SkyParams },
    Shader = "ProjectSkyHarmonics",
}

ProjectSkyHarmonicsEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { ProjectSkyHarmonicsParams = ProjectSkyHarmonicsParams, SkyParams = core.SkyParams },
    },
    Program = ProjectSkyHarmonicsProgram
}

ProjectSkyHarmonics = compute_stage {
    EffectType = ProjectSkyHarmonicsEffect.Type,
}

--------------------------
-- Metering
--------------------------

ExposureParams = frame_parameters {
    UnderexposureCorrection = float(0),
    OverexposureCorrection = float(0),
    UnderexposureCorrectionRate = float(0.001),
    OverexposureCorrectionRate = float(0.01),
    ExposureBias = float(0),
}

MeteringParams = frame_parameters {
    Source = texture2d(float4),
    ExposureInfoRw = buffer_rw(float),
    Cut = bool(0),
    DeltaSeconds = float(0),
}

MeteringProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearClamp },
    Parameters = { MeteringParams = MeteringParams, ExposureParams = ExposureParams },
    Shader = "Metering",
}

MeteringEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { MeteringParams = MeteringParams, ExposureParams = ExposureParams },
    },
    Program = MeteringProgram
}

Metering = compute_stage {
    EffectType = MeteringEffect.Type,
}

--------------------------
-- HDRLumaAnalyse
--------------------------

HDRLumaAnalysisParams = frame_parameters {
    InputTexture = texture2d(float4),
    TileStatistics = buffer_rw(float4),
    OutputStatistics = buffer_rw(float4),
    TextureSize = uint4(0, 0, 0, 0),
    TileCount = uint(0),
    ExposureInfo = buffer(float),
}

HDRLumaAnalyzeProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearClamp },
    Parameters = { HDRLumaAnalysisParams = HDRLumaAnalysisParams },
    Shader = "HDRLumaAnalyze",
}

HDRLumaAnalyzeEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { HDRLumaAnalysisParams = HDRLumaAnalysisParams },
    },
    Program = HDRLumaAnalyzeProgram
}

HDRLumaAnalzse = compute_stage {
    EffectType = HDRLumaAnalyzeEffect.Type,
}

--------------------------
-- HDRLumaReduce
--------------------------

HDRLumaReduceProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearClamp },
    Parameters = { HDRLumaAnalysisParams = HDRLumaAnalysisParams },
    Shader = "HDRLumaReduce",
}

HDRLumaReduceEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { HDRLumaAnalysisParams = HDRLumaAnalysisParams },
    },
    Program = HDRLumaReduceProgram
}

HDRLumaReduce = compute_stage {
    EffectType = HDRLumaReduceEffect.Type,
}


--------------------------
-- Simple Filters
--------------------------

FilterImageParams = frame_parameters {
    Source = texture2d(float4),
    RcpTargetSize = float2(1, 1),
    Progress = float(0),
}

SimpleFilterCommon = partial(graphics_program) {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { FilterImage = FilterImageParams },
    Samplers = { LinearSampler = samplers.LinearClamp },
}

-- Present

PresentParams = frame_parameters {
    Source = texture2d(float4),
    RcpTargetSize = float2(1, 1),
    UndistortCylinderFov = float(0),
    HdrTonemapping = uint(0),
    DisplayGammaMode = uint(0),
    DisplayGamma = float(0),
}

PresentEffectType = graphics_effect_type {
    TargetSignature = core.BackbufferSignature,
    StageParameters = { PresentParams = PresentParams },
}

PresentProgramCommon = partial(graphics_program) {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { PresentParams = PresentParams },
    Samplers = { LinearSampler = samplers.LinearClamp },
    PixelOutput = core.BackbufferSignature,
}

PresentProgram = graphics_program {
    PresentProgramCommon,
    VertexShader = "Present",
    PixelShader = "Present",
}

local presentEffect = graphics_effect {
    Type = PresentEffectType,
    Program = PresentProgram
}

Present = graphics_stage {
    EffectType = PresentEffectType,
    FixedDraws = { fixed_draw { Effect = presentEffect } }
}

-- Present HDR

PresentHDREffectType = graphics_effect_type {
    TargetSignature = core.BackbufferSignature,
    StageParameters = { PresentParams = PresentParams },
}

PresentHDRProgramCommon = partial(graphics_program) {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { PresentParams = PresentParams },
    Samplers = { LinearSampler = samplers.LinearClamp },
    PixelOutput = core.BackbufferSignature,
}

PresentHDRProgram = graphics_program {
    PresentProgramCommon,
    VertexShader = "Present",
    PixelShader = "Present",
    Macros = {"HDR"},
}

local presentHDREffect = graphics_effect {
    Type = PresentHDREffectType,
    Program = PresentHDRProgram
}

PresentHDR = graphics_stage {
    EffectType = PresentHDREffectType,
    FixedDraws = { fixed_draw { Effect = presentHDREffect } }
}

-- Present Resample

PresentResampleProgram = graphics_program {
    PresentProgramCommon,
    VertexShader = "PresentResample",
    PixelShader = "PresentResample",
}

local presentResampleEffect = graphics_effect {
    Type = PresentEffectType,
    Program = PresentResampleProgram
}

PresentResample = graphics_stage {
    EffectType = PresentEffectType,
    FixedDraws = { fixed_draw { Effect = presentResampleEffect } }
}

-- Present Pano

PresentPanoProgram = graphics_program {
    PresentProgramCommon,
    VertexShader = "PresentPano",
    PixelShader = "PresentPano",
}

local presentPanoEffect = graphics_effect {
    Type = PresentEffectType,
    Program = PresentPanoProgram
}

PresentPano = graphics_stage {
    EffectType = PresentEffectType,
    FixedDraws = { fixed_draw { Effect = presentPanoEffect } }
}

-- Downsample Depth Min Max

DownsampleDepthMinMaxProgram = graphics_program {
    SimpleFilterCommon,
    VertexShader = "DownsampleDepthMinMax",
    PixelShader = "DownsampleDepthMinMax",
    PixelOutput = targets.JustDepthMinMax,
}

local downsampleDepthMinMaxEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.JustDepthMinMax,
        StageParameters = { FilterImage = FilterImageParams },
    },
    Program = DownsampleDepthMinMaxProgram,
}

DownsampleDepthMinMax = graphics_stage {
    EffectType = downsampleDepthMinMaxEffect.Type,
    FixedDraws = { fixed_draw { Effect = downsampleDepthMinMaxEffect } }
}

-- Downsample Blur

local filterLuminanceEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance,
    StageParameters = { FilterImage = FilterImageParams },
}

DownsampleBlurProgram = graphics_program {
    SimpleFilterCommon,
    Parameters = { CameraInfo = core.CameraInfo },
    VertexShader = "DownsampleBlur",
    PixelShader = "DownsampleBlur",
    PixelOutput = targets.Luminance,
}

local downsampleBlurEffect = graphics_effect {
    Type = filterLuminanceEffectType,
    Program = DownsampleBlurProgram
}

DownsampleBlur = graphics_stage {
    EffectType = downsampleBlurEffect.Type,
    FixedDraws = { fixed_draw { Effect = downsampleBlurEffect } }
}

-- Downsample Blur - Reflection

local filterReflectionEffectType = graphics_effect_type {
    TargetSignature = targets.ReflectDownRes,
    StageParameters = { FilterImage = FilterImageParams },
}

DownsampleBlurReflectionProgram = graphics_program {
    SimpleFilterCommon,
    Parameters = { CameraInfo = core.CameraInfo },
    VertexShader = "DownsampleBlur",
    PixelShader = "DownsampleBlur",
    PixelOutput = targets.ReflectDownRes,
}

local downsampleBlurReflectionEffect = graphics_effect {
    Type = filterReflectionEffectType,
    Program = DownsampleBlurReflectionProgram
}

DownsampleBlurReflection = graphics_stage {
    EffectType = downsampleBlurReflectionEffect.Type,
    FixedDraws = { fixed_draw { Effect = downsampleBlurReflectionEffect } }
}

--------------------------
-- Bloom
--------------------------

BloomParams = frame_parameters {
    BloomStrength = float(0.35),
    BloomWidth = float(0.65),
    BloomWarmth = float(0.),
 }

-- Upsample Blur

UpsampleBlurProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { FilterImage = FilterImageParams, BloomParams = BloomParams },
    Samplers = { LinearSampler = samplers.LinearClamp },
    VertexShader = "UpsampleBlur",
    PixelShader = "UpsampleBlur",
    PixelOutput = targets.Luminance,
    BlendState = blendSettings.States.Over,
}

local upsampleBlurEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance,
        StageParameters = { FilterImage = FilterImageParams, BloomParams = BloomParams },
    },
    Program = UpsampleBlurProgram
}

UpsampleBlur = graphics_stage {
    EffectType = upsampleBlurEffect.Type,
    FixedDraws = { fixed_draw { Effect = upsampleBlurEffect } }
}

--------------------------------------------
-- Resolve Transparent
--------------------------------------------

ResolveTransparentParams = frame_parameters {
    Source = texture2d(float4),
    Transmittance = texture2d(float4),
}

ResolveTransparentProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { CameraInfo = core.CameraInfo, ResolveTransparentParams = ResolveTransparentParams },
    Samplers = { LinearSampler = samplers.LinearClamp },
    VertexShader = "ResolveTransparent",
    PixelShader = "ResolveTransparent",
    PixelOutput = targets.Luminance,
}
ResolveTransparentEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance,
        StageParameters = { CameraInfo = core.CameraInfo, ResolveTransparentParams = ResolveTransparentParams },
    },
    Program = ResolveTransparentProgram
}
ResolveTransparent = graphics_stage {
    EffectType = ResolveTransparentEffect.Type,
    FixedDraws = { fixed_draw { Effect = ResolveTransparentEffect } }
}

--------------------------
-- PostProcessing (Tonemapping)
--------------------------

PostProcessParams = frame_parameters {
    OutputStatistics = buffer(float4),
    Source = texture2d(float4),
    Trails = texture2d(float4),
    Bloom = texture2d(float4),
    ExposureInfo = buffer(float),
    HDRExposureInfo = buffer(uint),
    PostTint = float3(1, 1, 1),
    Sharpen = float(0),
    SharpenShockAllowance = float(0),
    VignetteStrength = float(0),
    SeparatorWidth = float(0),
    Noise = texture2d(float4),
    NoiseResolution = float(1),
    BlueNoise = texture2d(float4),
    BlueNoiseResolution = float(1),
    HdrMaxFALL = float(0),
    HdrMaxLuminance = float(0),
    HdrWhitePoint = float(0),
    HdrPqNormalizationRcp = float(0),
    Depth = texture2d(float),
    vrDisplay = uint(0),
    WaterEffect = uint(0),
}

local maybeCheaper = (variant == 'Low' and "CHEAPER" or "NOT_CHEAPER")

PostProcessProgramCommon = partial(graphics_program) {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { PostProcessParams = PostProcessParams, CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, BloomParams = BloomParams, PostEffectParams = PostEffectParams },
    Samplers = { PointSampler = samplers.PointClamp, LinearSampler = samplers.LinearClamp },
    PixelOutput = targets.Post_HDR_SDR,
}

PostProcessProgram = graphics_program {
    PostProcessProgramCommon,
    VertexShader = "PostProcess",
    PixelShader = "PostProcess",
    Macros = {maybeCheaper},
}

local postProcessEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Post_HDR_SDR,
        StageParameters = { PostProcessParams = PostProcessParams, CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, BloomParams = BloomParams, PostEffectParams = PostEffectParams },
    },
    Program = PostProcessProgram
}

PostProcess = graphics_stage {
    EffectType = postProcessEffect.Type,
    FixedDraws = { fixed_draw { Effect = postProcessEffect } }
}

-- HDR mode

PostProcessHDRProgram = graphics_program {
    PostProcessProgramCommon,
    VertexShader = "PostProcess",
    PixelShader = "PostProcess",
    Macros = {maybeCheaper, "HDR", "HDRCOLOR"},
}

local postProcessHDREffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Post_HDR_SDR,
        StageParameters = { PostProcessParams = PostProcessParams, CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo, BloomParams = BloomParams, PostEffectParams = PostEffectParams },
    },
    Program = PostProcessHDRProgram
}

PostProcessHDR = graphics_stage {
    EffectType = postProcessHDREffect.Type,
    FixedDraws = { fixed_draw { Effect = postProcessHDREffect } }
}

--------------------------
-- Emissive trail effect
--------------------------

EmissiveTrailParams = frame_parameters {
    Source = texture2d(float4),
    Mask = texture2d(float2),
    Prev = texture2d(float4),
    Motion = texture2d(float2),
    Depth = texture2d(float4),
}

EmissiveTrailProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { EmissiveTrailParams = EmissiveTrailParams, CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
    Samplers = { LinearSampler = samplers.LinearClamp, PointSampler = samplers.PointClamp },
    VertexShader = "EmissiveTrail",
    PixelShader = "EmissiveTrail",
    PixelOutput = targets.PostTrail,
    Macros = {maybeCheaper},
}

local emissiveTrailEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.PostTrail,
        StageParameters = { EmissiveTrailParams = EmissiveTrailParams, CameraInfo = core.CameraInfo, WorldInfo = core.WorldInfo },
    },
    Program = EmissiveTrailProgram
}

EmissiveTrail = graphics_stage {
    EffectType = emissiveTrailEffect.Type,
    FixedDraws = { fixed_draw { Effect = emissiveTrailEffect } }
}

-- Blit prev

EmissiveTrailBlitPrevParams = frame_parameters {
    Source = texture2d(float4),
}

EmissiveTrailBlitPrevProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { EmissiveTrailBlitPrevParams = EmissiveTrailBlitPrevParams },
    Samplers = { LinearSampler = samplers.LinearClamp },
    VertexShader = "EmissiveTrailBlitPrev",
    PixelShader = "EmissiveTrailBlitPrev",
    PixelOutput = targets.PostTrailPrev,
    Macros = {maybeCheaper},
}

local emissiveTrailBlitPrevEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.PostTrailPrev,
        StageParameters = { EmissiveTrailBlitPrevParams = EmissiveTrailBlitPrevParams },
    },
    Program = EmissiveTrailBlitPrevProgram
}

EmissiveTrailBlitPrev = graphics_stage {
    EffectType = emissiveTrailBlitPrevEffect.Type,
    FixedDraws = { fixed_draw { Effect = emissiveTrailBlitPrevEffect } }
}

-- Clear mask

EmissiveTrailClearMaskParams = frame_parameters {
    Target = texture2d_rw(float4),
}

EmissiveTrailClearMaskProgram = compute_program {
    Samplers = { LinearSampler = samplers.LinearBlackBorder },
    Parameters = { EmissiveTrailClearMaskParams = EmissiveTrailClearMaskParams },
    Shader = "EmissiveTrailClearMask",
}

EmissiveTrailClearMaskEffect = compute_effect {
    Type = compute_effect_type {
        StageParameters = { EmissiveTrailClearMaskParams = EmissiveTrailClearMaskParams },
    },
    Program = EmissiveTrailClearMaskProgram
}

EmissiveTrailClearMask = compute_stage {
    EffectType = EmissiveTrailClearMaskEffect.Type,
}

--------------------------
-- Diffraction shader
--------------------------

DiffractionBlitParams = frame_parameters {
    Source = texture2d(float4),
}

DiffractionAlbedoBlitProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { DiffractionBlitParams = DiffractionBlitParams },
    Samplers = { LinearSampler = samplers.LinearClamp },
    VertexShader = "DiffractionBlit",
    PixelShader = "DiffractionBlit",
    PixelOutput = effects.DiffractAlbedoVariant_Depth,
    Macros = {maybeCheaper},
}

local diffractionAlbedoBlitEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = effects.DiffractAlbedoVariant_Depth,
        StageParameters = { DiffractionBlitParams = DiffractionBlitParams },
    },
    Program = DiffractionAlbedoBlitProgram
}

DiffractionAlbedoBlit = graphics_stage {
    EffectType = diffractionAlbedoBlitEffect.Type,
    FixedDraws = { fixed_draw { Effect = diffractionAlbedoBlitEffect } }
}

--- Depth

DiffractionHistBlitProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    Parameters = { DiffractionBlitParams = DiffractionBlitParams },
    Samplers = { LinearSampler = samplers.LinearClamp },
    VertexShader = "DiffractionBlit",
    PixelShader = "DiffractionBlit",
    PixelOutput = targets.DiffractPrevDepth,
    Macros = {maybeCheaper},
}

local diffractionHistBlitEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.DiffractPrevDepth,
        StageParameters = { DiffractionBlitParams = DiffractionBlitParams },
    },
    Program = DiffractionHistBlitProgram
}

DiffractionHistBlit = graphics_stage {
    EffectType = diffractionHistBlitEffect.Type,
    FixedDraws = { fixed_draw { Effect = diffractionHistBlitEffect } }
}

--------------------------
-- ColorGradient
--------------------------

ColorGradientParams = frame_parameters {
    Source = texture2d(float4),
    ExposureInfo = buffer(float),
}
ColorGradientProgram = graphics_program {
    CullMode = CullMode.None,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
    BlendState = blendSettings.States.PremultipliedOver,
    Parameters = { ColorGradientParams = ColorGradientParams, DiagnosticsInfo = core.DiagnosticsInfo, CameraInfo = core.CameraInfo },
    Samplers = { },
    VertexShader = "ColorGradient",
    PixelShader = "ColorGradient",
    PixelOutput = targets.Luminance,
}
ColorGradientEffect = graphics_effect {
    Type = graphics_effect_type {
        TargetSignature = targets.Luminance,
        StageParameters = { ColorGradientParams = ColorGradientParams, DiagnosticsInfo = core.DiagnosticsInfo, CameraInfo = core.CameraInfo },
    },
    Program = ColorGradientProgram
}
ColorGradient = graphics_stage {
    EffectType = ColorGradientEffect.Type,
    FixedDraws = { fixed_draw { Effect = ColorGradientEffect } },
    Diagnostics = { CountStats = false },
}


--------------------------
-- Standalone stages
--------------------------

MaskUnlit = graphics_stage {
    EffectType = effects.UnlitAbsorptionEffectType,
    Bins = { effects.UnlitAlphaBin },
}

MaskEmissiveUnlit = graphics_stage {
    EffectType = effects.UnlitAbsorptionEmissiveEffectType,
    Bins = { effects.UnlitAlphaEmissiveBin },
}

RenderUnlit = graphics_stage {
    EffectType = effects.UnlitEffectType,
    Bins = { effects.UnlitAlphaBin },
}

RenderEmissiveUnlit = graphics_stage {
    EffectType = effects.UnlitEmissiveEffectType,
    Bins = { effects.UnlitAlphaEmissiveBin },
}

AddEmissive = graphics_stage {
    EffectType = effects.EmissiveEffectType,
    Bins = { effects.EmissiveOpaqueBin },
}

AddEnvironment = graphics_stage {
    EffectType = effects.EnvironmentEffectType,
    Bins = { effects.EnvironmentOpaqueBin },
}

AddTransmissiveEmissive = graphics_stage {  -- this is distinct from AddEmissive because transmissive-emissive stuff is rendered later in the pipeline
    EffectType = effects.EmissiveEffectType,
    Bins = { effects.EmissiveTransmissiveBin, effects.PureEmissiveBin },
}

AddMedia = graphics_stage {
    EffectType = effects.MediaAddEffectType,
    Bins = { effects.MediaBin, effects.ChromaKeyMediaBin },
}

ClearLuminance = graphics_stage {
    EffectType = graphics_effect_type { TargetSignature = targets.Luminance },
    ColorClears = { color_clear { Enable = true } },
    DepthStencilClear = { Enable = false },
}

ClearLuminance_Depth = graphics_stage {
    EffectType = graphics_effect_type { TargetSignature = targets.Luminance_Depth },
    ColorClears = { color_clear { Enable = true } },
    DepthStencilClear = { Enable = true },
}

ClearJustDepth = graphics_stage {
    EffectType = graphics_effect_type { TargetSignature = targets.JustDepth },
    ColorClears = { color_clear { Enable = false } },
    DepthStencilClear = { Enable = true },
}

ClearPost = graphics_stage {
    EffectType = graphics_effect_type { TargetSignature = targets.Post_Depth },
    ColorClears = { color_clear { Enable = true } },
    DepthStencilClear = { Enable = false },
}

ClearLDR = graphics_stage {
    EffectType = graphics_effect_type { TargetSignature = targets.RasterLDR_Depth },
    ColorClears = { color_clear { Enable = true } },
    DepthStencilClear = { Enable = false },
}

ClearGBuffer = graphics_stage {
    EffectType = graphics_effect_type { TargetSignature = effects.Highlight_Depth },
    ColorClears = { color_clear { Enable = true } },
    DepthStencilClear = { Enable = false },
}

ClearDiffraction_Depth = graphics_stage {
    EffectType = graphics_effect_type { TargetSignature = targets.DiffractAlbedo },
    ColorClears = { color_clear { Enable = false } },
    DepthStencilClear = { Enable = true },
}

MultiplyTransmissive = graphics_stage {
    EffectType = effects.TransmissiveEffectType,
    Bins = { effects.EmissiveTransmissiveBin },
}

PrepopulateZbuffer = graphics_stage {
    EffectType = effects.DepthOnlyEffectType,
    Bins = { effects.PreZBin },
}

PrepopulateDisplacementZbuffer = graphics_stage {
    EffectType = effects.DisplacementDepthOnlyEffectType,
    Bins = { effects.DisplacementPreZBin },
}

PrepopulateVATZbuffer = graphics_stage {
    EffectType = effects.VATDepthOnlyEffectType,
    Bins = { effects.VATPreZBin },
}

PrepopulateTransDiffractZbuffer = graphics_stage {
    EffectType = effects.TransDiffractDepthOnlyEffectType,
    Bins = { effects.TransDiffractPreZBin },
}

PopulateGbuffer = graphics_stage {
    EffectType = effects.GbufferMotionEffectType,
    Bins = { effects.LitOpaqueBin, effects.SubsurfaceBin, effects.EmissiveOpaqueBin, effects.MediaBin },
}
PopulateVRVisDepth = graphics_stage {
    EffectType = effects.VRVisDepthEffectType,
    Bins = { effects.VRVisDepthBin },
}
PopulateMediaGbuffer = graphics_stage {
    EffectType = effects.MediaGbufferMotionEffectType,
    Bins = { effects.ChromaKeyMediaBin },
}
PopulateGbufferFading = graphics_stage {
    EffectType = effects.GbufferMotionFadingEffectType,
    Bins = { effects.LitOpaqueFadingBin },
}

PopulateGbufferDiffraction = graphics_stage {
    EffectType = effects.GbufferMotionDiffractionEffectType,
    Bins = { effects.UnlitOpaqueDiffractionBin },
}

PopulateGbufferTransparent = graphics_stage {
    EffectType = effects.TransparentGbufferEffectType,
    Bins = { effects.TransparentBin },
}

PopulateGbufferTransparentDiffraction = graphics_stage {
    EffectType = effects.TransparentDiffractGbufferEffectType,
    Bins = { effects.TransparentDiffractBin },
}

PopulateTransmittance = graphics_stage {
    EffectType = effects.TransmittanceEffectType,
    Bins = { effects.TransparentBin },
}

GatherSubsurface = graphics_stage {
    EffectType = effects.SubsurfaceGatherEffectType,
    Bins = { effects.SubsurfaceBin },
}

RenderAllAsDebug = graphics_stage {
    EffectType = core.DebugGeometryEffectType,
    Bins = { effects.LitOpaqueBin, effects.LitOpaqueFadingBin, effects.SubsurfaceBin, effects.EmissiveOpaqueBin, effects.MediaBin, effects.ChromaKeyMediaBin, effects.TransparentBin },
    Diagnostics = { CountStats = false },
}

RenderDebug = graphics_stage {
    EffectType = core.DebugGeometryEffectType,
    Bins = { core.DebugGeometryBin },
    Diagnostics = { CountStats = false },
}

RenderHighlight = graphics_stage {
    EffectType = core.HighlightEffectType,
    Bins = { core.HighlightBin },
    Diagnostics = { CountStats = false },
}

RenderHighlightMask = graphics_stage {
    EffectType = core.HighlightMaskEffectType,
    Bins = { core.HighlightBin },
    Diagnostics = { CountStats = false },
}

RenderWidget = graphics_stage {
    EffectType = core.WidgetGeometryEffectType,
    Bins = { core.WidgetGeometryBin },
    Diagnostics = { CountStats = false },
}

RenderWidgetBehind = graphics_stage {
    EffectType = core.WidgetGeometryBehindEffectType,
    Bins = { core.WidgetGeometryBin },
    ColorClears = { color_clear { Enable = false } },
    DepthStencilClear = { Enable = true },
    Diagnostics = { CountStats = false },
}

RenderTriangleDensity = graphics_stage {
    EffectType = effects.TriangleDensityEffectType,
    Bins = { effects.LitOpaqueBin, effects.LitOpaqueFadingBin, effects.SubsurfaceBin, effects.EmissiveOpaqueBin, effects.MediaBin, effects.ChromaKeyMediaBin, effects.TransparentBin, effects.EmissiveTransmissiveBin },
    Diagnostics = { CountStats = false },
}

RenderOverdraw = graphics_stage {
    EffectType = effects.OverdrawEffectType,
    Bins = { effects.LitOpaqueBin, effects.LitOpaqueFadingBin, effects.SubsurfaceBin, effects.EmissiveOpaqueBin, effects.MediaBin, effects.ChromaKeyMediaBin, effects.TransparentBin, effects.EmissiveTransmissiveBin },
    Diagnostics = { CountStats = false },
}

RenderWireframe = graphics_stage {
    EffectType = effects.WireframeEffectType,
    Bins = { effects.LitOpaqueBin, effects.LitOpaqueFadingBin, effects.SubsurfaceBin, effects.EmissiveOpaqueBin, effects.MediaBin, effects.ChromaKeyMediaBin, effects.TransparentBin, effects.EmissiveTransmissiveBin },
    Diagnostics = { CountStats = false },
}

RenderBackfaces = graphics_stage {
    EffectType = effects.BackfacesEffectType,
    Bins = { effects.LitOpaqueBin, effects.LitOpaqueFadingBin, effects.SubsurfaceBin, effects.EmissiveOpaqueBin, effects.MediaBin, effects.ChromaKeyMediaBin, effects.TransparentBin, effects.EmissiveTransmissiveBin },
    Diagnostics = { CountStats = false },
}

PopulateDiagnosticsZbuffer = graphics_stage {
    EffectType = effects.DiagnosticFillDepthEffectType,
    Bins = { effects.LitOpaqueBin, effects.LitOpaqueFadingBin, effects.SubsurfaceBin, effects.EmissiveOpaqueBin, effects.MediaBin, effects.ChromaKeyMediaBin, effects.TransparentBin, effects.EmissiveTransmissiveBin },
    Diagnostics = { CountStats = false },
}