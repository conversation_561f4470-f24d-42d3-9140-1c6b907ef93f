/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#ifndef LLWINDOW_WINDOW_H
#define LLWINDOW_WINDOW_H

#include "LLCore/Allocator.h"
#include "LLCore/IList.h"
#include "LLCore/Log.h"
#include "LLCore/String.h"
#include "LLCore/Time.h"
#include "LLGems/ConfigBlock.h"
#include "LLGpu/HDR.h"

namespace LLCore
{
class EventQueue;
};

namespace LLInput
{
class KeyboardInputManager;
}

namespace LLWindow
{
class Window;
class WindowManager;
class WindowMessageData;

struct WindowCoord
{
public:
    WindowCoord(int32 x, int32 y)
        : m_x(x), m_y(y)
    {
    }

    WindowCoord() {}

    inline bool operator==(const WindowCoord& other)
    {
        return (m_x == other.m_x && m_y == other.m_y);
    }

    inline bool operator!=(const WindowCoord& other)
    {
        return !(this->operator==(other));
    }

    int32 m_x = 0;
    int32 m_y = 0;
};

struct WindowPositionCoord : public LLGems::ConfigChoiceBlock<WindowPositionCoord>
{
    Alternative<float> m_relative = {{this, {"relative"}}, 0.5f};
    Alternative<int>   m_absolute = {{this, {cPositional, "absolute"}}, 0};
};

struct WindowPlacement : public LLGems::ConfigBlock<WindowPlacement>
{
    Optional<WindowPositionCoord>     m_x         = {{this, "x"}};
    Optional<WindowPositionCoord>     m_y         = {{this, "y"}};
    Optional<int32>                   m_width     = {{this, "width"}, 800};
    Optional<int32>                   m_height    = {{this, "height"}, 600};
    Optional<int32>                   m_minWidth  = {{this, "minWidth"}, 0};
    Optional<int32>                   m_minHeight = {{this, "minHeight"}, 0};
    Optional<int32>                   m_maxWidth  = {{this, "maxWidth"}, LLCore::cMaxInt32};
    Optional<int32>                   m_maxHeight = {{this, "maxHeight"}, LLCore::cMaxInt32};
    Optional<LLCore::StringFixed<64>> m_monitorId = {{this, "monitorId"}};

    WindowPlacement() = default;
    WindowPlacement(int32 x, int32 y, int32 w, int32 h)
    {
        m_x.m_absolute.asWrite(false) = x;
        m_y.m_absolute.asWrite(false) = y;
        m_width.asWrite(false)        = w;
        m_height.asWrite(false)       = h;
    }
    //TODO: better overload that isn't easily confused with absolute
    WindowPlacement(float x, float y, int32 w, int32 h)
    {
        m_x.m_relative.asWrite(false) = x;
        m_x.m_relative.choose();
        m_y.m_relative.asWrite(false) = y;
        m_y.m_relative.choose();
        m_width.asWrite(false)  = w;
        m_height.asWrite(false) = h;
    }
};

enum class WindowState
{
    cNormal,
    cMaximized,
    cMinimized
};
LLCORE_CONVERT_ENUM_AS_STRING_ORDERED(WindowState, "normal", "maximized", "minimized");

class WindowResizeEvent
{
public:
    WindowResizeEvent(int32 width, int32 height, WindowState windowState)
        : m_width(width), m_height(height), m_windowState(windowState){};

    int32       m_width;
    int32       m_height;
    WindowState m_windowState;
};

class WindowStartResizeEvent
{
public:
};

class WindowEndResizeEvent
{
public:
};

class Window : public LLCore::RefCounted
    , private LLCore::NoCopy
{
public:
    struct Config : public LLGems::ConfigBlock<Config>
    {
        Optional<WindowPlacement>         m_placement            = {{this, {"placement", "rect", cInline}}, WindowPlacement{0.f, 0.f, 1024, 768}};
        Optional<LLCore::StringFixed<64>> m_title                = {{this, "title"}};
        Optional<bool>                    m_fullscreen           = {{this, "fullscreen"}, false};
        Optional<bool>                    m_visible              = {{this, "visible"}, true};
        Optional<bool>                    m_hasTitleBarAndBorder = {{this, "hasTitleBarAndBorder"}, true};
        Optional<WindowState>             m_windowState          = {{this, "windowState"}, WindowState::cNormal};
        Optional<bool>                    m_acceptDrops          = {{this, "acceptDrops"}, false};
        Optional<bool>                    m_enableHdr            = {{this, "enableHdr"}, false};
        Optional<bool>                    m_useVSync             = {{this, "useVSync"}, true};

        //TODO move this into windowmanager, since only one window should have focus at a time
        bool  m_hasFocus        = true;
        bool  m_isHdrEnabled    = false;
        bool  m_isHdrAvailable  = false;
        float m_hdrMaxFALL      = 0.0f;
        float m_hdrMaxLuminance = 0.0f;
        float m_hdrWhitePoint   = 0.0f;
        LLCore::Matrix3 m_hdrColorMatrix = LLCore::Matrix3();
    };
    class PlatformData;

private:
    Window(Config*, WindowManager*);

public:
    ~Window();

    //window properties
    void setTitle(const LLCore::String& text);
    void setFullscreen(bool fullscreen);
    bool getFullscreen() const;

    uint32 getDisplayRefreshRate() const;
    void   getDisplayDimensions(LLCore::Array<int32>* widths, LLCore::Array<int32>* heights) const;
    bool   getPrimaryDisplayDimension(int32* outWidth, int32* outHeight) const;

    // Used to capture mouse input even when moved outside of the client area
    void setMouseCapture();
    void releaseMouseCapture();

    // Forces mouse to stay within the window
    void constrainMouse();
    void deconstrainMouse();

    void show();
    void hide();

    //cursor modification functions (might be better in platform specific code)
    bool setCursorPosition(const LLWindow::WindowCoord& position);
    bool getCursorPosition(WindowCoord& positionOut);

    const PlatformData* getPlatformData() const { return m_platformData; }
    PlatformData*       getPlatformData() { return m_platformData; }

    void                  handleResize(int32 width, int32 height, WindowState windowState = WindowState::cNormal);
    void                  handleStartResize();
    void                  handleEndResize();
    LLWindow::WindowCoord getSize() const;
    void                  setTitleBarAndBorder(bool b);

    void acceptDrops(bool accept); // OS drag-and-drop

    // Flags
    bool getMaximized() const { return m_config->m_windowState.asRead() == WindowState::cMaximized; }
    bool getMinimized() const { return m_config->m_windowState.asRead() == WindowState::cMinimized; }
    bool getRestored() const { return m_config->m_windowState.asRead() == WindowState::cNormal; }
    bool getVisible() const { return m_config->m_visible; }
    bool getHasFocus() const { return m_config->m_hasFocus; }
    bool getIsHdrEnabled() const { return m_config->m_isHdrEnabled; }
    void setIsHdrEnabled(bool value) const { m_config->m_isHdrEnabled = value; }
    bool getIsHdrAvailable() const { return m_config->m_isHdrAvailable; }
    bool getIsHdrNotAvailable() const { return !m_config->m_isHdrAvailable; }
    void setIsHdrAvailable(bool value) const { m_config->m_isHdrAvailable = value; }
    void setHdrMaxFALL(float value) const { m_config->m_hdrMaxFALL = value; }
    void setHdrMaxLuminance(float value) const { m_config->m_hdrMaxLuminance = value; }
    void setHdrWhitePoint(float value) const { m_config->m_hdrWhitePoint = value; }
    void setHdrColorMatrix(LLCore::Matrix3 value) const { m_config->m_hdrColorMatrix = value; }
    bool getUseVSync() const { return m_config->m_useVSync; }
    bool getEnableHDR() const { return m_config->m_enableHdr; }
    void setEnableHDR(bool value);
    void setEnableVSync(bool value);
    void setCloseTimeout(LLCore::Duration timeout, LLCore::Function<void(void)> handler);
    void startCloseTimeout();


    // Shows a platform specific message box.
    static void ShowErrorMessage(LLCore::StringRef text);

protected:
    LLCore::DeferredConstruction<LLCore::AsyncTimer> m_windowCloseTimer;
    LLCore::Duration                                 m_windowCloseTimeout;
    LLCore::Function<void(void)>                     m_windowCloseTimeoutHandler;
    PlatformData*                                    m_platformData  = nullptr;
    WindowManager*                                   m_windowManager = nullptr;
    LLCore::IListLink                                m_link;
    Config*                                          m_config;
    friend class WindowManager;
};

class WindowManager
{
public:
    class PlatformData;

    WindowManager(LLCore::EventQueue* windowEventQueue);
    ~WindowManager();

    using WindowList = LLCore::IList<Window, &Window::m_link, 1>;

    void        setWindowTitleDecorator(const LLCore::String& decorator);
    void        getWindowTitleDecorator(LLCore::String* decorator);
    Window*     createWindow(const Window::Config&);
    Window*     createWindow(Window::Config*);
    WindowList& getWindowList() { return m_windows; }
    void        destroyWindow(LLWindow::Window* window);

    PlatformData*       getPlatformData() { return m_platformData; }
    LLCore::EventQueue* getWindowEventQueue() { return m_windowEventQueue; }

protected:
    static PlatformData* CreatePlatformData(WindowManager* windowManager);
    static void          DestroyPlatformData(PlatformData* data);

    LLCore::StringFixed<32>      m_windowTitleDecorator;
    PlatformData*                m_platformData;
    LLCore::List<Window::Config> m_windowConfigs;
    WindowList                   m_windows;
    LLCore::EventQueue*          m_windowEventQueue;
};
} // namespace LLWindow
#endif
