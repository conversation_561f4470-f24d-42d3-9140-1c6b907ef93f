﻿#pragma checksum "QuestDefinitionEditor.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "FA6B1523A8E59A354790580613E8FE02CBCFE9772837412551AE3CE3832F0AA1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using Microsoft.Expression.Interactivity.Core;
using Microsoft.Expression.Interactivity.Input;
using Microsoft.Expression.Interactivity.Layout;
using Microsoft.Expression.Interactivity.Media;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// QuestDefinitionEditorControl
    /// </summary>
    public partial class QuestDefinitionEditorControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 10 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.QuestDefinitionEditorControl QuestDefinitionEditor;
        
        #line default
        #line hidden
        
        
        #line 30 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid TabContent;
        
        #line default
        #line hidden
        
        
        #line 82 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer QuestDefinitionEditorScrollviewer;
        
        #line default
        #line hidden
        
        
        #line 108 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid QuestDefEditorNavBar;
        
        #line default
        #line hidden
        
        
        #line 110 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackToQuestDefList;
        
        #line default
        #line hidden
        
        
        #line 130 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid QuestDefinitionInformation;
        
        #line default
        #line hidden
        
        
        #line 142 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ObjectiveDefinitionInformation;
        
        #line default
        #line hidden
        
        
        #line 154 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ItemRewardDefinitionInformation;
        
        #line default
        #line hidden
        
        
        #line 167 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualStateGroup VisualStateGroup;
        
        #line default
        #line hidden
        
        
        #line 171 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState TabsToObjectiveInformation;
        
        #line default
        #line hidden
        
        
        #line 181 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState TabsToQuestInformation;
        
        #line default
        #line hidden
        
        
        #line 191 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState QuestDefinitionToTabs;
        
        #line default
        #line hidden
        
        
        #line 207 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState TabsToItemRewardInformation;
        
        #line default
        #line hidden
        
        
        #line 217 "QuestDefinitionEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState TabsToSelectInventoryItem;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/quests/questcreator/questdefinitioneditor.xaml", System.UriKind.Relative);
            
            #line 1 "QuestDefinitionEditor.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.QuestDefinitionEditor = ((LindenLab.QuestDefinitionEditorControl)(target));
            return;
            case 2:
            this.TabContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.QuestDefinitionEditorScrollviewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 4:
            this.QuestDefEditorNavBar = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.BackToQuestDefList = ((System.Windows.Controls.Button)(target));
            return;
            case 6:
            this.QuestDefinitionInformation = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.ObjectiveDefinitionInformation = ((System.Windows.Controls.Grid)(target));
            return;
            case 8:
            this.ItemRewardDefinitionInformation = ((System.Windows.Controls.Grid)(target));
            return;
            case 9:
            this.VisualStateGroup = ((System.Windows.VisualStateGroup)(target));
            return;
            case 10:
            this.TabsToObjectiveInformation = ((System.Windows.VisualState)(target));
            return;
            case 11:
            this.TabsToQuestInformation = ((System.Windows.VisualState)(target));
            return;
            case 12:
            this.QuestDefinitionToTabs = ((System.Windows.VisualState)(target));
            return;
            case 13:
            this.TabsToItemRewardInformation = ((System.Windows.VisualState)(target));
            return;
            case 14:
            this.TabsToSelectInventoryItem = ((System.Windows.VisualState)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

