#ifndef Common_ICCCorrection_hlsli
#define Common_ICCCorrection_hlsli
// ICC Color Profile integration for HDR tonemapping pipeline

static const float3 iccLuminanceRec2020Coeff = float3(0.2627, 0.6780, 0.0593); // Rec2020 luminance coefficients

// Structure to encapsulate ICC color transformation parameters
struct ICCTransformParameters
{
    float3x3 colorTransform; // Primary ICC color transform matrix
    float3 preOffset; // Optional pre-transform offset
    float3 postOffset; // Optional post-transform offset
    float saturationFactor; // Saturation adjustment (1.0 = no change)
    float gamutCompressionFactor; // How aggressively to handle out-of-gamut colors
};

// Create a default set of parameters for initialization
static const ICCTransformParameters kDefaultICCParams =
{
    float3x3(1, 0, 0, 0, 1, 0, 0, 0, 1), // Identity matrix
    float3(0, 0, 0), // No pre-offset
    float3(0, 0, 0), // No post-offset
    1.0f, // No saturation change
    1.0f // Standard gamut compression
};

// Optimal ICC color transform implementation
float3 ApplyICCColorTransform(float3 linearColor, ICCTransformParameters params)
{
    // 1. Apply pre-transform offset (often used for black level adjustment)
    float3 color = linearColor + params.preOffset;
    
    // 2. Apply primary color transform matrix
    color = mul(color, params.colorTransform);
    
    // 3. Apply post-transform offset
    color = color + params.postOffset;
    
    // 4. Handle out-of-gamut colors with intelligent gamut mapping
    // First detect if we have any negative components (out of gamut)
    float minComponent = min(min(color.r, color.g), color.b);
    if (minComponent < 0.0)
    {
        // Calculate how far out of gamut we are
        float outOfGamutAmount = abs(minComponent);
        
        // Apply gamut compression while preserving hue
        float luma = dot(color, iccLuminanceRec2020Coeff);
        
        // Move towards neutral gray based on how far out of gamut we are
        float compressionStrength = saturate(outOfGamutAmount * params.gamutCompressionFactor);
        float3 neutralGray = (luma).xxx;
        
        color = lerp(color, neutralGray, compressionStrength);
        
        // Ensure we're back in gamut by clamping
        color = max(color, 0.0);
    }
    
    // 5. Apply saturation adjustment if needed
    if (params.saturationFactor != 1.0)
    {
        float luma = dot(color, iccLuminanceRec2020Coeff);
        color = lerp(luma.xxx, color, params.saturationFactor);
    }
    
    return color;
}


float3 PreserveWhitePoint(float3 color, float3x3 colorTransform)
{
    // Create a reference white
    const float3 referenceWhite = float3(1.0, 1.0, 1.0);
    
    // See how the transform affects pure white
    float3 transformedWhite = mul(colorTransform, referenceWhite);
    
    // Create a correction vector that would restore white back to (1,1,1)
    float3 whiteCorrection = referenceWhite / (transformedWhite + 0.00001);
    
    // Apply the transform and then the white correction as a per-channel scale
    return color * whiteCorrection;
}

float3 PreserveD65WhitePoint(float3 color, float3x3 colorTransform)
{
    // D65 white point reference (this is the standard illuminant for DCI-P3-D65)
    // These RGB values represent D65 white in linear space
    const float3 d65White = float3(1.0, 1.0, 1.0);
    
    // Transform D65 reference through the color matrix
    float3 transformedD65 = mul(colorTransform, d65White);
    
    // Calculate correction vector to maintain D65 white point
    float3 whiteCorrection = d65White / (transformedD65 + 0.00001);
    
    // Apply transformation and then correct to preserve D65 white point
    return mul(colorTransform, color) * whiteCorrection;
}

#endif