﻿#pragma checksum "SettingsDialogControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "827735DE6B3FCC97DFD86B6CE1F61053EA317934E8ACA438C37006274AB165AB"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// SettingsDialogControl
    /// </summary>
    public partial class SettingsDialogControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 9 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.SettingsDialogControl SettingsRoot;
        
        #line default
        #line hidden
        
        
        #line 115 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl SettingsTabControl;
        
        #line default
        #line hidden
        
        
        #line 124 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem GeneralTab;
        
        #line default
        #line hidden
        
        
        #line 142 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid General;
        
        #line default
        #line hidden
        
        
        #line 400 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ComfortZone;
        
        #line default
        #line hidden
        
        
        #line 460 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DesktopAvatarControls;
        
        #line default
        #line hidden
        
        
        #line 539 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Camera;
        
        #line default
        #line hidden
        
        
        #line 582 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem AudioTab;
        
        #line default
        #line hidden
        
        
        #line 599 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AudioOutput;
        
        #line default
        #line hidden
        
        
        #line 797 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DesktopOutput;
        
        #line default
        #line hidden
        
        
        #line 812 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox VrOutput;
        
        #line default
        #line hidden
        
        
        #line 821 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AudioInput;
        
        #line default
        #line hidden
        
        
        #line 972 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DesktopInput;
        
        #line default
        #line hidden
        
        
        #line 987 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox VrInput;
        
        #line default
        #line hidden
        
        
        #line 1024 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AudioOptions;
        
        #line default
        #line hidden
        
        
        #line 1044 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem GraphicsTab;
        
        #line default
        #line hidden
        
        
        #line 1060 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Graphics;
        
        #line default
        #line hidden
        
        
        #line 1332 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ColorPickerPanel;
        
        #line default
        #line hidden
        
        
        #line 1339 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.TextBoxExt ColorTextBox;
        
        #line default
        #line hidden
        
        
        #line 1352 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button colorPickerButton;
        
        #line default
        #line hidden
        
        
        #line 1467 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid VR;
        
        #line default
        #line hidden
        
        
        #line 1599 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabItem AdvancedTab;
        
        #line default
        #line hidden
        
        
        #line 1615 "SettingsDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Advanced;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/settings/settingsdialogcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "SettingsDialogControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SettingsRoot = ((LindenLab.SettingsDialogControl)(target));
            return;
            case 2:
            this.SettingsTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 3:
            this.GeneralTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 4:
            this.General = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.ComfortZone = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.DesktopAvatarControls = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.Camera = ((System.Windows.Controls.Grid)(target));
            return;
            case 8:
            this.AudioTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 9:
            this.AudioOutput = ((System.Windows.Controls.Grid)(target));
            return;
            case 10:
            this.DesktopOutput = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.VrOutput = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.AudioInput = ((System.Windows.Controls.Grid)(target));
            return;
            case 13:
            this.DesktopInput = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.VrInput = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.AudioOptions = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            this.GraphicsTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 17:
            this.Graphics = ((System.Windows.Controls.Grid)(target));
            return;
            case 18:
            this.ColorPickerPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.ColorTextBox = ((LindenLab.TextBoxExt)(target));
            return;
            case 20:
            this.colorPickerButton = ((System.Windows.Controls.Button)(target));
            return;
            case 21:
            this.VR = ((System.Windows.Controls.Grid)(target));
            return;
            case 22:
            this.AdvancedTab = ((System.Windows.Controls.TabItem)(target));
            return;
            case 23:
            this.Advanced = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

