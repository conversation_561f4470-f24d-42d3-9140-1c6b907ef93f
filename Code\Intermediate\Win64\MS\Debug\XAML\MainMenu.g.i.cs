﻿#pragma checksum "MainMenu.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "18B9FAB3D9143BB9D66765E3264AAB5975533579C451E0C2DD97F43CB652E3FC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using Microsoft.Expression.Interactivity.Core;
using Microsoft.Expression.Interactivity.Input;
using Microsoft.Expression.Interactivity.Layout;
using Microsoft.Expression.Interactivity.Media;
using NoesisGUIExtensions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// MainMenu
    /// </summary>
    public partial class MainMenu : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 391 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid BgOverlay;
        
        #line default
        #line hidden
        
        
        #line 425 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ButtonBar;
        
        #line default
        #line hidden
        
        
        #line 427 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton TippingButton;
        
        #line default
        #line hidden
        
        
        #line 453 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton GoButton;
        
        #line default
        #line hidden
        
        
        #line 500 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button goCodex;
        
        #line default
        #line hidden
        
        
        #line 548 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RespawnButton;
        
        #line default
        #line hidden
        
        
        #line 567 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton SocializeButton;
        
        #line default
        #line hidden
        
        
        #line 635 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton EmoteButton;
        
        #line default
        #line hidden
        
        
        #line 663 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ScreenshotButton;
        
        #line default
        #line hidden
        
        
        #line 682 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CreateButton;
        
        #line default
        #line hidden
        
        
        #line 773 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ShopButton;
        
        #line default
        #line hidden
        
        
        #line 813 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button storeAvatar;
        
        #line default
        #line hidden
        
        
        #line 824 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button storeScene;
        
        #line default
        #line hidden
        
        
        #line 855 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton OptionsButton;
        
        #line default
        #line hidden
        
        
        #line 903 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button controlsDesktop;
        
        #line default
        #line hidden
        
        
        #line 914 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button controlsVr;
        
        #line default
        #line hidden
        
        
        #line 954 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton VolumeButton;
        
        #line default
        #line hidden
        
        
        #line 990 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.VolumeSlidersDialogControl VolumeSlidersControl;
        
        #line default
        #line hidden
        
        
        #line 998 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton VolumeButtonDisabled;
        
        #line default
        #line hidden
        
        
        #line 1027 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RuntimeInventoryButton;
        
        #line default
        #line hidden
        
        
        #line 1055 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton RuntimeInventoryButtonDisabled;
        
        #line default
        #line hidden
        
        
        #line 1082 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MicAvailableButtons;
        
        #line default
        #line hidden
        
        
        #line 1084 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton MicButtonOff;
        
        #line default
        #line hidden
        
        
        #line 1118 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton MicButtonOn;
        
        #line default
        #line hidden
        
        
        #line 1152 "MainMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton DisabledMicButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/mainmenu/mainmenu.xaml", System.UriKind.Relative);
            
            #line 1 "MainMenu.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BgOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.ButtonBar = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.TippingButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 4:
            this.GoButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 5:
            this.goCodex = ((System.Windows.Controls.Button)(target));
            return;
            case 6:
            this.RespawnButton = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.SocializeButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 8:
            this.EmoteButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 9:
            this.ScreenshotButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 10:
            this.CreateButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 11:
            this.ShopButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 12:
            this.storeAvatar = ((System.Windows.Controls.Button)(target));
            return;
            case 13:
            this.storeScene = ((System.Windows.Controls.Button)(target));
            return;
            case 14:
            this.OptionsButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 15:
            this.controlsDesktop = ((System.Windows.Controls.Button)(target));
            return;
            case 16:
            this.controlsVr = ((System.Windows.Controls.Button)(target));
            return;
            case 17:
            this.VolumeButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 18:
            this.VolumeSlidersControl = ((LindenLab.VolumeSlidersDialogControl)(target));
            return;
            case 19:
            this.VolumeButtonDisabled = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 20:
            this.RuntimeInventoryButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 21:
            this.RuntimeInventoryButtonDisabled = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 22:
            this.MicAvailableButtons = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 23:
            this.MicButtonOff = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 24:
            this.MicButtonOn = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 25:
            this.DisabledMicButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

