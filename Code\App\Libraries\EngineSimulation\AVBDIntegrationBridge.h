#pragma once

#include "LLCore/Types.h"
#include "LLCore/Vector3.h"
#include "LLCore/Quaternion.h"
#include "LLCore/TaskQueue.h"

// Forward declarations
class hkTaskQueue;

// Include the AVBD solver headers
#include "solver.h"

namespace EngineSimulation
{
    class RigidBodyComponent;
    class SimulationWorld;

    class AVBDIntegrationBridge
    {
    public:
        AVBDIntegrationBridge();
        ~AVBDIntegrationBridge();

        void initialize(float gravity);
        void step(float deltaTime);

        // Body management
        void addBody(RigidBodyComponent* component);
        void removeBody(RigidBodyComponent* component);
        void updateBody(RigidBodyComponent* component);

        // Transform synchronization
        void syncTransforms();

        // Enable/disable AVBD physics
        void setEnabled(bool enabled) { m_enabled = enabled; }
        bool isEnabled() const { return m_enabled; }

    private:
        void convertToAVBDRigid(RigidBodyComponent* component, Rigid& avbdRigid);
        void convertFromAVBDRigid(const Rigid& avbdRigid, RigidBodyComponent* component);

        struct BodyMapping {
            RigidBodyComponent* component;
            Rigid* avbdRigid;
            bool isActive;
        };

        Solver m_solver;
        std::unordered_map<RigidBodyComponent*, BodyMapping> m_bodyMap;
        std::unordered_map<Rigid*, RigidBodyComponent*> m_reverseMap;
        
        bool m_enabled;
        bool m_initialized;
        float m_gravity;
    };
}