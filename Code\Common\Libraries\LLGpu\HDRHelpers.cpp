/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#include "HDRHelpers.h"
#include "LLCore/Log.h"
#include <LLCore/BasicMath.h>

#if LLGPU_ACTIVE_API == LLGPU_API_D3D11

#include <cfloat>
#include <dxgi1_6.h>


namespace LLGpu
{
    namespace HDRHelpers
    {


        // Colorspace detection based on display primaries and white point

        enum class DetectedColorSpace
        {
            Unknown,
            sRGB_Rec709,
            DCI_P3,
            DisplayP3,
            Rec2020,
            AdobeRGB,
            Custom
        };

        struct ColorSpaceDefinition
        {
            const char* name;
            float       redPrimary[2];
            float       greenPrimary[2];
            float       bluePrimary[2];
            float       whitePoint[2];
            float       coverage; // Approximate % of visible spectrum
        };

        // Standard color space definitions
        static const ColorSpaceDefinition kStandardColorSpaces[] = {
            {"sRGB/Rec.709", {0.64f, 0.33f}, {0.30f, 0.60f}, {0.15f, 0.06f}, {0.3127f, 0.3290f}, 35.9f},
            {"DCI-P3", {0.68f, 0.32f}, {0.265f, 0.69f}, {0.15f, 0.06f}, {0.314f, 0.351f}, 45.5f},
            {"Display P3", {0.68f, 0.32f}, {0.265f, 0.69f}, {0.15f, 0.06f}, {0.3127f, 0.3290f}, 45.4f},
            {"Rec.2020", {0.708f, 0.292f}, {0.17f, 0.797f}, {0.131f, 0.046f}, {0.3127f, 0.3290f}, 75.8f},
            {"Adobe RGB", {0.64f, 0.33f}, {0.21f, 0.71f}, {0.15f, 0.06f}, {0.3127f, 0.3290f}, 52.1f}};

        static DetectedColorSpace IdentifyDisplayColorSpace(const DXGI_OUTPUT_DESC1& desc, float* matchConfidence = nullptr)
        {
            // Calculate distance metric (weighted sum of distances for each primary and white point)
            auto calculateDistance = [](const float* p1, const float* p2, int components)
            {
                float sumSquaredDiff = 0.0f;
                for (int i = 0; i < components; i++)
                {
                    float diff = p1[i] - p2[i];
                    sumSquaredDiff += diff * diff;
                }
                return LLCore::Sqrt(sumSquaredDiff);
            };

            // Calculate triangle area for gamut size comparison
            auto calculateGamutArea = [](const float* r, const float* g, const float* b)
            {
                // Area = 0.5 * |cross product|
                float x1 = g[0] - r[0];
                float y1 = g[1] - r[1];
                float x2 = b[0] - r[0];
                float y2 = b[1] - r[1];
                return 0.5f * LLCore::Abs(x1 * y2 - x2 * y1);
            };

            // Extract display primaries into a flat array for simpler comparison
            float displayPrimaries[8] = {
                desc.RedPrimary[0], desc.RedPrimary[1], desc.GreenPrimary[0], desc.GreenPrimary[1], desc.BluePrimary[0], desc.BluePrimary[1], desc.WhitePoint[0], desc.WhitePoint[1]};

            // Calculate display's gamut area
            float displayArea = calculateGamutArea(desc.RedPrimary, desc.GreenPrimary, desc.BluePrimary);

            // Find closest matching color space
            float minDistance    = FLT_MAX;
            int   bestMatchIndex = -1;

            for (int i = 0; i < sizeof(kStandardColorSpaces) / sizeof(kStandardColorSpaces[0]); i++)
            {
                const auto& stdSpace = kStandardColorSpaces[i];

                // Create flat array of reference primaries
                float refPrimaries[8] = {
                    stdSpace.redPrimary[0], stdSpace.redPrimary[1], stdSpace.greenPrimary[0], stdSpace.greenPrimary[1], stdSpace.bluePrimary[0], stdSpace.bluePrimary[1], stdSpace.whitePoint[0], stdSpace.whitePoint[1]};

                // Calculate distance between this reference color space and the display's reported primaries
                float distance = static_cast<float>(calculateDistance(displayPrimaries, refPrimaries, 8));

                // Calculate reference gamut area
                float refArea = calculateGamutArea(
                    stdSpace.redPrimary, stdSpace.greenPrimary, stdSpace.bluePrimary);

                // Adjust distance based on gamut size difference (penalize mismatched sizes)
                float areaDifference = abs(displayArea - refArea) / LLCore::Max(displayArea, refArea);
                distance += areaDifference * 0.2f; // Weight the area difference less than primary positions

                if (distance < minDistance)
                {
                    minDistance    = distance;
                    bestMatchIndex = i;
                }
            }

            // Apply confidence threshold
            const float MATCH_THRESHOLD = 0.05f; // Lower values = higher confidence required

            if (matchConfidence)
            {
                *matchConfidence = 1.0f - minDistance * 5.0f; // Convert to 0-1 range
            }

            if (minDistance > MATCH_THRESHOLD || bestMatchIndex < 0)
            {
                return DetectedColorSpace::Custom;
            }

            // Map index to enum
            switch (bestMatchIndex)
            {
                case 0:
                    return DetectedColorSpace::sRGB_Rec709;
                case 1:
                    return DetectedColorSpace::DCI_P3;
                case 2:
                    return DetectedColorSpace::DisplayP3;
                case 3:
                    return DetectedColorSpace::Rec2020;
                case 4:
                    return DetectedColorSpace::AdobeRGB;
                default:
                    return DetectedColorSpace::Unknown;
            }
        }
        //


        // Color profile generation

        static bool ValidateColorPrimaries(const DXGI_OUTPUT_DESC1& desc)
        {
            LLCore::Vector4 redPrimary(desc.RedPrimary[0], desc.RedPrimary[1], 0.0f);
            LLCore::Vector4 greenPrimary(desc.GreenPrimary[0], desc.GreenPrimary[1], 0.0f);
            LLCore::Vector4 bluePrimary(desc.BluePrimary[0], desc.BluePrimary[1], 0.0f);
            LLCore::Vector4 whitePoint(desc.WhitePoint[0], desc.WhitePoint[1], 0.0f);

            // Positive Y values (prevent division by zero)
            const float MIN_Y = 0.0001f;
            if (redPrimary.getY().asFloat() < MIN_Y || greenPrimary.getY().asFloat() < MIN_Y || bluePrimary.getY().asFloat() < MIN_Y || whitePoint.getY().asFloat() < MIN_Y)
            {
                return false;
            }

            // Chromaticity coordinates boundary check
            const float MIN_CHROMATICITY = 0.0f;
            const float MAX_CHROMATICITY = 1.0f;

            // Check all x,y values are in valid range
            auto checkPrimary = [&](const LLCore::Vector4& primary, const char* name)
            {
                float x = primary.getX().asFloat();
                float y = primary.getY().asFloat();

                if (x < MIN_CHROMATICITY || x > MAX_CHROMATICITY || y < MIN_CHROMATICITY || y > MAX_CHROMATICITY || x + y > 1.0f)
                { // Check x+y <= 1.0 (Z must be non-negative)
                    LLCore::LogWarning("HDR", LLCore::Format("Invalid primary %s: (%f, %f)", name, x, y));
                    return false;
                }
                return true;
            };

            return checkPrimary(redPrimary, "red") && checkPrimary(greenPrimary, "green") && checkPrimary(bluePrimary, "blue") && checkPrimary(whitePoint, "white");
        }

        static bool ValidateColorGamut(const DXGI_OUTPUT_DESC1& desc)
        {
            LLCore::Vector4 redPrimary(desc.RedPrimary[0], desc.RedPrimary[1], 0.0f);
            LLCore::Vector4 greenPrimary(desc.GreenPrimary[0], desc.GreenPrimary[1], 0.0f);
            LLCore::Vector4 bluePrimary(desc.BluePrimary[0], desc.BluePrimary[1], 0.0f);
            LLCore::Vector4 whitePoint(desc.WhitePoint[0], desc.WhitePoint[1], 0.0f);

            // 1. Check the primaries form a valid triangle (not collinear)
            float triangleArea = 0.5f * abs((greenPrimary.getX().asFloat() - redPrimary.getX().asFloat()) * (bluePrimary.getY().asFloat() - redPrimary.getY().asFloat()) - (bluePrimary.getX().asFloat() - redPrimary.getX().asFloat()) * (greenPrimary.getY().asFloat() - redPrimary.getY().asFloat()));

            if (triangleArea < 0.001f)
            {
                LLCore::LogError("HDR", "Primaries are nearly collinear - cannot form valid color space");
                return false;
            }

            // 2. Check if white point is inside the RGB triangle
            // Use barycentric coordinates
            float wx = whitePoint.getX().asFloat();
            float wy = whitePoint.getY().asFloat();
            float rx = redPrimary.getX().asFloat();
            float ry = redPrimary.getY().asFloat();
            float gx = greenPrimary.getX().asFloat();
            float gy = greenPrimary.getY().asFloat();
            float bx = bluePrimary.getX().asFloat();
            float by = bluePrimary.getY().asFloat();

            float denominator = (gy - by) * (rx - bx) + (bx - gx) * (ry - by);
            float a           = ((gy - by) * (wx - bx) + (bx - gx) * (wy - by)) / denominator;
            float b           = ((by - ry) * (wx - bx) + (rx - bx) * (wy - by)) / denominator;
            float c           = 1.0f - a - b;

            const float EPSILON = -0.001f; // Small negative value for numerical stability
            if (a < EPSILON || b < EPSILON || c < EPSILON)
            {
                LLCore::LogWarning("HDR", "White point lies outside RGB triangle - may require negative scale factors");
            }

            // 3. Verify primaries are not too close to the spectral locus edge
            // This is a simplification - professional implementations would use actual spectral locus data
            bool validGamut = true;

            return validGamut;
        }

        // Option 1: Matrix conditioning (modifying your current approach)
        static LLCore::Matrix3 InvertWithConditioning(const LLCore::Matrix3& matrix)
        {
            LLCore::Matrix3 result = matrix;
            LLCore::Scalar  det    = matrix.getDeterminant();

            // Check if determinant is too small
            if (abs(det.asFloat()) < 1e-5f)
            {
                // Add a small value to diagonal elements to improve conditioning
                const float epsilon = 1e-4f;
                for (int i = 0; i < 3; i++)
                {
                    LLCore::Vector4 col = result.getColumn(i);
                    col.setScalarAt(i, col.getScalarAt(i) + epsilon);
                    result.getColumnRw(i) = col;
                }

                // Check if conditioning helped
                det = result.getDeterminant();
                if (abs(det.asFloat()) < 1e-5f)
                {
                    // If still not invertible, use identity
                    return LLCore::Matrix3(LLCore::Matrix3Constants::cIdentity);
                }
            }

            result.invert();
            return result;
        }

        // Option 2: Blend with identity matrix based on condition number
        static LLCore::Matrix3 GetRobustInverse(const LLCore::Matrix3& matrix)
        {
            LLCore::Scalar det    = matrix.getDeterminant();
            float          absDet = abs(det.asFloat());

            if (absDet < 1e-5f)
            {
                // Create a blend between identity and the original matrix
                float blendFactor = LLCore::Clamp(absDet / 1e-5f, 0.0f, 1.0f);

                LLCore::Matrix3 result;
                for (int i = 0; i < 3; i++)
                {
                    for (int j = 0; j < 3; j++)
                    {
                        float identity                       = (i == j) ? 1.0f : 0.0f;
                        float value                          = matrix.getColumn(j).getScalarAt(i).asFloat();
                        result.getColumnRw(j).getScalarAt(i) = identity + (value - identity) * blendFactor;
                    }
                }

                result.invert();
                return result;
            }

            // Original matrix has good determinant, proceed with normal inversion
            LLCore::Matrix3 result = matrix;
            result.invert();
            return result;
        }

        static LLCore::Matrix3 ApplyGamutCompressionMatrix(float compressionFactor)
        {
            // Ensure the factor is in [0,1] range
            compressionFactor = LLCore::Clamp(compressionFactor, 0.0f, 1.0f);

            // Calculate how much of the original color to preserve
            float preserveFactor = 1.0f - compressionFactor;

            // Use Rec.709/sRGB luminance coefficients for calculating neutral axis
            const float lumR = 0.2126f;
            const float lumG = 0.7152f;
            const float lumB = 0.0722f;

            // Create matrix that blends between identity and luminance conversion
            LLCore::Matrix3 compressionMatrix;

            // Red output = (preserve * original.r) + (compress * luminance)
            compressionMatrix.getColumnRw(0).setScalarAt(0, preserveFactor + compressionFactor * lumR);
            compressionMatrix.getColumnRw(1).setScalarAt(0, compressionFactor * lumG);
            compressionMatrix.getColumnRw(2).setScalarAt(0, compressionFactor * lumB);

            // Green output = (preserve * original.g) + (compress * luminance)
            compressionMatrix.getColumnRw(0).setScalarAt(1, compressionFactor * lumR);
            compressionMatrix.getColumnRw(1).setScalarAt(1, preserveFactor + compressionFactor * lumG);
            compressionMatrix.getColumnRw(2).setScalarAt(1, compressionFactor * lumB);

            // Blue output = (preserve * original.b) + (compress * luminance)
            compressionMatrix.getColumnRw(0).setScalarAt(2, compressionFactor * lumR);
            compressionMatrix.getColumnRw(1).setScalarAt(2, compressionFactor * lumG);
            compressionMatrix.getColumnRw(2).setScalarAt(2, preserveFactor + compressionFactor * lumB);

            return compressionMatrix;
        }

        static LLCore::Matrix3 CalculateRGBToXYZMatrix(LLCore::Vector4 redPrimary, LLCore::Vector4 greenPrimary, LLCore::Vector4 bluePrimary, LLCore::Vector4 whitePoint)
        {
            // Verify primary coordinates are valid
            LLCORE_ASSERT(redPrimary.getY().asFloat() > 0.0f, "Red primary Y coordinate must be positive");
            LLCORE_ASSERT(greenPrimary.getY().asFloat() > 0.0f, "Green primary Y coordinate must be positive");
            LLCORE_ASSERT(bluePrimary.getY().asFloat() > 0.0f, "Blue primary Y coordinate must be positive");
            LLCORE_ASSERT(whitePoint.getY().asFloat() > 0.0f, "White point Y coordinate must be positive");

            // Convert CIE xy chromaticity coordinates to XYZ (normalized to Y=1)
            // Formula: X = x/y, Y = 1.0, Z = (1-x-y)/y
            LLCore::Vector4 r = LLCore::Vector4(
                redPrimary.getX().asFloat() / redPrimary.getY().asFloat(),
                1.0f,
                (1.0f - redPrimary.getX().asFloat() - redPrimary.getY().asFloat()) / redPrimary.getY().asFloat(),
                0.0f);

            LLCore::Vector4 g = LLCore::Vector4(
                greenPrimary.getX().asFloat() / greenPrimary.getY().asFloat(),
                1.0f,
                (1.0f - greenPrimary.getX().asFloat() - greenPrimary.getY().asFloat()) / greenPrimary.getY().asFloat(),
                0.0f);

            LLCore::Vector4 b = LLCore::Vector4(
                bluePrimary.getX().asFloat() / bluePrimary.getY().asFloat(),
                1.0f,
                (1.0f - bluePrimary.getX().asFloat() - bluePrimary.getY().asFloat()) / bluePrimary.getY().asFloat(),
                0.0f);

            LLCore::Vector4 w = LLCore::Vector4(
                whitePoint.getX().asFloat() / whitePoint.getY().asFloat(),
                1.0f,
                (1.0f - whitePoint.getX().asFloat() - whitePoint.getY().asFloat()) / whitePoint.getY().asFloat(),
                0.0f);

            // Create primaries matrix [r,g,b]
            LLCore::Matrix3 primaries(r, g, b); // columns are primaries

            // Check invertibility before attempting inversion
            LLCore::Matrix3 primariesInv = primaries;

            primariesInv = InvertWithConditioning(primariesInv);

            // Solve for scaling coefficients: [r,g,b][s] = w
            LLCore::Vector4 scales = LLCore::Mul(primariesInv, w);

            // Handle negative scale coefficients which lead to invalid transformations
            if (scales.getX().asFloat() <= 0.0f || scales.getY().asFloat() <= 0.0f || scales.getZ().asFloat() <= 0.0f)
            {
                LLCore::LogWarning("HDR", "Color space has negative scale factors - check primaries accuracy");
            }

            // Construct the final RGB to XYZ transformation matrix
            // Scale each primary by its respective coefficient
            return LLCore::Matrix3(
                LLCore::Vector4(r.getX().asFloat() * scales.getX().asFloat(), g.getX().asFloat() * scales.getY().asFloat(), b.getX().asFloat() * scales.getZ().asFloat()),
                LLCore::Vector4(r.getY().asFloat() * scales.getX().asFloat(), g.getY().asFloat() * scales.getY().asFloat(), b.getY().asFloat() * scales.getZ().asFloat()),
                LLCore::Vector4(r.getZ().asFloat() * scales.getX().asFloat(), g.getZ().asFloat() * scales.getY().asFloat(), b.getZ().asFloat() * scales.getZ().asFloat()));
        }
        
        static LLCore::Matrix3 GenerateTransformForSDRDisplay(DXGI_OUTPUT_DESC1& desc)
        {
            // For sRGB/Rec.709 displays in HDR mode (very limited gamut)
            // We need a gentler transform to avoid excessive saturation shifts

            // Create standard BT.2020 reference primaries
            LLCore::Vector4 bt2020RedPrimary(0.708f, 0.292f, 0.0f);
            LLCore::Vector4 bt2020GreenPrimary(0.170f, 0.797f, 0.0f);
            LLCore::Vector4 bt2020BluePrimary(0.131f, 0.046f, 0.0f);
            LLCore::Vector4 bt2020WhitePoint(0.3127f, 0.3290f, 0.0f);

            // Step 1: Calculate BT.2020 to Rec.709 transform
            LLCore::Vector4 rec709RedPrimary(0.64f, 0.33f, 0.0f);
            LLCore::Vector4 rec709GreenPrimary(0.30f, 0.60f, 0.0f);
            LLCore::Vector4 rec709BluePrimary(0.15f, 0.06f, 0.0f);
            LLCore::Vector4 rec709WhitePoint(0.3127f, 0.3290f, 0.0f);

            // Calculate intermediate transform matrices
            const LLCore::Matrix3 bt2020ToXYZ = HDRHelpers::CalculateRGBToXYZMatrix(
                bt2020RedPrimary, bt2020GreenPrimary, bt2020BluePrimary, bt2020WhitePoint);

            const LLCore::Matrix3 rec709ToXYZ = HDRHelpers::CalculateRGBToXYZMatrix(
                rec709RedPrimary, rec709GreenPrimary, rec709BluePrimary, rec709WhitePoint);

            // Get inverse of Rec.709 RGB to XYZ
            LLCore::Matrix3 xyzToRec709 = rec709ToXYZ;
            xyzToRec709                 = HDRHelpers::InvertWithConditioning(xyzToRec709);

            // Calculate BT.2020 to Rec.709 transform
            LLCore::Matrix3 bt2020ToRec709 = LLCore::Matrix3();
            bt2020ToRec709.setMul(xyzToRec709, bt2020ToXYZ);

            // Step 2: Apply perceptual gamut compression to avoid excessive saturation
            LLCore::Matrix3 gamutCompression = ApplyGamutCompressionMatrix(0.15f); // 15% compression

            // Final transform: combines standard transform with gamut compression
            LLCore::Matrix3 combine = LLCore::Matrix3();
            combine.setMul(gamutCompression, bt2020ToRec709);
            return combine;
        }

        static LLCore::Matrix3 GenerateTransformForP3Display(DXGI_OUTPUT_DESC1& desc)
        {
            // For P3 displays that report HDR capability
            // We'll use a two-stage transform for better perceptual mapping

            // Standard P3 primaries (can use either DCI or Display P3 depending on detection)
            LLCore::Vector4 p3RedPrimary(0.68f, 0.32f, 0.0f);
            LLCore::Vector4 p3GreenPrimary(0.265f, 0.69f, 0.0f);
            LLCore::Vector4 p3BluePrimary(0.15f, 0.06f, 0.0f);
            LLCore::Vector4 p3WhitePoint(0.3127f, 0.3290f, 0.0f); // D65 for Display P3

            // For wide-gamut display optimization, we can use the actual reported primaries
            // to make subtle corrections between standard P3 and the specific display
            LLCore::Vector4 displayRedPrimary(desc.RedPrimary[0], desc.RedPrimary[1], 0.0f);
            LLCore::Vector4 displayGreenPrimary(desc.GreenPrimary[0], desc.GreenPrimary[1], 0.0f);
            LLCore::Vector4 displayBluePrimary(desc.BluePrimary[0], desc.BluePrimary[1], 0.0f);
            LLCore::Vector4 displayWhitePoint(desc.WhitePoint[0], desc.WhitePoint[1], 0.0f);

            // Step 1: Calculate BT.2020 to standard P3 transform (optimized mapping)
            LLCore::Vector4 bt2020RedPrimary(0.708f, 0.292f, 0.0f);
            LLCore::Vector4 bt2020GreenPrimary(0.170f, 0.797f, 0.0f);
            LLCore::Vector4 bt2020BluePrimary(0.131f, 0.046f, 0.0f);
            LLCore::Vector4 bt2020WhitePoint(0.3127f, 0.3290f, 0.0f);

            // Calculate transform matrices
            const LLCore::Matrix3 bt2020ToXYZ = HDRHelpers::CalculateRGBToXYZMatrix(
                bt2020RedPrimary, bt2020GreenPrimary, bt2020BluePrimary, bt2020WhitePoint);

            const LLCore::Matrix3 p3ToXYZ = HDRHelpers::CalculateRGBToXYZMatrix(
                p3RedPrimary, p3GreenPrimary, p3BluePrimary, p3WhitePoint);

            LLCore::Matrix3 xyzToP3 = p3ToXYZ;
            xyzToP3                 = HDRHelpers::InvertWithConditioning(xyzToP3);

            // Step 2: Calculate display-specific fine tuning (P3 → actual display primaries)
            const LLCore::Matrix3 displayToXYZ = HDRHelpers::CalculateRGBToXYZMatrix(
                displayRedPrimary, displayGreenPrimary, displayBluePrimary, displayWhitePoint);

            LLCore::Matrix3 xyzToDisplay = displayToXYZ;
            xyzToDisplay                 = HDRHelpers::InvertWithConditioning(xyzToDisplay);

            // Step 3: Combine matrices for final transform (BT.2020 → P3 → Display)
            // This two-stage approach preserves perceptual accuracy better than direct conversion
            LLCore::Matrix3 bt2020ToP3  = LLCore::Matrix3();
            bt2020ToP3.setMul(xyzToDisplay, p3ToXYZ);

            LLCore::Matrix3 p3ToDisplay = LLCore::Matrix3();
            p3ToDisplay.setMul(xyzToDisplay, p3ToXYZ);
            return p3ToDisplay;
        }

        static LLCore::Matrix3 GenerateTransformForNativeRec2020Display(DXGI_OUTPUT_DESC1& desc)
        {
            // For native Rec.2020 displays, we want to preserve the wide color gamut
            // with minimal transformation, possibly just fine-tuning based on actual primaries

            // Standard BT.2020 reference primaries (ideal case)
            LLCore::Vector4 bt2020RedPrimary(0.708f, 0.292f, 0.0f);
            LLCore::Vector4 bt2020GreenPrimary(0.170f, 0.797f, 0.0f);
            LLCore::Vector4 bt2020BluePrimary(0.131f, 0.046f, 0.0f);
            LLCore::Vector4 bt2020WhitePoint(0.3127f, 0.3290f, 0.0f);

            // Get the display's actual reported primaries
            LLCore::Vector4 displayRedPrimary(desc.RedPrimary[0], desc.RedPrimary[1], 0.0f);
            LLCore::Vector4 displayGreenPrimary(desc.GreenPrimary[0], desc.GreenPrimary[1], 0.0f);
            LLCore::Vector4 displayBluePrimary(desc.BluePrimary[0], desc.BluePrimary[1], 0.0f);
            LLCore::Vector4 displayWhitePoint(desc.WhitePoint[0], desc.WhitePoint[1], 0.0f);

            // Calculate similarity between standard Rec.2020 and reported primaries
            float redDistance   = (bt2020RedPrimary - displayRedPrimary).getLength<1>().asFloat();;
            float greenDistance = (bt2020GreenPrimary - displayGreenPrimary).getLength<1>().asFloat();
            float blueDistance  = (bt2020BluePrimary - displayBluePrimary).getLength<1>().asFloat();
            float whiteDistance = (bt2020WhitePoint - displayWhitePoint).getLength<1>().asFloat();
            float totalDistance = redDistance + greenDistance + blueDistance + whiteDistance;

            // If the display's primaries are very close to standard Rec.2020, we can use identity
            // This preserves full gamut with no distortion
            const float CLOSE_ENOUGH_THRESHOLD = 0.02f; // Adjust based on testing - smaller = stricter

            if (totalDistance < CLOSE_ENOUGH_THRESHOLD)
            {
                // Display is very close to true Rec.2020 - use identity for maximum gamut preservation
                LLCore::LogInfo("HDR", "Display primaries very close to true Rec.2020 - using identity transform");
                return LLCore::Matrix3(LLCore::Matrix3Constants::cIdentity);
            }
            else
            {
                // Display deviates from true Rec.2020 - calculate correction matrix
                LLCore::LogInfo("HDR", LLCore::Format("Rec.2020 primaries differ (distance: %.3f) - applying correction", totalDistance));

                // Calculate BT.2020 standard to XYZ transform
                const LLCore::Matrix3 bt2020ToXYZ = HDRHelpers::CalculateRGBToXYZMatrix(
                    bt2020RedPrimary, bt2020GreenPrimary, bt2020BluePrimary, bt2020WhitePoint);

                // Calculate display's actual primaries to XYZ transform
                const LLCore::Matrix3 displayToXYZ = HDRHelpers::CalculateRGBToXYZMatrix(
                    displayRedPrimary, displayGreenPrimary, displayBluePrimary, displayWhitePoint);

                // Get inverse (XYZ to display's actual color space)
                LLCore::Matrix3 xyzToDisplay = displayToXYZ;
                xyzToDisplay                 = HDRHelpers::InvertWithConditioning(xyzToDisplay);

                // Direct transformation from BT.2020 to display's actual color space
                LLCore::Matrix3 bt2020ToDisplay = LLCore::Matrix3();
                bt2020ToDisplay.setMul(xyzToDisplay, bt2020ToXYZ);

                // Apply a very mild gamut compression to avoid clipping
                // For Rec.2020 displays, we use a much lighter compression than for other spaces
                LLCore::Matrix3 gamutCompression = ApplyGamutCompressionMatrix(0.05f); // Just 5% compression

                // Combine the transforms
                LLCore::Matrix3 result = LLCore::Matrix3();
                result.setMul(gamutCompression, bt2020ToDisplay);

                return result;
            }
        }

        static LLCore::Matrix3 GenerateColorTransformForDisplay(DXGI_OUTPUT_DESC1& desc)
        {
            if (!ValidateColorPrimaries(desc) || !ValidateColorGamut(desc))
            {
                LLCore::LogError("HDR", "Display color primaries or gamut validation failed.");
                return LLCore::Matrix3(LLCore::Matrix3Constants::cIdentity);
            }

            // First, detect the display's actual color space capabilities
            float                          confidence         = 0.0f;
            HDRHelpers::DetectedColorSpace physicalColorSpace = HDRHelpers::IdentifyDisplayColorSpace(desc, &confidence);

            // Log the detected color space
            const char* colorSpaceNames[] = {
                "Unknown", "sRGB/Rec.709", "DCI-P3", "Display P3", "Rec.2020", "Adobe RGB", "Custom"};

            LLCore::LogInfo("HDR", LLCore::Format("Display physical color space detected as %s (confidence: %.1f%%)", colorSpaceNames[static_cast<int>(physicalColorSpace)], confidence * 100.0f));

            // The additional transform functions exist only for the academic value
            // Windows expects applications to submit their HDR10 frame-buffers in Rec2020
            // and will perform its own transformation to the display using the installed
            // Advanced ICM profile to reach a final color accurate presentation.
            return GenerateTransformForNativeRec2020Display(desc);

            /*
            // Use different strategies based on detected color space
            switch (physicalColorSpace)
            {
                case HDRHelpers::DetectedColorSpace::sRGB_Rec709:
                    return HDRHelpers::GenerateTransformForSDRDisplay(desc);

                case HDRHelpers::DetectedColorSpace::DCI_P3:
                case HDRHelpers::DetectedColorSpace::DisplayP3:
                    return GenerateTransformForP3Display(desc);

                case HDRHelpers::DetectedColorSpace::Rec2020:
                    return GenerateTransformForNativeRec2020Display(desc);

                case HDRHelpers::DetectedColorSpace::AdobeRGB:
                case HDRHelpers::DetectedColorSpace::Unknown:
                case HDRHelpers::DetectedColorSpace::Custom:
                default:
                    // Fall back to standard transform but with more robust validation
                    return LLCore::Matrix3Constants::cIdentity;
            }
            */
        }
    }
} // namespace LLGpu
#endif