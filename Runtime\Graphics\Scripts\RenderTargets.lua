local scriptParams = get_script_params()
local config = scriptParams.scriptConfig

local pipelineTypes = {
    'None',
    'Low',
    'High',
}
local pipelineTypeChanged = is_undeclared('PipelineType') or PipelineType ~= pipelineTypes[1 + config.pipelineType]
PipelineType = pipelineTypes[1 + config.pipelineType]

local platforms = {
    'pc11',
}
PackagePlatform = platforms[1 + config.platform]
local configurations = {
    'Release',
    'Debug',
}
Configuration = configurations[1 + config.configuration]
if Configuration == 'Debug' then
    PackageExtension = "glld"
else
    PackageExtension = "gll"
end

local common = package("Common", PackagePlatform, PackageExtension)

local function roundUp(x, w)
    return math.ceil(x/w)*w
end

RenderScale = math.sqrt(scriptParams.samplesPerPixel)

local displayWidth = get_surface_width()
local displayHeight = get_surface_height()
local renderWidth = roundUp(displayWidth*RenderScale, 32)
local renderHeight = roundUp(displayHeight*RenderScale, 32)
displayWidth = roundUp(displayWidth, 32)
displayHeight = roundUp(displayHeight, 32)

local isFirstRun = is_undeclared('InitializeOnceGuard')
if isFirstRun then
    InitializeOnceGuard    = true

    SkyL2CoeffsTarget      = targetBuffer { Bytes = 9*4*4 }
    SkyL2Coeffs            = buffer    { Target = SkyL2CoeffsTarget, View = { Format = common.Targets.Float4.Colors[1], Length = 9 } }
    SkyL2CoeffsRw          = buffer_rw { Target = SkyL2CoeffsTarget, View = { Format = common.Targets.Float4.Colors[1], Length = 9 } }

    ExposureInfoTarget     = targetBuffer { Bytes = 16 }
    ExposureInfo           = buffer    { Target = ExposureInfoTarget, View = { Format = common.Targets.Float1.Colors[1], Length = 3 } }
    ExposureInfoRw         = buffer_rw { Target = ExposureInfoTarget, View = { Format = common.Targets.Float1.Colors[1], Length = 3 } }

    HDRExposureInfoTarget  = targetBuffer { Bytes = 40 }
    HDRExposureInfo        = buffer    { Target = HDRExposureInfoTarget, View = { Format = common.Targets.Uint1.Colors[1], Length = 5 } }
    HDRExposureInfoRw      = buffer_rw { Target = HDRExposureInfoTarget, View = { Format = common.Targets.Uint1.Colors[1], Length = 5 } }

    HDRTonemappingTarget  = targetBuffer { Bytes = 16 }
    HDRTonemapping        = buffer    { Target = HDRTonemappingTarget, View = { Format = common.Targets.Float4.Colors[1], Length = 1 } }
    HDRTonemappingRw      = buffer_rw { Target = HDRTonemappingTarget, View = { Format = common.Targets.Float4.Colors[1], Length = 1 } }

    VideoEmissionTarget    = target2d { Width = 8, Height = 8, Format = common.Targets.VideoEmissions.Colors[1] }
    VideoEmissionTexture   = texture2d { Target = VideoEmissionTarget }
    VideoEmissionTextureRw = texture2d_rw { Target = VideoEmissionTarget }
end

local displayDimensionsChanged = is_undeclared('DisplayDimensions') or DisplayDimensions.Width ~= displayWidth or DisplayDimensions.Height ~= displayHeight
if displayDimensionsChanged then
    if not isFirstRun then
        trace("DisplayDimensions changed: " .. tostring(displayWidth) .. ' x ' .. tostring(displayHeight))
    end
    DisplayDimensions   = { Width = displayWidth, Height = displayHeight }

    local HdrLuminanceTileCount = ((displayWidth+15)/16) * ((displayHeight+15)/16)
    HdrLuminanceTilesTarget  = targetBuffer { Bytes = HdrLuminanceTileCount * 16 }
    HdrLuminanceTiles        = buffer    { Target = HdrLuminanceTilesTarget, View = { Format = common.Targets.Float4.Colors[1], Length = HdrLuminanceTileCount } }
    HdrLuminanceTilesRw      = buffer_rw { Target = HdrLuminanceTilesTarget, View = { Format = common.Targets.Float4.Colors[1], Length = HdrLuminanceTileCount } }

    PostColor           = target2d { Width = displayWidth, Height = displayHeight, Format = common.Targets.Post.Colors[1] }
    PostColorSDR        = target2d { Width = displayWidth, Height = displayHeight, Format = common.Targets.Post_SDR.Colors[1] }
    PostTrailColor      = target2d { Width = displayWidth, Height = displayHeight, Format = common.Targets.PostTrail.Colors[1] }
    PostTrailPrevColor  = target2d { Width = displayWidth, Height = displayHeight, Format = common.Targets.PostTrailPrev.Colors[1] }
    DisplayHdr1         = target2d { Width = displayWidth, Height = displayHeight, Format = common.Targets.Luminance.Colors[1] }
    DisplayHdr2         = target2d { Width = displayWidth, Height = displayHeight, Format = common.Targets.Luminance.Colors[1] }
    FSRTonemapped       = target2d { Width = displayWidth, Height = displayHeight, Format = common.Targets.FSR.Colors[1] }

    UIDepthTarget       = target2d { Width = displayWidth, Height = displayHeight, Format = common.Targets.JustDepth.Depth }

    HdrDownresTargets = {
        target2d { Width = displayWidth/2,   Height = displayHeight/2,   Format = common.Targets.Luminance.Colors[1] },
        target2d { Width = displayWidth/4,   Height = displayHeight/4,   Format = common.Targets.Luminance.Colors[1] },
        target2d { Width = displayWidth/8,   Height = displayHeight/8,   Format = common.Targets.Luminance.Colors[1] },
        target2d { Width = displayWidth/16,  Height = displayHeight/16,  Format = common.Targets.Luminance.Colors[1] },
        target2d { Width = displayWidth/32,  Height = displayHeight/32,  Format = common.Targets.Luminance.Colors[1] },
        target2d { Width = displayWidth/64,  Height = displayHeight/64,  Format = common.Targets.Luminance.Colors[1] },
        target2d { Width = displayWidth/128, Height = displayHeight/128, Format = common.Targets.Luminance.Colors[1] },
        target2d { Width = displayWidth/256, Height = displayHeight/256, Format = common.Targets.Luminance.Colors[1] },
    }

    HdrDownres = {
        { Source = HdrDownresTargets[1], Texture = texture2d { Target = HdrDownresTargets[1] }, TargetOnly = multi_target_easy( common.Targets.Luminance, {HdrDownresTargets[1]} ) },
        { Source = HdrDownresTargets[2], Texture = texture2d { Target = HdrDownresTargets[2] }, TargetOnly = multi_target_easy( common.Targets.Luminance, {HdrDownresTargets[2]} ) },
        { Source = HdrDownresTargets[3], Texture = texture2d { Target = HdrDownresTargets[3] }, TargetOnly = multi_target_easy( common.Targets.Luminance, {HdrDownresTargets[3]} ) },
        { Source = HdrDownresTargets[4], Texture = texture2d { Target = HdrDownresTargets[4] }, TargetOnly = multi_target_easy( common.Targets.Luminance, {HdrDownresTargets[4]} ) },
        { Source = HdrDownresTargets[5], Texture = texture2d { Target = HdrDownresTargets[5] }, TargetOnly = multi_target_easy( common.Targets.Luminance, {HdrDownresTargets[5]} ) },
        { Source = HdrDownresTargets[6], Texture = texture2d { Target = HdrDownresTargets[6] }, TargetOnly = multi_target_easy( common.Targets.Luminance, {HdrDownresTargets[6]} ) },
        { Source = HdrDownresTargets[7], Texture = texture2d { Target = HdrDownresTargets[7] }, TargetOnly = multi_target_easy( common.Targets.Luminance, {HdrDownresTargets[7]} ) },
        { Source = HdrDownresTargets[8], Texture = texture2d { Target = HdrDownresTargets[8] }, TargetOnly = multi_target_easy( common.Targets.Luminance, {HdrDownresTargets[8]} ) },
    }

    HdrHalfResTargets = {
        HdrDownresTargets[1],
        target2d { Width = displayWidth/2,   Height = displayHeight/2,   Format = common.Targets.Luminance.Colors[1] },
    }
    HdrFourthResTargets = {
        HdrDownresTargets[2],
        target2d { Width = displayWidth/4,   Height = displayHeight/4,   Format = common.Targets.Luminance.Colors[1] },
    }
    HdrHalfResTextures = {
        texture2d { Target = HdrHalfResTargets[1] },
        texture2d { Target = HdrHalfResTargets[2] },
    }
    HdrFourthResTextures = {
        texture2d { Target = HdrFourthResTargets[1] },
        texture2d { Target = HdrFourthResTargets[2] },
    }
    HdrHalfRes_HdrHalfRes           = multi_target_easy( common.Targets.Luminance_Luminance, HdrHalfResTargets )
    HdrFourthRes_HdrFourthRes       = multi_target_easy( common.Targets.Luminance_Luminance, HdrFourthResTargets )

    DisplayHdr1Texture              = texture2d { Target = DisplayHdr1 }
    DisplayHdr2Texture              = texture2d { Target = DisplayHdr2 }
    DisplayHdr1TextureRw            = texture2d_rw { Target = DisplayHdr1 }
    DisplayHdr2TextureRw            = texture2d_rw { Target = DisplayHdr2 }

    FSRTonemappedTexture            = texture2d { Target = FSRTonemapped }
    FSRTonemappedTextureRw          = texture2d_rw { Target = FSRTonemapped }

    PostColorTexture                = texture2d { Target = PostColor }
    PostColorTextureRw              = texture2d_rw { Target = PostColor }
    PostColorSDRTexture             = texture2d { Target = PostColorSDR }
    PostTrailColorTexture           = texture2d { Target = PostTrailColor }
    PostTrailPrevColorTexture       = texture2d { Target = PostTrailPrevColor }

    UIDepth_Only                    = multi_target_easy( common.Targets.JustDepth,              {}, UIDepthTarget )

    FSRTonemapped_Only              = multi_target_easy( common.Targets.FSR,                    {FSRTonemapped} )
    Post_Only                       = multi_target_easy( common.Targets.Post,                   {PostColor} )
    Post_HDR_SDR                    = multi_target_easy( common.Targets.Post_HDR_SDR,           {PostColor, PostColorSDR} )
    Post_HDR_SDR_DDepth             = multi_target_easy( common.Targets.Post_HDR_SDR_Depth,     {PostColor, PostColorSDR}, UIDepthTarget )
    PostTrail_Only                  = multi_target_easy( common.Targets.PostTrail,              {PostTrailColor} )
    PostTrailPrev_Only              = multi_target_easy( common.Targets.PostTrailPrev,          {PostTrailPrevColor} )

    DisplayHdr1_Only                = multi_target_easy( common.Targets.Luminance,              {DisplayHdr1} )
    DisplayHdr2_Only                = multi_target_easy( common.Targets.Luminance,              {DisplayHdr2} )
end --displayDimensionsChanged

local renderDimensionsChanged = is_undeclared('RenderDimensions') or RenderDimensions.Width ~= renderWidth or RenderDimensions.Height ~= renderHeight or pipelineTypeChanged
if renderDimensionsChanged then
    if not isFirstRun then
        trace("RenderDimensions changed: " .. tostring(renderWidth) .. ' x ' .. tostring(renderHeight))
    end
    RenderDimensions                    = { Width = renderWidth, Height = renderHeight }
    RenderHdr1                          = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.Luminance.Colors[1] }
    RenderLdr1                          = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.RasterLDR.Colors[1] }
    Motion                              = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.Motion.Colors[1] }
    DepthTarget                         = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.JustDepth.Depth }
    ScratchDepthTarget                  = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.JustDepth.Depth }
    VisibilityTarget                    = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.Rgba8Snorm.Colors[1] }
    if PipelineType == 'High' then
        Gbuffer1                        = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.GbufferHigh.Colors[1] }
        Gbuffer2                        = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.GbufferHigh.Colors[2] }
        Gbuffer3                        = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.GbufferHigh.Colors[3] }
    else
        Gbuffer1                        = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.GbufferLow.Colors[1] }
        Gbuffer2                        = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.GbufferLow.Colors[2] }
    end

    Hightlight                          = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.RasterLDR.Colors[1] }

    DiffractDepthTarget                 = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.JustDepth.Depth }
    DiffractCurDepthTarget              = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.DiffractCurDepth.Colors[1] }
    DiffractPrevDepthTarget             = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.DiffractPrevDepth.Colors[1] }
    DiffractPrevTransDepthTarget        = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.DiffractPrevTransDepth.Colors[1] }

    if PipelineType == 'High' then
        DiffractAlbedo                  = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.DiffractAlbedoHigh.Colors[1] }
    else
        DiffractAlbedo                  = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.DiffractAlbedoLow.Colors[1] }
    end

    DepthMinMaxHalfRes                  = target2d { Width = renderWidth/2, Height = renderHeight/2, Format = common.Targets.JustDepthMinMax.Colors[1] }
    DepthMinMaxFourthRes                = target2d { Width = renderWidth/4, Height = renderHeight/4, Format = common.Targets.JustDepthMinMax.Colors[1] }

    Visibility1ATarget                  = target2d { Width = renderWidth/4, Height = renderHeight/4, Format = common.Targets.Rgba8Snorm.Colors[1] }
    Visibility2ATarget                  = target2d { Width = renderWidth/4, Height = renderHeight/4, Format = common.Targets.Rgba8Snorm.Colors[1] }
    Visibility1BTarget                  = target2d { Width = renderWidth/4, Height = renderHeight/4, Format = common.Targets.Rgba8Snorm.Colors[1] }
    Visibility2BTarget                  = target2d { Width = renderWidth/4, Height = renderHeight/4, Format = common.Targets.Rgba8Snorm.Colors[1] }

    if PipelineType == 'High' then
        ReflectTrace                    = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.ReflectTrace.Colors[1] }
        ReflectSolve                    = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.ReflectSolve.Colors[1] }
        ReflectReproj                   = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.ReflectReproj.Colors[1] }
        ReflectPrevReproj               = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.ReflectPrevReproj.Colors[1] }

        ReflectDownresTargets = {
            target2d { Width = renderWidth/2,   Height = renderHeight/2,   Format = common.Targets.ReflectDownRes.Colors[1] },
            target2d { Width = renderWidth/4,   Height = renderHeight/4,   Format = common.Targets.ReflectDownRes.Colors[1] },
            target2d { Width = renderWidth/8,   Height = renderHeight/8,   Format = common.Targets.ReflectDownRes.Colors[1] },
            target2d { Width = renderWidth/16,  Height = renderHeight/16,  Format = common.Targets.ReflectDownRes.Colors[1] },
            target2d { Width = renderWidth/32,  Height = renderHeight/16,  Format = common.Targets.ReflectDownRes.Colors[1] },
        }
    else
        ReflectTrace                    = target2d { Width = 4, Height = 4, Format = common.Targets.ReflectTrace.Colors[1] }
        ReflectSolve                    = target2d { Width = 4, Height = 4, Format = common.Targets.ReflectSolve.Colors[1] }
        ReflectReproj                   = target2d { Width = 4, Height = 4, Format = common.Targets.ReflectReproj.Colors[1] }
        ReflectPrevReproj               = target2d { Width = 4, Height = 4, Format = common.Targets.ReflectPrevReproj.Colors[1] }

        ReflectDownresTargets = {
            target2d { Width = 4,  Height = 4,  Format = common.Targets.ReflectDownRes.Colors[1] },
            target2d { Width = 4,  Height = 4,  Format = common.Targets.ReflectDownRes.Colors[1] },
            target2d { Width = 4,  Height = 4,  Format = common.Targets.ReflectDownRes.Colors[1] },
            target2d { Width = 4,  Height = 4,  Format = common.Targets.ReflectDownRes.Colors[1] },
            target2d { Width = 4,  Height = 4,  Format = common.Targets.ReflectDownRes.Colors[1] },
        }
    end

    ReflectDownres = {
        { Source = ReflectDownresTargets[1], Texture = texture2d { Target = ReflectDownresTargets[1] }, TargetOnly = multi_target_easy( common.Targets.ReflectDownRes, {ReflectDownresTargets[1]} ) },
        { Source = ReflectDownresTargets[2], Texture = texture2d { Target = ReflectDownresTargets[2] }, TargetOnly = multi_target_easy( common.Targets.ReflectDownRes, {ReflectDownresTargets[2]} ) },
        { Source = ReflectDownresTargets[3], Texture = texture2d { Target = ReflectDownresTargets[3] }, TargetOnly = multi_target_easy( common.Targets.ReflectDownRes, {ReflectDownresTargets[3]} ) },
        { Source = ReflectDownresTargets[4], Texture = texture2d { Target = ReflectDownresTargets[4] }, TargetOnly = multi_target_easy( common.Targets.ReflectDownRes, {ReflectDownresTargets[4]} ) },
        { Source = ReflectDownresTargets[5], Texture = texture2d { Target = ReflectDownresTargets[5] }, TargetOnly = multi_target_easy( common.Targets.ReflectDownRes, {ReflectDownresTargets[5]} ) },
    }

    VisibilityTextureRw                 = texture2d_rw { Target = VisibilityTarget }
    Visibility1ATextureRw               = texture2d_rw { Target = Visibility1ATarget }
    Visibility2ATextureRw               = texture2d_rw { Target = Visibility2ATarget }
    Visibility1BTextureRw               = texture2d_rw { Target = Visibility1BTarget }
    Visibility2BTextureRw               = texture2d_rw { Target = Visibility2BTarget }

    VisibilityTexture                   = texture2d { Target = VisibilityTarget }
    Visibility1ATexture                 = texture2d { Target = Visibility1ATarget }
    Visibility2ATexture                 = texture2d { Target = Visibility2ATarget }
    Visibility1BTexture                 = texture2d { Target = Visibility1BTarget }
    Visibility2BTexture                 = texture2d { Target = Visibility2BTarget }

    RenderHdr1Texture                   = texture2d { Target = RenderHdr1 }
    RenderLdr1Texture                   = texture2d { Target = RenderLdr1 }
    MotionTexture                       = texture2d { Target = Motion }

    DepthTexture                        = texture2d { Target = DepthTarget, View = { Format = common.Targets.JustDepthAsColor.Colors[1] } }
    ScratchDepthTexture                 = texture2d { Target = ScratchDepthTarget, View = { Format = common.Targets.JustDepthAsColor.Colors[1] } }
    DepthMinMaxHalfResTexture           = texture2d { Target = DepthMinMaxHalfRes }
    DepthMinMaxFourthResTexture         = texture2d { Target = DepthMinMaxFourthRes }
    Gbuffer1Texture                     = texture2d { Target = Gbuffer1 }
    Gbuffer1TextureRw                   = texture2d_rw { Target = Gbuffer1 }
    Gbuffer2Texture                     = texture2d { Target = Gbuffer2 }
    if PipelineType == 'High' then
        Gbuffer3Texture                 = texture2d { Target = Gbuffer3 }
        OpacityGbufferTexture           = Gbuffer3Texture
    else
        OpacityGbufferTexture           = Gbuffer2Texture
    end

    RenderHdr1TextureRw                 = texture2d_rw { Target = RenderHdr1 }
    RenderLdr1TextureRw                 = texture2d_rw { Target = RenderLdr1 }

    HighlightTexture                    = texture2d { Target = Hightlight }

    ReflectTraceColorTexture            = texture2d { Target = ReflectTrace }
    ReflectSolveColorTexture            = texture2d { Target = ReflectSolve }
    ReflectReprojColorTexture           = texture2d { Target = ReflectReproj }
    ReflectPrevReprojColorTexture       = texture2d { Target = ReflectPrevReproj }

    DiffractDepthTexture                = texture2d { Target = DiffractDepthTarget, View = { Format = common.Targets.JustDepthAsColor.Colors[1] } }
    DiffractCurDepthTexture             = texture2d { Target = DiffractCurDepthTarget, View = { Format = common.Targets.JustDepthAsColor.Colors[1] } }
    DiffractPrevDepthTexture            = texture2d { Target = DiffractPrevDepthTarget, View = { Format = common.Targets.JustDepthAsColor.Colors[1] } }
    DiffractPrevTransDepthTexture       = texture2d { Target = DiffractPrevTransDepthTarget, View = { Format = common.Targets.JustDepthAsColor.Colors[1] } }
    DiffractAlbedoTexture               = texture2d { Target = DiffractAlbedo }

    RenderHdr1_Only                     = multi_target_easy( common.Targets.Luminance,                 {RenderHdr1} )
    RenderHdr1_Depth                    = multi_target_easy( common.Targets.Luminance_Depth,           {RenderHdr1}, DepthTarget )
    RenderHdr1_Motion_Depth             = multi_target_easy( common.Targets.Luminance_Motion_Depth,    {RenderHdr1, Motion}, DepthTarget )
    RenderHdr1_Motion_ScratchDepth      = multi_target_easy( common.Targets.Luminance_Motion_Depth,    {RenderHdr1, Motion}, ScratchDepthTarget )
    RenderLdr1_Only                     = multi_target_easy( common.Targets.RasterLDR,                 {RenderLdr1} )
    RenderLdr1_Depth                    = multi_target_easy( common.Targets.RasterLDR_Depth,           {RenderLdr1}, DepthTarget )
    RenderLdr1_Motion_Depth             = multi_target_easy( common.Targets.RasterLDR_Motion_Depth,    {RenderLdr1, Motion}, DepthTarget )

    Motion_Depth                        = multi_target_easy( common.Targets.Motion_Depth,              {Motion}, DepthTarget )
    Depth_Only                          = multi_target_easy( common.Targets.JustDepth,                 {}, DepthTarget )
    ScratchDepth_Only                   = multi_target_easy( common.Targets.JustDepth,                 {}, ScratchDepthTarget )
    DepthMinMaxHalfRes_Only             = multi_target_easy( common.Targets.JustDepthMinMax,           {DepthMinMaxHalfRes} )
    DepthMinMaxFourthRes_Only           = multi_target_easy( common.Targets.JustDepthMinMax,           {DepthMinMaxFourthRes} )
    if PipelineType == 'High' then
        Gbuffer1_Only                   = multi_target_easy( common.Targets.Gbuffer1High,              {Gbuffer1} )
        Gbuffer1_Depth                  = multi_target_easy( common.Targets.Gbuffer1High_Depth,        {Gbuffer1}, DepthTarget )
        Gbuffer_Depth                   = multi_target_easy( common.Targets.GbufferHigh_Depth,         {Gbuffer1, Gbuffer2, Gbuffer3}, DepthTarget )
        Gbuffer_Motion_Depth            = multi_target_easy( common.Targets.GbufferHigh_Motion_Depth,  {Gbuffer1, Gbuffer2, Gbuffer3, Motion}, DepthTarget )
    else
        Gbuffer1_Only                   = multi_target_easy( common.Targets.Gbuffer1Low,               {Gbuffer1} )
        Gbuffer1_Depth                  = multi_target_easy( common.Targets.Gbuffer1Low_Depth,         {Gbuffer1}, DepthTarget )
        Gbuffer_Depth                   = multi_target_easy( common.Targets.GbufferLow_Depth,          {Gbuffer1, Gbuffer2}, DepthTarget )
        Gbuffer_Motion_Depth            = multi_target_easy( common.Targets.GbufferLow_Motion_Depth,   {Gbuffer1, Gbuffer2, Motion}, DepthTarget )
    end

    if PipelineType == 'Low' then
        RenderHdr3                      = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.Luminance.Colors[1] }
    else
        RenderHdr3                      = Gbuffer3  -- can reuse in this case, because same format
    end
    RenderHdr3Texture                   = texture2d { Target = RenderHdr3 }
    RenderHdr3TextureRw                 = texture2d_rw { Target = RenderHdr3 }
    RenderHdr3_Only                     = multi_target_easy( common.Targets.Luminance,                  {RenderHdr3} )
    RenderHdr3_Depth                    = multi_target_easy( common.Targets.Luminance_Depth,            {RenderHdr3}, DepthTarget )


    Highlight_Depth                     = multi_target_easy( common.Targets.RasterLDR_Depth,            {Hightlight}, DepthTarget )

    ReflectTrace_Only                   = multi_target_easy( common.Targets.ReflectTrace,               {ReflectTrace} )
    ReflectSolve_Only                   = multi_target_easy( common.Targets.ReflectSolve,               {ReflectSolve} )
    ReflectReproj_Only                  = multi_target_easy( common.Targets.ReflectReproj,              {ReflectReproj} )
    ReflectPrevReproj_Only              = multi_target_easy( common.Targets.ReflectPrevReproj,          {ReflectPrevReproj} )

    DiffractDepth_Only                  = multi_target_easy( common.Targets.JustDepth,                  {}, DiffractDepthTarget )
    DiffractCurDepth_Only               = multi_target_easy( common.Targets.DiffractCurDepth,           {DiffractCurDepthTarget} )
    DiffractPrevDepth_Only              = multi_target_easy( common.Targets.DiffractPrevDepth,          {DiffractPrevDepthTarget} )
    DiffractPrevTransDepth_Only         = multi_target_easy( common.Targets.DiffractPrevTransDepth,     {DiffractPrevTransDepthTarget} )

    if PipelineType == 'High' then
        DiffractAlbedoWithDepth         = multi_target_easy( common.Targets.DiffractAlbedoHigh,         {DiffractAlbedo}, DiffractDepthTarget)
    else
        DiffractAlbedoWithDepth         = multi_target_easy( common.Targets.DiffractAlbedoLow,          {DiffractAlbedo}, DiffractDepthTarget)
    end
    DiffractWithDepth                   = multi_target_easy( common.Targets.DiffractAlbedo,             {RenderHdr1}, DiffractDepthTarget )

    Post_HDR_SDR_RDepth                 = multi_target_easy( common.Targets.Post_HDR_SDR_Depth,         {PostColor, PostColorSDR}, DepthTarget )

    PostTrailMask                       = target2d { Width = renderWidth, Height = renderHeight, Format = common.Targets.PostTrailMask.Colors[1] }
    PostTrailMaskColorTexture           = texture2d { Target = PostTrailMask }
    PostTrailMaskColorTextureRw         = texture2d_rw { Target = PostTrailMask }
    PostTrailMask_Only                  = multi_target_easy( common.Targets.PostTrailMask, {PostTrailMask} )

    Gbuffer_Emissive_Depth              = multi_target_easy( common.Targets.Gbuffer_Emissive_Depth, {RenderHdr1, PostTrailMask, Motion}, DepthTarget )
    Gbuffer_Emissive_AA_Depth           = multi_target_easy( common.Targets.Gbuffer_Emissive_Depth, {RenderHdr3, PostTrailMask, Motion}, DepthTarget )

    LightingResult = { Texture = RenderHdr1Texture, TextureRw = RenderHdr1TextureRw, Target = RenderHdr1, TargetOnly = RenderHdr1_Only, WithDepth = RenderHdr1_Depth, WithMotionAndDepth = RenderHdr1_Motion_Depth, WithMotionAndScratchDepth = RenderHdr1_Motion_ScratchDepth }
end --renderDimensionsChanged
