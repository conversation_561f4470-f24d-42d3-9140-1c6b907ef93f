#include "Generated/Render_High_HDRLumaReduceProgram.hlsli"
#include "Generated/Render_Low_HDRLumaReduceProgram.hlsli"

#include "../Common/MathUtil.hlsli"
#include "../Common/ColorUtil.hlsli"


// Shared memory for local computations
groupshared float2 localData[256]; // [average sum, max value] per thread

// Second pass - final reduction of tile data
[numthreads(256, 1, 1)]
void ComputeMain(uint3 dispatchThreadId : SV_DispatchThreadID, uint groupIndex : SV_GroupIndex)
{
    float2 result = float2(0.0, 0.0); // [luminance sum, max luminance]
    
    // Each thread processes one tile's result
    if (dispatchThreadId.x < HDRLumaAnalysisParams(TileCount))
    {
        result = HDRLumaAnalysisParams(TileStatistics)[dispatchThreadId.x];
    }
    
    // Store in shared memory
    localData[groupIndex] = result;
    
    // Synchronize threads
    GroupMemoryBarrierWithGroupSync();
    
    // Parallel reduction for final output
    for (uint stride = 128; stride > 0; stride >>= 1)
    {
        if (groupIndex < stride)
        {
            localData[groupIndex].x += localData[groupIndex + stride].x;
            localData[groupIndex].y = max(localData[groupIndex].y, localData[groupIndex + stride].y);
        }
        
        GroupMemoryBarrierWithGroupSync();
    }
    
    // Write final results
    if (groupIndex == 0)
    {
        // Calculate average by dividing by total pixel count
        float totalPixels = HDRLumaAnalysisParams(TextureSize).x * HDRLumaAnalysisParams(TextureSize).y;
        float avgLuminance = localData[0].x / totalPixels;
        float peakLuminance = localData[0].y;
        
        // Apply log adaptation for average (common in HDR tone mapping)
        avgLuminance = max(avgLuminance, 0.001); // Avoid log(0)
        
        // Store results for HDR adaptation
        HDRLumaAnalysisParams(OutputStatistics)[0] = float4(avgLuminance * 80, peakLuminance * 80, 0.0, 0.0);
    }
}