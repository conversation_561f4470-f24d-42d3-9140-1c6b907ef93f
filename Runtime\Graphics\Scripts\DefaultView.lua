----------------------------------------------------------------------------------------------------------------------------
--
-- San<PERSON> Render Script doc https://docs.google.com/document/d/1tN68pmOU35mhpJnqSzK_TvOBhPhMXr_4VVXbdqoiNWw/edit?usp=sharing

-- New Wookey link https://docs.google.com/document/d/1dep9wxwR-bVFke1cpZ_GJczp19bU9mxS5oTtXpJY-So/edit
--
----------------------------------------------------------------------------------------------------------------------------


local renderTargets = import("RenderTargets.lua")
local pipelineType     = renderTargets.PipelineType
local packagePlatform  = renderTargets.PackagePlatform
local packageExtension = renderTargets.PackageExtension
local configuration    = renderTargets.Configuration

local scriptParams = get_script_params()
local config = scriptParams.scriptConfig

local common = package("Common", packagePlatform, packageExtension)
local engine = package("Engine_"..pipelineType, packagePlatform, packageExtension)
local materialDefs = package("MaterialDefinitions_"..pipelineType, packagePlatform, packageExtension)
local uiSystem = package("UISystem", packagePlatform, packageExtension)

local vrDisplay = get_vr_display()
local worldParams = scriptParams.worldParams
local lightSystemConfig = worldParams.lightingParams
local Modes = {
    'Normal',
    'EditorLighting',
    'VisualizeNormals',
    'Debug',
    'VisualizeTriangleDensity',
    'VisualizeOverdraw',
    'VisualizeWireframe',
    'VisualizeLightingComplexity',
    'VisualizeBackfaces',
    'VisualizeRadiance'
}
local mode = Modes[1 + (config.mode % #Modes)]
local isDiagnosticMode = (mode == 'VisualizeTriangleDensity') or (mode == 'VisualizeOverdraw') or (mode == 'VisualizeWireframe') or (mode == 'VisualizeLightingComplexity') or (mode == 'VisualizeBackfaces') or (mode == 'VisualizeRadiance')
local enableStereoSplit = vrDisplay ~= nil
local enableEditorFeatures = (config.enableEditorFeatures == true)
local enableShadowedAtmosphere = scriptParams.enableShadowedAtmosphere
local enableOcclusion = pipelineType == 'High' and scriptParams.enableAmbientOcclusion
local enableReflections = pipelineType == 'High' and scriptParams.enableReflections
local enableOcclusionSmoothing = false
local enableProbes = pipelineType == 'High'
local enableSubsurface = pipelineType == 'High'
local isPanorama = scriptParams.viewSegmentCount > 1

local uniformNoise = texture2d(loadTexture("uniform_noise.dds"))
local blueNoise = texture2d(loadTexture("blue_noise.dds"))
local dielectricLookup = texture2d(loadTexture("ior_1_4.dds"))

local transmittanceOutput = renderTargets.RenderLdr1_Depth
local transmittanceInput = renderTargets.RenderLdr1Texture
local resolvedTransparentOutput = renderTargets.RenderHdr3_Only
local colorGradientInput = renderTargets.RenderLdr1Texture
local highlightMaskOutput = renderTargets.Highlight_Depth
local primitivesOutput = scriptParams.enableTemporalAntialiasing and renderTargets.RenderHdr3_Depth or renderTargets.LightingResult.WithDepth
local primitivesEmissiveOutput = scriptParams.enableTemporalAntialiasing and renderTargets.Gbuffer_Emissive_AA_Depth or renderTargets.Gbuffer_Emissive_Depth

local temporalConvergenceFrames = 8
local halton23Points = {
    {1/2,     1/3   },
    {1/4,     2/3   },
    {3/4,     1/9   },
    {1/8,     4/9   },
    {5/8,     7/9   },
    {3/8,     2/9   },
    {7/8,     5/9   },
    {1/16,    8/9   },
    {9/16,    1/27  },
--    {5/16,    10/27 },
--    {13/16,   19/27 },
--    {3/16,    4/27  },
--    {11/16,   13/27 },
--    {7/16,    22/27 },
--    {15/16,   7/27  },
--    {1/32,    16/27 },
};

local displayViewports = { autoRefresh = true }
local cameraInfos = {}
for i=1,scriptParams.viewSegmentCount do
    displayViewports[i] = get_viewport(i)
    cameraInfos[i] = vrDisplay and late_binding_parameter_block(common.Core.CameraInfo) or parameter_block(common.Core.CameraInfo)
end

local visibleWidth = displayViewports[scriptParams.viewSegmentCount].X + displayViewports[scriptParams.viewSegmentCount].Width - displayViewports[1].X
local visibleHeight = displayViewports[scriptParams.viewSegmentCount].Y + displayViewports[scriptParams.viewSegmentCount].Height - displayViewports[1].Y

trace("Running pipeline " .. configuration .. " " .. pipelineType .. " " .. packagePlatform .. ". " .. visibleWidth .. " x " .. visibleHeight .. (vrDisplay and " [VR]" or "") .. (enableEditorFeatures and " [Edit Mode]" or ""))

local originalViewportDimensions = {}
for i=1,scriptParams.viewSegmentCount do
    originalViewportDimensions[i] = { X = displayViewports[i].X, Y = displayViewports[i].Y, Width = displayViewports[i].Width, Height = displayViewports[i].Height }
end

local function scaleViewports(sources, factor, scaledViewports)
    local scaledViewports = scaledViewports or {}
    for i=1,#sources do
        if scaledViewports[i] == nil then scaledViewports[i] = viewport{} end
        local scaled = scaledViewports[i]
        local src = sources[i]
        scaled.X = math.floor(src.X * factor)
        scaled.Y = math.floor(src.Y * factor)
        scaled.Width = math.ceil((src.X + src.Width) * factor) - scaled.X
        scaled.Height = math.ceil((src.Y + src.Height) * factor) - scaled.Y
    end
    scaledViewports.autoRefresh = true
    return scaledViewports
end

local renderViewports = scaleViewports(displayViewports, renderTargets.RenderScale)

local scaledRenderViewports = {
    scaleViewports(renderViewports, 1.0/2.0),
    scaleViewports(renderViewports, 1.0/4.0),
}

local scaledDisplayViewports = {
    scaleViewports(displayViewports, 1.0/2.0),
    scaleViewports(displayViewports, 1.0/4.0),
    scaleViewports(displayViewports, 1.0/8.0),
    scaleViewports(displayViewports, 1.0/16.0),
    scaleViewports(displayViewports, 1.0/32.0),
    scaleViewports(displayViewports, 1.0/64.0),
    scaleViewports(displayViewports, 1.0/128.0),
    scaleViewports(displayViewports, 1.0/256.0),
}

local function makeDispatchesForViewports(effect, samplingReduction, tileSize, viewports, dispatches)
    local dispatches = dispatches or {}
    for i=1,#viewports do
        local viewp = viewports[i]
        dispatches[i] = dispatch {
            Effect = effect,
            ThreadGroups = uint3(math.ceil(viewp.Width/(samplingReduction*tileSize)), math.ceil(viewp.Height/(samplingReduction*tileSize)), 1),
            ThreadsPerGroup = uint3(tileSize, tileSize, 1),
        }
    end
    dispatches.samplingReduction = samplingReduction
    dispatches.viewportTileSize = tileSize
    return dispatches
end

local worldInfo = worldParams.worldInfoParams
local mediaParams = worldParams.mediaParams
local diagnosticsParams = worldParams.diagnosticsParams

local clearLightingResultStage = graphics_stage { Type = engine.Render.ClearLuminance_Depth, Target = renderTargets.LightingResult.WithDepth }
local clearHighlightMaskStage = graphics_stage { Type = engine.Render.ClearGBuffer, Target = highlightMaskOutput } -- note: clears color, not depth
local clearTransmittanceStage = graphics_stage { Type = engine.Render.ClearLDR, Target = transmittanceOutput } -- note: clears color, not depth
local clearDiagnosticsStage = graphics_stage { Type = engine.Render.ClearLDR, Target = renderTargets.RenderLdr1_Depth } -- note: clears color, not depth
local clearDiffractionStage = graphics_stage { Type = engine.Render.ClearDiffraction_Depth, Target = renderTargets.DiffractWithDepth }
local clearHighResZStage = graphics_stage { Type = engine.Render.ClearJustDepth, Target = renderTargets.UIDepth_Only }

local prerenderZStage = graphics_stage {
    Type = engine.Render.PrepopulateZbuffer,
    Target = renderTargets.Depth_Only,
    Parameters = { cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderTransDiffractZStage = graphics_stage {
    Type = engine.Render.PrepopulateTransDiffractZbuffer,
    Target = renderTargets.DiffractDepth_Only,
    Parameters = { cameraInfos, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local deferredBuffers = parameter_block(materialDefs.Effects.DeferredBuffers)
deferredBuffers.Gbuffer1 = renderTargets.Gbuffer1Texture
deferredBuffers.Gbuffer2 = renderTargets.Gbuffer2Texture
if pipelineType == 'High' then
    deferredBuffers.Gbuffer3 = renderTargets.Gbuffer3Texture
end
deferredBuffers.Depth = renderTargets.DepthTexture
deferredBuffers.Visibility = renderTargets.VisibilityTexture
deferredBuffers.DielectricLookup = dielectricLookup
deferredBuffers.Noise = uniformNoise
deferredBuffers.ProbeCoeffsL1 = lightSystemConfig.probeCoeffsL1
deferredBuffers.SkyCoeffsL2 = renderTargets.SkyL2Coeffs

local deferredBuffersTarget = parameter_block(engine.Render.DeferredBuffersTarget)
deferredBuffersTarget.Target = renderTargets.LightingResult.TextureRw

local deferredBuffersVisualizeTarget = parameter_block(engine.Render.DeferredBuffersTarget)
deferredBuffersVisualizeTarget.Target = renderTargets.LightingResult.TextureRw
deferredBuffersVisualizeTarget.VisualizeRadiance = 1

local subsurfaceParams = parameter_block(common.Core.SubsurfaceParams)
subsurfaceParams.ExitAlbedo = renderTargets.Gbuffer1Texture

local renderLitOpaqueStage = graphics_stage {
    Type = engine.Render.PopulateGbuffer,
    Target = renderTargets.Gbuffer_Motion_Depth,
    Parameters = { cameraInfos, mediaParams, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderVRVisDepthStage = graphics_stage {
    Type = engine.Render.PopulateVRVisDepth,
    Target = renderTargets.UIDepth_Only,
    Parameters = { cameraInfos, mediaParams, worldInfo },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderMediaGBufferState = graphics_stage {
    Type = engine.Render.PopulateMediaGbuffer,
    Target = renderTargets.Gbuffer_Motion_Depth,
    Parameters = { cameraInfos, mediaParams, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderLitOpaqueFadingStage = graphics_stage {
    Type = engine.Render.PopulateGbufferFading,
    Target = renderTargets.Gbuffer_Motion_Depth,
    Parameters = { cameraInfos, mediaParams, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local diffractionAlbedoParams = parameter_block(materialDefs.Effects.DiffractionAlbedoParams)
diffractionAlbedoParams.Albedo = renderTargets.DiffractAlbedoTexture
diffractionAlbedoParams.Motion = renderTargets.MotionTexture
diffractionAlbedoParams.OpaqueDepth = renderTargets.DepthTexture
diffractionAlbedoParams.DiffractDepth = renderTargets.DiffractPrevDepthTexture

local diffractionTransparentParams = parameter_block(materialDefs.Effects.DiffractionAlbedoParams)
diffractionTransparentParams.Albedo = renderTargets.DiffractAlbedoTexture
diffractionTransparentParams.Motion = renderTargets.MotionTexture
diffractionTransparentParams.OpaqueDepth = renderTargets.DiffractCurDepthTexture
diffractionTransparentParams.DiffractDepth = renderTargets.DiffractPrevTransDepthTexture

local renderUnlitOpaqueDiffractionStage = graphics_stage {
    Type = engine.Render.PopulateGbufferDiffraction,
    Target = renderTargets.DiffractWithDepth,
    Parameters = { cameraInfos, worldInfo, diffractionAlbedoParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local transparentStageParams = parameter_block(materialDefs.Effects.TransparentStageParams)
transparentStageParams.DielectricLookup = dielectricLookup

local renderLitTransparentStage = graphics_stage {
    Type = engine.Render.PopulateGbufferTransparent,
    Target = renderTargets.Gbuffer_Depth,
    Parameters = { cameraInfos, worldInfo, transparentStageParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderLitDiffractTransparentStage = graphics_stage {
    Type = engine.Render.PopulateGbufferTransparentDiffraction,
    Target = renderTargets.Gbuffer_Depth,
    Parameters = { cameraInfos, worldInfo, transparentStageParams, diffractionTransparentParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderTransmittanceStage = graphics_stage {
    Type = engine.Render.PopulateTransmittance,
    Target = transmittanceOutput,
    Parameters = { cameraInfos, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local multiplyTransmissiveStage = graphics_stage {
    Type = engine.Render.MultiplyTransmissive,
    Target = renderTargets.LightingResult.WithDepth,
    Parameters = { cameraInfos, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local addEmissiveStage = graphics_stage {
    Type = engine.Render.AddEmissive,
    Target = renderTargets.LightingResult.WithDepth,
    Parameters = { cameraInfos, mediaParams, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local addEnvironmentStage = graphics_stage {
    Type = engine.Render.AddEnvironment,
    Target = renderTargets.LightingResult.WithDepth,
    Parameters = { cameraInfos, worldParams.skyParams, worldInfo, deferredBuffers },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local addTransmissiveEmissiveStage = graphics_stage {
    Type = engine.Render.AddTransmissiveEmissive,
    Target = renderTargets.LightingResult.WithDepth,
    Parameters = { cameraInfos, mediaParams, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local gatherSubsurfaceStage = enableSubsurface and graphics_stage {
    Type = engine.Render.GatherSubsurface,
    Target = renderTargets.LightingResult.WithDepth,
    Parameters = { cameraInfos, subsurfaceParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
} or {}

local maskUnlitStage = graphics_stage {
    Type = engine.Render.MaskUnlit,
    Target = primitivesOutput,
    Parameters = { cameraInfos, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local maskEmissiveUnlitStage = graphics_stage {
    Type = engine.Render.MaskEmissiveUnlit,
    Target = primitivesEmissiveOutput,
    Parameters = { cameraInfos, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderUnlitStage = graphics_stage {
    Type = engine.Render.RenderUnlit,
    Target = primitivesOutput,
    Parameters = { cameraInfos, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderEmissiveUnlitStage = graphics_stage {
    Type = engine.Render.RenderEmissiveUnlit,
    Target = primitivesEmissiveOutput,
    Parameters = { cameraInfos, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local addMediaStage = graphics_stage {
    Type = engine.Render.AddMedia,
    Target = renderTargets.LightingResult.WithDepth,
    Parameters = { cameraInfos, mediaParams, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local deferredLightStage = compute_stage {
    Type = enableOcclusion and engine.Render.DeferredLight or engine.Render.DeferredUnoccludedLight,
    Parameters = { deferredBuffers, deferredBuffersTarget, lightSystemConfig.lightData, lightSystemConfig.sunProjectionInfo, cameraInfos, worldParams.postEffectParams },
    IterationCount = scriptParams.viewSegmentCount,
    FixedDispatches = makeDispatchesForViewports(enableOcclusion and engine.Render.DeferredLightEffect or engine.Render.DeferredUnoccludedLightEffect, 1, 16, renderViewports),
}

local lightingComplexityData = parameter_block(engine.Render.LightingComplexityData)
lightingComplexityData.Depth = renderTargets.DepthTexture
lightingComplexityData.Target = renderTargets.RenderLdr1TextureRw

local lightingComplexityStage = compute_stage {
    Type = engine.Render.LightingComplexity,
    Parameters = { diagnosticsParams, lightingComplexityData, lightSystemConfig.lightData, cameraInfos },
    IterationCount = scriptParams.viewSegmentCount,
    FixedDispatches = makeDispatchesForViewports(engine.Render.LightingComplexityEffect, 1, 16, renderViewports),
}

local editorLightingStage = compute_stage {
    Type = engine.Render.EditorLighting,
    Parameters = { deferredBuffers, deferredBuffersTarget, cameraInfos },
    IterationCount = scriptParams.viewSegmentCount,
    FixedDispatches = makeDispatchesForViewports(engine.Render.EditorLightingEffect, 1, 16, renderViewports),
}

local visualizeNormalsStage = compute_stage {
    Type = engine.Render.VisualizeNormals,
    Parameters = { deferredBuffers, deferredBuffersTarget, cameraInfos, lightSystemConfig.lightData },
    IterationCount = scriptParams.viewSegmentCount,
    FixedDispatches = makeDispatchesForViewports(engine.Render.VisualizeNormalsEffect, 1, 16, renderViewports),
}

local visualizeRadianceStage = compute_stage {
    Type = engine.Render.DeferredUnoccludedLight,
    Parameters = { deferredBuffers, deferredBuffersVisualizeTarget, lightSystemConfig.lightData, lightSystemConfig.sunProjectionInfo, cameraInfos, worldParams.postEffectParams },
    IterationCount = scriptParams.viewSegmentCount,
    FixedDispatches = makeDispatchesForViewports(engine.Render.DeferredUnoccludedLightEffect, 1, 16, renderViewports),
}

local downsampleDepthMinMaxParams = parameter_block(engine.Render.FilterImageParams)
downsampleDepthMinMaxParams.Source = renderTargets.DepthTexture
downsampleDepthMinMaxParams.RcpTargetSize = float4(1/renderTargets.DepthMinMaxFourthRes.Width, 1/renderTargets.DepthMinMaxFourthRes.Height, 0, 0)

local downsampleDepthMinMaxStage = graphics_stage {
    Type = engine.Render.DownsampleDepthMinMax,
    Target = renderTargets.DepthMinMaxFourthRes_Only,
    Parameters = { downsampleDepthMinMaxParams },
    --Viewports = scaledRenderViewports[2],
}

local atmosphereBuffers = parameter_block(engine.Render.AtmosphereBuffers)
atmosphereBuffers.DepthMinMax = renderTargets.DepthMinMaxFourthResTexture
atmosphereBuffers.Noise = uniformNoise
atmosphereBuffers.NoiseResolution = uniformNoise.Resource.Width
atmosphereBuffers.SkyCoeffsL2 = renderTargets.SkyL2Coeffs

local lightAtmosphereStage = graphics_stage {
    Type = engine.Render.LightAtmosphere,
    Target = renderTargets.HdrFourthRes_HdrFourthRes,
    Parameters = { atmosphereBuffers, lightSystemConfig.sunProjectionInfo, cameraInfos, lightSystemConfig.lightData },
    Viewports = scaledRenderViewports[2],
    IterationCount = scriptParams.viewSegmentCount,
}

local resolveAtmosphereBuffers = parameter_block(engine.Render.ResolveAtmosphereBuffers)
resolveAtmosphereBuffers.Depth = renderTargets.DepthTexture
resolveAtmosphereBuffers.DepthMinMax = renderTargets.DepthMinMaxFourthResTexture
resolveAtmosphereBuffers.Inscatter1 = renderTargets.HdrFourthResTextures[1]
resolveAtmosphereBuffers.Inscatter2 = renderTargets.HdrFourthResTextures[2]
resolveAtmosphereBuffers.Noise = uniformNoise
resolveAtmosphereBuffers.NoiseResolution = uniformNoise.Resource.Width
resolveAtmosphereBuffers.SkyCoeffsL2 = renderTargets.SkyL2Coeffs

local resolveAtmosphereStage = graphics_stage {
    Type = engine.Render.ResolveAtmosphere,
    Target = renderTargets.LightingResult.TargetOnly,
    Parameters = { resolveAtmosphereBuffers, lightSystemConfig.sunProjectionInfo, cameraInfos, lightSystemConfig.lightData },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local unshadowedAtmosphereStage = graphics_stage {
    Type = engine.Render.UnshadowedAtmosphere,
    Target = renderTargets.LightingResult.TargetOnly,
    Parameters = { resolveAtmosphereBuffers, lightSystemConfig.sunProjectionInfo, cameraInfos, lightSystemConfig.lightData },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local occlusionBuffers = parameter_block(engine.Render.OcclusionBuffers)
occlusionBuffers.DepthMinMax = renderTargets.DepthMinMaxFourthResTexture
occlusionBuffers.Depth = renderTargets.DepthTexture
occlusionBuffers.Noise = uniformNoise
occlusionBuffers.NoiseResolution = uniformNoise.Resource.Width
occlusionBuffers.VisibilityRw1 = enableOcclusionSmoothing and renderTargets.Visibility1ATextureRw or renderTargets.Visibility1BTextureRw
occlusionBuffers.VisibilityRw2 = enableOcclusionSmoothing and renderTargets.Visibility2ATextureRw or renderTargets.Visibility2BTextureRw

local filterOcclusionBuffers = parameter_block(engine.Render.FilterOcclusionBuffers)
filterOcclusionBuffers.DepthMinMax = renderTargets.DepthMinMaxFourthResTexture
filterOcclusionBuffers.Visibility1 = renderTargets.Visibility1ATexture
filterOcclusionBuffers.Visibility2 = renderTargets.Visibility2ATexture
filterOcclusionBuffers.VisibilityRw1 = renderTargets.Visibility1BTextureRw
filterOcclusionBuffers.VisibilityRw2 = renderTargets.Visibility2BTextureRw

local resolveOcclusionBuffers = parameter_block(engine.Render.ResolveOcclusionBuffers)
resolveOcclusionBuffers.DepthMinMax = renderTargets.DepthMinMaxFourthResTexture
resolveOcclusionBuffers.Depth = renderTargets.DepthTexture
resolveOcclusionBuffers.Noise = uniformNoise
resolveOcclusionBuffers.NoiseResolution = uniformNoise.Resource.Width
resolveOcclusionBuffers.Visibility1 = renderTargets.Visibility1BTexture
resolveOcclusionBuffers.Visibility2 = renderTargets.Visibility2BTexture
resolveOcclusionBuffers.VisibilityRw = renderTargets.VisibilityTextureRw

local occlusionStage = compute_stage {
    Type = engine.Render.Occlusion,
    Parameters = { occlusionBuffers, cameraInfos },
    IterationCount = scriptParams.viewSegmentCount,
    FixedDispatches = makeDispatchesForViewports(engine.Render.OcclusionEffect, 4, 8, renderViewports),        -- (not using "scaledViewports" here because 4x reduction is arg to function)
}

local filterOcclusionStage = compute_stage {
    Type = engine.Render.FilterOcclusion,
    Parameters = { filterOcclusionBuffers, cameraInfos },
    IterationCount = scriptParams.viewSegmentCount,
    FixedDispatches = makeDispatchesForViewports(engine.Render.FilterOcclusionEffect, 4, 8, renderViewports),
}

local resolveOcclusionStage = compute_stage {
    Type = engine.Render.ResolveOcclusion,
    Parameters = { resolveOcclusionBuffers, cameraInfos },
    IterationCount = scriptParams.viewSegmentCount,
    FixedDispatches = makeDispatchesForViewports(engine.Render.ResolveOcclusionEffect, 1, 8, renderViewports),
}

local reflectTraceParams = parameter_block(engine.Render.ReflectTraceParams)
reflectTraceParams.Source = renderTargets.RenderHdr3Texture
reflectTraceParams.Noise = blueNoise
reflectTraceParams.ExposureInfo = renderTargets.ExposureInfo
reflectTraceParams.RaySamples = enableStereoSplit and 12 or 24

local reflectTraceStage = graphics_stage {
    Type = engine.Render.ReflectTrace,
    Target = renderTargets.ReflectTrace_Only,
    Parameters = { reflectTraceParams, deferredBuffers, cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local reflectSolveParams = parameter_block(engine.Render.ReflectSolveParams)
reflectSolveParams.Source = renderTargets.RenderHdr3Texture
reflectSolveParams.Trace = renderTargets.ReflectTraceColorTexture
reflectSolveParams.Motion = renderTargets.MotionTexture
reflectSolveParams.Noise = blueNoise
reflectSolveParams.HDRDownres1 = renderTargets.HdrDownres[1].Texture
reflectSolveParams.HDRDownres2 = renderTargets.HdrDownres[2].Texture
reflectSolveParams.HDRDownres3 = renderTargets.HdrDownres[3].Texture
reflectSolveParams.HDRDownres4 = renderTargets.HdrDownres[4].Texture
reflectSolveParams.HDRDownres5 = renderTargets.HdrDownres[5].Texture
reflectSolveParams.ExposureInfo = renderTargets.ExposureInfo

local reflectSolveStage = graphics_stage {
    Type = enableStereoSplit and engine.Render.ReflectSolveVR or engine.Render.ReflectSolve,
    Target = renderTargets.ReflectSolve_Only,
    Parameters = { reflectSolveParams, deferredBuffers, cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local reflectReprojParams = parameter_block(engine.Render.ReflectReprojParams)
reflectReprojParams.PrevReproj = renderTargets.ReflectPrevReprojColorTexture
reflectReprojParams.ReflectTrace = renderTargets.ReflectTraceColorTexture
reflectReprojParams.ReflectSolve = renderTargets.ReflectSolveColorTexture
reflectReprojParams.Motion = renderTargets.MotionTexture

local reflectReprojStage = graphics_stage {
    Type = engine.Render.ReflectReproj,
    Target = renderTargets.ReflectReproj_Only,
    Parameters = { reflectReprojParams, deferredBuffers, cameraInfos, worldInfo },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local reflectBlitPrevReprojParams = parameter_block(engine.Render.ReflectBlitPrevReprojParams)
reflectBlitPrevReprojParams.ReflectReproj = renderTargets.ReflectReprojColorTexture

local reflectBlitPrevReprojStage = graphics_stage {
    Type = engine.Render.ReflectBlitPrevReproj,
    Target = renderTargets.ReflectPrevReproj_Only,
    Parameters = { reflectBlitPrevReprojParams, cameraInfos[1] },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local reflectBlendParams = parameter_block(engine.Render.ReflectBlendParams)
reflectBlendParams.ReflectTrace = renderTargets.ReflectTraceColorTexture
reflectBlendParams.ReflectMip0 = renderTargets.ReflectReprojColorTexture
reflectBlendParams.ReflectMip1 = renderTargets.ReflectDownres[1].Texture
reflectBlendParams.ReflectMip2 = renderTargets.ReflectDownres[2].Texture
reflectBlendParams.ReflectMip3 = renderTargets.ReflectDownres[3].Texture
reflectBlendParams.ReflectMip4 = renderTargets.ReflectDownres[4].Texture
reflectBlendParams.ReflectMip5 = renderTargets.ReflectDownres[5].Texture
reflectBlendParams.ExposureInfo = renderTargets.ExposureInfo

local reflectBlendStage = graphics_stage {
    Type = engine.Render.ReflectBlend,
    Target = renderTargets.RenderHdr3_Only,
    Parameters = { deferredBuffers, reflectBlendParams, cameraInfos[1] },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local reflectDownsampleBlurParams
local reflectDownsampleBlurStages = {}
for i=5,1,-1 do -- iterate backwards so 'reflectDownsampleBlurParams' will have the paramblock for the first downsample stage, which is needed later
    reflectDownsampleBlurParams = parameter_block(engine.Render.FilterImageParams)
    reflectDownsampleBlurParams.Source = i ~= 1 and renderTargets.ReflectDownres[i-1].Texture or renderTargets.ReflectReprojColorTexture
    reflectDownsampleBlurParams.RcpTargetSize = float4(1/renderTargets.ReflectDownresTargets[i].Width, 1/renderTargets.ReflectDownresTargets[i].Height, 0, 0)

    reflectDownsampleBlurStages[2*i - 1] = graphics_stage { Type = engine.Render.DownsampleBlurReflection, Target = renderTargets.ReflectDownres[i].TargetOnly, Parameters = { reflectDownsampleBlurParams } }
    reflectDownsampleBlurStages[2*i - 0] = target_state_setting(renderTargets.ReflectDownres[i].Source, 'GenericRead')
end

local fsrTonemapParams = parameter_block(engine.Render.FSRTonemapParams)
fsrTonemapParams.Source = renderTargets.PostColorTexture

local fsrTonemapStage = graphics_stage {
    Type = engine.Render.FSRTonemap,
    Target = renderTargets.FSRTonemapped_Only,
    Parameters = { deferredBuffers, fsrTonemapParams, cameraInfos },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local fsrSharpenData = parameter_block(engine.Render.FSRUpscaleData)
fsrSharpenData.Source = renderTargets.FSRTonemappedTexture
fsrSharpenData.Target = renderTargets.PostColorTextureRw
fsrSharpenData.sharpness = 0.25

local fsrSharpenStage = compute_stage {
    Type = engine.Render.FSRSharpen,
    Parameters = { deferredBuffers, fsrSharpenData, cameraInfos },
    IterationCount = scriptParams.viewSegmentCount,
    FixedDispatches = makeDispatchesForViewports(engine.Render.FSRSharpenEffect, 8, 2, displayViewports),
}

local renderHighlightMaskStage = graphics_stage {
    Type = engine.Render.RenderHighlightMask,
    Target = highlightMaskOutput,
    Parameters = { cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local highlightParams = parameter_block(common.Core.HighlightParams)
highlightParams.Mask = renderTargets.HighlightTexture

local renderHighlightStage = graphics_stage {
    Type = engine.Render.RenderHighlight,
    Target = renderTargets.LightingResult.WithDepth,
    Parameters = { cameraInfos, highlightParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderAllAsDebugStage = graphics_stage {
    Type = engine.Render.RenderAllAsDebug,
    Target = renderTargets.LightingResult.WithMotionAndDepth,
    Parameters = { cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderDebugStage = graphics_stage {
    Type = engine.Render.RenderDebug,
    Target = renderTargets.LightingResult.WithMotionAndDepth,
    Parameters = { cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderTriangleDensityStage = graphics_stage {
    Type = engine.Render.RenderTriangleDensity,
    Target = renderTargets.RenderLdr1_Motion_Depth,
    Parameters = { cameraInfos, diagnosticsParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderOverdrawStage = graphics_stage {
    Type = engine.Render.RenderOverdraw,
    Target = renderTargets.RenderLdr1_Motion_Depth,
    Parameters = { cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderWireframeStage = graphics_stage {
    Type = engine.Render.RenderWireframe,
    Target = renderTargets.RenderLdr1_Motion_Depth,
    Parameters = { cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderBackfacesStage = graphics_stage {
    Type = engine.Render.RenderBackfaces,
    Target = renderTargets.RenderLdr1_Motion_Depth,
    Parameters = { cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local populateDiagnosticsZStage = graphics_stage {
    Type = engine.Render.PopulateDiagnosticsZbuffer,
    Target = renderTargets.Depth_Only,
    Parameters = { cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderColorGradientParams = parameter_block(engine.Render.ColorGradientParams)
renderColorGradientParams.Source = colorGradientInput
renderColorGradientParams.ExposureInfo = renderTargets.ExposureInfo

local renderColorGradientStage = graphics_stage {
    Type = engine.Render.ColorGradient,
    Target = renderTargets.LightingResult.TargetOnly,
    Parameters = { renderColorGradientParams, diagnosticsParams, cameraInfos },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local widgetFrameParams = parameter_block(common.Core.WidgetFrameParams)
widgetFrameParams.Exposure = renderTargets.ExposureInfo

local renderWidgetBehindStage = graphics_stage {
    Type = engine.Render.RenderWidgetBehind,
    Target = renderTargets.LightingResult.WithMotionAndScratchDepth,
    Parameters = { cameraInfos, widgetFrameParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderWidgetStage = graphics_stage {
    Type = engine.Render.RenderWidget,
    Target = renderTargets.LightingResult.WithMotionAndDepth,
    Parameters = { cameraInfos, widgetFrameParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderPrimitivesStages = {
    graphics_stage {
        Type = common.DrawPrimitive.DrawPrimitiveStageType,
        Target = primitivesOutput,
        Parameters = { cameraInfos },
        Viewports = renderViewports,
        IterationCount = scriptParams.viewSegmentCount,
        InstanceCountMultiplier = enableStereoSplit and 2 or 1
    },
    graphics_stage {
        Type = common.DrawPrimitive.DrawMeshWireframePrimitiveStageType,
        Target = primitivesOutput,
        Parameters = { cameraInfos },
        Viewports = renderViewports,
        IterationCount = scriptParams.viewSegmentCount,
        InstanceCountMultiplier = enableStereoSplit and 2 or 1
    },
    graphics_stage {
        Type = common.DrawPrimitive.DrawMeshWithBackfacesPrimitiveStageType,
        Target = primitivesOutput,
        Parameters = { cameraInfos },
        Viewports = renderViewports,
        IterationCount = scriptParams.viewSegmentCount,
        InstanceCountMultiplier = enableStereoSplit and 2 or 1
    },
    graphics_stage {
        Type = common.DrawPrimitive.DrawMeshWireframeBackfacesStageType,
        Target = primitivesOutput,
        Parameters = { cameraInfos },
        Viewports = renderViewports,
        IterationCount = scriptParams.viewSegmentCount,
        InstanceCountMultiplier = enableStereoSplit and 2 or 1
    },
}
local renderUIBackgroundStage = graphics_stage {
    Type = uiSystem.UI.UIBackgroundStageType,
    Target = renderTargets.Post_HDR_SDR,
    Parameters = { cameraInfos },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = 1	-- (it's a solid color so stereo is irrelevant)
}

local renderUIViewsStage = graphics_stage {
    Type = uiSystem.UI.UIViewStageType,
    Target = renderTargets.Post_HDR_SDR,
    Parameters = { cameraInfos },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local uiFrameInfo = parameter_block(uiSystem.UI.UIFrameInfo)
uiFrameInfo.Exposure = renderTargets.ExposureInfo
uiFrameInfo.HDRTonemapping = scriptParams.hdrTonemapping

local renderUIInWorldStage = graphics_stage {
    Type = uiSystem.UI.UIInWorldStageType,
    Target = scriptParams.samplesPerPixel ~= 1 and renderTargets.Post_HDR_SDR_DDepth or renderTargets.Post_HDR_SDR_RDepth,
    Parameters = { cameraInfos, uiFrameInfo },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local renderUIIndirectStage = graphics_stage {
    Type = uiSystem.UI.UIIndirectStageType,
    Target = renderTargets.Post_HDR_SDR,
    Parameters = { cameraInfos, uiFrameInfo },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local projectSkyHarmonicsParams = parameter_block(engine.Render.ProjectSkyHarmonicsParams)
projectSkyHarmonicsParams.RgbCoeffsRw = renderTargets.SkyL2CoeffsRw

local skyHarmonicsParams = parameter_block(engine.Render.SkyHarmonicsParams)
skyHarmonicsParams.RgbCoeffs = renderTargets.SkyL2Coeffs

local projectSkyHarmonicsStage = compute_stage {
    Type = engine.Render.ProjectSkyHarmonics,
    Parameters = { projectSkyHarmonicsParams, worldParams.skyParams },
    FixedDispatches = {
        dispatch {
            Effect = engine.Render.ProjectSkyHarmonicsEffect,
            ThreadGroups = uint3(1, 1, 1),
            ThreadsPerGroup = uint3(64, 1, 1)
        }
    },
}

local renderSkyStage = graphics_stage {
    Type = engine.Render.RenderSky,
    Target = renderTargets.LightingResult.WithMotionAndDepth,
    Parameters = { cameraInfos, worldParams.skyParams, skyHarmonicsParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local toonOutlineBuffers = parameter_block(engine.Render.ToonOutlineBuffers)
toonOutlineBuffers.Opacity = renderTargets.OpacityGbufferTexture
toonOutlineBuffers.Depth = renderTargets.DepthTexture

local renderToonOutlines = graphics_stage {
    Type = engine.Render.ToonOutlineStage,
    Target = renderTargets.LightingResult.TargetOnly,
    Parameters = { cameraInfos, toonOutlineBuffers, worldParams.postEffectParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local meteringParams = parameter_block(engine.Render.MeteringParams)
meteringParams.Source = renderTargets.HdrDownres[3].Texture
meteringParams.ExposureInfoRw = renderTargets.ExposureInfoRw

local meteringStage = compute_stage {
    Type = engine.Render.Metering,
    Parameters = { meteringParams, worldParams.exposureParams },
    FixedDispatches = {
        dispatch {
            Effect = engine.Render.MeteringEffect,
            ThreadGroups = uint3(1, 1, 1),
            ThreadsPerGroup = uint3(64, 1, 1)
        }
    },
}

local filterVideoEmissionParams = parameter_block(engine.Render.FilterVideoEmissionParams)
filterVideoEmissionParams.VideoEmissionTextureRw = renderTargets.VideoEmissionTextureRw;

local filterVideoEmissionStage = compute_stage {
    Type = engine.Render.FilterVideoEmissionStage,
    Parameters = { filterVideoEmissionParams, mediaParams },
    FixedDispatches = {
        dispatch {
            Effect = engine.Render.FilterVideoEmissionEffect,
            ThreadGroups = uint3(1, 1, 1),
            ThreadsPerGroup = uint3(8, 8, 1)
        }
    },
}

local shadeProbesStage = {}
local infillProbesStages = {}
local blendFringeProbesStage = {}

if enableProbes and lightSystemConfig.probeShadeCount > 0 then
    local shadeProbesParams = parameter_block(engine.Render.ShadeProbesParams)
    shadeProbesParams.SkyTransferL1 = lightSystemConfig.probeSkyTransferL1
    shadeProbesParams.ProbeEmissionL1a = lightSystemConfig.probeEmissionL1a
    shadeProbesParams.ProbeEmissionL1b = lightSystemConfig.probeEmissionL1b
    shadeProbesParams.SkyTransferProbeIds = lightSystemConfig.skyTransferProbeIds
    shadeProbesParams.VideoTexture = renderTargets.VideoEmissionTexture
    shadeProbesParams.ProbeVideoEmission = lightSystemConfig.probeVideoEmission
    shadeProbesParams.ProbeCoeffsL1Rw = lightSystemConfig.probeCoeffsL1Rw

    local infillProbesParams = {
        parameter_block(engine.Render.InfillProbesParams),
        parameter_block(engine.Render.InfillProbesParams),
        parameter_block(engine.Render.InfillProbesParams),
    }
    infillProbesParams[1].PhaseStart = 0
    infillProbesParams[1].PhaseEnd = lightSystemConfig.probeInfillPhaseOffset1
    infillProbesParams[1].InfillCount = lightSystemConfig.probeInfillCount
    infillProbesParams[1].InfillProbeIds = lightSystemConfig.infillProbeIds
    infillProbesParams[1].InfillNeighborIds = lightSystemConfig.infillNeighborIds
    infillProbesParams[1].ProbeCoeffsL1Rw = lightSystemConfig.probeCoeffsL1Rw

    infillProbesParams[2].PhaseStart = lightSystemConfig.probeInfillPhaseOffset1
    infillProbesParams[2].PhaseEnd = lightSystemConfig.probeInfillPhaseOffset2
    infillProbesParams[2].InfillCount = lightSystemConfig.probeInfillCount
    infillProbesParams[2].InfillProbeIds = lightSystemConfig.infillProbeIds
    infillProbesParams[2].InfillNeighborIds = lightSystemConfig.infillNeighborIds
    infillProbesParams[2].ProbeCoeffsL1Rw = lightSystemConfig.probeCoeffsL1Rw

    infillProbesParams[3].PhaseStart = lightSystemConfig.probeInfillPhaseOffset2
    infillProbesParams[3].PhaseEnd = lightSystemConfig.probeInfillPhaseOffset3
    infillProbesParams[3].InfillCount = lightSystemConfig.probeInfillCount
    infillProbesParams[3].InfillProbeIds = lightSystemConfig.infillProbeIds
    infillProbesParams[3].InfillNeighborIds = lightSystemConfig.infillNeighborIds
    infillProbesParams[3].ProbeCoeffsL1Rw = lightSystemConfig.probeCoeffsL1Rw

    local blendFringeProbesParams = parameter_block(engine.Render.BlendFringeProbesParams)
    blendFringeProbesParams.ProbeCount = lightSystemConfig.probeFringeCount
    blendFringeProbesParams.ProbeLocs = lightSystemConfig.probeFringeLocs
    blendFringeProbesParams.SkyCoeffsL2 = renderTargets.SkyL2Coeffs;
    blendFringeProbesParams.ProbeCoeffsL1Rw = lightSystemConfig.probeCoeffsL1Rw

    shadeProbesStage = compute_stage {
        Type = engine.Render.ShadeProbesStage,
        Parameters = {shadeProbesParams, skyHarmonicsParams, lightSystemConfig.lightData},

        FixedDispatches = {
            dispatch {
                Effect = engine.Render.ShadeProbesEffect,
                ThreadGroups = uint3(1, (lightSystemConfig.probeShadeCount + 63)/64, 1),
                ThreadsPerGroup = uint3(64, 1, 1)
            }
        },
    }

    infillProbesStages[1] = compute_stage {
        Type = engine.Render.InfillProbesStage,
        Parameters = {infillProbesParams[1], lightSystemConfig.lightData},
        FixedDispatches = {
            dispatch {
                Effect = engine.Render.InfillProbesEffect,
                ThreadGroups = uint3(1, (lightSystemConfig.probeInfillPhaseOffset1 + 63)/64, 1),
                ThreadsPerGroup = uint3(64, 1, 1)
            }
        },
    }
    infillProbesStages[2] = compute_stage {
        Type = engine.Render.InfillProbesStage,
        Parameters = {infillProbesParams[2], lightSystemConfig.lightData},
        FixedDispatches = {
            dispatch {
                Effect = engine.Render.InfillProbesEffect,
                ThreadGroups = uint3(1, (lightSystemConfig.probeInfillPhaseOffset2 - lightSystemConfig.probeInfillPhaseOffset1 + 63)/64, 1),
                ThreadsPerGroup = uint3(64, 1, 1)
            }
        },
    }
    infillProbesStages[3] = compute_stage {
        Type = engine.Render.InfillProbesStage,
        Parameters = {infillProbesParams[3], lightSystemConfig.lightData},
        FixedDispatches = {
            dispatch {
                Effect = engine.Render.InfillProbesEffect,
                ThreadGroups = uint3(1, (lightSystemConfig.probeInfillPhaseOffset3 - lightSystemConfig.probeInfillPhaseOffset2 + 63)/64, 1),
                ThreadsPerGroup = uint3(64, 1, 1)
            }
        },
    }

    blendFringeProbesStage = compute_stage {
        Type = engine.Render.BlendFringeProbesStage,
        Parameters = {blendFringeProbesParams, lightSystemConfig.lightData},
        FixedDispatches = {
            dispatch {
                Effect = engine.Render.BlendFringeProbesEffect,
                ThreadGroups = uint3(1, (lightSystemConfig.probeFringeCount + 63)/64, 1),
                ThreadsPerGroup = uint3(64, 1, 1)
            }
        },
    }

end

local hdrLumaAnalysisParams = parameter_block(engine.Render.HDRLumaAnalysisParams)
hdrLumaAnalysisParams.InputTexture = renderTargets.PostColorTexture
hdrLumaAnalysisParams.TileStatistics = renderTargets.HdrLuminanceTilesRw
hdrLumaAnalysisParams.OutputStatistics = renderTargets.HDRTonemappingRw
hdrLumaAnalysisParams.TextureSize = uint4(renderTargets.DisplayHdr2.Width, renderTargets.DisplayHdr2.Height, 0, 0)
hdrLumaAnalysisParams.TileCount = ((renderTargets.DisplayHdr2.Width+15)/16) * ((renderTargets.DisplayHdr2.Height+15)/16)
hdrLumaAnalysisParams.ExposureInfo = renderTargets.ExposureInfo

local hdrLumaAnalysisStage = compute_stage {
    Type = engine.Render.HDRLumaAnalzse,
    Parameters = { hdrLumaAnalysisParams },
    FixedDispatches = {
        dispatch {
            Effect = engine.Render.HDRLumaAnalyzeEffect,
            ThreadGroups = uint3((renderTargets.DisplayHdr2.Width+15)/16, (renderTargets.DisplayHdr2.Height+15)/16, 1),
            ThreadsPerGroup = uint3(16, 16, 1)
        }
    },
}

local hdrLumaReduceStage = compute_stage {
    Type = engine.Render.HDRLumaReduce,
    Parameters = { hdrLumaAnalysisParams },
    FixedDispatches = {
        dispatch {
            Effect = engine.Render.HDRLumaReduceEffect,
            ThreadGroups = uint3(1, 1, 1),
            ThreadsPerGroup = uint3(256, 1, 1)
        }
    },
}

local postProcessParams = parameter_block(engine.Render.PostProcessParams)
postProcessParams.Noise = uniformNoise
postProcessParams.BlueNoise = blueNoise
postProcessParams.BlueNoiseResolution = blueNoise.Resource.Width
postProcessParams.NoiseResolution = uniformNoise.Resource.Width
postProcessParams.Bloom = renderTargets.HdrDownres[1].Texture
postProcessParams.ExposureInfo = renderTargets.ExposureInfo
postProcessParams.HDRExposureInfo = renderTargets.HDRExposureInfo
postProcessParams.OutputStatistics = renderTargets.HDRTonemapping

local presentParams = parameter_block(engine.Render.PresentParams)
presentParams.Source = renderTargets.PostColorTexture
presentParams.RcpTargetSize = float4(1/renderTargets.PostColor.Width, 1/renderTargets.PostColor.Height, 0, 0)
presentParams.HdrTonemapping = scriptParams.hdrTonemapping
presentParams.DisplayGammaMode = scriptParams.displayGammaMode
presentParams.DisplayGamma = scriptParams.displayGamma

postProcessParams.Trails = renderTargets.PostTrailColorTexture
postProcessParams.Sharpen = 1.5
postProcessParams.SharpenShockAllowance = 0.05
postProcessParams.VignetteStrength = 0.0
postProcessParams.SeparatorWidth = vrDisplay and 0.005 or 0
postProcessParams.HdrMaxLuminance = scriptParams.hdrMaxLuminance
postProcessParams.HdrWhitePoint = scriptParams.hdrWhitePoint
postProcessParams.HdrPqNormalizationRcp = scriptParams.HdrPqNormalizationRcp
postProcessParams.Depth = renderTargets.DepthTexture
postProcessParams.vrDisplay = vrDisplay and 1 or 0
postProcessParams.WaterEffect = 0

local emissiveTrailParams = parameter_block(engine.Render.EmissiveTrailParams)
emissiveTrailParams.Motion = renderTargets.MotionTexture
emissiveTrailParams.Depth = renderTargets.DepthTexture
emissiveTrailParams.Mask = renderTargets.PostTrailMaskColorTexture
emissiveTrailParams.Prev = renderTargets.PostTrailPrevColorTexture

local emissiveTrailBlitPrevParams = parameter_block(engine.Render.EmissiveTrailBlitPrevParams)
emissiveTrailBlitPrevParams.Source = renderTargets.PostTrailColorTexture

local emissiveTrailClearMaskParams = parameter_block(engine.Render.EmissiveTrailClearMaskParams)
emissiveTrailClearMaskParams.Target = renderTargets.PostTrailMaskColorTextureRw

local diffractionAlbedoBlitParams = parameter_block(engine.Render.DiffractionBlitParams)
diffractionAlbedoBlitParams.Source = renderTargets.LightingResult.Texture

local diffractionTransparentBlitParams = parameter_block(engine.Render.DiffractionBlitParams)
diffractionTransparentBlitParams.Source = renderTargets.Gbuffer1Texture

local diffractionHistBlitParams = parameter_block(engine.Render.DiffractionBlitParams)
diffractionHistBlitParams.Source = renderTargets.DiffractDepthTexture

local diffractionCurBlitParams = parameter_block(engine.Render.DiffractionBlitParams)
diffractionCurBlitParams.Source = renderTargets.DepthTexture

local diffractionTransparentHistBlitParams = parameter_block(engine.Render.DiffractionBlitParams)
diffractionTransparentHistBlitParams.Source = renderTargets.DepthTexture

local transparentResolveParams = parameter_block(engine.Render.ResolveTransparentParams)
transparentResolveParams.Source = renderTargets.LightingResult.Texture
transparentResolveParams.Transmittance = transmittanceInput

local transparentResolveStage = graphics_stage {
    Type = engine.Render.ResolveTransparent,
    Target = resolvedTransparentOutput,
    Parameters = { cameraInfos, transparentResolveParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local temporalResolveParams = parameter_block(engine.Render.TemporalResolveParams)
temporalResolveParams.Source = renderTargets.RenderHdr3Texture
temporalResolveParams.History = renderTargets.DisplayHdr1Texture
temporalResolveParams.Motion = renderTargets.MotionTexture
temporalResolveParams.Depth = renderTargets.DepthTexture
temporalResolveParams.ExposureInfo = renderTargets.ExposureInfo

local temporalResolveStage = graphics_stage {
    Type = engine.Render.TemporalResolve,
    Target = renderTargets.DisplayHdr2_Only,
    Parameters = { temporalResolveParams, cameraInfos },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local downsampleBlurParams
local downsampleBlurStages = {}
for i=8,1,-1 do -- iterate backwards so 'downsampleBlurParams' will have the paramblock for the first downsample stage, which is needed later
    downsampleBlurParams = parameter_block(engine.Render.FilterImageParams)
    downsampleBlurParams.Source = i ~= 1 and renderTargets.HdrDownres[i-1].Texture or renderTargets.LightingResult.Texture
    downsampleBlurParams.RcpTargetSize = float4(1/renderTargets.HdrDownresTargets[i].Width, 1/renderTargets.HdrDownresTargets[i].Height, 0, 0)

    downsampleBlurStages[2*i - 1] = graphics_stage { Type = engine.Render.DownsampleBlur, Target = renderTargets.HdrDownres[i].TargetOnly, Parameters = { downsampleBlurParams } }
    downsampleBlurStages[2*i - 0] = target_state_setting(renderTargets.HdrDownres[i].Source, 'GenericRead')
end

local upsampleBlurStages = {}
for i=1,7 do
    local imgIndex = 8 - i
    local upsampleBlurParams = parameter_block(engine.Render.FilterImageParams)
    upsampleBlurParams.Source = renderTargets.HdrDownres[1+imgIndex].Texture
    upsampleBlurParams.RcpTargetSize = float4(1/renderTargets.HdrDownresTargets[imgIndex].Width, 1/renderTargets.HdrDownresTargets[imgIndex].Height, 0, 0)
    upsampleBlurParams.Progress = (i - 0.5)/8.0

    upsampleBlurStages[2*i - 1] = graphics_stage { Type = engine.Render.UpsampleBlur, Target = renderTargets.HdrDownres[imgIndex].TargetOnly, Parameters = { upsampleBlurParams, worldParams.bloomParams } }
    upsampleBlurStages[2*i - 0] = target_state_setting(renderTargets.HdrDownres[imgIndex].Source, 'GenericRead')
end

local postProcessStage = graphics_stage {
    Type = scriptParams.hdrTonemapping == 1 and engine.Render.PostProcessHDR or engine.Render.PostProcess,
    Target = renderTargets.Post_HDR_SDR,
    Parameters = { postProcessParams, cameraInfos[1], worldInfo, worldParams.bloomParams, worldParams.postEffectParams },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local emissiveTrailStage = graphics_stage {
    Type = engine.Render.EmissiveTrail,
    Target = renderTargets.PostTrail_Only,
    Parameters = { emissiveTrailParams, cameraInfos, worldInfo },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
    InstanceCountMultiplier = enableStereoSplit and 2 or 1
}

local emissiveTrailBlitPrevStage = graphics_stage {
    Type = engine.Render.EmissiveTrailBlitPrev,
    Target = renderTargets.PostTrailPrev_Only,
    Parameters = { emissiveTrailBlitPrevParams },
    Viewports = displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local emissiveTrailClearMaskStage = compute_stage {
    Type = engine.Render.EmissiveTrailClearMask,
    Parameters = { emissiveTrailClearMaskParams },
    FixedDispatches = {
        dispatch {
            Effect = engine.Render.EmissiveTrailClearMaskEffect,
            ThreadGroups = uint3(renderTargets.PostTrailMask.Width / 8, renderTargets.PostTrailMask.Height / 8, 1),
            ThreadsPerGroup = uint3(8, 8, 1)
        }
    },
}

local diffractionAlbedoBlitStage = graphics_stage {
    Type = engine.Render.DiffractionAlbedoBlit,
    Target = renderTargets.DiffractAlbedoWithDepth,
    Parameters = { diffractionAlbedoBlitParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local diffractionTransparentBlitStage = graphics_stage {
    Type = engine.Render.DiffractionAlbedoBlit,
    Target = renderTargets.DiffractAlbedoWithDepth,
    Parameters = { diffractionTransparentBlitParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local diffractionHistBlitStage = graphics_stage {
    Type = engine.Render.DiffractionHistBlit,
    Target = renderTargets.DiffractPrevDepth_Only,
    Parameters = { diffractionHistBlitParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local diffractionTransparentHistBlitStage = graphics_stage {
    Type = engine.Render.DiffractionHistBlit,
    Target = renderTargets.DiffractPrevTransDepth_Only,
    Parameters = { diffractionHistBlitParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local diffractionTransparentCurBlitStage = graphics_stage {
    Type = engine.Render.DiffractionHistBlit,
    Target = renderTargets.DiffractCurDepth_Only,
    Parameters = { diffractionCurBlitParams },
    Viewports = renderViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local presentStage = graphics_stage {
    Type = vrDisplay and engine.Render.PresentResample or isPanorama and engine.Render.PresentPano or scriptParams.hdrOsEnabled == 1 and engine.Render.PresentHDR or engine.Render.Present,
    WindowSurface = get_window_surface(),
    Parameters = { presentParams },
    Viewports = vrDisplay == nil and displayViewports,
    IterationCount = scriptParams.viewSegmentCount,
}

local atmosphereStages = enableShadowedAtmosphere and { lightAtmosphereStage, resolveAtmosphereStage } or unshadowedAtmosphereStage
local occlusionStages = enableOcclusionSmoothing and {occlusionStage, filterOcclusionStage, resolveOcclusionStage} or {occlusionStage, resolveOcclusionStage}


local displayDesktopWindow = (vrDisplay == nil) or scriptParams.mirrorVrDisplay

local isDiagnosticOrNormalMode = isDiagnosticMode or (mode == 'Normal')

local stages = flatten {
    projectSkyHarmonicsStage,												        -- computes spherical harmonics for the sky, which is later incorporated into the probes
    filterVideoEmissionStage,												        -- prefilters the media texture for later samples by probes
    clearLightingResultStage,												        -- clears the primary render target
    mode ~= 'Debug' and prerenderZStage,									        -- populates the z buffer with static opaque geometry
    mode ~= 'Debug' and {renderLitOpaqueStage, renderLitOpaqueFadingStage},	        -- populates the gbuffers
    mode ~= 'Debug' and renderMediaGBufferState,                                    -- populate the media gbuffer
    mode ~= 'Debug' and renderLitTransparentStage,							        -- populates the gbuffers with transparent (stippled) materials
    mode ~= 'Debug' and {                                                           -- populates the gbuffers with diffracted transparent materials
        clearDiffractionStage,
        diffractionTransparentBlitStage,
        diffractionTransparentCurBlitStage,
        renderTransDiffractZStage,
        renderLitDiffractTransparentStage,
        diffractionTransparentHistBlitStage },
    mode ~= 'Debug' and downsampleDepthMinMaxStage,							        -- produces a low-res min/max depth buffer for use by atmospheric scattering and SSAO

    isDiagnosticOrNormalMode and enableOcclusion and occlusionStages,		        -- SSAO computation and reconstruction
    shadeProbesStage,														        -- computes final probe data
    infillProbesStages,														        -- fills in gaps in the probe grid
    blendFringeProbesStage,													        -- interpolates probes to enable seamless interpolation across grid levels

    isDiagnosticOrNormalMode and deferredLightStage,					            -- does all the lighting/shading
    mode == 'EditorLighting' and editorLightingStage,						        -- diagnostic "fullbright" visualizer
    mode == 'VisualizeNormals' and visualizeNormalsStage,					        -- diagnostic normals visualizer
    isDiagnosticOrNormalMode and gatherSubsurfaceStage,						        -- screen space subsurface scattering integrator
    mode ~= 'Debug' and addEnvironmentStage,									    -- adds enviromental/skybox contribution from enviromental materials
    mode ~= 'Debug' and addEmissiveStage,									        -- adds emissive contribution from emissive materials
    mode ~= 'Debug' and addMediaStage,										        -- same as above, but samples the media surface
    isDiagnosticOrNormalMode and renderSkyStage,							        -- renders the background sky

    isDiagnosticOrNormalMode and renderToonOutlines,						        -- applies full-screen "toon" effect
    isDiagnosticOrNormalMode and atmosphereStages,							        -- renders the fog scattering ("god rays")
    mode ~= 'Debug' and {clearTransmittanceStage, renderTransmittanceStage},        -- renders transmissive materials, which sample the frame buffer for transparency effect
    mode ~= 'Debug' and multiplyTransmissiveStage,							        -- tints the transmission from preceeding pass
    mode ~= 'Debug' and addTransmissiveEmissiveStage,						        -- adds emissive contribution for "transmissive + emissive" materials
    mode ~= 'Debug' and {
        clearDiffractionStage,
        diffractionAlbedoBlitStage,
        renderUnlitOpaqueDiffractionStage,
        diffractionHistBlitStage },                                                 -- populate the gbuffers with defracted materials

    mode == 'Debug' and renderAllAsDebugStage,								        -- diagnostic world-space grid visualizer
    isDiagnosticMode and clearDiagnosticsStage,								        -- clears the "heat map" buffer for diagnostic shaders
    isDiagnosticMode and populateDiagnosticsZStage,							        -- clears the z buffer for diagnostic rendering
    mode == 'VisualizeTriangleDensity' and renderTriangleDensityStage,		        -- various diagnostic visualization effects...
    mode == 'VisualizeOverdraw' and renderOverdrawStage,				    	    -- ...
    mode == 'VisualizeWireframe' and renderWireframeStage,
    mode == 'VisualizeLightingComplexity' and lightingComplexityStage,
    mode == 'VisualizeBackfaces' and renderBackfacesStage,
    mode == 'VisualizeRadiance' and visualizeRadianceStage,
    isDiagnosticMode and renderColorGradientStage,							        -- converts diagnostic output to colorful gradient
    clearHighlightMaskStage,												        -- clears an alpha mask for object highlighting
    renderHighlightMaskStage,												        -- fills the highlighted object in the alpha mask (used for outline)
    enableEditorFeatures and renderWidgetStage,								        -- renders widgets (translation/rotation/etc)
    target_state_setting(renderTargets.PostColor, 'GenericRead'),			        -- resource barrier
    renderHighlightStage,													        -- renders the highlight effect for selected objects
    enableEditorFeatures and renderWidgetBehindStage,						        -- renders depth-obscured widgets with a stipple/stripe pattern
    renderDebugStage,														        -- renders various visual debugging aids
    transparentResolveStage,												        -- destipples/reconstructs transparent surfaces

    mode == 'Normal' and maskUnlitStage,									        -- attenuates frame buffer based on absorption of unlit materials
    mode == 'Normal' and maskEmissiveUnlitStage,    						        -- attenuates frame buffer based on absorption of unlit materials
    mode == 'Normal' and renderUnlitStage,									        -- adds emissive contribution from unlit (e.g. billboard) materials
    mode == 'Normal' and renderEmissiveUnlitStage,							        -- adds emissive contribution from unlit (e.g. billboard) materials

    target_state_setting(renderTargets.Motion, 'GenericRead'),				        -- resource barrier

    mode == 'Normal' and enableReflections and {
        reflectTraceStage,
        reflectSolveStage,
        reflectReprojStage,
        reflectBlitPrevReprojStage,
        reflectDownsampleBlurStages,
        reflectBlendStage },                                                        -- reflection computation

    renderPrimitivesStages,													        -- points, lines, debug text, etc
    target_state_setting(renderTargets.LightingResult.Target, 'GenericRead'),       -- resource barrier
    scriptParams.enableTemporalAntialiasing and temporalResolveStage,		        -- temporal AA

    mode == 'Normal' and {
        emissiveTrailStage,
        emissiveTrailBlitPrevStage,
        emissiveTrailClearMaskStage },                                              -- psychedelic after-images effect

    downsampleBlurStages,													        -- render blur pyramid for bloom
    meteringStage,															        -- histogram analysis for auto exposure
    upsampleBlurStages,														        -- compose final bloom from blur pyramid
    scriptParams.samplesPerPixel ~= 1 and scriptParams.useFSRUpscaler == true and {
        fsrTonemapStage,
        fsrSharpenStage },                                                          -- FSR/RCAS sharpening

    postProcessStage,							  						            -- tone mapping; also applies bloom, image sharpening, and other effects
    vrDisplay ~= nil and {
        hdrLumaAnalysisStage,										    	        -- computes luminance histogram for HDR tonemapping
        hdrLumaReduceStage },											            -- computes luminance reduction for HDR tonemapping

    enableStereoSplit and scriptParams.samplesPerPixel ~= 1 and {
        clearHighResZStage },
        --clearHighResZStage,
        --renderVRVisDepthStage },                                                  -- custom depth pass for VR visualisation layer
    renderUIInWorldStage,                                                           -- 3D UI elements with z-test  
    renderUIIndirectStage,
    renderUIBackgroundStage,												        -- 2D background UI
    renderUIViewsStage,														        -- 2D UI panels
    target_state_setting(renderTargets.PostColor, 'GenericRead'),			        -- resource barrier
    displayDesktopWindow and presentStage,									        -- blits to the swap chain (for VR, this pertains only to the desktop window)
}

local frameNumber = 0
local seconds = 0

local identity4x4 = float4x4(float4(1,0,0,0),float4(0,1,0,0),float4(0,0,1,0),float4(0,0,0,1))
local headToWorldHistories = {}
local leftEyeToClipHistories = {}
local rightEyeToClipHistories = {}

local function maintainViewport()
    -- recongigure stages for new viewports (if any changed)
    local allSame = true
    for i=1,scriptParams.viewSegmentCount do
        allSame = allSame and displayViewports[i].X == originalViewportDimensions[i].X
        allSame = allSame and displayViewports[i].Y == originalViewportDimensions[i].Y
        allSame = allSame and displayViewports[i].Width == originalViewportDimensions[i].Width
        allSame = allSame and displayViewports[i].Height == originalViewportDimensions[i].Height
    end
    if allSame then return end

    for i=1,scriptParams.viewSegmentCount do
        originalViewportDimensions[i].X = displayViewports[i].X
        originalViewportDimensions[i].Y = displayViewports[i].Y
        originalViewportDimensions[i].Width = displayViewports[i].Width
        originalViewportDimensions[i].Height = displayViewports[i].Height
    end

    for i=1,#scaledDisplayViewports do
        scaleViewports(displayViewports, 2^-i, scaledDisplayViewports[i])
    end
    for i=1,#scaledRenderViewports do
        scaleViewports(renderViewports, 2^-i, scaledRenderViewports[i])
    end

    for k,v in pairs(stages) do
        if type(v) == 'table' then
            if v.Viewports then
                if v.Viewports.autoRefresh then
                    v.Viewports = v.Viewports   -- this is a silly way to trigger a refresh, todo
                end
            elseif v.FixedDispatches then
                if v.FixedDispatches.viewportTileSize then
                    v.FixedDispatches = makeDispatchesForViewports(v.FixedDispatches[1].Effect, v.FixedDispatches.samplingReduction, v.FixedDispatches.viewportTileSize, renderViewports, v.FixedDispatches)
                end
            end
        end
    end
end

if vrDisplay then
    late_vr_transform(1, cameraInfos[1], 'HeadToWorld',            vrDisplay.LocalToWorld)
    late_vr_transform(1, cameraInfos[1], 'HeadToWorldHistory',     vrDisplay.LocalToWorldHistory)
end

set_vr_display_source(renderTargets.PostColorSDR)
set_screenshot_source(renderTargets.PostColorSDR)

local temporalResultTargets = { renderTargets.DisplayHdr1_Only, renderTargets.DisplayHdr2_Only }
local temporalResultTextures = { renderTargets.DisplayHdr1Texture, renderTargets.DisplayHdr2Texture }
local temporalResultTextureRws = { renderTargets.DisplayHdr1TextureRw, renderTargets.DisplayHdr2TextureRw }

return function()
    maintainViewport()

    local isFirstFrame = (frameNumber == 0)
    frameNumber = frameNumber + 1

    local deltaSeconds = isFirstFrame and 0.001 or (get_seconds() - seconds)
    seconds = get_seconds()

    local globalMipBias = scriptParams.enableTemporalAntialiasing and -math.log(temporalConvergenceFrames) / (2*math.log(2)) or 0
    globalMipBias = globalMipBias / 2   -- split the difference on speed v. quality

    local horzFov = 0

    for i=1,scriptParams.viewSegmentCount do
        local cam = get_camera(i)
        local displayViewport = displayViewports[i]
        local renderViewport = renderViewports[i]

        local renderPixelWidth = 1/renderViewport.Width
        local renderPixelHeight = 1/renderViewport.Height

        local temporalJitter = halton23Points[1 + (frameNumber + 1) % #halton23Points]
        temporalJitter = scriptParams.enableTemporalAntialiasing and float4((temporalJitter[1]*2-1)*renderPixelWidth, (temporalJitter[2]*2-1)*renderPixelHeight, 0, 0) or float4(0,0,0,0)

        if isFirstFrame then
            headToWorldHistories[i] = cam.HeadToWorld
            leftEyeToClipHistories[i] = cam.LeftEyeToClip
            rightEyeToClipHistories[i] = cam.RightEyeToClip
        end

        horzFov = cam.HorizontalFieldOfView

        cameraInfos[i].HeadToWorld = cam.HeadToWorld
        cameraInfos[i].HeadToWorldHistory = headToWorldHistories[i]
        cameraInfos[i].WorldToHead = cam.WorldToHead
        cameraInfos[i].LeftEyeToHead = cam.LeftEyeToHead
        cameraInfos[i].RightEyeToHead = cam.RightEyeToHead
        cameraInfos[i].LeftHeadToEye = cam.LeftHeadToEye
        cameraInfos[i].RightHeadToEye = cam.RightHeadToEye
        cameraInfos[i].ClipToLeftEye = cam.ClipToLeftEye
        cameraInfos[i].ClipToRightEye = cam.ClipToRightEye
        cameraInfos[i].LeftEyeToClip = cam.LeftEyeToClip
        cameraInfos[i].RightEyeToClip = cam.RightEyeToClip
        cameraInfos[i].LeftEyeToClipHistory = leftEyeToClipHistories[i]
        cameraInfos[i].RightEyeToClipHistory = rightEyeToClipHistories[i]
        cameraInfos[i].DisplayViewportOffset = float4(displayViewport.X, displayViewport.Y, 0, 0)
        cameraInfos[i].DisplayViewportSize = float4(displayViewport.Width, displayViewport.Height, 0, 0)
        cameraInfos[i].RcpDisplayViewportSize = float4(1/displayViewport.Width, 1/displayViewport.Height, 0, 0)
        cameraInfos[i].RenderViewportOffset = float4(renderViewport.X, renderViewport.Y, 0, 0)
        cameraInfos[i].RenderViewportSize = float4(renderViewport.Width, renderViewport.Height, 0, 0)
        cameraInfos[i].RcpRenderViewportSize = float4(renderPixelWidth, renderPixelHeight, 0, 0)
        cameraInfos[i].DisplayTargetSize = float4(renderTargets.DisplayDimensions.Width, renderTargets.DisplayDimensions.Height, 0, 0)
        cameraInfos[i].RcpDisplayTargetSize = float4(1.0/renderTargets.DisplayDimensions.Width, 1.0/renderTargets.DisplayDimensions.Height, 0, 0)
        cameraInfos[i].RenderTargetSize = float4(renderTargets.RenderDimensions.Width, renderTargets.RenderDimensions.Height, 0, 0)
        cameraInfos[i].RcpRenderTargetSize = float4(1.0/renderTargets.RenderDimensions.Width, 1.0/renderTargets.RenderDimensions.Height, 0, 0)
        cameraInfos[i].TemporalJitter = temporalJitter
        cameraInfos[i].GlobalMipBias = globalMipBias
        cameraInfos[i].FrameNumber = frameNumber

        headToWorldHistories[i] = cam.HeadToWorld
        leftEyeToClipHistories[i] = cam.LeftEyeToClip
        rightEyeToClipHistories[i] = cam.RightEyeToClip
    end

    projectSkyHarmonicsParams.FrameNumber = frameNumber;

    -- borrowing temporal resolve output target for subsurface to avoid allocating yet another massive render target...
    deferredBuffersTarget.Subsurface = temporalResultTextureRws[1]
    subsurfaceParams.SubsurfaceIrradiance = temporalResultTextures[1]

    reflectTraceParams.Cut = isFirstFrame
    reflectSolveParams.Cut = isFirstFrame
    reflectBlendParams.Cut = isFirstFrame

    temporalResolveParams.History = temporalResultTextures[2]
    temporalResolveStage.Target = temporalResultTargets[1]
    temporalResolveParams.Cut = isFirstFrame

    local sourceOfTruth = scriptParams.enableTemporalAntialiasing and temporalResultTextures[1] or renderTargets.LightingResult.Texture
    downsampleBlurParams.Source = sourceOfTruth
    postProcessParams.Source = sourceOfTruth
    emissiveTrailParams.Source = sourceOfTruth

    meteringParams.Cut = isFirstFrame
    meteringParams.DeltaSeconds = deltaSeconds

    presentParams.UndistortCylinderFov = horzFov

    temporalResultTargets[1], temporalResultTargets[2] = temporalResultTargets[2], temporalResultTargets[1]
    temporalResultTextures[1], temporalResultTextures[2] = temporalResultTextures[2], temporalResultTextures[1]
    temporalResultTextureRws[1], temporalResultTextureRws[2] = temporalResultTextureRws[2], temporalResultTextureRws[1]

    return stages
end
