﻿  unity_T6KRJM793EYLJOUH.cpp
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(50,14): error C2039: 'unordered_map': is not a member of 'std'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\cmath(955): message : see declaration of 'std'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(50,27): error C2143: syntax error: missing ';' before '<'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(50,27): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(50,71): error C2238: unexpected token(s) preceding ';'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(51,14): error C2039: 'unordered_map': is not a member of 'std'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\cmath(955): message : see declaration of 'std'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(51,27): error C2143: syntax error: missing ';' before '<'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(51,27): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(51,69): error C2238: unexpected token(s) preceding ';'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,23): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,23): error C2672: 'begin': no matching overloaded function found
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): error C2893: Failed to specialize function template 'unknown-type std::begin(_Container &)'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\xutility(1844): message : see declaration of 'std::begin'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): message : With the following template arguments:
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): message : '_Container=unknown-type'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): error C2784: 'const _Elem *std::begin(std::initializer_list<_Elem>) noexcept': could not deduce template argument for 'std::initializer_list<_Elem>' from 'unknown-type'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\initializer_list(55): message : see declaration of 'std::begin'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,23): error C2672: 'end': no matching overloaded function found
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): error C2893: Failed to specialize function template 'unknown-type std::end(_Container &)'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\xutility(1854): message : see declaration of 'std::end'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): message : With the following template arguments:
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): message : '_Container=unknown-type'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): error C2784: 'const _Elem *std::end(std::initializer_list<_Elem>) noexcept': could not deduce template argument for 'std::initializer_list<_Elem>' from 'unknown-type'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\initializer_list(60): message : see declaration of 'std::end'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): error C3536: '<begin>$L0': cannot be used before it is initialized
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,34): error C3536: '<end>$L0': cannot be used before it is initialized
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(14,32): error C2100: illegal indirection
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(16,22): error C2039: 'remove': is not a member of 'Solver'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(197): message : see declaration of 'Solver'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(20,5): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(21,5): error C2065: 'm_reverseMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(36,9): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(36,38): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(41,33): error C2512: 'Rigid::Rigid': no appropriate default constructor available
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(45,14): error C2039: 'add': is not a member of 'Solver'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(197): message : see declaration of 'Solver'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(53,5): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(54,5): error C2065: 'm_reverseMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(60,15): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(61,8): error C3536: 'it': cannot be used before it is initialized
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(61,15): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(66,14): error C2039: 'remove': is not a member of 'Solver'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(197): message : see declaration of 'Solver'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(70,5): error C2065: 'm_reverseMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(71,5): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(77,15): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(78,8): error C3536: 'it': cannot be used before it is initialized
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(78,15): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(83,56): error C2660: 'EngineSimulation::AVBDIntegrationBridge::convertToAVBDRigid': function does not take 1 arguments
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(40,14): message : see declaration of 'EngineSimulation::AVBDIntegrationBridge::convertToAVBDRigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,23): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,23): error C2672: 'begin': no matching overloaded function found
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): error C2893: Failed to specialize function template 'unknown-type std::begin(_Container &)'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\xutility(1844): message : see declaration of 'std::begin'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): message : With the following template arguments:
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): message : '_Container=unknown-type'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): error C2784: 'const _Elem *std::begin(std::initializer_list<_Elem>) noexcept': could not deduce template argument for 'std::initializer_list<_Elem>' from 'unknown-type'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\initializer_list(55): message : see declaration of 'std::begin'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,23): error C2672: 'end': no matching overloaded function found
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): error C2893: Failed to specialize function template 'unknown-type std::end(_Container &)'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\xutility(1854): message : see declaration of 'std::end'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): message : With the following template arguments:
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): message : '_Container=unknown-type'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): error C2784: 'const _Elem *std::end(std::initializer_list<_Elem>) noexcept': could not deduce template argument for 'std::initializer_list<_Elem>' from 'unknown-type'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\initializer_list(60): message : see declaration of 'std::end'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): error C3536: '<begin>$L0': cannot be used before it is initialized
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,34): error C3536: '<end>$L0': cannot be used before it is initialized
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(90,32): error C2100: illegal indirection
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(98,28): error C2660: 'Solver::step': function does not take 1 arguments
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(216,10): message : see declaration of 'Solver::step'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,23): error C2065: 'm_bodyMap': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,23): error C2672: 'begin': no matching overloaded function found
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): error C2893: Failed to specialize function template 'unknown-type std::begin(_Container &)'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\xutility(1844): message : see declaration of 'std::begin'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): message : With the following template arguments:
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): message : '_Container=unknown-type'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): error C2784: 'const _Elem *std::begin(std::initializer_list<_Elem>) noexcept': could not deduce template argument for 'std::initializer_list<_Elem>' from 'unknown-type'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\initializer_list(55): message : see declaration of 'std::begin'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,23): error C2672: 'end': no matching overloaded function found
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): error C2893: Failed to specialize function template 'unknown-type std::end(_Container &)'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\xutility(1854): message : see declaration of 'std::end'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): message : With the following template arguments:
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): message : '_Container=unknown-type'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): error C2784: 'const _Elem *std::end(std::initializer_list<_Elem>) noexcept': could not deduce template argument for 'std::initializer_list<_Elem>' from 'unknown-type'
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\initializer_list(60): message : see declaration of 'std::end'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): error C3536: '<begin>$L0': cannot be used before it is initialized
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,34): error C3536: '<end>$L0': cannot be used before it is initialized
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(105,32): error C2100: illegal indirection
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(107,79): error C2660: 'EngineSimulation::AVBDIntegrationBridge::convertFromAVBDRigid': function does not take 1 arguments
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(41,14): message : see declaration of 'EngineSimulation::AVBDIntegrationBridge::convertFromAVBDRigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(114,61): error C2440: 'initializing': cannot convert from 'const LLCore::MTransform *' to 'LLCore::MTransform'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(114,61): message : No constructor could take the source type, or constructor overload resolution was ambiguous
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(115,13): error C2039: 'Vector3': is not a member of 'LLCore'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\FrameSynchronizer.h(30): message : see declaration of 'LLCore'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(115,21): error C2065: 'Vector3': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(115,21): error C2146: syntax error: missing ';' before identifier 'position'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(115,21): error C2065: 'position': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(116,58): error C2440: 'initializing': cannot convert from 'const LLCore::Rotation' to 'LLCore::Quaternion'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(116,33): message : No user-defined-conversion operator available that can perform this conversion, or the operator cannot be called
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(119,25): error C2676: binary '[': 'LLCore::Vector4' does not define this operator or a conversion to a type acceptable to the predefined operator
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(119,29): error C2065: 'position': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(120,25): error C2676: binary '[': 'LLCore::Vector4' does not define this operator or a conversion to a type acceptable to the predefined operator
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(120,29): error C2065: 'position': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(121,25): error C2676: binary '[': 'LLCore::Vector4' does not define this operator or a conversion to a type acceptable to the predefined operator
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(121,29): error C2065: 'position': undeclared identifier
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(123,15): error C2039: 'rotation': is not a member of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(41): message : see declaration of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(123,38): error C2039: 'x': is not a member of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(124,15): error C2039: 'rotation': is not a member of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(41): message : see declaration of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(124,38): error C2039: 'y': is not a member of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(125,15): error C2039: 'rotation': is not a member of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(41): message : see declaration of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(125,38): error C2039: 'z': is not a member of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(126,15): error C2039: 'rotation': is not a member of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(41): message : see declaration of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(126,38): error C2039: 'w': is not a member of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(129,33): error C2039: 'getMass': is not a member of 'EngineSimulation::RigidBodyComponent'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.h(15): message : see declaration of 'EngineSimulation::RigidBodyComponent'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(130,15): error C2039: 'invMass': is not a member of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(41): message : see declaration of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(134,25): error C2676: binary '[': 'LLCore::Vector4' does not define this operator or a conversion to a type acceptable to the predefined operator
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(135,25): error C2676: binary '[': 'LLCore::Vector4' does not define this operator or a conversion to a type acceptable to the predefined operator
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(136,25): error C2676: binary '[': 'LLCore::Vector4' does not define this operator or a conversion to a type acceptable to the predefined operator
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(139,15): error C2039: 'angularVelocity': is not a member of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(41): message : see declaration of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(140,15): error C2039: 'angularVelocity': is not a member of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(41): message : see declaration of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(141,15): error C2039: 'angularVelocity': is not a member of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(41): message : see declaration of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(144,39): error C2039: 'getScale': is not a member of 'LLCore::MTransform'
C:\localDev\sansar\Code\Common\Libraries\LLCore\MTransform.inl(14): message : see declaration of 'LLCore::MTransform'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,39): error C2039: 'x': is not a member of 'LLCore::Vector4'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Vector4.inl(14): message : see declaration of 'LLCore::Vector4'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,57): error C2039: 'y': is not a member of 'LLCore::Vector4'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Vector4.inl(14): message : see declaration of 'LLCore::Vector4'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,66): error C2039: 'z': is not a member of 'LLCore::Vector4'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Vector4.inl(14): message : see declaration of 'LLCore::Vector4'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,47): error C2672: 'std::max': no matching overloaded function found
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,67): error C2780: '_Ty std::max(std::initializer_list<_Elem>,_Pr)': expects 2 arguments - 1 provided
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\utility(48): message : see declaration of 'std::max'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,67): error C2780: 'const _Ty &std::max(const _Ty &,const _Ty &) noexcept(<expr>)': expects 2 arguments - 1 provided
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\utility(40): message : see declaration of 'std::max'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,67): error C2780: 'const _Ty &std::max(const _Ty &,const _Ty &,_Pr) noexcept(<expr>)': expects 3 arguments - 1 provided
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\utility(31): message : see declaration of 'std::max'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,29): error C2672: 'std::max': no matching overloaded function found
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,68): error C2780: '_Ty std::max(std::initializer_list<_Elem>,_Pr)': expects 2 arguments - 1 provided
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\utility(48): message : see declaration of 'std::max'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,68): error C2780: 'const _Ty &std::max(const _Ty &,const _Ty &) noexcept(<expr>)': expects 2 arguments - 1 provided
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\utility(40): message : see declaration of 'std::max'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(145,68): error C2780: 'const _Ty &std::max(const _Ty &,const _Ty &,_Pr) noexcept(<expr>)': expects 3 arguments - 1 provided
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.29.30133\include\utility(31): message : see declaration of 'std::max'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(151,69): error C2676: binary '[': 'const LLCore::Vector4' does not define this operator or a conversion to a type acceptable to the predefined operator
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(151,92): error C2676: binary '[': 'const LLCore::Vector4' does not define this operator or a conversion to a type acceptable to the predefined operator
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(151,115): error C2676: binary '[': 'const LLCore::Vector4' does not define this operator or a conversion to a type acceptable to the predefined operator
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(152,59): error C2039: 'rotation': is not a member of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\solver.h(41): message : see declaration of 'Rigid'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\AVBDIntegrationBridge.cpp(152,67): fatal error C1003: error count exceeds 100; stopping compilation
