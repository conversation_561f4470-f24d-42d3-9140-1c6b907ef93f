
#include "Generated/Materials_High_OpaqueSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueSingleLayerFadingStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueSingleLayerFadingSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueEnvironmentMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueEnvironmentMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaquePixelizeMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_OpaquePixelizeMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueDiffractMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueDiffractMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueEmissiveSingleLayerDetailBaseStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueEmissiveSingleLayerDetailBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueEmissiveFresnelSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueEmissiveFresnelSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueEmissivePixelizeSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_ScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_ScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueMaskEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueMaskEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_ScrollingScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_ScrollingScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueEnvironmentSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueEnvironmentSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueEnvironmentSingleLayerFadingStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueEnvironmentSingleLayerFadingSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueSingleLayerDetailFadingStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueSingleLayerDetailFadingSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueMaskSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueMaskSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueSubsurfaceSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueSubsurfaceSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueMaskSubsurfaceSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueMaskSubsurfaceSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_High_OpaqueDiffuseMultiLayerStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueDiffuseMultiLayerFadingStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueTripleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_High_OpaqueTripleLayerDetailFadingStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueSingleLayerFadingStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueSingleLayerFadingSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaquePixelizeMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaquePixelizeMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueDiffractMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueDiffractMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueSingleLayerDetailFadingStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueSingleLayerDetailFadingSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueMaskSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueMaskSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_High_StereographicOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_StereographicOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueEmissivePixelizeSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueDiffractMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_ChromaKeyOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_ChromaKeyOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueEmissiveSingleLayerDetailBaseStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueEmissiveSingleLayerDetailBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueEmissiveFresnelSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueEmissiveFresnelSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueEmissivePixelizeSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingOpaqueMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingOpaqueMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingOpaqueDiffractMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_MediaScrollingOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_TwoSidedMediaScrollingOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_High_MediaScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_TwoSidedMediaScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueMaskPixelSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_High_ScrollingOpaqueMaskPixelSingleLayerSkinnedProgram.hlsli"

#include "Generated/Materials_Low_OpaqueSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSingleLayerFadingStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSingleLayerFadingSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEnvironmentMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEnvironmentMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaquePixelizeMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaquePixelizeMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueDiffractMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueDiffractMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEmissiveSingleLayerDetailBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEmissiveSingleLayerDetailBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEmissiveFresnelSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEmissiveFresnelSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEmissivePixelizeSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_ScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_ScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueMaskEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueMaskEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_ScrollingScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_ScrollingScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEnvironmentSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEnvironmentSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEnvironmentSingleLayerFadingStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueEnvironmentSingleLayerFadingSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSingleLayerFadingStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSingleLayerFadingSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSingleLayerDetailFadingStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSingleLayerDetailFadingSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueMaskSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueMaskSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSubsurfaceSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueSubsurfaceSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueMaskSubsurfaceSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueMaskSubsurfaceSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_Low_OpaqueDiffuseMultiLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueDiffuseMultiLayerFadingStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueTripleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_Low_OpaqueTripleLayerDetailFadingStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueSingleLayerFadingStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueSingleLayerFadingSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaquePixelizeMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaquePixelizeMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueDiffractMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueDiffractMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueSingleLayerDetailFadingStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueSingleLayerDetailFadingSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueMaskSingleLayerDetailStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueMaskSingleLayerDetailSkinnedProgram.hlsli"
#include "Generated/Materials_Low_StereographicOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_StereographicOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueEmissivePixelizeSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueDiffractMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_ChromaKeyOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_ChromaKeyOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueEmissiveSingleLayerDetailBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueEmissiveSingleLayerDetailBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingOpaqueMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingOpaqueMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingOpaqueDiffractMaskSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueEmissiveFresnelSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueEmissiveFresnelSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedOpaqueEmissivePixelizeSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_MediaScrollingOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedMediaScrollingOpaqueEmissiveSingleLayerBaseStaticProgram.hlsli"
#include "Generated/Materials_Low_MediaScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_TwoSidedMediaScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueMaskPixelSingleLayerStaticProgram.hlsli"
#include "Generated/Materials_Low_ScrollingOpaqueMaskPixelSingleLayerSkinnedProgram.hlsli"

#ifndef MaterialInfo2
#define MaterialInfo2 MaterialInfo
#endif

#include "../Common/MathUtil.hlsli"
#include "../Common/MotionUtil.hlsli"
#include "../Common/StereoUtil.hlsli"
#include "ScrollingUtil.hlsli"
#include "OffsetUtil.hlsli"
#include "RenderUtil.hlsli"
#include "MaterialUtil.hlsli"

#if defined(FRESNEL)
static uint cFresnelFlag_Invert = 1 << 0;
static uint cFresnelFlag_BlendAlbedo2 = 1 << 1;
static uint cFresnelFlag_BlendEmissive2 = 1 << 2;
#endif

struct NormalRoughnessInfo
{
#ifdef COMPACT_GBUFFER
    float2 normalOctUnorm;
#else
    float2 normalOct;
    float3 varCovar;
    float2 dInvW_dXY;
#endif
    float roughness;
};

#ifdef BILLBOARD
float4x4 ConstructFacingTransform(float3 target, float3 camera, float3 scale)
{
    float3 y = normalize(target - camera);
    float3 axis = MaterialInfo(InheritOrientation) ? ModelInfo(WorldToLocal)[2].xyz : float3(0., 0., 1.);
    float3 x = normalize(cross(y, axis));
    if (MaterialInfo(Lollipop))
    {
        y = normalize(cross(axis, x));
    }
    return transpose(float4x4(float4(x * scale.x, 0.), float4(y * scale.y, 0.), float4(cross(x, y) * scale.z, 0.), float4(target, 1.)));
}

float4x4 GetBillboardModelToWorld()
{
    float scale = length(ModelInfo(LocalToWorld)[0].xyz);
    return ConstructFacingTransform(translationOf(ModelInfo(LocalToWorld)).xyz, stereoGetHeadPositionWorldSpace(), scale);
}
#endif

#ifdef SCROLLING
float2 CalcualteFluidDistortion(float2 uv)
{
    if (MaterialInfo(UvFluidDistortion))
    {
        float localTimeSeconds = (WorldInfo(WorldTimeSeconds) - ModelInfo(InitialTime)) * MaterialInfo(UvFluidDistortion) * 0.25;

        float X = uv.x * MaterialInfo(UvFluidDistortionScale) + localTimeSeconds;
        float Y = uv.y * MaterialInfo(UvFluidDistortionScale) + localTimeSeconds;
        uv.y += cos(X + Y) * (MaterialInfo(UvFluidDistortionAmp) * 0.025) * cos(Y);
        uv.x += sin(X - Y) * (MaterialInfo(UvFluidDistortionAmp) * 0.025) * sin(Y);
    }
    return uv;
}
#endif

uint dither3x3(uint2 coord)
{
    uint3x3 bayer = uint3x3(0, 7, 4, 3, 8, 1, 6, 2, 5);
    coord.y += coord.x / 4;
    coord %= 3;
    return bayer[coord.x][coord.y];
}

uint dither2x2(uint2 coord)
{
    coord.y += coord.x / 4;
    coord &= 1;
    return (coord.x * 3) ^ (coord.y * 2);
}

uint dither2x3(uint2 coord)
{
    uint2x3 bayer = uint2x3(uint3(0, 2, 4), uint3(3, 5, 1));
    coord.x %= 2;
    coord.y %= 3;
    return bayer[coord.x][coord.y];
}

float getSubsurface()
{
#ifdef SUBSURFACE
    return 1.;
#else
    return 0.;
#endif
}

float getAlphaThreshold(uint2 coord, uint frame)
{
    //return ((dither3x3(coord) + frame) % 9) / 8.;
    return ((dither2x3(coord) + frame) % 6) / 5.;
}

float getAlphaMaskValue(float a)
{
#ifdef ALPHA_MASK
    return saturate((a - MaterialInfo(MaskThreshold)) / MaterialInfo(MaskSoftness) + MaterialInfo(MaskThreshold));
#else
    return 1.;
#endif
}

float distanceVisibility(float3 v)
{
    [branch]
    if (ModelInfo(MinDrawDistance) > 0.0)
    {
        return distanceFadeVisibilityMinMax(length(v) * (float) WorldInfo(EnableDistanceFading), ModelInfo(MinDrawDistance), ModelInfo(MaxDrawDistance));
    }
    else
    {
        return distanceFadeVisibilityMax(length(v) * (float) WorldInfo(EnableDistanceFading), ModelInfo(MaxDrawDistance));
    }
}

#if defined(MEDIA)
float Epsilon = 1e-10;

float3 RGBtoHSV(in float3 RGB)
{
    float4 P = (RGB.g < RGB.b) ? float4(RGB.bg, -1.0, 2.0 / 3.0) : float4(RGB.gb, 0.0, -1.0 / 3.0);
    float4 Q = (RGB.r < P.x) ? float4(P.xyw, RGB.r) : float4(RGB.r, P.yzx);
    float C = Q.x - min(Q.w, Q.y);
    float S = C / (Q.x + Epsilon);
    float H = abs((Q.w - Q.y) / (6 * C + Epsilon) + Q.z);
    return float3(H, S, Q.x);
}

#endif

float ChromaAlpha(in float3 keyColor, in float3 RGB)
{
#if defined(MEDIA)
    float3 HSV = RGBtoHSV(RGB);
    HSV -= keyColor;
    HSV = float3(abs(HSV.x), abs(HSV.y), abs(HSV.z));

    float ret = 0.;

    if (HSV.x <= MaterialInfo(HueThreshold) && HSV.y <= MaterialInfo(SaturationThreshold) && HSV.z <= MaterialInfo(ValueThreshold))
    {
        ret = 1.;
    }
    else
    {
        ret = 0.;
    }
    return ret;
#else
    return 1.;
#endif
}

float3 SampleMediaTexture(float2 uv)
{
#ifdef MEDIA
    return MediaParams(MediaTexture).Sample(AnisoSampler, uv).rgb;
#else
    return 0.f;
#endif
}

#if defined(MEDIA)
float2 GetUvScale()
{
#if defined(SCROLLING) && !defined(SCROLLING_UV_SCALE)
    return 1.;
#else
    return MaterialInfo(UvScale);
#endif
}
#endif

float2 GetUvRotation(float2 uv)
{
    if (MaterialInfo(UvRotate))
    {
        float cosAngle = cos(MaterialInfo(UvRotate));
        float sinAngle = sin(MaterialInfo(UvRotate));
        float2 p = uv - (0.5).xx;
        return float2(
            cosAngle * p.x + sinAngle * p.y + 0.5,
            cosAngle * p.y - sinAngle * p.x + 0.5
        );
    }
    else
    {
        return uv;
    }
}

float AlphaTest(float a, float2 screenCoord, float3 v, float2 uv)
{
    float alphaValue = 1.;

#if defined(MEDIA)
    uv = getScrollingUV(uv, scrollEmissiveUv()) * GetUvScale();
#if defined(PIXELIZE)
    float4 emissive = MaterialInfo2(EmissiveMap).SampleBias(PointSampler, uv, CameraInfo(GlobalMipBias));
#else
    float4 emissive = MaterialInfo2(EmissiveMap).SampleBias(AnisoSampler, uv, CameraInfo(GlobalMipBias));
#endif

    emissive.rgb *= MaterialInfo(EmissiveIntensity) * MaterialInfo(Tint);
    emissive.rgb *= emissive.a; // legacy reasons
    emissive.rgb += SampleMediaTexture(uv) * MaterialInfo(EmissiveIntensity);

    alphaValue = 1.0 - ChromaAlpha(MaterialInfo(ChromaKeyColor), emissive.rgb);

    if (alphaValue == 0.)
    {
        discard;
    }

#elif defined(DISTANCE_FADE) | defined(ALPHA_MASK)
    float threshold = getAlphaThreshold(screenCoord, CameraInfo(FrameNumber));
    alphaValue = distanceVisibility(v) * getAlphaMaskValue(a);

    if (alphaValue == 0. || alphaValue < threshold)
    {
        discard;
    }
#endif

    return alphaValue;
}

float4 SampleLayerWeights(float2 uv)
{
#if defined(FOUR_LAYER_ALBEDO) || defined(FOUR_LAYER_BUMP)
    float4 weights = MaterialInfo(BlendMap).Sample(AnisoSampler, uv);
    float rgbSum = hsum(weights.rgb);
    weights.a = saturate(1. - rgbSum);
    weights *= rcp(hsum(weights));
    return weights.argb;
#elif defined(THREE_LAYER_ALBEDO) || defined(THREE_LAYER_BUMP)
    float4 weights = MaterialInfo(BlendMap).Sample(AnisoSampler, uv);
    float rgSum = hsum(weights.rg);
    weights.b = saturate(1. - rgSum);
    weights.a = 0;
    weights *= rcp(hsum(weights));
    return weights;
#else
    return 0.;
#endif
}

float4 SampleAlbedo(float2 uv, float4 weights)
{
#if defined(FOUR_LAYER_ALBEDO)
    float4 result = 0.;
    result += weights.r * MaterialInfo(AlbedoMap1).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).x, CameraInfo(GlobalMipBias));
    result += weights.g * MaterialInfo(AlbedoMap2).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).y, CameraInfo(GlobalMipBias));
    result += weights.b * MaterialInfo(AlbedoMap3).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).z, CameraInfo(GlobalMipBias));
    result += weights.a * MaterialInfo(AlbedoMap4).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).w, CameraInfo(GlobalMipBias));
    return result;
#elif defined(THREE_LAYER_ALBEDO)
    float4 result = 0.;
    result += weights.r * MaterialInfo(AlbedoMap1).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).x, CameraInfo(GlobalMipBias));
    result += weights.g * MaterialInfo(AlbedoMap2).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).y, CameraInfo(GlobalMipBias));
    result += weights.b * MaterialInfo(AlbedoMap3).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).z, CameraInfo(GlobalMipBias));
    return result;
#elif defined(SCROLLING)
    return MaterialInfo(AlbedoMap).SampleBias(AnisoSampler, uv, CameraInfo(GlobalMipBias));
#else
#if defined(PIXELIZE)
    return MaterialInfo(AlbedoMap).SampleBias(PointSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias));
#else
    return MaterialInfo(AlbedoMap).SampleBias(AnisoSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias));
#endif
#endif
}

#ifdef FRESNEL
float4 SampleAlbedo2(float2 uv, float4 weights)
{
    return MaterialInfo(AlbedoMap2).SampleBias(AnisoSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias));
}
#endif

#if defined(DIFFRACTION)
void takeNearestZ(float2 loc, in out float nearestZ, in out float2 locOfNearest)
{
    float z = DiffractionAlbedoParams(OpaqueDepth)[loc].x;
    nearestZ = max(nearestZ, z);
    locOfNearest = (nearestZ == z ? loc : locOfNearest);
}

float3 SampleAlbedoClamp(float2 uv, float2 clampedUv, float2 clampedUvMotion, float2 uvOffset)
{
    float2 uvAdj = clampedUv + uvOffset;
    float2 uvAdjMotion = clampedUvMotion + uvOffset;
    float opaqueDepth = DiffractionAlbedoParams(OpaqueDepth).Sample(PointSampler, uvAdj).r;
    float diffractDepth = DiffractionAlbedoParams(DiffractDepth).Sample(PointSampler, uvAdjMotion).r;

    if (opaqueDepth > diffractDepth)
    {
        uvAdj = uv;
    }

    return DiffractionAlbedoParams(Albedo).Sample(LinearSampler, uvAdj).rgb;
}
#endif

#ifdef DIFFRACTION
float3 SampleDiffractedAlbedo(float2 screenPosition, float3 normal, float4 OrigColor, float ao, float3 view)
{
    float2 motion = DiffractionAlbedoParams(Motion)[screenPosition];

    float2 uv = screenPosition / CameraInfo(RenderTargetSize).xy;
    float2 uvMotion = (screenPosition - motion) / CameraInfo(RenderTargetSize).xy;
    float2 pixel = 1 / CameraInfo(RenderTargetSize).xy;
    bool stereoSide = (screenPosition.x >= CameraInfo(RenderViewportSize).x * 0.5);
    float3 viewNorm = mul((float3x3)stereoGetWorldToEye(stereoSide), normal);
#ifdef SCROLLING
    float2 diffractedUv = CalcualteFluidDistortion(uv) + (viewNorm.xy * MaterialInfo(DiffractiveIndex) * 0.01);
#else
    float2 diffractedUv = uv + (viewNorm.xy * MaterialInfo(DiffractiveIndex) * 0.01);
#endif
    float2 diffractedUvMotion = uvMotion + (viewNorm.xy * MaterialInfo(DiffractiveIndex) * 0.01);
    float2 clampedDiffractUv = clamp(diffractedUv.xy, 0, 1 - (pixel.xy * 30));
    float2 clampedDiffractUvMotion = clamp(diffractedUvMotion.xy, 0, 1 - (pixel.xy * 30));

    float opaqueDepth = DiffractionAlbedoParams(OpaqueDepth).Sample(PointSampler, uv).r;
    float diffractDepth = DiffractionAlbedoParams(DiffractDepth).Sample(PointSampler, uvMotion).r;

    if (clampedDiffractUv.x != diffractedUv.x || clampedDiffractUv.y != diffractedUv.y || opaqueDepth > diffractDepth || diffractDepth == 0)
    {
        return DiffractionAlbedoParams(Albedo).Sample(PointSampler, uv).rgb;
    }

    opaqueDepth = DiffractionAlbedoParams(OpaqueDepth).Sample(PointSampler, clampedDiffractUv).r;
    diffractDepth = DiffractionAlbedoParams(DiffractDepth).Sample(PointSampler, clampedDiffractUvMotion).r;

    float3 color;
    if (opaqueDepth > diffractDepth)
    {
        float2 width = pixel * 2.5;
        color = SampleAlbedoClamp(uv, diffractedUv, diffractedUvMotion, float2(width.x, width.y));
        color += SampleAlbedoClamp(uv, diffractedUv, diffractedUvMotion, float2(-width.x, width.y));
        color += SampleAlbedoClamp(uv, diffractedUv, diffractedUvMotion, float2(width.x, -width.y));
        color += SampleAlbedoClamp(uv, diffractedUv, diffractedUvMotion, float2(-width.x, -width.y));
        color /= 4;
    }
    else
    {
        color = DiffractionAlbedoParams(Albedo).Sample(AnisoSampler, clampedDiffractUv).rgb;
    }

    color.rgb = lerp(color.rgb, OrigColor.rgb, OrigColor.a) * MaterialInfo(Tint) * ao;
    return color;
}
#endif

float2 SampleBump(float2 uv, float4 weights)
{
#if defined(FOUR_LAYER_BUMP)
    float2 result = 0.;
    result += weights.r * slopeFromUnorm8(MaterialInfo2(NormalMap1).Sample(AnisoSampler, uv * MaterialInfo(UvScales).x).xy);
    result += weights.g * slopeFromUnorm8(MaterialInfo2(NormalMap2).Sample(AnisoSampler, uv * MaterialInfo(UvScales).y).xy);
    result += weights.b * slopeFromUnorm8(MaterialInfo2(NormalMap3).Sample(AnisoSampler, uv * MaterialInfo(UvScales).z).xy);
    result += weights.a * slopeFromUnorm8(MaterialInfo2(NormalMap4).Sample(AnisoSampler, uv * MaterialInfo(UvScales).w).xy);
    return result;
#elif defined(THREE_LAYER_BUMP)
    float2 result = 0.;
    result += weights.r * slopeFromUnorm8(MaterialInfo2(NormalMap1).Sample(AnisoSampler, uv * MaterialInfo(UvScales).x).xy);
    result += weights.g * slopeFromUnorm8(MaterialInfo2(NormalMap2).Sample(AnisoSampler, uv * MaterialInfo(UvScales).y).xy);
    result += weights.b * slopeFromUnorm8(MaterialInfo2(NormalMap3).Sample(AnisoSampler, uv * MaterialInfo(UvScales).z).xy);
    return result;
#elif defined(SCROLLING)
    return slopeFromUnorm8(MaterialInfo2(NormalMap).SampleBias(AnisoSampler, uv, CameraInfo(GlobalMipBias)).xy);
#else
#if defined(PIXELIZE)
    return slopeFromUnorm8(MaterialInfo2(NormalMap).SampleBias(PointSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias)).xy);
#else
    return slopeFromUnorm8(MaterialInfo2(NormalMap).SampleBias(AnisoSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias)).xy);
#endif
#endif
}

float3 GetNonuniformDetailScale()
{
#ifdef ONE_LAYER_DETAIL_BUMP
    // fixing maya export bug in pixel shader... definitely going to hell for this
    return MaterialInfo(DetailScale) == 0. ? 1. : MaterialInfo(DetailScale);
#else
    return 1.;
#endif
}

float2 ApplyBumpDetail(float2 bumpSlope, float2 uv, float4 weights)
{
#ifdef ONE_LAYER_DETAIL_BUMP
    float3 detailScale = GetNonuniformDetailScale();
    return bumpSlope + slopeFromUnorm8(MaterialInfo2(DetailNormalMap).Sample(AnisoSampler, uv * detailScale.xy).xy, detailScale.z) * maxNormalize(detailScale.xy);
#elif defined(THREE_LAYER_DETAIL_BUMP)
    float2 result = bumpSlope;
    result += weights.r * slopeFromUnorm8(MaterialInfo2(DetailNormalMap1).Sample(AnisoSampler, uv * MaterialInfo(DetailScale).x).xy);
    result += weights.g * slopeFromUnorm8(MaterialInfo2(DetailNormalMap2).Sample(AnisoSampler, uv * MaterialInfo(DetailScale).y).xy);
    result += weights.b * slopeFromUnorm8(MaterialInfo2(DetailNormalMap3).Sample(AnisoSampler, uv * MaterialInfo(DetailScale).z).xy);
    return result;
#else
    return bumpSlope;
#endif
}

float2 ApplyRoughnessDetail(float2 variance, float2 uv, float4 weights)
{
#ifdef ONE_LAYER_DETAIL_BUMP
    float3 detailScale = GetNonuniformDetailScale();
    return variance + square(MaterialInfo2(DetailRoughnessMap).Sample(AnisoSampler, uv * detailScale.xy).xy * detailScale.z * maxNormalize(detailScale.xy));
#elif defined(THREE_LAYER_DETAIL_BUMP)
    float2 result = variance;
    result += square(weights.r * MaterialInfo2(DetailRoughnessMap1).Sample(AnisoSampler, uv * MaterialInfo(DetailScale).x).xy);
    result += square(weights.g * MaterialInfo2(DetailRoughnessMap2).Sample(AnisoSampler, uv * MaterialInfo(DetailScale).y).xy);
    result += square(weights.b * MaterialInfo2(DetailRoughnessMap3).Sample(AnisoSampler, uv * MaterialInfo(DetailScale).z).xy);
    return result;
#else
    return variance;
#endif
}

float2 SampleRoughness(float2 uv, float4 weights)
{
#if defined(FORCE_MAX_ROUGHNESS)
    return 1.5;
#elif defined(THREE_LAYER_BUMP)
    float2 result = 1. / 32768.;
    result += weights.r * MaterialInfo2(RoughnessMap1).Sample(AnisoSampler, uv * MaterialInfo(UvScales).x).xy;
    result += weights.g * MaterialInfo2(RoughnessMap2).Sample(AnisoSampler, uv * MaterialInfo(UvScales).y).xy;
    result += weights.b * MaterialInfo2(RoughnessMap3).Sample(AnisoSampler, uv * MaterialInfo(UvScales).z).xy;
    return result;
#elif defined(SCROLLING)
#if defined(PIXELIZE)
    float2 roughness = MaterialInfo2(RoughnessMap).Sample(PointSampler, uv).xy;
#else
    float2 roughness = MaterialInfo2(RoughnessMap).Sample(AnisoSampler, uv).xy;
#endif
    roughness += 1. / 32768.;
    return roughness;
#else
#if defined(PIXELIZE)
    float2 roughness = MaterialInfo2(RoughnessMap).Sample(PointSampler, uv * MaterialInfo(UvScale)).xy;
#else
    float2 roughness = MaterialInfo2(RoughnessMap).Sample(AnisoSampler, uv * MaterialInfo(UvScale)).xy;
#endif
    roughness += 1. / 32768.;
    return roughness;
#endif
}

float SampleMetalness(float2 uv, float4 weights)
{
#if defined(FORCE_DIELECTRIC)
    return 0.;
#elif defined(THREE_LAYER_METAL)
    float result = 0.;
    result += weights.r * MaterialInfo2(MetalnessMap1).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).x, CameraInfo(GlobalMipBias)).x;
    result += weights.g * MaterialInfo2(MetalnessMap2).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).y, CameraInfo(GlobalMipBias)).x;
    result += weights.b * MaterialInfo2(MetalnessMap3).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales).z, CameraInfo(GlobalMipBias)).x;
    return result;
#elif defined(SCROLLING)
    return MaterialInfo2(MetalnessMap).SampleBias(AnisoSampler, uv, CameraInfo(GlobalMipBias)).x;
#else
#if defined(PIXELIZE)
    return MaterialInfo2(MetalnessMap).SampleBias(PointSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias)).x;
#else
    return MaterialInfo2(MetalnessMap).SampleBias(AnisoSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias)).x;
#endif
#endif
}

#if !defined (FOUR_LAYER_ALBEDO) && !defined(THREE_LAYER_ALBEDO)
float SampleAmbientOcclusion(float2 uv, float4 weights)
{
#if defined(FORCE_DIELECTRIC)
    return 0.;
#elif defined(THREE_LAYER_AO)
    return MaterialInfo(AmbientOcclusionMap).SampleBias(AnisoSampler, uv * MaterialInfo(UvScales), CameraInfo(GlobalMipBias)).x;
#elif defined(SCROLLING)
    return MaterialInfo(AmbientOcclusionMap).SampleBias(AnisoSampler, uv, CameraInfo(GlobalMipBias)).x;
#else
#if defined (AMBIENTOCCLUSION)
#if defined(PIXELIZE)
    return MaterialInfo(AmbientOcclusionMap).SampleBias(PointSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias)).x;
#else
    return MaterialInfo(AmbientOcclusionMap).SampleBias(AnisoSampler, uv * MaterialInfo(UvScale), CameraInfo(GlobalMipBias)).x;
#endif
#endif
#endif
}
#endif

struct VertexOutput
{
    float4 position : SV_Position;
    float4 history : TEXCOORD0;
    half4 tangent_u : TEXCOORD1;
    half4 bitangent_v : TEXCOORD2;
    half3 view : TEXCOORD3;
    nointerpolation half handedness : TEXCOORD4;

#ifdef SCROLLING
    // uvScrollRate.xy is the actual scroll rate
    // uvScrollRate.zw is the uv coordinate with scrolling applied
    HALF_UVCOORD uvScrollRate : TEXCOORD5;
#endif

#if defined(FRESNEL)
    float fresnel : TEXCOORD6;
#endif

    float stereoClip : SV_ClipDistance;
};

struct PixelInput
{
    float4 position : SV_Position;
    float4 history : TEXCOORD0;
    half4 tangent_u : TEXCOORD1;
    half4 bitangent_v : TEXCOORD2;
    half3 view : TEXCOORD3;
    nointerpolation half handedness : TEXCOORD4;

#ifdef SCROLLING
    // uvScrollRate.xy is the actual scroll rate
    // uvScrollRate.zw is the uv coordinate with scrolling applied
    HALF_UVCOORD uvScrollRate : TEXCOORD5;
#endif

#if defined(FRESNEL)
    float fresnel : TEXCOORD6;
#endif
};

float2 EncodeNormal(float3 normal, bool isFrontFace)
{
    normal = isFrontFace ? normal : -normal;
    float2 encoded = float3ToOctahedral2(normal);
    return encoded;
}

NormalRoughnessInfo GenerateNormalRoughnessInfo(float2 bump, float2 variance, float rawRoughness, float3 tangent, float3 bitangent, float3 view, float handedness, bool isFrontFace, float positionClipW, inout float3 normal)
{
    NormalRoughnessInfo result;

    float3 geoNormal = normalize(cross(tangent, bitangent));

    // build surface slope basis as reciprocal of tangent basis (important for correct stretching/shearing)
    float3 du = cross(bitangent, geoNormal);
    float3 dv = cross(geoNormal, tangent);
    du /= dot(du, tangent);
    dv /= dot(dv, bitangent);

    normal = geoNormal * handedness + bump.x * du + bump.y * dv;

#ifdef COMPACT_GBUFFER
    result.normalOctUnorm = snormToUnorm(EncodeNormal(normal, isFrontFace));
    result.roughness = sqrt(0.5 * (variance.x + variance.y));
#else

    normal = normalize(normal);
    float rcpw = rcp(positionClipW);

    result.normalOct = EncodeNormal(normal, isFrontFace);
    result.dInvW_dXY = float2(ddx(rcpw), ddy(rcpw));

    float3 viewtangent = normalize(view - normal * dot(view, normal));
    float3 viewbitangent = cross(normal, viewtangent);
    float2 roughness = sqrt(variance);

    // pixel footprint spec filter
    float3 geoDeviationBounds = abs(ddx_fine(geoNormal)) + abs(ddy_fine(geoNormal));

    roughness.x = max(roughness.x, 0.5 * dot(abs(tangent), geoDeviationBounds));
    roughness.y = max(roughness.y, 0.5 * dot(abs(bitangent), geoDeviationBounds));

    // project into view-tangent covariance
    float2x2 covar = mul(float2x3(du, dv), transpose(float2x3(viewtangent, viewbitangent)));
    covar[0] *= roughness.x;
    covar[1] *= roughness.y;
    covar = mul(transpose(covar), covar);

    result.varCovar.x = sqrt(covar[0].x); // roughness
    result.varCovar.y = sqrt(covar[1].y); // roughness
    result.varCovar.z = covar[0].y * rsqrt(covar[0].x * covar[1].y); // correlation coefficient

    result.roughness = rawRoughness;
#endif

    return result;
}

PixelOutput PopulateGbuffer(float3 color, float opacity, float metalness, NormalRoughnessInfo normalRoughness, float2 motion, bool immobile)
{
    PixelOutput output;

#if defined(DIFFRACTION)
    output.color0.rgb = color;
#else
#ifdef COMPACT_GBUFFER
    {
        output.color0.rgb = color;
        output.color0.w = metalness;
        output.color1.xy = normalRoughness.normalOctUnorm;
        output.color1.z = normalRoughness.roughness;
        output.color1.w = opacity;
        output.color2.xy = motion;
    }
#else
    {
        output.color0.rgb = color;
        output.color0.w = metalness;
        output.color1.xy = normalRoughness.normalOct;
        //output.color1.zw = normalRoughness.dInvW_dXY;
        output.color1.z = getSubsurface(); // subsurface scatter
        output.color1.w = normalRoughness.roughness;
        output.color2.xyz = normalRoughness.varCovar;
        output.color2.x = immobile ? -output.color2.x : output.color2.x; // smuggle immobility flag in sign bit
        output.color2.w = opacity;
        output.color3.xy = motion;
    }
#endif
#endif

    return output;
}

PixelOutput PixelMain(PixelInput input, bool isFrontFace : SV_IsFrontFace)
{
#ifndef TWO_SIDED
    isFrontFace = true;
#endif


#ifdef SCROLLING
    float4 uv = float4(input.tangent_u.w, input.bitangent_v.w, 0, 0);
    float4 uvAO;
#else
    float2 uv = float2(input.tangent_u.w, input.bitangent_v.w);
    float2 uvAO;
#endif

#if defined(SCREENSPACEUV)
    if (!stereoIsEnabled())
    {
        uv.xy = input.position.xy / CameraInfo(RenderViewportSize).xy;
        uv.xy *= MaterialInfo(ScreenSpaceUVScale);
    }
    else
    {
        // Screen space to Clip Space
        float z = 10.0; // z is irrelevant since we don't apply the perspective divide
        float4 clipCoord = pixelCoordToClipCoord(input.position.xy, z, 0., CameraInfo(RcpRenderViewportSize), float2(0.0,0.0));

        // Clip Space to Eye Space
        bool stereoSide = (input.position.x >= CameraInfo(RenderViewportSize).x*0.5);
        float4 pseudoEyeCoord = transform(stereoGetClipToEye(stereoSide), clipCoord);

        // Map Eye Space to Texture UV Space
        uv.x = +0.5*pseudoEyeCoord.x + 0.5; // Eye space X goes right
        uv.y = -0.5*pseudoEyeCoord.z + 0.5; // Eye space Z goes up
    }
#endif

#if defined(SCROLLING)
    HALF_UVCOORD origUvScrollRate = input.uvScrollRate;
#if defined(SCREENSPACEUV)
    origUvScrollRate.xy = uv.xy;
    float localTimeSeconds = WorldInfo(WorldTimeSeconds) - ModelInfo(InitialTime);
    float2 scrollOffset = MaterialInfo(ScrollRate) * localTimeSeconds;
    scrollOffset -= floor(scrollOffset);
    float frame = MaterialInfo(Frame) + MaterialInfo(StepRate) * localTimeSeconds;
    float2 frameSize = (1.0 / MaterialInfo(UvFrames));
    float frameNumber = trunc(frame);
    origUvScrollRate.z = frameSize.x * (uv.x + frac(frameNumber * frameSize.x) * MaterialInfo(UvFrames).x) + scrollOffset.x;
    origUvScrollRate.w = frameSize.y * (uv.y + frac(trunc(frameNumber * frameSize.x) * frameSize.y) * MaterialInfo(UvFrames).y) + scrollOffset.y;
#endif
#endif


    HALF_UVCOORD uvScrollRate;
#if defined(SCROLLING)
    float2 inputUv = uv.xy;
    if (clampUVs())
    {
        //getScrollingUV will return uv.xy if 'scrollXXXX == false' otherwise will return uv.zw
        float localTimeSeconds = WorldInfo(WorldTimeSeconds) - ModelInfo(InitialTime);
        float2 scrollOffset = MaterialInfo(ScrollRate) * localTimeSeconds;
        scrollOffset -= floor(scrollOffset);
        float2 frameSize = (1.0 / MaterialInfo(UvFrames));
        float frameNumber = trunc(MaterialInfo(Frame) + MaterialInfo(StepRate) * localTimeSeconds);
        frameNumber = loopFlipbook() ? frameNumber : min(frameNumber, MaterialInfo(MaxFrames));
        frameNumber = frac(frameNumber / MaterialInfo(MaxFrames)) * MaterialInfo(MaxFrames);
        uvScrollRate = float4(origUvScrollRate.x, origUvScrollRate.y, 0, 0);
        uvScrollRate.z = frameSize.x * (frac(inputUv.x) + frac(frameNumber * frameSize.x) * MaterialInfo(UvFrames).x) + scrollOffset.x;
        uvScrollRate.w = frameSize.y * (frac(inputUv.y) + frac(trunc(frameNumber * frameSize.x) * frameSize.y) * MaterialInfo(UvFrames).y) + scrollOffset.y;
        uv = float4(inputUv.xy, uvScrollRate.zw);
    }
    else
    {
        uvScrollRate = origUvScrollRate;
        uv = float4(inputUv.xy, origUvScrollRate.zw);
    }
    uv = float4(CalcualteFluidDistortion(uv.xy), CalcualteFluidDistortion(uv.zw));
#elif defined(UVOFFSET)
    float2 uvNoOffset = uv.xy;
    if (offsetAlbedoUv()) uv = GetUvRotation(uv.xy + MaterialInfo(UvOffsets).xy);
    else uv = GetUvRotation(uv.xy);
#else
    uv = GetUvRotation(uv.xy);
#endif

#if defined(PIXELIZE)
    float halfPixel = 0.5 / MaterialInfo(PixelSize);
    uv.x = (floor(frac(uv.x) * (MaterialInfo(PixelSize))) / MaterialInfo(PixelSize)) + halfPixel;
    uv.y = (floor(frac(uv.y) * (MaterialInfo(PixelSize))) / MaterialInfo(PixelSize)) + halfPixel;
#if defined(SCROLLING)
    uv.z = (floor(frac(uv.z) * (MaterialInfo(PixelSize))) / MaterialInfo(PixelSize)) + halfPixel;
    uv.w = (floor(frac(uv.w) * (MaterialInfo(PixelSize))) / MaterialInfo(PixelSize)) + halfPixel;
#endif
#endif

    // stabilize UVs for TAA
    //uv.xy += (ddx(uv.xy)*CameraInfo(TemporalJitter).x*CameraInfo(RenderViewportSize).x - ddy(uv.xy)*CameraInfo(TemporalJitter).y*CameraInfo(RenderViewportSize).y)*0.5;

    float4 layerWeights = SampleLayerWeights(uv);

    float4 color = SampleAlbedo(getScrollingUV(uv, scrollAlbedoUv()), layerWeights);

#if defined(FRESNEL)
    uint fresnelFlags = MaterialInfo(FresnelFlags);
    bool blendAlbedo2 = fresnelFlags & cFresnelFlag_BlendAlbedo2;

    if (blendAlbedo2)
    {
        float4 color2 = SampleAlbedo2(getScrollingUV(uv, scrollAlbedoUv()), layerWeights);
        color = lerp(color, color2, input.fresnel);
    }
#endif

    float ao = 1;
#if defined (FOUR_LAYER_ALBEDO) || defined(THREE_LAYER_ALBEDO)
    color.rgb *= MaterialInfo(Tint);
#elif defined (AMBIENTOCCLUSION)
#if defined(UVOFFSET)
        uvAO = uvNoOffset;
        if (offsetAmbientOcclusionUv()) uvAO = GetUvRotation(uvNoOffset.xy + MaterialInfo(UvOffsets).xy);
#else
        uvAO = uv;
#endif
    ao = SampleAmbientOcclusion(getScrollingUV(uvAO, scrollAmbientOcclusion()), layerWeights);
    ao = lerp(1, ao, MaterialInfo(AmbientOcclusionStrength));
    color.rgb *= MaterialInfo(Tint) * ao;
#else
    color.rgb *= MaterialInfo(Tint);
#endif

    float opacity = AlphaTest(color.a, input.position.xy, input.view, uv);

    float3 view = input.view;

    float roughnessForReflections;
    float2 roughness;
    float2 bump, variance, textureMotion;
    {
        float2 uvBump = getScrollingUV(uv, scrollBumpUv());
        bump = SampleBump(uvBump, layerWeights);
        bump = ApplyBumpDetail(bump, uvBump, layerWeights);
        roughness = SampleRoughness(uvBump, layerWeights);
        variance = square(roughness);
        variance = ApplyRoughnessDetail(variance, uvBump, layerWeights);
        roughnessForReflections = sqrt(0.5 * (variance.x + variance.y));
#ifdef REFLECTIONFLAGS
        // The design intent behind this is to smuggle reflection disable and compatibility
        // flags within the exponent and sign bits of the roughness value as described below.
        // Roughness ==  1.0  is used to disable reflection
        // Rougeness == -1.0 will skip motions during reprojection
        // Roughness == -2.0 will skip temporal accumulation during reflection
        roughnessForReflections = reflectionReceiver() ? roughnessForReflections : 1.0;
#endif
        roughnessForReflections = getSubsurface() ? 1.0 : roughnessForReflections;

#ifdef SCROLLING
        float2x2 dUVdXY = float2x2(ddx(uvBump), ddy(uvBump));
        float dXYdUV_norm = rcp(determinant(dUVdXY));
        dXYdUV_norm = abs(dXYdUV_norm) < 1e8f ? dXYdUV_norm : 1.;
        textureMotion = opacity < 0.5 || fixScrollShimmer() ? 0. : mul(uvScrollRate.xy, adjugate2x2(dUVdXY)) * dXYdUV_norm;    // dxy/dt == duv/dt  /  duv/dxy
#else
        textureMotion = 0.;
#endif
    }

    float3 normal;
    NormalRoughnessInfo normalRoughness = GenerateNormalRoughnessInfo(bump, variance, roughnessForReflections, input.tangent_u.xyz, input.bitangent_v.xyz, view, input.handedness, isFrontFace, input.position.w, normal);

    float metalness = SampleMetalness(getScrollingUV(uv, scrollMetalnessUv()), layerWeights);

#if 0//defined(COMPILING_Materials_High_OpaqueSingleLayerSkinnedProgram) || defined(COMPILING_Materials_High_OpaqueSingleLayerDetailSkinnedProgram)
    color.rgb = float3(1., 0.71, 0.29);
    metalness = 1.;
    //normalRoughness.varCovar.xy *= 0.1;
#endif

#if defined (DIFFRACTION)
    color.rgb = SampleDiffractedAlbedo(input.position.xy, normal, color, ao, input.view);
#endif

    float2 motion = calculatePixelMotion(CameraInfo(RenderViewportOffset), CameraInfo(RenderViewportSize), input.position, input.history) - textureMotion;
#ifdef SCREENSPACEUV
    if (!stereoIsEnabled())
    {
        motion = (0).xx;
    }
#endif

    return PopulateGbuffer(color.rgb, opacity, metalness, normalRoughness, motion, abs(input.handedness) - 1. != 0.);
}
