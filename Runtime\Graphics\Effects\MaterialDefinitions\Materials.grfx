local variant = current_variant()

local core = Common.Core
local effects = import("Effects.grfx")

EmissiveMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    EmissiveIntensity = float(1.0),
    Absorption = float(1),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    UvFluidDistortion = float(0.0),
    UvFluidDistortionAmp = float(0.1),
    UvFluidDistortionScale = float(25.0),
    UvFrames = float2(1.0, 1.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    TrailsDecay = float(1.0),
    ScrollRate = float2(0.0, 0.0),
    ScrollFlags = uint(0),
    Frame = float(0.0),
    EmissiveMap = texture2d(float4),
    MaskMap = texture2d(float4),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

BillboardMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    Brightness = float(1),
    Absorption = float(1),
    UvScale = float(1.0),
    Lollipop = uint(0),
    InheritOrientation = uint(0),
    ScrollFlags = uint(0),
    UvFrames = float2(1.0, 1.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    ScrollRate = float2(0.0, 0.0),
    Frame = float(0.0),
    EmissiveMap = texture2d(float4),
    MaskMap = texture2d(float4),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

BillboardPixelizeMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    PixelSize = float(50.0),
    Brightness = float(1),
    Absorption = float(1),
    UvScale = float(1.0),
    Lollipop = uint(0),
    InheritOrientation = uint(0),
    ScrollFlags = uint(0),
    UvFrames = float2(1.0, 1.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    ScrollRate = float2(0.0, 0.0),
    Frame = float(0.0),
    EmissiveMap = texture2d(float4),
    MaskMap = texture2d(float4),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

OpaqueSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    OffsetFlags = uint(0),
}

OpaqueVATSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(1.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    VatFlags = uint(0),
    VATMap = texture2d(float4),
    VATNormalsMap = texture2d(float4),
    VATScale = float3(1,1,1),
    StepRate = float(0.0),
    Frame = float(0.0),
    RenderFlags = uint(0),
}

OpaqueMaskSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    UvRotate = float(0.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(1.0),
    AlbedoMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    OffsetFlags = uint(0),
    RenderFlags = uint(0),
}
OpaqueMaskSingleLayerMaterialInfo2 = draw_parameters {
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
}
OpaqueEnvironmentMaskSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    UvRotate = float(0.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(1.0),
    AlbedoMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    SkyboxIntensity = float(1.0),
    OffsetFlags = uint(0),
    RenderFlags = uint(0),
}
OpaqueEnvironmentMaskSingleLayerMaterialInfo2 = draw_parameters {
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
}

OpaquePixelizeMaskSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    PixelSize = float(50.0),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(1.0),
    AlbedoMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    RenderFlags = uint(0),
}

OpaqueDiffractMaskSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(1.0),
    AlbedoMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DiffractiveIndex = float(-1.3),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

OpaqueEmissiveSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    ScreenSpaceUVScale = float(1.0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    OffsetFlags = uint(0),
}

TwoSidedOpaqueEmissiveSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    ScreenSpaceUVScale = float(1.0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
    OffsetFlags = uint(0),
}

OpaqueEmissiveVATSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    UvRotate = float(0.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(1.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    OffsetFlags = uint(0),
    VatFlags = uint(0),
    VATMap = texture2d(float4),
    VATNormalsMap = texture2d(float4),
    VATScale = float3(1,1,1),
    StepRate = float(0.0),
    Frame = float(0.0),
    RenderFlags = uint(0),
}

OpaqueEnvironmentSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    SkyboxIntensity = float(1.0),
    OffsetFlags = uint(0),
}

OpaqueEmissiveFresnelSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    FresnelTint = float3(1,1,1),
    FresnelExponent = float(1.0),
    FresnelIntensity = float(1.0),
    FresnelBias = float(1.0),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    FresnelFlags = uint(0),
    AlbedoMap = texture2d(float4),
    AlbedoMap2 = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveMap2 = texture2d(float4),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
}

TwoSidedOpaqueEmissiveFresnelSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    FresnelTint = float3(1,1,1),
    FresnelExponent = float(1.0),
    FresnelIntensity = float(1.0),
    FresnelBias = float(1.0),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    FresnelFlags = uint(0),
    AlbedoMap = texture2d(float4),
    AlbedoMap2 = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveMap2 = texture2d(float4),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
}

OpaqueEmissivePixelizeSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    PixelSize = float(50.0),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
}

TwoSidedOpaqueEmissivePixelizeSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    PixelSize = float(50.0),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
}

StereographicOpaqueEmissiveSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveIntensity = float(1.0),
    RotationFactor = float(0.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

ScrollingOpaqueEmissiveSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveMask = texture2d(float4),
    EmissiveIntensity = float(1.0),
    UvFrames = float2(1.0, 1.0),
    UvRotate = float(0.0),
    UvFluidDistortion = float(0.0),
    UvFluidDistortionAmp = float(0.1),
    UvFluidDistortionScale = float(25.0),
    ScreenSpaceUVScale = float(1.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    ScrollRate = float2(0.0, 0.0),
    ScrollFlags = uint(0),
    Frame = float(0.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveMask = texture2d(float4),
    EmissiveIntensity = float(1.0),
    UvFrames = float2(1.0, 1.0),
    UvRotate = float(0.0),
    UvFluidDistortion = float(0.0),
    UvFluidDistortionAmp = float(0.1),
    UvFluidDistortionScale = float(25.0),
    ScreenSpaceUVScale = float(1.0),
    MaxDisplacementClamp = float(1.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    ScrollRate = float2(0.0, 0.0),
    ScrollFlags = uint(0),
    Frame = float(0.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(0.0),
    PixelSize = float(50.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveMask = texture2d(float4),
    EmissiveIntensity = float(1.0),
    UvFrames = float2(1.0, 1.0),
    UvRotate = float(0.0),
    UvFluidDistortion = float(0.0),
    UvFluidDistortionAmp = float(0.1),
    UvFluidDistortionScale = float(25.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    ScrollRate = float2(0.0, 0.0),
    ScrollFlags = uint(0),
    Frame = float(0.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo = draw_parameters {
    AlbedoMap = texture2d(float4),
    Tint = float3(1,1,1),
    EmissiveIntensity = float(1.0),
    UvFrames = float2(1.0, 1.0),
    UvRotate = float(0.0),
    UvFluidDistortion = float(0.0),
    UvFluidDistortionAmp = float(0.1),
    UvFluidDistortionScale = float(25.0),
    ScrollRate = float2(0.0, 0.0),
    MaskThreshold = float(0.5),
    MaskSoftness = float(0.0),
    StepRate = float(256.0),
    ScrollFlags = uint(0),
    MaxFrames = float(256.0),
    Frame = float(0.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}
ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo2 = draw_parameters {
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveMask = texture2d(float4),
}

ScrollingOpaqueMaskSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    MaskThreshold = float(0.5),
    MaskSoftness = float(0.0),
    UvFrames = float2(1.0, 1.0),
    UvRotate = float(0.0),
    UvFluidDistortion = float(0.0),
    UvFluidDistortionAmp = float(0.1),
    UvFluidDistortionScale = float(25.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    ScrollRate = float2(0.0, 0.0),
    ScrollFlags = uint(0),
    Frame = float(0.0),
    AlbedoMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
    RenderFlags = uint(0),
}
ScrollingOpaqueMaskSingleLayerMaterialInfo2 = draw_parameters {
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
}
ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    MaskThreshold = float(0.5),
    MaskSoftness = float(0.0),
    PixelSize = float(50.0),
    UvFrames = float2(1.0, 1.0),
    UvRotate = float(0.0),
    UvFluidDistortion = float(0.0),
    UvFluidDistortionAmp = float(0.1),
    UvFluidDistortionScale = float(25.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    ScrollRate = float2(0.0, 0.0),
    ScrollFlags = uint(0),
    Frame = float(0.0),
    AlbedoMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
    Lollipop = uint(0),
    InheritOrientation = uint(0),
    RenderFlags = uint(0),
}

ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    MaskThreshold = float(0.5),
    MaskSoftness = float(0.0),
    UvFrames = float2(1.0, 1.0),
    UvRotate = float(0.0),
    UvFluidDistortion = float(0.0),
    UvFluidDistortionAmp = float(0.1),
    UvFluidDistortionScale = float(25.0),
    StepRate = float(0.0),
    MaxFrames = float(256.0),
    ScrollRate = float2(0.0, 0.0),
    ScrollFlags = uint(0),
    Frame = float(0.0),
    AlbedoMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DiffractiveIndex = float(-1.3),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

OpaqueSingleLayerDetailMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    DetailNormalMap = texture2d(float4),
    DetailRoughnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveMask = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DetailScale = float3(1,1,1),
    OffsetFlags = uint(0),
}

OpaqueMaskSingleLayerDetailMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    UvRotate = float(0.0),
    DetailScale = float3(1,1,1),
    MaskThreshold = float(0.5),
    MaskSoftness = float(1.0),
    AlbedoMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    OffsetFlags = uint(0),
    RenderFlags = uint(0),
}
OpaqueMaskSingleLayerDetailMaterialInfo2 = draw_parameters {
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    DetailNormalMap = texture2d(float4),
    DetailRoughnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveMask = texture2d(float4),
}

OpaqueSubsurfaceSingleLayerDetailMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    BleedTint = float3(1,0.2,0.24),
    BleedDistance = float(1.),
    DetailScale = float3(1,1,1),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    DetailNormalMap = texture2d(float4),
    DetailRoughnessMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
}

OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    BleedTint = float3(1,0.2,0.24),
    BleedDistance = float(1.),
    DetailScale = float3(1,1,1),
    MaskThreshold = float(0.5),
    MaskSoftness = float(1.0),
    AlbedoMap = texture2d(float4),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    RenderFlags = uint(0),
}
OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo2 = draw_parameters {
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    DetailNormalMap = texture2d(float4),
    DetailRoughnessMap = texture2d(float4),
}

OpaqueEmissiveSingleLayerDetailMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    DetailNormalMap = texture2d(float4),
    DetailRoughnessMap = texture2d(float4),
    DetailScale = float3(1,1,1),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
    OffsetFlags = uint(0),
}

TwoSidedOpaqueEmissiveSingleLayerDetailMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvOffsets = float3(0,0,0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    DetailNormalMap = texture2d(float4),
    DetailRoughnessMap = texture2d(float4),
    DetailScale = float3(1,1,1),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    OffsetFlags = uint(0),
}

OpaqueDiffuseMultiLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    BlendMap = texture2d(float4),
    AlbedoMap1 = texture2d(float4),
    AlbedoMap2 = texture2d(float4),
    AlbedoMap3 = texture2d(float4),
    AlbedoMap4 = texture2d(float4),
    NormalMap1 = texture2d(float4),
    NormalMap2 = texture2d(float4),
    NormalMap3 = texture2d(float4),
    NormalMap4 = texture2d(float4),
    UvScales = float4(1,1,1,1),
    UvRotate = float(0.0),
}

OpaqueTripleLayerDetailMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    BlendMap = texture2d(float4),
    AlbedoMap1 = texture2d(float4),
    AlbedoMap2 = texture2d(float4),
    AlbedoMap3 = texture2d(float4),
    NormalMap1 = texture2d(float4),
    NormalMap2 = texture2d(float4),
    NormalMap3 = texture2d(float4),
    RoughnessMap1 = texture2d(float4),
    RoughnessMap2 = texture2d(float4),
    RoughnessMap3 = texture2d(float4),
    MetalnessMap1 = texture2d(float4),
    MetalnessMap2 = texture2d(float4),
    MetalnessMap3 = texture2d(float4),
    DetailNormalMap1 = texture2d(float4),
    DetailNormalMap2 = texture2d(float4),
    DetailNormalMap3 = texture2d(float4),
    DetailRoughnessMap1 = texture2d(float4),
    DetailRoughnessMap2 = texture2d(float4),
    DetailRoughnessMap3 = texture2d(float4),
    DetailScale = float3(1,1,1),
    UvScales = float4(1,1,1),
    UvRotate = float(0.0),
}

TransmissiveEmissiveMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveIntensity = float(1.0),
}

TransparentMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    BlendFlags = uint(0),
    RenderFlags = uint(0),
}

TransparentMultibumpMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    ScrollRate = float2(0.3, 0.0),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    UvFluidDistortion = float(0.0),
    UvFluidDistortionAmp = float(0.1),
    UvFluidDistortionScale = float(25.0),
    ScrollFlags = uint(0),
    BlendFlags = uint(0),
    WavelengthDecay = float(0.618033989),
    BumpScale = float(1.0),
    BumpScaleDecay = float(0.5),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    DiffractiveIndex = float(-1.3),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
    SkyboxIntensity = float(1.0),
    RenderFlags = uint(0),
}

ChromaKeySingleLayerMaterialInfo = draw_parameters {
    Tint = float3(1,1,1),
    ChromaKeyColor = float3(1,1,1),
    HueThreshold = float(1.0),
    SaturationThreshold = float(1.0),
    ValueThreshold = float(1.0),
    HueThresholdGlare = float(1.0),
    SaturationThresholdGlare = float(1.0),
    ValueThresholdGlare = float(1.0),
    UvScale = float(1.0),
    UvRotate = float(0.0),
    AlbedoMap = texture2d(float4),
    NormalMap = texture2d(float4),
    RoughnessMap = texture2d(float4),
    MetalnessMap = texture2d(float4),
    EmissiveMap = texture2d(float4),
    EmissiveIntensity = float(1.0),
    AmbientOcclusionMap = texture2d(float4),
    AmbientOcclusionStrength = float(1.0),
    DisplacementMap = texture2d(float4),
    DisplacementFactor = texture2d(float4),
    DisplacementScale = float3(1,1,1),
}

PositionOnlyStaticVertexInput = vertex_input { position = float3 }
PositionOnlySkinnedVertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 }

PositionUvStaticVertexInput = vertex_input { position = float3, texCoord0 = half2 }
PositionUvSkinnedVertexInput = vertex_input { position = float3, texCoord0 = half2, blendWeights = byte4_unorm, blendIndices = byte4 }

SingleUvStaticVertexInput = vertex_input { position = float3, texCoord0 = half2, tangent = half4, bitangent = half4 }
SingleUvSkinnedVertexInput = vertex_input { position = float3, texCoord0 = half2, tangent = half4, bitangent = half4, blendWeights = byte4_unorm, blendIndices = byte4 }

SingleUvTangentlessStaticVertexInput = vertex_input { position = float3, texCoord0 = half2 }
SingleUvTangentlessSkinnedVertexInput = vertex_input { position = float3, texCoord0 = half2, blendWeights = byte4_unorm, blendIndices = byte4 }

DoubleUvStaticVertexInput = vertex_input { position = float3, texCoord0 = half2, tangent = half4, bitangent = half4, texCoord1 = half2 }
DoubleUvSkinnedVertexInput = vertex_input { position = float3, texCoord0 = half2, tangent = half4, bitangent = half4, blendWeights = byte4_unorm, blendIndices = byte4, texCoord1 = half2 }

local opaqueDepthOnlyCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo },
    PixelOutput = Common.Targets.JustDepth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
}

local DiffractDepthOnlyCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo },
    PixelOutput = Common.Targets.JustDepth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
}

local TransDiffractDepthOnlyCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.JustDepth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
}

OpaqueDepthOnlyStaticProgram = graphics_program {
    opaqueDepthOnlyCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = PositionOnlyStaticVertexInput,
    VertexShader = "OpaqueDepthOnlyStatic",
}

TrivialDepthOnlyStaticEffect = graphics_effect {
    Type = effects.DepthOnlyEffectType,
    Program = OpaqueDepthOnlyStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}

DisplacementOpaqueDepthOnlyStaticProgram = graphics_program {
    opaqueDepthOnlyCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, WorldInfo = Common.Core.WorldInfo, DisplacementDepthStageParams = effects.DisplacementDepthStageParams },
    Samplers = { PointSampler = Common.Samplers.PointClamp },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "DisplacementOpaqueDepthOnlyStatic",
}

DisplacementDepthOnlyStaticEffect = graphics_effect {
    Type = effects.DisplacementDepthOnlyEffectType,
    Program = DisplacementOpaqueDepthOnlyStaticProgram,
    Arguments = { CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', DisplacementDepthStageParams = 'DisplacementDepthStageParams' },
}

VATOpaqueDepthOnlyStaticCommon = partial(graphics_program) {
    opaqueDepthOnlyCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { PointClampSampler = Common.Samplers.PointClamp },
    VertexInput = DoubleUvStaticVertexInput,
    VertexShader = "VATOpaqueDepthOnlyStatic",
    PixelShader = "VATOpaqueDepthOnlyStatic",
}
VATOpaqueDepthOnlySkinnedCommon = partial(graphics_program) {
    opaqueDepthOnlyCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, WorldInfo = Common.Core.WorldInfo},
    Samplers = { PointClampSampler = Common.Samplers.PointClamp },
    VertexInput = DoubleUvSkinnedVertexInput,
    VertexShader = "VATOpaqueDepthOnlySkinned",
    PixelShader = "VATOpaqueDepthOnlySkinned",
}

VATOpaqueDepthOnlyStaticProgram = graphics_program {
    opaqueDepthOnlyCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, WorldInfo = Common.Core.WorldInfo, VATDepthStageParams = effects.VATDepthStageParams },
    Samplers = { PointSampler = Common.Samplers.PointClamp },
    VertexInput = DoubleUvStaticVertexInput,
    VertexShader = "VATOpaqueDepthOnlyStaticEffect",
}
VATDepthOnlyStaticEffect = graphics_effect {
    Type = effects.VATDepthOnlyEffectType,
    Program = VATOpaqueDepthOnlyStaticProgram,
    Arguments = { CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', VATDepthStageParams = 'VATDepthStageParams' },
}

TransDiffractDepthOnlyStaticProgram = graphics_program {
    TransDiffractDepthOnlyCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = TransparentMultibumpMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "DiffractDepthOnlyStatic",
    Macros = {"DISPLACEMENT", "SCROLLING"}
}

TransDiffractDepthOnlySkinnedProgram = graphics_program {
    TransDiffractDepthOnlyCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = TransparentMultibumpMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "DiffractDepthOnlySkinned",
    Macros = {"DISPLACEMENT", "SCROLLING"}
}

TransDiffractDepthOnlyStaticEffect = graphics_effect {
    Type = effects.TransDiffractDepthOnlyEffectType,
    Program = TransDiffractDepthOnlyStaticProgram,
    Arguments = { CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo' },
}

TransDiffractDepthOnlySkinnedEffect = graphics_effect {
    Type = effects.TransDiffractDepthOnlyEffectType,
    Program = TransDiffractDepthOnlySkinnedProgram,
    Arguments = { CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo' },
}


local maybeCompactGbuffer = (variant == 'Low' and "COMPACT_GBUFFER" or "FAT_GBUFFER")

local deferredOpaqueCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { PointSampler = Common.Samplers.PointWrap, AnisoSampler = Common.Samplers.Aniso8Wrap, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = effects.GbufferVariant_Motion_Depth,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    Macros = {maybeCompactGbuffer}
}

local deferredOpaqueVRVisCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { PointSampler = Common.Samplers.PointWrap, AnisoSampler = Common.Samplers.Aniso8Wrap, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = Common.Targets.JustDepth,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    Macros = {maybeCompactGbuffer}
}

local deferredOpaqueDiffractCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { PointSampler = Common.Samplers.PointClamp, LinearSampler = Common.Samplers.LinearClamp, AnisoSampler = Common.Samplers.Aniso8Wrap, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = Common.Targets.DiffractAlbedo,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    Macros = {"DIFFRACTION", maybeCompactGbuffer}
}

local deferredOpaqueTransparentDiffractCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { PointSampler = Common.Samplers.PointClamp, LinearSampler = Common.Samplers.LinearClamp, AnisoSampler = Common.Samplers.Aniso8Wrap, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = effects.GbufferVariant_Depth,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    Macros = {"DIFFRACTION", maybeCompactGbuffer}
}

local additiveCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.PremultipliedAdd,
}

local additiveEffectCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.PremultipliedAdd,
}

local opaqueAdditiveCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = Common.Targets.Luminance_Depth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.PremultipliedAdd,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal, DepthWriteEnable = false },
}

local opaqueAdditiveEffectCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.PremultipliedAdd,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal, DepthWriteEnable = false },
}

local TwoSidedOpaqueAdditiveCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = Common.Targets.Luminance_Depth,
    CullMode = CullMode.None,
    BlendState = Common.BlendSettings.States.PremultipliedAdd,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal, DepthWriteEnable = false },
}

local TwoSidedOpaqueAdditiveEffectCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    CullMode = CullMode.None,
    BlendState = Common.BlendSettings.States.PremultipliedAdd,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal, DepthWriteEnable = false },
}

local opaqueSingleLayerStaticCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
}
local opaqueSingleLayerSkinnedCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
}
local opaqueSingleLayerCasterCommon = partial(graphics_program) {
    Parameters = { ShadowProjectionInfo = Common.Core.ShadowProjectionInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = Common.Targets.JustShadowDepth,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Greater },
}
local opaqueSingleLayerDetailStaticCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"ONE_LAYER_DETAIL_BUMP"},
}
local opaqueSingleLayerDetailSkinnedCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"ONE_LAYER_DETAIL_BUMP"},
}

local opaqueVRVisLayerStaticCommon = partial(graphics_program) {
    deferredOpaqueVRVisCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "VRVisualisationDepthStaticMaterial",
    PixelShader = "VRVisualisationDepthStaticMaterial",
}
local opaqueVRVisLayerSkinnedCommon = partial(graphics_program) {
    deferredOpaqueVRVisCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "VRVisualisationDepthSkinnedMaterial",
    PixelShader = "VRVisualisationDepthSkinnedMaterial",
}

local opaqueVATSingleLayerStaticCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = DoubleUvStaticVertexInput,
    VertexShader = "OpaqueVATStaticMaterial",
    PixelShader = "OpaqueVATStaticMaterial",
}
local opaqueVATSingleLayerSkinnedCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = DoubleUvSkinnedVertexInput,
    VertexShader = "OpaqueVATSkinnedMaterial",
    PixelShader = "OpaqueVATSkinnedMaterial",
}

local opaqueDiffractSingleLayerStaticCommon = partial(graphics_program) {
    deferredOpaqueDiffractCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
}
local opaqueDiffractSingleLayerSkinnedCommon = partial(graphics_program) {
    deferredOpaqueDiffractCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
}

local transparentDiffractSingleLayerStaticCommon = partial(graphics_program) {
    deferredOpaqueTransparentDiffractCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "TransparentMultibumpStaticMaterial",
    PixelShader = "TransparentMultibumpStaticMaterial",
}
local transparentDiffractSingleLayerSkinnedCommon = partial(graphics_program) {
    deferredOpaqueTransparentDiffractCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "TransparentMultibumpSkinnedMaterial",
    PixelShader = "TransparentMultibumpSkinnedMaterial",
}

OpaqueSingleLayerStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
OpaqueSingleLayerSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
OpaqueSingleLayerFadingStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"DISTANCE_FADE", "AMBIENTOCCLUSION"},
}
OpaqueSingleLayerFadingSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"DISTANCE_FADE", "AMBIENTOCCLUSION"},
}
TwoSidedOpaqueSingleLayerStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED", "AMBIENTOCCLUSION", "UVOFFSET"},
}
TwoSidedOpaqueSingleLayerSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED", "UVOFFSET"},
}
TwoSidedOpaqueSingleLayerFadingStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED", "DISTANCE_FADE", "AMBIENTOCCLUSION"},
}
TwoSidedOpaqueSingleLayerFadingSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED", "DISTANCE_FADE", "AMBIENTOCCLUSION"},
}
OpaqueMaskSingleLayerStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"ALPHA_MASK", "AMBIENTOCCLUSION", "UVOFFSET", "REFLECTIONFLAGS"},
}
OpaqueMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"ALPHA_MASK", "UVOFFSET", "REFLECTIONFLAGS"},
}
OpaqueEnvironmentMaskSingleLayerStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueEnvironmentMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"ALPHA_MASK", "AMBIENTOCCLUSION", "UVOFFSET", "REFLECTIONFLAGS"},
}
OpaqueEnvironmentMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueEnvironmentMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"ALPHA_MASK", "UVOFFSET", "REFLECTIONFLAGS"},
}
TwoSidedOpaqueMaskSingleLayerStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"ALPHA_MASK", "TWO_SIDED", "AMBIENTOCCLUSION", "UVOFFSET", "REFLECTIONFLAGS"},
}
TwoSidedOpaqueMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"ALPHA_MASK", "TWO_SIDED", "AMBIENTOCCLUSION", "UVOFFSET", "REFLECTIONFLAGS"},
}
OpaquePixelizeMaskSingleLayerStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"PIXELIZE", "ALPHA_MASK", "AMBIENTOCCLUSION", "REFLECTIONFLAGS"},
}
TwoSidedOpaquePixelizeMaskSingleLayerStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"PIXELIZE", "ALPHA_MASK", "TWO_SIDED", "AMBIENTOCCLUSION", "REFLECTIONFLAGS"},
}
OpaquePixelizeMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"PIXELIZE", "ALPHA_MASK", "REFLECTIONFLAGS"},
}
TwoSidedOpaquePixelizeMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"PIXELIZE", "ALPHA_MASK", "TWO_SIDED", "AMBIENTOCCLUSION", "REFLECTIONFLAGS"},
}
OpaqueEnvironmentSingleLayerFadingStaticProgram = graphics_program {
    opaqueSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"DISTANCE_FADE", "AMBIENTOCCLUSION"},
}
OpaqueEnvironmentSingleLayerFadingSkinnedProgram = graphics_program {
    opaqueSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"DISTANCE_FADE", "AMBIENTOCCLUSION"},
}

DepthVRVisOpaqueSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueSubsurfaceSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueSubsurfaceSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueSubsurfaceSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSubsurfaceSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueMaskSubsurfaceSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueMaskSubsurfaceSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEnvironmentSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEnvironmentSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueMaskSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueMaskSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueSingleLayerDetailStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueSingleLayerDetailStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueMaskSingleLayerDetailStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueMaskSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueMaskSingleLayerDetailStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueMaskSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEmissiveSingleLayerDetailStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEmissiveSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueEmissiveSingleLayerDetailStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueEmissiveSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTransmissiveEmissiveStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = TransmissiveEmissiveMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTransmissiveEmissiveSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = TransmissiveEmissiveMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisEmissiveStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = EmissiveMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisEmissiveSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = EmissiveMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedEmissiveStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = EmissiveMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedEmissiveSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = EmissiveMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingOpaqueMaskSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingOpaqueMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingOpaqueMaskSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingOpaqueMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisMediaScrollingOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisMediaScrollingOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedMediaScrollingOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedMediaScrollingOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisDepthVRVisOpaqueEmissiveSingleLayerStaticProgramProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisDepthVRVisOpaqueEmissiveSingleLayerSkinnedProgramProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingOpaqueDiffractMaskSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingOpaqueMaskPixelSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingOpaqueMaskPixelSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingOpaqueEmissivePixelizeSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingOpaqueEmissivePixelizeSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingOpaqueEmissivePixelizeSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingOpaqueEmissivePixelizeSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingScreenspaceOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisScrollingScreenspaceOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisTwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
DepthVRVisVideoScreenStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisVideoScreenSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEmissivePixelizeSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueEmissivePixelizeSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEmissivePixelizeSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEmissivePixelizeSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueEmissivePixelizeSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissivePixelizeSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaqueEmissivePixelizeSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissivePixelizeSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaquePixelizeMaskSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaquePixelizeMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaquePixelizeMaskSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedOpaquePixelizeMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisScreenspaceOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisScreenspaceOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedScreenspaceOpaqueEmissiveSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisTwoSidedScreenspaceOpaqueEmissiveSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueVATSingleLayerStaticProgram = graphics_program {
    VATOpaqueDepthOnlyStaticCommon,
    Parameters = { MaterialInfo = OpaqueVATSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueVATSingleLayerSkinnedProgram = graphics_program {
    VATOpaqueDepthOnlySkinnedCommon,
    Parameters = { MaterialInfo = OpaqueVATSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEmissiveVATSingleLayerStaticProgram = graphics_program {
    VATOpaqueDepthOnlyStaticCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveVATSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEmissiveVATSingleLayerSkinnedProgram = graphics_program {
    VATOpaqueDepthOnlySkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveVATSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEnvironmentMaskSingleLayerStaticProgram = graphics_program {
    opaqueVRVisLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}
DepthVRVisOpaqueEnvironmentMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueVRVisLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentMaskSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
}

OpaqueVATSingleLayerStaticProgram = graphics_program {
    opaqueVATSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueVATSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "VAT", "REFLECTIONFLAGS"},
}
OpaqueVATSingleLayerSkinnedProgram = graphics_program {
    opaqueVATSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueVATSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "VAT", "REFLECTIONFLAGS"},
}
OpaqueVATSingleLayerFadingStaticProgram = graphics_program {
    opaqueVATSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueVATSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"DISTANCE_FADE", "VAT", "REFLECTIONFLAGS"},
}
OpaqueVATSingleLayerFadingSkinnedProgram = graphics_program {
    opaqueVATSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueVATSingleLayerMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"DISTANCE_FADE", "VAT", "REFLECTIONFLAGS"},
}

OpaqueDiffractMaskSingleLayerStaticProgram = graphics_program {
    opaqueDiffractSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    CullMode = CullMode.Back,
    Macros = {"ALPHA_MASK", "DISPLACEMENT", "AMBIENTOCCLUSION", "DIFFRACTION"},
}
OpaqueDiffractMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueDiffractSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    CullMode = CullMode.Back,
    Macros = {"ALPHA_MASK", "DISPLACEMENT", "DIFFRACTION"},
}
TwoSidedOpaqueDiffractMaskSingleLayerStaticProgram = graphics_program {
    opaqueDiffractSingleLayerStaticCommon,
    Parameters = { MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    CullMode = CullMode.None,
    Macros = {"ALPHA_MASK", "TWO_SIDED", "AMBIENTOCCLUSION", "DISPLACEMENT", "DIFFRACTION"},
}
TwoSidedOpaqueDiffractMaskSingleLayerSkinnedProgram = graphics_program {
    opaqueDiffractSingleLayerSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerMaterialInfo2, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    CullMode = CullMode.None,
    Macros = {"ALPHA_MASK", "TWO_SIDED", "AMBIENTOCCLUSION", "DISPLACEMENT", "DIFFRACTION"},
}

OpaqueSingleLayerDetailStaticProgram = graphics_program {
    opaqueSingleLayerDetailStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
OpaqueSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueSingleLayerDetailSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
OpaqueSingleLayerDetailFadingStaticProgram = graphics_program {
    opaqueSingleLayerDetailStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"DISTANCE_FADE", "AMBIENTOCCLUSION"},
}
OpaqueSingleLayerDetailFadingSkinnedProgram = graphics_program {
    opaqueSingleLayerDetailSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"DISTANCE_FADE", "AMBIENTOCCLUSION"},
}
TwoSidedOpaqueSingleLayerDetailStaticProgram = graphics_program {
    opaqueSingleLayerDetailStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED", "AMBIENTOCCLUSION", "UVOFFSET"},
}
TwoSidedOpaqueSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueSingleLayerDetailSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED", "AMBIENTOCCLUSION", "UVOFFSET"},
}
TwoSidedOpaqueSingleLayerDetailFadingStaticProgram = graphics_program {
    opaqueSingleLayerDetailStaticCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED", "DISTANCE_FADE", "AMBIENTOCCLUSION"},
}
TwoSidedOpaqueSingleLayerDetailFadingSkinnedProgram = graphics_program {
    opaqueSingleLayerDetailSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED", "DISTANCE_FADE", "AMBIENTOCCLUSION"},
}
OpaqueMaskSingleLayerDetailStaticProgram = graphics_program {
    opaqueSingleLayerDetailStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerDetailMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "ALPHA_MASK", "UVOFFSET", "REFLECTIONFLAGS"},
}
OpaqueMaskSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueSingleLayerDetailSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerDetailMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "ALPHA_MASK", "UVOFFSET"},
}
TwoSidedOpaqueMaskSingleLayerDetailStaticProgram = graphics_program {
    opaqueSingleLayerDetailStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerDetailMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"AMBIENTOCCLUSION", "ALPHA_MASK", "TWO_SIDED", "UVOFFSET", "REFLECTIONFLAGS"},
}
TwoSidedOpaqueMaskSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueSingleLayerDetailSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo, MaterialInfo2 = OpaqueMaskSingleLayerDetailMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.None,
    Macros = {"AMBIENTOCCLUSION", "ALPHA_MASK", "TWO_SIDED", "UVOFFSET"},
}

OpaqueSubsurfaceSingleLayerDetailStaticProgram = graphics_program {
    opaqueSingleLayerDetailStaticCommon,
    Parameters = { MaterialInfo = OpaqueSubsurfaceSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SUBSURFACE", "AMBIENTOCCLUSION"},
}
OpaqueSubsurfaceSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueSingleLayerDetailSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueSubsurfaceSingleLayerDetailMaterialInfo, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"SUBSURFACE", "AMBIENTOCCLUSION", "SKINNED"},
}

OpaqueMaskSubsurfaceSingleLayerDetailStaticProgram = graphics_program {
    opaqueSingleLayerDetailStaticCommon,
    Parameters = { MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo, MaterialInfo2 = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "ALPHA_MASK", "SUBSURFACE", "REFLECTIONFLAGS"},
}
OpaqueMaskSubsurfaceSingleLayerDetailSkinnedProgram = graphics_program {
    opaqueSingleLayerDetailSkinnedCommon,
    Parameters = { MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo, MaterialInfo2 = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo2, MediaParams = core.MediaParams },
    CullMode = CullMode.Back,
    Macros = {"AMBIENTOCCLUSION", "ALPHA_MASK", "SUBSURFACE", "SKINNED", "REFLECTIONFLAGS"},
}

local premultipliedAddRgbOnly = blend_setting {
    BlendEnable = true,
    BlendColor = blend_add(BlendCoefficient.One, BlendCoefficient.One),
    BlendAlpha = blend_add(BlendCoefficient.One, BlendCoefficient.One),
    WriteMask = rgba_mask(true, true, true, false),
}

local gatherSubsurfaceProgramCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo, SubsurfaceParams = core.SubsurfaceParams },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    BlendState = blend_state { TargetSettings = {premultipliedAddRgbOnly} },
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexShader = "SubsurfaceGatherMaterial",
    PixelShader = "SubsurfaceGatherMaterial",
}
GatherSubsurfaceStaticProgram = graphics_program {
    gatherSubsurfaceProgramCommon,
    Parameters = { MaterialInfo = OpaqueSubsurfaceSingleLayerDetailMaterialInfo, ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = SingleUvStaticVertexInput,
}
GatherSubsurfaceSkinnedProgram = graphics_program {
    gatherSubsurfaceProgramCommon,
    Parameters = { MaterialInfo = OpaqueSubsurfaceSingleLayerDetailMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = PositionUvSkinnedVertexInput,
    Macros = {"SKINNED", "DISPLACEMENT"},
}
GatherMaskedSubsurfaceStaticProgram = graphics_program {
    gatherSubsurfaceProgramCommon,
    Parameters = { MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo, ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = SingleUvStaticVertexInput,
}
GatherMaskedSubsurfaceSkinnedProgram = graphics_program {
    gatherSubsurfaceProgramCommon,
    Parameters = { MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = PositionUvSkinnedVertexInput,
    Macros = {"SKINNED"},
}

OpaqueDiffuseMultiLayerStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueDiffuseMultiLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"FOUR_LAYER_ALBEDO", "FOUR_LAYER_BUMP", "FORCE_MAX_ROUGHNESS", "FORCE_DIELECTRIC"},
}
OpaqueDiffuseMultiLayerFadingStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueDiffuseMultiLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"FOUR_LAYER_ALBEDO", "FOUR_LAYER_BUMP", "FORCE_MAX_ROUGHNESS", "FORCE_DIELECTRIC", "DISTANCE_FADE"},
}
OpaqueTripleLayerDetailStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueTripleLayerDetailMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"THREE_LAYER_ALBEDO", "THREE_LAYER_BUMP", "THREE_LAYER_DETAIL_BUMP", "THREE_LAYER_METAL", "THREE_LAYER_AO"},
}
OpaqueTripleLayerDetailFadingStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueTripleLayerDetailMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"THREE_LAYER_ALBEDO", "THREE_LAYER_BUMP", "THREE_LAYER_DETAIL_BUMP", "THREE_LAYER_METAL", "THREE_LAYER_AO", "DISTANCE_FADE"},
}

OpaqueVATMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueVATSingleLayerMaterialInfo },
    VertexInput = DoubleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "VAT", "SHADOWFLAGS" },
}
OpaqueVATMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueVATSingleLayerMaterialInfo },
    VertexInput = DoubleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "VAT", "SHADOWFLAGS" },
}
OpaqueVATMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueVATSingleLayerMaterialInfo },
    VertexInput = DoubleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "VAT", "SHADOWFLAGS" },
}
OpaqueVATMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueVATSingleLayerMaterialInfo },
    VertexInput = DoubleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED", "VAT", "SHADOWFLAGS" },
}

OpaqueEmissiveVATMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueEmissiveVATSingleLayerMaterialInfo },
    VertexInput = DoubleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "VAT", "SHADOWFLAGS" },
}
OpaqueEmissiveVATMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueEmissiveVATSingleLayerMaterialInfo },
    VertexInput = DoubleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "VAT", "SHADOWFLAGS" },
}
OpaqueEmissiveVATMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueEmissiveVATSingleLayerMaterialInfo },
    VertexInput = DoubleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "VAT", "SHADOWFLAGS" },
}
OpaqueEmissiveVATMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueEmissiveVATSingleLayerMaterialInfo },
    VertexInput = DoubleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED", "VAT", "SHADOWFLAGS" },
}

ScrollingOpaqueEmissiveDisplacementShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueEmissiveDisplacementShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueEmissiveDisplacementShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "SKINNED", "DISPLACEMENT" },
}

MediaScrollingOpaqueEmissiveDisplacementShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
MediaScrollingOpaqueEmissiveDisplacementShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
MediaScrollingOpaqueEmissiveDisplacementShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "DISPLACEMENT" },
}
MediaScrollingOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "SKINNED", "DISPLACEMENT" },
}

ScrollingOpaqueEmissivePixelizeDisplacementShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueEmissivePixelizeDisplacementShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueEmissivePixelizeDisplacementShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueEmissivePixelizeDisplacementShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "SKINNED", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueEmissivePixelizeDisplacementShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueEmissivePixelizeDisplacementShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueEmissivePixelizeDisplacementShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueEmissivePixelizeDisplacementShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "SKINNED", "DISPLACEMENT" },
}
ScrollingScreenspaceOpaqueEmissiveDisplacementShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingScreenspaceOpaqueEmissiveDisplacementShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingScreenspaceOpaqueEmissiveDisplacementShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "DISPLACEMENT" },
}
ScrollingScreenspaceOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "SKINNED", "DISPLACEMENT" },
}
TwoSidedScrollingScreenspaceOpaqueEmissiveDisplacementShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingScreenspaceOpaqueEmissiveDisplacementShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingScreenspaceOpaqueEmissiveDisplacementShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingScreenspaceOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SCROLLING", "SKINNED", "DISPLACEMENT" },
}


OpaqueMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
}
OpaqueMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
}
OpaqueMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK" },
}
OpaqueMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED" },
}
OpaqueMaskDetailShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
}
OpaqueMaskDetailShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
}
OpaqueMaskDetailShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK" },
}
OpaqueMaskDetailShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED" },
}
OpaquePixelizeMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
}
OpaquePixelizeMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
}
OpaquePixelizeMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK" },
}
OpaquePixelizeMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED" },
}
OpaqueEnvironmentMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueEnvironmentMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
}
OpaqueEnvironmentMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueEnvironmentMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
}
OpaqueEnvironmentMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueEnvironmentMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK" },
}
OpaqueEnvironmentMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueEnvironmentMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED" },
}

OpaqueDiffractMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "DISPLACEMENT" },
}
OpaqueDiffractMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "DISPLACEMENT" },
}
OpaqueDiffractMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "DISPLACEMENT" },
}
OpaqueDiffractMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED", "DISPLACEMENT" },
}

TwoSidedOpaqueMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
}
TwoSidedOpaqueMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
}
TwoSidedOpaqueMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK" },
}
TwoSidedOpaqueMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED" },
}
TwoSidedOpaqueMaskDetailShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
}
TwoSidedOpaqueMaskDetailShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
}
TwoSidedOpaqueMaskDetailShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK" },
}
TwoSidedOpaqueMaskDetailShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED" },
}
TwoSidedOpaquePixelizeMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
}
TwoSidedOpaquePixelizeMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
}
TwoSidedOpaquePixelizeMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK" },
}
TwoSidedOpaquePixelizeMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaquePixelizeMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED" },
}

TwoSidedOpaqueDiffractMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "DISPLACEMENT" },
}
TwoSidedOpaqueDiffractMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "DISPLACEMENT" },
}
TwoSidedOpaqueDiffractMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "DISPLACEMENT" },
}
TwoSidedOpaqueDiffractMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED", "DISPLACEMENT" },
}

ScrollingOpaqueMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueMaskPixelShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueMaskPixelShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "SCROLLING", "ALPHA_MASK", "DISPLACEMENT" },
}
ScrollingOpaqueMaskPixelShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "SCROLLING", "ALPHA_MASK", "DISPLACEMENT" },
}
ScrollingOpaqueMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "SCROLLING", "ALPHA_MASK", "SKINNED", "DISPLACEMENT" },
}
ScrollingOpaqueMaskPixelShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "SCROLLING", "ALPHA_MASK", "SKINNED", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "TWO_SIDED", "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "TWO_SIDED", "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "TWO_SIDED", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "TWO_SIDED", "SCROLLING", "ALPHA_MASK", "SKINNED", "DISPLACEMENT" },
}
ScrollingOpaqueDiffractMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueDiffractMaskShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
    Macros = { "TWO_SIDED", "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueDiffractMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueDiffractMaskShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
    Macros = { "TWO_SIDED", "SCROLLING", "DISPLACEMENT" },
}
ScrollingOpaqueDiffractMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "SCROLLING", "ALPHA_MASK", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueDiffractMaskShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "TWO_SIDED", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT" },
}
ScrollingOpaqueDiffractMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "SCROLLING", "ALPHA_MASK", "SKINNED", "DISPLACEMENT"},
}
TwoSidedScrollingOpaqueDiffractMaskShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.None,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "TWO_SIDED", "SCROLLING", "ALPHA_MASK", "SKINNED", "DISPLACEMENT" },
}

OpaqueMaskSubsurfaceDetailShadowStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "MaskCasterStatic",
    PixelShader = "MaskCasterStatic",
}
OpaqueMaskSubsurfaceDetailShadowSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "MaskCasterSkinned",
    PixelShader = "MaskCasterSkinned",
}
OpaqueMaskSubsurfaceDetailShadowCascadeStaticProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK" },
}
OpaqueMaskSubsurfaceDetailShadowCascadeSkinnedProgram = graphics_program {
    opaqueSingleLayerCasterCommon,
    CullMode = CullMode.Back,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MaterialInfo = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "CascadedCaster",
    PixelShader = "CascadedCaster",
    Macros = { "ALPHA_MASK", "SKINNED" },
}

local gbufferEffectCommon = partial(graphics_effect) {
    Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', MediaParams = 'MediaParams'},
}
local opaqueMaskShadowEffectCommon = partial(graphics_effect) {
    Type = effects.ShadowDepthOnlyEffectType,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo', WorldInfo = 'WorldInfo'},
}
local opaqueMaskShadowCascadeEffectCommon = partial(graphics_effect) {
    Type = effects.ShadowCascadeDepthOnlyFadingEffectType,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo', WorldInfo = 'WorldInfo'},
}
local opaqueEmissiveMaskShadowEffectCommon = partial(graphics_effect) {
    Type = effects.ShadowDepthOnlyEffectType,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo', WorldInfo = 'WorldInfo'},
}
local opaqueEmissiveMaskShadowCascadeEffectCommon = partial(graphics_effect) {
    Type = effects.ShadowCascadeDepthOnlyEffectType,
    Arguments = {ShadowProjectionInfo = 'ShadowProjectionInfo', WorldInfo = 'WorldInfo'},
}

local gbufferDiffractEffectCommon = partial(graphics_effect) {
    Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', DiffractionAlbedoParams = 'DiffractionAlbedoParams'},
}

local opaqueEmissiveSingleLayerBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerMaterialInfo },
}
local opaqueEmissiveSingleLayerDetailBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerDetailMaterialInfo },
    Macros = {"ONE_LAYER_DETAIL_BUMP"},
}
local opaqueEmissiveFresnelSingleLayerBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueEmissiveFresnelSingleLayerMaterialInfo },
}
local opaqueEmissivePixelizeSingleLayerBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueEmissivePixelizeSingleLayerMaterialInfo },
}
local opaqueEnvironmentSingleLayerBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueEnvironmentSingleLayerMaterialInfo },
}
local opaqueEmissiveVATSingleLayerBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = OpaqueEmissiveVATSingleLayerMaterialInfo },
}

local TwoSidedOpaqueEmissiveSingleLayerBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED"},
}
local TwoSidedOpaqueEmissiveSingleLayerDetailBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerDetailMaterialInfo },
    Macros = {"ONE_LAYER_DETAIL_BUMP"},
}
local TwoSidedOpaqueEmissiveFresnelSingleLayerBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveFresnelSingleLayerMaterialInfo },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED"},
}
local TwoSidedOpaqueEmissivePixelizeSingleLayerBaseCommon = partial(graphics_program) {
    deferredOpaqueCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissivePixelizeSingleLayerMaterialInfo },
    CullMode = CullMode.None,
    Macros = {"TWO_SIDED"},
}

local opaqueEmissiveSingleLayerAddCommon = partial(graphics_program) {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerMaterialInfo },
}
local opaqueEmissiveSingleLayerAddEffectCommon = partial(graphics_program) {
    opaqueAdditiveEffectCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerMaterialInfo },
}
local opaqueEmissiveSingleLayerDetailAddCommon = partial(graphics_program) {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveSingleLayerDetailMaterialInfo },
}
local opaqueEmissiveFresnelSingleLayerAddCommon = partial(graphics_program) {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveFresnelSingleLayerMaterialInfo },
}
local opaqueEmissivePixelizeSingleLayerAddCommon = partial(graphics_program) {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = OpaqueEmissivePixelizeSingleLayerMaterialInfo },
}
local opaqueEnvironmentSingleLayerAddCommon = partial(graphics_program) {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentSingleLayerMaterialInfo },
}
local opaqueEnvironmentMaskSingleLayerAddCommon = partial(graphics_program) {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = OpaqueEnvironmentMaskSingleLayerMaterialInfo },
}
local opaqueEmissiveVATSingleLayerAddCommon = partial(graphics_program) {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = OpaqueEmissiveVATSingleLayerMaterialInfo },
}

local transparentEnvironmentSingleLayerAddCommon = partial(graphics_program) {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = TransparentMultibumpMaterialInfo },
    Macros = { "SCROLLING", "TRANSDISPLACEMENT" },
}

local TwoSidedOpaqueEmissiveSingleLayerAddCommon = partial(graphics_program) {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo },
}
local TwoSidedOpaqueEmissiveSingleLayerDetailAddCommon = partial(graphics_program) {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveSingleLayerDetailMaterialInfo },
}
local TwoSidedOpaqueEmissiveFresnelSingleLayerAddCommon = partial(graphics_program) {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissiveFresnelSingleLayerMaterialInfo },
}
local TwoSidedOpaqueEmissivePixelizeSingleLayerAddCommon = partial(graphics_program) {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = TwoSidedOpaqueEmissivePixelizeSingleLayerMaterialInfo },
}

OpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    opaqueEmissiveSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
OpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    opaqueEmissiveSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
OpaqueEmissiveFresnelSingleLayerBaseStaticProgram = graphics_program {
    opaqueEmissiveFresnelSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"FRESNEL", "AMBIENTOCCLUSION"},
}
OpaqueEmissiveFresnelSingleLayerBaseSkinnedProgram = graphics_program {
    opaqueEmissiveFresnelSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"FRESNEL", "AMBIENTOCCLUSION"},
}
OpaqueEmissivePixelizeSingleLayerBaseStaticProgram = graphics_program {
    opaqueEmissivePixelizeSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "PIXELIZE"},
}
OpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram = graphics_program {
    opaqueEmissivePixelizeSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "PIXELIZE"},
}
OpaqueEmissiveVATSingleLayerBaseStaticProgram = graphics_program {
    opaqueEmissiveVATSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = DoubleUvStaticVertexInput,
    VertexShader = "OpaqueVATStaticMaterial",
    PixelShader = "OpaqueVATStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "VAT", "REFLECTIONFLAGS"},
}
OpaqueEmissiveVATSingleLayerBaseSkinnedProgram = graphics_program {
    opaqueEmissiveVATSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = DoubleUvSkinnedVertexInput,
    VertexShader = "OpaqueVATSkinnedMaterial",
    PixelShader = "OpaqueVATSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "VAT", "REFLECTIONFLAGS"},
}

ScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    opaqueEmissiveSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"SCREENSPACEUV", "AMBIENTOCCLUSION"},
}
ScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    opaqueEmissiveSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"SCREENSPACEUV", "AMBIENTOCCLUSION"},
}
OpaqueEnvironmentSingleLayerBaseStaticProgram = graphics_program {
    opaqueEnvironmentSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
OpaqueEnvironmentSingleLayerBaseSkinnedProgram = graphics_program {
    opaqueEnvironmentSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}

TwoSidedOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
TwoSidedOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
TwoSidedOpaqueEmissiveFresnelSingleLayerBaseStaticProgram = graphics_program {
    TwoSidedOpaqueEmissiveFresnelSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION"},
}
TwoSidedOpaqueEmissiveFresnelSingleLayerBaseSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissiveFresnelSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION"},
}
TwoSidedOpaqueEmissivePixelizeSingleLayerBaseStaticProgram = graphics_program {
    TwoSidedOpaqueEmissivePixelizeSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "PIXELIZE"},
}
TwoSidedOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissivePixelizeSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "PIXELIZE"},
}
TwoSidedScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"SCREENSPACEUV", "AMBIENTOCCLUSION"},
}
TwoSidedScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"SCREENSPACEUV", "AMBIENTOCCLUSION"},
}

EmissiveMaskStaticProgram = graphics_program {
    Parameters = { MaterialInfo = EmissiveMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    BlendState = Common.BlendSettings.States.Over,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveMaterial",
    PixelShader = "EmissiveMaterial",
    Macros = {"SCROLLING", "ABSORPTION_MASK", "DISPLACEMENT"},
}
EmissiveMaskSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = EmissiveMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    BlendState = Common.BlendSettings.States.Over,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveMaterial",
    PixelShader = "EmissiveMaterial",
    Macros = {"SCROLLING", "ABSORPTION_MASK", "SKINNED", "DISPLACEMENT"},
}
EmissiveStaticProgram = graphics_program {
    Parameters = { MaterialInfo = EmissiveMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    BlendState = Common.BlendSettings.States.Add,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveMaterial",
    PixelShader = "EmissiveMaterial",
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
EmissiveSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = EmissiveMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    BlendState = Common.BlendSettings.States.Add,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveMaterial",
    PixelShader = "EmissiveMaterial",
    Macros = {"SCROLLING", "SKINNED", "DISPLACEMENT"},
}
TwoSidedEmissiveMaskStaticProgram = graphics_program {
    Parameters = { MaterialInfo = EmissiveMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    BlendState = Common.BlendSettings.States.Over,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.None,
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveMaterial",
    PixelShader = "EmissiveMaterial",
    Macros = {"TWO_SIDED", "SCROLLING", "ABSORPTION_MASK", "DISPLACEMENT"},
}
TwoSidedEmissiveMaskSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = EmissiveMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    BlendState = Common.BlendSettings.States.Over,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.None,
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveMaterial",
    PixelShader = "EmissiveMaterial",
    Macros = {"TWO_SIDED", "SCROLLING", "ABSORPTION_MASK", "SKINNED", "DISPLACEMENT"},
}
TwoSidedEmissiveStaticProgram = graphics_program {
    Parameters = { MaterialInfo = EmissiveMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    BlendState = Common.BlendSettings.States.Add,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.None,
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveMaterial",
    PixelShader = "EmissiveMaterial",
    Macros = {"TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedEmissiveSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = EmissiveMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Gbuffer_Emissive_Depth,
    BlendState = Common.BlendSettings.States.Add,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.None,
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveMaterial",
    PixelShader = "EmissiveMaterial",
    Macros = {"TWO_SIDED", "SCROLLING", "SKINNED", "DISPLACEMENT"},
}

BillboardMaskStaticProgram = graphics_program {
    Parameters = { MaterialInfo = BillboardMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    BlendState = Common.BlendSettings.States.Over,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "BillboardMaterial",
    PixelShader = "BillboardMaterial",
    Macros = {"SCROLLING", "ABSORPTION_MASK", "DISPLACEMENT"},
}
BillboardMaskSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = BillboardMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    BlendState = Common.BlendSettings.States.Over,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "BillboardMaterial",
    PixelShader = "BillboardMaterial",
    Macros = {"SCROLLING", "ABSORPTION_MASK", "DISPLACEMENT", "SKINNED"},
}
BillboardPixelizeMaskStaticProgram = graphics_program {
    Parameters = { MaterialInfo = BillboardPixelizeMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    BlendState = Common.BlendSettings.States.Over,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "BillboardMaterial",
    PixelShader = "BillboardMaterial",
    Macros = {"PIXELIZE", "SCROLLING", "ABSORPTION_MASK", "DISPLACEMENT"},
}
BillboardPixelizeMaskSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = BillboardPixelizeMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    BlendState = Common.BlendSettings.States.Over,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "BillboardMaterial",
    PixelShader = "BillboardMaterial",
    Macros = {"PIXELIZE", "SCROLLING", "ABSORPTION_MASK", "DISPLACEMENT", "SKINNED"},
}

BillboardStaticProgram = graphics_program {
    Parameters = { MaterialInfo = BillboardMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    BlendState = Common.BlendSettings.States.Add,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "BillboardMaterial",
    PixelShader = "BillboardMaterial",
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
BillboardSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = BillboardMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    BlendState = Common.BlendSettings.States.Add,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "BillboardMaterial",
    PixelShader = "BillboardMaterial",
    Macros = {"SCROLLING", "SKINNED", "DISPLACEMENT"},
}
BillboardPixelizeStaticProgram = graphics_program {
    Parameters = { MaterialInfo = BillboardPixelizeMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    BlendState = Common.BlendSettings.States.Add,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "BillboardMaterial",
    PixelShader = "BillboardMaterial",
    Macros = {"PIXELIZE", "SCROLLING", "DISPLACEMENT"},
}
BillboardPixelizeSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = BillboardPixelizeMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    BlendState = Common.BlendSettings.States.Add,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
    CullMode = CullMode.Back,
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "BillboardMaterial",
    PixelShader = "BillboardMaterial",
    Macros = {"PIXELIZE", "SCROLLING", "SKINNED", "DISPLACEMENT"},
}

StereographicOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"STEREOGRAPHIC"},
}
StereographicOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"STEREOGRAPHIC"},
}
ScrollingOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "SCROLLING", "DISPLACEMENT"},
}
ScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "SCROLLING", "DISPLACEMENT"},
}
MediaScrollingOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "SCROLLING", "DISPLACEMENT", "MEDIADISPLACEMENT"},
}
MediaScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "SCROLLING", "DISPLACEMENT", "MEDIADISPLACEMENT"},
}

ScrollingOpaqueMaskEmissiveSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo2, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"SCROLLING", "ALPHA_MASK"},
}
ScrollingOpaqueMaskEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo2, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"SCROLLING", "ALPHA_MASK"},
}
ScrollingOpaqueMaskSingleLayerStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT", "REFLECTIONFLAGS"},
}
ScrollingOpaqueMaskPixelSingleLayerStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT", "BILLBOARD", "PIXELIZE", "REFLECTIONFLAGS"},
}
ScrollingOpaqueMaskSingleLayerSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT", "REFLECTIONFLAGS"},
}
ScrollingOpaqueMaskPixelSingleLayerSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT", "BILLBOARD", "PIXELIZE", "REFLECTIONFLAGS"},
}
TwoSidedScrollingOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedMediaScrollingOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "DISPLACEMENT", "MEDIADISPLACEMENT"},
}
TwoSidedMediaScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "DISPLACEMENT", "MEDIADISPLACEMENT"},
}
TwoSidedScrollingOpaqueMaskSingleLayerStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT", "REFLECTIONFLAGS"},
}
TwoSidedScrollingOpaqueMaskSingleLayerSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueMaskSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT", "REFLECTIONFLAGS"},
}
ScrollingOpaqueDiffractMaskSingleLayerStaticProgram = graphics_program {
    deferredOpaqueDiffractCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.StaticModelInfo, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = { "AMBIENTOCCLUSION", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueDiffractMaskSingleLayerStaticProgram = graphics_program {
    deferredOpaqueDiffractCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.StaticModelInfo, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = { "AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT" },
}
ScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram = graphics_program {
    deferredOpaqueDiffractCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.RiggedModelInfo, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = { "SCROLLING", "ALPHA_MASK", "AMBIENTOCCLUSION", "DISPLACEMENT" },
}
TwoSidedScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram = graphics_program {
    deferredOpaqueDiffractCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelInfo = Common.Core.RiggedModelInfo, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = { "TWO_SIDED", "SCROLLING", "ALPHA_MASK", "DISPLACEMENT" },
}
ScrollingScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"SCREENSPACEUV", "AMBIENTOCCLUSION", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"SCREENSPACEUV", "AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
ScrollingScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"SCREENSPACEUV", "AMBIENTOCCLUSION", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"SCREENSPACEUV", "AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
ScrollingOpaqueEmissivePixelizeSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"PIXELIZE", "AMBIENTOCCLUSION", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"PIXELIZE", "AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
ScrollingOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"PIXELIZE", "AMBIENTOCCLUSION", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.None,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"PIXELIZE", "AMBIENTOCCLUSION", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}

OpaqueEmissiveSingleLayerDetailBaseStaticProgram = graphics_program {
    opaqueEmissiveSingleLayerDetailBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
OpaqueEmissiveSingleLayerDetailBaseSkinnedProgram = graphics_program {
    opaqueEmissiveSingleLayerDetailBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}

TwoSidedOpaqueEmissiveSingleLayerDetailBaseStaticProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerDetailBaseCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}
TwoSidedOpaqueEmissiveSingleLayerDetailBaseSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerDetailBaseCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"AMBIENTOCCLUSION", "UVOFFSET"},
}

OpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    opaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"UVOFFSET"},
}
OpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    opaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"UVOFFSET"},
}
TwoSidedOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"UVOFFSET"},
}
TwoSidedOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"UVOFFSET"},
}
OpaqueEnvironmentSingleLayerAddStaticProgram = graphics_program {
    opaqueEnvironmentSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, SkyParams = core.SkyParams, DeferredBuffers = effects.DeferredBuffers },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EnvironmentAddStaticMaterial",
    PixelShader = "EnvironmentAddStaticMaterial",
}
OpaqueEnvironmentSingleLayerAddSkinnedProgram = graphics_program {
    opaqueEnvironmentSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, SkyParams = core.SkyParams, DeferredBuffers = effects.DeferredBuffers },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EnvironmentAddSkinnedMaterial",
    PixelShader = "EnvironmentAddSkinnedMaterial",
}
OpaqueEnvironmentMaskSingleLayerAddStaticProgram = graphics_program {
    opaqueEnvironmentMaskSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, SkyParams = core.SkyParams, DeferredBuffers = effects.DeferredBuffers },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EnvironmentAddStaticMaterial",
    PixelShader = "EnvironmentAddStaticMaterial",
}
OpaqueEnvironmentMaskSingleLayerAddSkinnedProgram = graphics_program {
    opaqueEnvironmentMaskSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, SkyParams = core.SkyParams, DeferredBuffers = effects.DeferredBuffers },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EnvironmentAddSkinnedMaterial",
    PixelShader = "EnvironmentAddSkinnedMaterial",
}
OpaqueEmissiveFresnelSingleLayerAddStaticProgram = graphics_program {
    opaqueEmissiveFresnelSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"FRESNEL"},
}
OpaqueEmissiveFresnelSingleLayerAddSkinnedProgram = graphics_program {
    opaqueEmissiveFresnelSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"FRESNEL"},
}
TwoSidedOpaqueEmissiveFresnelSingleLayerAddStaticProgram = graphics_program {
    TwoSidedOpaqueEmissiveFresnelSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"FRESNEL"},
}
TwoSidedOpaqueEmissiveFresnelSingleLayerAddSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissiveFresnelSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"FRESNEL"},
}
OpaqueEmissivePixelizeSingleLayerAddStaticProgram = graphics_program {
    opaqueEmissivePixelizeSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"PIXELIZE"},
}
OpaqueEmissivePixelizeSingleLayerAddSkinnedProgram = graphics_program {
    opaqueEmissivePixelizeSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"PIXELIZE"},
}
TwoSidedOpaqueEmissivePixelizeSingleLayerAddStaticProgram = graphics_program {
    TwoSidedOpaqueEmissivePixelizeSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"PIXELIZE"},
}
TwoSidedOpaqueEmissivePixelizeSingleLayerAddSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissivePixelizeSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"PIXELIZE"},
}
OpaqueEmissiveVATSingleLayerAddStaticProgram = graphics_program {
    opaqueEmissiveVATSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = DoubleUvStaticVertexInput,
    VertexShader = "EmissiveVATAddStaticMaterial",
    PixelShader = "EmissiveVATAddStaticMaterial",
    Macros = {"VAT"},
}
OpaqueEmissiveVATSingleLayerAddSkinnedProgram = graphics_program {
    opaqueEmissiveVATSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = DoubleUvSkinnedVertexInput,
    VertexShader = "EmissiveVATAddSkinnedMaterial",
    PixelShader = "EmissiveVATAddSkinnedMaterial",
    Macros = {"VAT"},
}
ScreenspaceOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    opaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"SCREENSPACEUV"},
}
ScreenspaceOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    opaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"SCREENSPACEUV"},
}
TwoSidedScreenspaceOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"SCREENSPACEUV"},
}
TwoSidedScreenspaceOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"SCREENSPACEUV"},
}
TransparentEnvironmentSingleLayerAddStaticProgram = graphics_program {
    transparentEnvironmentSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, SkyParams = core.SkyParams, DeferredBuffers = effects.DeferredBuffers },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EnvironmentAddStaticMaterial",
    PixelShader = "EnvironmentAddStaticMaterial",
}
TransparentEnvironmentSingleLayerAddSkinnedProgram = graphics_program {
    transparentEnvironmentSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, SkyParams = core.SkyParams, DeferredBuffers = effects.DeferredBuffers },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EnvironmentAddSkinnedMaterial",
    PixelShader = "EnvironmentAddSkinnedMaterial",
}

OpaqueEmissiveSingleLayerDetailAddStaticProgram = graphics_program {
    opaqueEmissiveSingleLayerDetailAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"UVOFFSET"},
}
OpaqueEmissiveSingleLayerDetailAddSkinnedProgram = graphics_program {
    opaqueEmissiveSingleLayerDetailAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"UVOFFSET"},
}
TwoSidedOpaqueEmissiveSingleLayerDetailAddStaticProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerDetailAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"UVOFFSET"},
}
TwoSidedOpaqueEmissiveSingleLayerDetailAddSkinnedProgram = graphics_program {
    TwoSidedOpaqueEmissiveSingleLayerDetailAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"UVOFFSET"},
}
ScrollingOpaqueMaskEmissiveSingleLayerAddStaticProgram = graphics_program {
    additiveCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams, MaterialInfo = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo2 },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal, DepthWriteEnable = false },
    Macros = {"SCROLLING", "ALPHA_MASK"},
}
ScrollingOpaqueMaskEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    additiveCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams, MaterialInfo = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo, MaterialInfo2 = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo2 },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal, DepthWriteEnable = false },
    Macros = {"SCROLLING", "ALPHA_MASK"},
}

TransmissiveEmissiveAddStaticProgram = graphics_program {
    additiveCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams, MaterialInfo = TransmissiveEmissiveMaterialInfo },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
}
TransmissiveEmissiveAddSkinnedProgram = graphics_program {
    additiveCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams, MaterialInfo = TransmissiveEmissiveMaterialInfo },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual, DepthWriteEnable = false },
}
ScrollingOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
ScrollingOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"SCROLLING", "DISPLACEMENT"},
}
MediaScrollingOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"SCROLLING", "DISPLACEMENT", "MEDIADISPLACEMENT"},
}
MediaScrollingOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"SCROLLING", "DISPLACEMENT", "MEDIADISPLACEMENT"},
}
StereographicOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"STEREOGRAPHIC"},
}
StereographicOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"STEREOGRAPHIC"},
}
TwoSidedScrollingOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedMediaScrollingOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"TWO_SIDED", "SCROLLING", "DISPLACEMENT", "MEDIADISPLACEMENT"},
}
TwoSidedMediaScrollingOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"TWO_SIDED", "SCROLLING", "DISPLACEMENT", "MEDIADISPLACEMENT"},
}
ScrollingScreenspaceOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"SCREENSPACEUV", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerAddStaticProgram = graphics_program {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"SCREENSPACEUV", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
ScrollingScreenspaceOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"SCREENSPACEUV", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerAddSkinnedProgram = graphics_program {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"SCREENSPACEUV", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
ScrollingOpaqueEmissivePixelizeSingleLayerAddStaticProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"PIXELIZE", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerAddStaticProgram = graphics_program {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = {"PIXELIZE", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}
ScrollingOpaqueEmissivePixelizeSingleLayerAddSkinnedProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"PIXELIZE", "SCROLLING", "DISPLACEMENT"},
}
TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerAddSkinnedProgram = graphics_program {
    TwoSidedOpaqueAdditiveCommon,
    Parameters = { MaterialInfo = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = {"PIXELIZE", "TWO_SIDED", "SCROLLING", "DISPLACEMENT"},
}

local transmissiveMultiplyCommon = partial(graphics_program) {
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.Multiply,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    Parameters = { CameraInfo = Common.Core.CameraInfo, MaterialInfo = TransmissiveEmissiveMaterialInfo },
}

TransmissiveMultiplyStaticProgram = graphics_program {
    transmissiveMultiplyCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = SingleUvTangentlessStaticVertexInput,
    VertexShader = "TransmissiveMultiplyStaticMaterial",
    PixelShader = "TransmissiveMultiplyStaticMaterial",
}
TransmissiveMultiplySkinnedProgram = graphics_program {
    transmissiveMultiplyCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = SingleUvTangentlessSkinnedVertexInput,
    VertexShader = "TransmissiveMultiplySkinnedMaterial",
    PixelShader = "TransmissiveMultiplySkinnedMaterial",
}

TransparentGbufferStaticProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, TransparentStageParams = effects.TransparentStageParams },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, LinearClampSampler = Common.Samplers.LinearClamp },
    PixelOutput = effects.GbufferVariant_Depth,
    VertexInput = SingleUvStaticVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransparentStaticMaterial",
    PixelShader = "TransparentStaticMaterial",
    Macros = {maybeCompactGbuffer, "REFLECTIONFLAGS"},
}
TransparentGbufferSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, TransparentStageParams = effects.TransparentStageParams },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, LinearClampSampler = Common.Samplers.LinearClamp },
    PixelOutput = effects.GbufferVariant_Depth,
    VertexInput = SingleUvSkinnedVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransparentSkinnedMaterial",
    PixelShader = "TransparentSkinnedMaterial",
    Macros = {maybeCompactGbuffer, "REFLECTIONFLAGS"},
}
TransparentTransmittanceStaticProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.RasterLDR_Depth,
    VertexInput = SingleUvStaticVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransmittanceStaticMaterial",
    PixelShader = "TransmittanceStaticMaterial",
}
TransparentTransmittanceSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.RasterLDR_Depth,
    VertexInput = SingleUvSkinnedVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransmittanceSkinnedMaterial",
    PixelShader = "TransmittanceSkinnedMaterial",
}

TransparentMultibumpGbufferStaticProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMultibumpMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo, TransparentStageParams = effects.TransparentStageParams },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, LinearClampSampler = Common.Samplers.LinearClamp, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = effects.GbufferVariant_Depth,
    VertexInput = SingleUvStaticVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransparentStaticMaterial",
    PixelShader = "TransparentStaticMaterial",
    Macros = {"MULTIBUMP", "SCROLLING", "DISPLACEMENT", "REFLECTIONFLAGS", maybeCompactGbuffer},
}
TransparentMultibumpGbufferSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMultibumpMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo, TransparentStageParams = effects.TransparentStageParams },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, LinearClampSampler = Common.Samplers.LinearClamp, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = effects.GbufferVariant_Depth,
    VertexInput = SingleUvSkinnedVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransparentSkinnedMaterial",
    PixelShader = "TransparentSkinnedMaterial",
    Macros = {"MULTIBUMP", "SCROLLING", "DISPLACEMENT", "REFLECTIONFLAGS", maybeCompactGbuffer},
}
TransparentMultibumpTransmittanceStaticProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMultibumpMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, WorldInfo = Common.Core.WorldInfo, CameraInfo = Common.Core.CameraInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.RasterLDR_Depth,
    VertexInput = SingleUvStaticVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransmittanceStaticMaterial",
    PixelShader = "TransmittanceStaticMaterial",
    Macros = { "SCROLLING", "DISPLACEMENT"},
}
TransparentMultibumpTransmittanceSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMultibumpMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, WorldInfo = Common.Core.WorldInfo, CameraInfo = Common.Core.CameraInfo },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.RasterLDR_Depth,
    VertexInput = SingleUvSkinnedVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransmittanceSkinnedMaterial",
    PixelShader = "TransmittanceSkinnedMaterial",
    Macros = { "SCROLLING", "DISPLACEMENT" },
}
TransparentMultibumpDiffractGbufferStaticProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMultibumpMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo, TransparentStageParams = effects.TransparentStageParams, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, LinearClampSampler = Common.Samplers.LinearClamp, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = effects.GbufferVariant_Depth,
    VertexInput = SingleUvStaticVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransparentStaticMaterial",
    PixelShader = "TransparentStaticMaterial",
    Macros = {"MULTIBUMP", "SCROLLING", "DISPLACEMENT", "DIFFRACTION", "REFLECTIONFLAGS", maybeCompactGbuffer},
}
TransparentMultibumpDiffractGbufferSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMultibumpMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo, TransparentStageParams = effects.TransparentStageParams, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, LinearClampSampler = Common.Samplers.LinearClamp, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = effects.GbufferVariant_Depth,
    VertexInput = SingleUvSkinnedVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransparentSkinnedMaterial",
    PixelShader = "TransparentSkinnedMaterial",
    Macros = {"MULTIBUMP", "SCROLLING", "DISPLACEMENT", "DIFFRACTION", "REFLECTIONFLAGS", maybeCompactGbuffer},
}
TransparentMultibumpEnvironmentDiffractGbufferStaticProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMultibumpMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo, TransparentStageParams = effects.TransparentStageParams, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, LinearClampSampler = Common.Samplers.LinearClamp, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = effects.GbufferVariant_Depth,
    VertexInput = SingleUvStaticVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransparentStaticMaterial",
    PixelShader = "TransparentStaticMaterial",
    Macros = {"MULTIBUMP", "SCROLLING", "DISPLACEMENT", "DIFFRACTION", "REFLECTIONFLAGS", maybeCompactGbuffer},
}
TransparentMultibumpEnvironmentDiffractGbufferSkinnedProgram = graphics_program {
    Parameters = { MaterialInfo = TransparentMultibumpMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo, TransparentStageParams = effects.TransparentStageParams, DiffractionAlbedoParams = effects.DiffractionAlbedoParams },
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap, LinearClampSampler = Common.Samplers.LinearClamp, PointClampSampler = Common.Samplers.PointClamp },
    PixelOutput = effects.GbufferVariant_Depth,
    VertexInput = SingleUvSkinnedVertexInput,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
    CullMode = CullMode.Back,
    VertexShader = "TransparentSkinnedMaterial",
    PixelShader = "TransparentSkinnedMaterial",
    Macros = {"MULTIBUMP", "SCROLLING", "DISPLACEMENT", "DIFFRACTION", "REFLECTIONFLAGS", maybeCompactGbuffer},
}

VideoScreenAddStaticProgram = graphics_program {
    opaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = { "MEDIA", "UVOFFSET" },
}
VideoScreenAddSkinnedProgram = graphics_program {
    opaqueEmissiveSingleLayerAddCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = { "MEDIA", "UVOFFSET" },
}
StereographicVideoScreenAddStaticProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "EmissiveAddStaticMaterial",
    PixelShader = "EmissiveAddStaticMaterial",
    Macros = { "MEDIA", "STEREOGRAPHIC" },
}
StereographicVideoScreenAddSkinnedProgram = graphics_program {
    opaqueAdditiveCommon,
    Parameters = { MaterialInfo = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "EmissiveAddSkinnedMaterial",
    PixelShader = "EmissiveAddSkinnedMaterial",
    Macros = { "MEDIA", "STEREOGRAPHIC" },
}

ChromaKeyOpaqueEmissiveSingleLayerBaseStaticProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ChromaKeySingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "OpaqueStaticMaterial",
    PixelShader = "OpaqueStaticMaterial",
    Macros = {"MEDIA"},
}
ChromaKeyOpaqueEmissiveSingleLayerBaseSkinnedProgram = graphics_program {
    deferredOpaqueCommon,
    CullMode = CullMode.Back,
    Parameters = { MaterialInfo = ChromaKeySingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "OpaqueSkinnedMaterial",
    PixelShader = "OpaqueSkinnedMaterial",
    Macros = {"MEDIA"},
}

ChromaKeyVideoScreenAddStaticProgram = graphics_program {
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.PremultipliedAdd,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal, DepthWriteEnable = false },
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo, MaterialInfo = ChromaKeySingleLayerMaterialInfo, ModelInfo = Common.Core.StaticModelInfo, MediaParams = core.MediaParams },
    PixelOutput = Common.Targets.Luminance_Depth,
    VertexInput = SingleUvTangentlessStaticVertexInput,
    VertexShader = "ChromaKeyStaticMaterial",
    PixelShader = "ChromaKeyStaticMaterial",
    Macros = { "MEDIA" },
}

ChromaKeyVideoScreenAddSkinnedProgram = graphics_program {
    Samplers = { AnisoSampler = Common.Samplers.Aniso8Wrap },
    PixelOutput = Common.Targets.Luminance_Depth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.PremultipliedAdd,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal, DepthWriteEnable = false },
    Parameters = { CameraInfo = Common.Core.CameraInfo, WorldInfo = Common.Core.WorldInfo, MaterialInfo = ChromaKeySingleLayerMaterialInfo, ModelInfo = Common.Core.RiggedModelInfo, MediaParams = core.MediaParams },
    PixelOutput = Common.Targets.Luminance_Depth,
    VertexInput = SingleUvTangentlessSkinnedVertexInput,
    VertexShader = "ChromaKeySkinnedMaterial",
    PixelShader = "ChromaKeySkinnedMaterial",
    Macros = { "MEDIA" },
}

local gatherSubsurfaceEffectCommon = partial(graphics_effect) {
    Type = effects.SubsurfaceGatherEffectType,
    Arguments = {CameraInfo = 'CameraInfo', SubsurfaceParams = 'SubsurfaceParams'},
}

local emissiveEffectCommon = partial(graphics_effect) {
    Type = effects.EmissiveEffectType,
    Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', MediaParams = 'MediaParams'},
}

local environmentEffectCommon = partial(graphics_effect) {
    Type = effects.EnvironmentEffectType,
    Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', SkyParams = 'SkyParams', DeferredBuffers = 'DeferredBuffers'},
}

local emissiveTrailsEffectCommon = partial(graphics_effect) {
    Type = effects.UnlitEmissiveEffectType,
    Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
}

local transmissiveEffectCommon = partial(graphics_effect) {
    Type = effects.TransmissiveEffectType,
    Arguments = {CameraInfo = 'CameraInfo'},
}

local videoScreenAddEffectCommon = partial(graphics_effect) {
    Type = effects.MediaAddEffectType,
    Arguments = {CameraInfo = 'CameraInfo', MediaParams = 'MediaParams', WorldInfo = 'WorldInfo'},
}

local triangleDensityCommon = partial(graphics_program) {
    Parameters = {CameraInfo = Common.Core.CameraInfo, DiagnosticsInfo = Common.Core.DiagnosticsInfo },
    Samplers = {},
    PixelOutput = Common.Targets.RasterLDR_Motion_Depth,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
}

local overdrawCommon = partial(graphics_program) {
    Parameters = {CameraInfo = Common.Core.CameraInfo},
    Samplers = {},
    PixelOutput = Common.Targets.RasterLDR_Motion_Depth,
    BlendState = Common.BlendSettings.States.PremultipliedAdd,
    DepthStencilState = depth_stencil_state { DepthEnable = false },
}

local wireframeCommon = partial(graphics_program) {
    Parameters = {CameraInfo = Common.Core.CameraInfo},
    Samplers = {},
    PixelOutput = Common.Targets.RasterLDR_Motion_Depth,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
}

local backfacesCommon = partial(graphics_program) {
    Parameters = {CameraInfo = Common.Core.CameraInfo},
    Samplers = {},
    CullMode = CullMode.Front,
    PixelOutput = Common.Targets.RasterLDR_Motion_Depth,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Greater },
}

local backfacesStaticCommon = partial(graphics_program) {
    backfacesCommon,
    Parameters = {ModelInfo = Common.Core.StaticModelInfo},
    VertexInput = SingleUvStaticVertexInput,
    VertexShader = "BackfacesStatic",
    PixelShader = "BackfacesStatic",
}

local backfacesSkinnedCommon = partial(graphics_program) {
    backfacesCommon,
    Parameters = {ModelInfo = Common.Core.RiggedModelInfo},
    VertexInput = SingleUvSkinnedVertexInput,
    VertexShader = "BackfacesSkinned",
    PixelShader = "BackfacesSkinned",
}

TriangleDensityStaticProgram = graphics_program {
    triangleDensityCommon,
    CullMode = CullMode.Back,
    Parameters = {ModelInfo = Common.Core.StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "TriangleDensityStatic",
    GeometryShader = "TriangleDensityStatic",
    PixelShader = "TriangleDensityStatic",
}
OverdrawStaticProgram = graphics_program {
    overdrawCommon,
    CullMode = CullMode.Back,
    Parameters = {ModelInfo = Common.Core.StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "OverdrawStatic",
    PixelShader = "OverdrawStatic",
}
WireframeStaticProgram = graphics_program {
    wireframeCommon,
    CullMode = CullMode.Back,
    Parameters = {ModelInfo = Common.Core.StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "WireframeStatic",
    GeometryShader = "WireframeStatic",
    PixelShader = "WireframeStatic",
}
BackfacesStaticProgram = graphics_program {
    backfacesStaticCommon,
}

TriangleDensitySkinnedProgram = graphics_program {
    triangleDensityCommon,
    CullMode = CullMode.Back,
    Parameters = {ModelInfo = Common.Core.RiggedModelInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "TriangleDensitySkinned",
    GeometryShader = "TriangleDensitySkinned",
    PixelShader = "TriangleDensitySkinned",
}
OverdrawSkinnedProgram = graphics_program {
    overdrawCommon,
    CullMode = CullMode.Back,
    Parameters = {ModelInfo = Common.Core.RiggedModelInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "OverdrawSkinned",
    PixelShader = "OverdrawSkinned",
}
WireframeSkinnedProgram = graphics_program {
    wireframeCommon,
    CullMode = CullMode.Back,
    Parameters = {ModelInfo = Common.Core.RiggedModelInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "WireframeSkinned",
    GeometryShader = "WireframeSkinned",
    PixelShader = "WireframeSkinned",
}
BackfacesSkinnedProgram = graphics_program {
    backfacesSkinnedCommon,
}

TwoSidedTriangleDensityStaticProgram = graphics_program {
    triangleDensityCommon,
    CullMode = CullMode.None,
    Parameters = {ModelInfo = Common.Core.StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "TriangleDensityStatic",
    GeometryShader = "TriangleDensityStatic",
    PixelShader = "TriangleDensityStatic",
}
TwoSidedOverdrawStaticProgram = graphics_program {
    overdrawCommon,
    CullMode = CullMode.None,
    Parameters = {ModelInfo = Common.Core.StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "OverdrawStatic",
    PixelShader = "OverdrawStatic",
}
TwoSidedWireframeStaticProgram = graphics_program {
    wireframeCommon,
    CullMode = CullMode.None,
    Parameters = {ModelInfo = Common.Core.StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "WireframeStatic",
    GeometryShader = "WireframeStatic",
    PixelShader = "WireframeStatic",
}
TwoSidedBackfacesStaticProgram = graphics_program {
    backfacesStaticCommon,
    Macros = { "TWO_SIDED" },
}

TwoSidedTriangleDensitySkinnedProgram = graphics_program {
    triangleDensityCommon,
    CullMode = CullMode.None,
    Parameters = {ModelInfo = Common.Core.RiggedModelInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "TriangleDensitySkinned",
    GeometryShader = "TriangleDensitySkinned",
    PixelShader = "TriangleDensitySkinned",
}
TwoSidedOverdrawSkinnedProgram = graphics_program {
    overdrawCommon,
    CullMode = CullMode.None,
    Parameters = {ModelInfo = Common.Core.RiggedModelInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "OverdrawSkinned",
    PixelShader = "OverdrawSkinned",
}
TwoSidedWireframeSkinnedProgram = graphics_program {
    wireframeCommon,
    CullMode = CullMode.None,
    Parameters = {ModelInfo = Common.Core.RiggedModelInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "WireframeSkinned",
    GeometryShader = "WireframeSkinned",
    PixelShader = "WireframeSkinned",
}
TwoSidedBackfacesSkinnedProgram = graphics_program {
    backfacesSkinnedCommon,
    Macros = { "TWO_SIDED" },
}

TriangleDensityStaticEffect = graphics_effect {
    Type = effects.TriangleDensityEffectType,
    Program = TriangleDensityStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo', DiagnosticsInfo = 'DiagnosticsInfo'},
}
OverdrawStaticEffect = graphics_effect {
    Type = effects.OverdrawEffectType,
    Program = OverdrawStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
WireframeStaticEffect = graphics_effect {
    Type = effects.WireframeEffectType,
    Program = WireframeStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
BackfacesStaticEffect = graphics_effect {
    Type = effects.BackfacesEffectType,
    Program = BackfacesStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}

TriangleDensitySkinnedEffect = graphics_effect {
    Type = effects.TriangleDensityEffectType,
    Program = TriangleDensitySkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo', DiagnosticsInfo = 'DiagnosticsInfo'},
}
OverdrawSkinnedEffect = graphics_effect {
    Type = effects.OverdrawEffectType,
    Program = OverdrawSkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
WireframeSkinnedEffect = graphics_effect {
    Type = effects.WireframeEffectType,
    Program = WireframeSkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
BackfacesSkinnedEffect = graphics_effect {
    Type = effects.BackfacesEffectType,
    Program = BackfacesSkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}

TwoSidedTriangleDensityStaticEffect = graphics_effect {
    Type = effects.TriangleDensityEffectType,
    Program = TwoSidedTriangleDensityStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo', DiagnosticsInfo = 'DiagnosticsInfo'},
}
TwoSidedOverdrawStaticEffect = graphics_effect {
    Type = effects.OverdrawEffectType,
    Program = TwoSidedOverdrawStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
TwoSidedWireframeStaticEffect = graphics_effect {
    Type = effects.WireframeEffectType,
    Program = TwoSidedWireframeStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
TwoSidedBackfacesStaticEffect = graphics_effect {
    Type = effects.BackfacesEffectType,
    Program = TwoSidedBackfacesStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}

TwoSidedTriangleDensitySkinnedEffect = graphics_effect {
    Type = effects.TriangleDensityEffectType,
    Program = TwoSidedTriangleDensitySkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo', DiagnosticsInfo = 'DiagnosticsInfo'},
}
TwoSidedOverdrawSkinnedEffect = graphics_effect {
    Type = effects.OverdrawEffectType,
    Program = TwoSidedOverdrawSkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
TwoSidedWireframeSkinnedEffect = graphics_effect {
    Type = effects.WireframeEffectType,
    Program = TwoSidedWireframeSkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
TwoSidedBackfacesSkinnedEffect = graphics_effect {
    Type = effects.BackfacesEffectType,
    Program = TwoSidedBackfacesSkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}

local diagnosticsDepthOnlyCommon = partial(graphics_program) {
    Parameters = { CameraInfo = Common.Core.CameraInfo },
    PixelOutput = Common.Targets.JustDepth,
    CullMode = CullMode.Back,
    BlendState = Common.BlendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
}

DiagnosticsDepthOnlyStaticProgram = graphics_program {
    diagnosticsDepthOnlyCommon,
    Parameters = { ModelInfo = Common.Core.StaticModelInfo },
    VertexInput = PositionOnlyStaticVertexInput,
    VertexShader = "DiagnosticsDepthOnlyStatic",
}

DiagnosticsDepthOnlyStaticEffect = graphics_effect {
    Type = effects.DiagnosticFillDepthEffectType,
    Program = DiagnosticsDepthOnlyStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}

DiagnosticsDepthOnlySkinnedProgram = graphics_program {
    diagnosticsDepthOnlyCommon,
    Parameters = { ModelInfo = Common.Core.RiggedModelInfo },
    VertexInput = PositionOnlySkinnedVertexInput,
    VertexShader = "DiagnosticsDepthOnlySkinned",
}

DiagnosticsDepthOnlySkinnedEffect = graphics_effect {
    Type = effects.DiagnosticFillDepthEffectType,
    Program = DiagnosticsDepthOnlySkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}

local staticGraphicsEffectsCommon = {
    core.DebugStaticGeometryEffect,
    core.HighlightMaskStaticEffect,
    core.HighlightSmoothStaticEffect,
    TriangleDensityStaticEffect,
    OverdrawStaticEffect,
    WireframeStaticEffect,
    BackfacesStaticEffect,
    DiagnosticsDepthOnlyStaticEffect,
}

local skinnedGraphicsEffectsCommon = {
    core.DebugSkinnedGeometryEffect,
    core.HighlightMaskSkinnedEffect,
    core.HighlightSmoothSkinnedEffect,
    TriangleDensitySkinnedEffect,
    OverdrawSkinnedEffect,
    WireframeSkinnedEffect,
    BackfacesSkinnedEffect,
    DiagnosticsDepthOnlySkinnedEffect,
}

local twoSidedStaticGraphicsEffectsCommon = {
    core.DebugStaticGeometryEffect,
    core.HighlightMaskStaticEffect,
    core.HighlightSmoothStaticEffect,
    TwoSidedTriangleDensityStaticEffect,
    TwoSidedOverdrawStaticEffect,
    TwoSidedWireframeStaticEffect,
    TwoSidedBackfacesStaticEffect,
    DiagnosticsDepthOnlyStaticEffect
}

local twoSidedSkinnedGraphicsEffectsCommon = {
    core.DebugSkinnedGeometryEffect,
    core.HighlightMaskSkinnedEffect,
    core.HighlightSmoothSkinnedEffect,
    TwoSidedTriangleDensitySkinnedEffect,
    TwoSidedOverdrawSkinnedEffect,
    TwoSidedWireframeSkinnedEffect,
    TwoSidedBackfacesSkinnedEffect,
    DiagnosticsDepthOnlySkinnedEffect
}

OpaqueSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin, effects.PreZBin},
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueSingleLayerStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueSingleLayerFadingStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
        effects.SolidCasterCascadeFadingStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueSingleLayerSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueSingleLayerFadingSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
        effects.SolidCasterCascadeFadingSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueSingleLayerNoShadowStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin, effects.PreZBin},
        DepthPrepass = render_bins { effects.PreZBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueSingleLayerStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueSingleLayerFadingStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueSingleLayerNoShadowSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueSingleLayerSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueSingleLayerFadingSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEnvironmentSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.EnvironmentOpaqueBin, effects.VRVisDepthBin, effects.PreZBin},
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEnvironmentSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            environmentEffectCommon,
            Program = OpaqueEnvironmentSingleLayerAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEnvironmentSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
        effects.SolidCasterCascadeFadingStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEnvironmentSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEnvironmentSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.EnvironmentOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEnvironmentSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            environmentEffectCommon,
            Program = OpaqueEnvironmentSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEnvironmentSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
        effects.SolidCasterCascadeFadingSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEnvironmentSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueVATSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueVATSingleLayerStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueVATSingleLayerFadingStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueVATMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueVATMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueVATSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueVATSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueVATSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueVATSingleLayerSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueVATSingleLayerFadingSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueVATMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueVATMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueVATSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueVATSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveVATSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveVATSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveVATSingleLayerAddStaticProgram,
        },
        graphics_effect {
            opaqueEmissiveMaskShadowEffectCommon,
            Program = OpaqueEmissiveVATMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueEmissiveMaskShadowCascadeEffectCommon,
            Program = OpaqueEmissiveVATMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissiveVATSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEmissiveVATSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveVATSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveVATSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveVATSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueEmissiveMaskShadowEffectCommon,
            Program = OpaqueEmissiveVATMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueEmissiveMaskShadowCascadeEffectCommon,
            Program = OpaqueEmissiveVATMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissiveVATSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEmissiveVATSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}


TwoSidedOpaqueSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueSingleLayerStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueSingleLayerFadingStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
        effects.SolidCasterCascadeFadingStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueSingleLayerSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueSingleLayerFadingSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
        effects.SolidCasterCascadeFadingSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueMaskSingleLayerNoShadowStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEnvironmentMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.EnvironmentOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEnvironmentMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            environmentEffectCommon,
            Program = OpaqueEnvironmentMaskSingleLayerAddStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueEnvironmentMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueEnvironmentMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEnvironmentMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEnvironmentMaskSingleLayerMaterialInfo, Material2 = OpaqueEnvironmentMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedOpaqueMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedOpaqueMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueMaskSingleLayerNoShadowStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueMaskSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueMaskSingleLayerNoShadowSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueMaskSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEnvironmentMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.EnvironmentOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEnvironmentMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            environmentEffectCommon,
            Program = OpaqueEnvironmentMaskSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueEnvironmentMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueEnvironmentMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEnvironmentMaskSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEnvironmentMaskSingleLayerMaterialInfo, Material2 = OpaqueEnvironmentMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedOpaqueMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedOpaqueMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueMaskSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueMaskSingleLayerNoShadowSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueMaskSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaquePixelizeMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaquePixelizeMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaquePixelizeMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaquePixelizeMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaquePixelizeMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaquePixelizeMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaquePixelizeMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaquePixelizeMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedOpaquePixelizeMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedOpaquePixelizeMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaquePixelizeMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaquePixelizeMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaquePixelizeMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaquePixelizeMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaquePixelizeMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaquePixelizeMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaquePixelizeMaskSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaquePixelizeMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaquePixelizeMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaquePixelizeMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedOpaquePixelizeMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedOpaquePixelizeMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaquePixelizeMaskSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaquePixelizeMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueDiffractMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitOpaqueDiffractionBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferDiffractEffectCommon,
            Type = effects.GbufferMotionDiffractionEffectType,
            Program = OpaqueDiffractMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueDiffractMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueDiffractMaskShadowCascadeStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueDiffractMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueDiffractMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitOpaqueDiffractionBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferDiffractEffectCommon,
            Type = effects.GbufferMotionDiffractionEffectType,
            Program = TwoSidedOpaqueDiffractMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedOpaqueDiffractMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedOpaqueDiffractMaskShadowCascadeStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueDiffractMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueDiffractMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitOpaqueDiffractionBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferDiffractEffectCommon,
            Type = effects.GbufferMotionDiffractionEffectType,
            Program = OpaqueDiffractMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueDiffractMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueDiffractMaskShadowCascadeSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueDiffractMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueDiffractMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitOpaqueDiffractionBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferDiffractEffectCommon,
            Type = effects.GbufferMotionDiffractionEffectType,
            Program = TwoSidedOpaqueDiffractMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedOpaqueDiffractMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedOpaqueDiffractMaskShadowCascadeSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueDiffractMaskSingleLayerMaterialInfo, Material2 = OpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveSingleLayerAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissiveSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissiveSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveSingleLayerNoShadowStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveSingleLayerAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissiveSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveSingleLayerNoShadowSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissiveSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueEmissiveSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueEmissiveSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScreenspaceOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScreenspaceOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScreenspaceOpaqueEmissiveSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScreenspaceOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScreenspaceOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScreenspaceOpaqueEmissiveSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScreenspaceOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedScreenspaceOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScreenspaceOpaqueEmissiveSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScreenspaceOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedScreenspaceOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScreenspaceOpaqueEmissiveSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TwoSidedOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveFresnelSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveFresnelSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveFresnelSingleLayerAddStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEmissiveFresnelSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveFresnelSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveFresnelSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveFresnelSingleLayerAddSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEmissiveFresnelSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueEmissiveFresnelSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueEmissiveFresnelSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedOpaqueEmissiveFresnelSingleLayerAddStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TwoSidedOpaqueEmissiveFresnelSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueEmissiveFresnelSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueEmissiveFresnelSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedOpaqueEmissiveFresnelSingleLayerAddSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TwoSidedOpaqueEmissiveFresnelSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissivePixelizeSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissivePixelizeSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissivePixelizeSingleLayerAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissivePixelizeSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissivePixelizeSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissivePixelizeSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissivePixelizeSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueEmissivePixelizeSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueEmissivePixelizeSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedOpaqueEmissivePixelizeSingleLayerAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueEmissivePixelizeSingleLayerStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TwoSidedOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueEmissivePixelizeSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedOpaqueEmissivePixelizeSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueEmissivePixelizeSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TwoSidedOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueMaskEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScrollingOpaqueMaskEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScrollingOpaqueMaskEmissiveSingleLayerAddStaticProgram,
        },
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueMaskEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScrollingOpaqueMaskEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScrollingOpaqueMaskEmissiveSingleLayerAddSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskEmissiveSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

BillboardStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitAlphaBin },
    },
    GraphicsEffects = flatten {
        graphics_effect {
            Type = effects.UnlitAbsorptionEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = BillboardMaskStaticProgram,
        },
        graphics_effect {
            Type = effects.UnlitEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = BillboardStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = BillboardMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

BillboardSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitAlphaBin },
    },
    GraphicsEffects = flatten {
        graphics_effect {
            Type = effects.UnlitAbsorptionEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = BillboardMaskSkinnedProgram,
        },
        graphics_effect {
            Type = effects.UnlitEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = BillboardSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = BillboardMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

BillboardPixelizeStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitAlphaBin },
    },
    GraphicsEffects = flatten {
        graphics_effect {
            Type = effects.UnlitAbsorptionEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = BillboardPixelizeMaskStaticProgram,
        },
        graphics_effect {
            Type = effects.UnlitEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = BillboardPixelizeStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = BillboardPixelizeMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

BillboardPixelizeSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitAlphaBin },
    },
    GraphicsEffects = flatten {
        graphics_effect {
            Type = effects.UnlitAbsorptionEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = BillboardPixelizeMaskSkinnedProgram,
        },
        graphics_effect {
            Type = effects.UnlitEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = BillboardPixelizeSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = BillboardPixelizeMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

StereographicOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = StereographicOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = StereographicOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

StereographicOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = StereographicOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = StereographicOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.DisplacementPreZBin },
        DepthPrepass = render_bins { effects.DisplacementPreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScrollingOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScrollingOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        DisplacementDepthOnlyStaticEffect,
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueEmissiveDisplacementShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueEmissiveDisplacementShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingOpaqueEmissiveSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.DisplacementPreZBin },
        DepthPrepass = render_bins { effects.DisplacementPreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedScrollingOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedScrollingOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        DisplacementDepthOnlyStaticEffect,
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueEmissiveDisplacementShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueEmissiveDisplacementShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingOpaqueEmissiveSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

MediaScrollingOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.DisplacementPreZBin },
        DepthPrepass = render_bins { effects.DisplacementPreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = MediaScrollingOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = MediaScrollingOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        DisplacementDepthOnlyStaticEffect,
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = MediaScrollingOpaqueEmissiveDisplacementShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = MediaScrollingOpaqueEmissiveDisplacementShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisMediaScrollingOpaqueEmissiveSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedMediaScrollingOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.DisplacementPreZBin },
        DepthPrepass = render_bins { effects.DisplacementPreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedMediaScrollingOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedMediaScrollingOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        DisplacementDepthOnlyStaticEffect,
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = MediaScrollingOpaqueEmissiveDisplacementShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = MediaScrollingOpaqueEmissiveDisplacementShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedMediaScrollingOpaqueEmissiveSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScrollingOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueEmissiveDisplacementShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingOpaqueEmissiveSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedScrollingOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueEmissiveDisplacementShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingOpaqueEmissiveSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

MediaScrollingOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = MediaScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = MediaScrollingOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = MediaScrollingOpaqueEmissiveDisplacementShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = MediaScrollingOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisMediaScrollingOpaqueEmissiveSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedMediaScrollingOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedMediaScrollingOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedMediaScrollingOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = MediaScrollingOpaqueEmissiveDisplacementShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = MediaScrollingOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedMediaScrollingOpaqueEmissiveSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueEmissiveMediaSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueEmissivePixelizeSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.DisplacementPreZBin },
        DepthPrepass = render_bins { effects.DisplacementPreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScrollingOpaqueEmissivePixelizeSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScrollingOpaqueEmissivePixelizeSingleLayerAddStaticProgram,
        },
        DisplacementDepthOnlyStaticEffect,
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueEmissivePixelizeDisplacementShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueEmissivePixelizeDisplacementShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingOpaqueEmissivePixelizeSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.DisplacementPreZBin },
        DepthPrepass = render_bins { effects.DisplacementPreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerAddStaticProgram,
        },
        DisplacementDepthOnlyStaticEffect,
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedScrollingOpaqueEmissivePixelizeDisplacementShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedScrollingOpaqueEmissivePixelizeDisplacementShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingOpaqueEmissivePixelizeSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueEmissivePixelizeSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScrollingOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScrollingOpaqueEmissivePixelizeSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueEmissivePixelizeDisplacementShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueEmissivePixelizeDisplacementShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingOpaqueEmissivePixelizeSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedScrollingOpaqueEmissivePixelizeDisplacementShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedScrollingOpaqueEmissivePixelizeDisplacementShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingOpaqueEmissivePixelizeSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueEmissivePixelizeSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingScreenspaceOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.DisplacementPreZBin },
        DepthPrepass = render_bins { effects.DisplacementPreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScrollingScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScrollingScreenspaceOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        DisplacementDepthOnlyStaticEffect,
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingScreenspaceOpaqueEmissiveDisplacementShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingScreenspaceOpaqueEmissiveDisplacementShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingScreenspaceOpaqueEmissiveSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.DisplacementPreZBin },
        DepthPrepass = render_bins { effects.DisplacementPreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerAddStaticProgram,
        },
        DisplacementDepthOnlyStaticEffect,
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedScrollingScreenspaceOpaqueEmissiveDisplacementShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedScrollingScreenspaceOpaqueEmissiveDisplacementShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingScreenspaceOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = ScrollingScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = ScrollingScreenspaceOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingScreenspaceOpaqueEmissiveDisplacementShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingScreenspaceOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingScreenspaceOpaqueEmissiveSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerAddSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedScrollingScreenspaceOpaqueEmissiveDisplacementShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedScrollingScreenspaceOpaqueEmissiveDisplacementShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = ScrollingOpaqueMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingOpaqueMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueMaskSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueMaskPixelSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = ScrollingOpaqueMaskPixelSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueMaskPixelShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueMaskPixelShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingOpaqueMaskPixelSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingOpaqueMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedScrollingOpaqueMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedScrollingOpaqueMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedScrollingOpaqueMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingOpaqueMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueMaskSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = ScrollingOpaqueMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingOpaqueMaskSingleLayerSkinnedProgram,
        },

    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueMaskSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueMaskPixelSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = ScrollingOpaqueMaskPixelSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueMaskPixelShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueMaskPixelShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisScrollingOpaqueMaskPixelSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueMaskBillboardSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingOpaqueMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedScrollingOpaqueMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedScrollingOpaqueMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedScrollingOpaqueMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingOpaqueMaskSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueMaskSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueDiffractMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitOpaqueDiffractionBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferDiffractEffectCommon,
            Type = effects.GbufferMotionDiffractionEffectType,
            Program = ScrollingOpaqueDiffractMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueDiffractMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueDiffractMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisDepthVRVisOpaqueEmissiveSingleLayerStaticProgramProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingOpaqueDiffractMaskSingleLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitOpaqueDiffractionBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferDiffractEffectCommon,
            Type = effects.GbufferMotionDiffractionEffectType,
            Program = TwoSidedScrollingOpaqueDiffractMaskSingleLayerStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedScrollingOpaqueDiffractMaskShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedScrollingOpaqueDiffractMaskShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingOpaqueDiffractMaskSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

ScrollingOpaqueDiffractMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitOpaqueDiffractionBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferDiffractEffectCommon,
            Type = effects.GbufferMotionDiffractionEffectType,
            Program = ScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = ScrollingOpaqueDiffractMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = ScrollingOpaqueDiffractMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisDepthVRVisOpaqueEmissiveSingleLayerSkinnedProgramProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedScrollingOpaqueDiffractMaskSingleLayerSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitOpaqueDiffractionBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferDiffractEffectCommon,
            Type = effects.GbufferMotionDiffractionEffectType,
            Program = TwoSidedScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = TwoSidedScrollingOpaqueDiffractMaskShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = TwoSidedScrollingOpaqueDiffractMaskShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedScrollingOpaqueDiffractMaskSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ScrollingOpaqueDiffractMaskSingleLayerMaterialInfo, Material2 = ScrollingOpaqueMaskSingleLayerMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

EmissiveStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitAlphaEmissiveBin, effects.VRVisDepthBin },
    },
    GraphicsEffects = flatten {
        graphics_effect {
            Type = effects.UnlitAbsorptionEmissiveEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = EmissiveMaskStaticProgram,
        },
        graphics_effect {
            Type = effects.UnlitEmissiveEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = EmissiveStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisEmissiveStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = EmissiveMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedEmissiveStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitAlphaEmissiveBin, effects.VRVisDepthBin },
    },
    GraphicsEffects = flatten {
        graphics_effect {
            Type = effects.UnlitAbsorptionEmissiveEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TwoSidedEmissiveMaskStaticProgram,
        },
        graphics_effect {
            Type = effects.UnlitEmissiveEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TwoSidedEmissiveStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedEmissiveStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = EmissiveMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

EmissiveSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitAlphaEmissiveBin, effects.VRVisDepthBin },
    },
    GraphicsEffects = flatten {
        graphics_effect {
            Type = effects.UnlitAbsorptionEmissiveEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = EmissiveMaskSkinnedProgram,
        },
        graphics_effect {
            Type = effects.UnlitEmissiveEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = EmissiveSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisEmissiveSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = EmissiveMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedEmissiveSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.UnlitAlphaEmissiveBin, effects.VRVisDepthBin },
    },
    GraphicsEffects = flatten {
        graphics_effect {
            Type = effects.UnlitAbsorptionEmissiveEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TwoSidedEmissiveMaskSkinnedProgram,
        },
        graphics_effect {
            Type = effects.UnlitEmissiveEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TwoSidedEmissiveSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedEmissiveSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = EmissiveMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransmissiveEmissiveStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveTransmissiveBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            transmissiveEffectCommon,
            Program = TransmissiveMultiplyStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TransmissiveEmissiveAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTransmissiveEmissiveStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TransmissiveEmissiveMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransmissiveEmissiveSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveTransmissiveBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            transmissiveEffectCommon,
            Program = TransmissiveMultiplySkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TransmissiveEmissiveAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTransmissiveEmissiveSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TransmissiveEmissiveMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransparentStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.TransparentBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            Type = effects.TransparentGbufferEffectType,
            Arguments = {CameraInfo = 'CameraInfo', TransparentStageParams = 'TransparentStageParams'},
            Program = TransparentGbufferStaticProgram,
        },
        graphics_effect {
            Type = effects.TransmittanceEffectType,
            Arguments = {CameraInfo = 'CameraInfo'},
            Program = TransparentTransmittanceStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TransparentMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransparentSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.TransparentBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            Type = effects.TransparentGbufferEffectType,
            Arguments = {CameraInfo = 'CameraInfo', TransparentStageParams = 'TransparentStageParams'},
            Program = TransparentGbufferSkinnedProgram,
        },
        graphics_effect {
            Type = effects.TransmittanceEffectType,
            Arguments = {CameraInfo = 'CameraInfo'},
            Program = TransparentTransmittanceSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TransparentMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransparentMultibumpStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.TransparentBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            Type = effects.TransparentGbufferEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', TransparentStageParams = 'TransparentStageParams'},
            Program = TransparentMultibumpGbufferStaticProgram,
        },
        graphics_effect {
            Type = effects.TransmittanceEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TransparentMultibumpTransmittanceStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TransparentMultibumpMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransparentMultibumpSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.TransparentBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            Type = effects.TransparentGbufferEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', TransparentStageParams = 'TransparentStageParams'},
            Program = TransparentMultibumpGbufferSkinnedProgram,
        },
        graphics_effect {
            Type = effects.TransmittanceEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TransparentMultibumpTransmittanceSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TransparentMultibumpMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransparentMultibumpDiffractStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.TransparentDiffractBin, effects.TransDiffractPreZBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            Type = effects.TransparentDiffractGbufferEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', TransparentStageParams = 'TransparentStageParams', DiffractionAlbedoParams = 'DiffractionAlbedoParams'},
            Program = TransparentMultibumpDiffractGbufferStaticProgram,
        },
        graphics_effect {
            Type = effects.TransmittanceEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TransparentMultibumpTransmittanceStaticProgram,
        },
        TransDiffractDepthOnlyStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TransparentMultibumpMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransparentMultibumpDiffractSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.TransparentDiffractBin, effects.TransDiffractPreZBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            Type = effects.TransparentDiffractGbufferEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', TransparentStageParams = 'TransparentStageParams', DiffractionAlbedoParams = 'DiffractionAlbedoParams'},
            Program = TransparentMultibumpDiffractGbufferSkinnedProgram,
        },
        graphics_effect {
            Type = effects.TransmittanceEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TransparentMultibumpTransmittanceSkinnedProgram,
        },
        TransDiffractDepthOnlySkinnedEffect
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TransparentMultibumpMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransparentMultibumpEnvironmentDiffractStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.TransparentDiffractBin, effects.EnvironmentOpaqueBin, effects.TransDiffractPreZBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            Type = effects.TransparentDiffractGbufferEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', TransparentStageParams = 'TransparentStageParams', DiffractionAlbedoParams = 'DiffractionAlbedoParams'},
            Program = TransparentMultibumpEnvironmentDiffractGbufferStaticProgram,
        },
        graphics_effect {
            Type = effects.TransmittanceEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TransparentMultibumpTransmittanceStaticProgram,
        },
        graphics_effect {
            environmentEffectCommon,
            Program = TransparentEnvironmentSingleLayerAddStaticProgram,
        },
        TransDiffractDepthOnlyStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TransparentMultibumpMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TransparentMultibumpEnvironmentDiffractSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.TransparentDiffractBin, effects.EnvironmentOpaqueBin, effects.TransDiffractPreZBin },
        CastShadowsStatic = render_bins { },
        CastShadowsDynamic = render_bins { },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            Type = effects.TransparentDiffractGbufferEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo', TransparentStageParams = 'TransparentStageParams', DiffractionAlbedoParams = 'DiffractionAlbedoParams'},
            Program = TransparentMultibumpEnvironmentDiffractGbufferSkinnedProgram,
        },
        graphics_effect {
            Type = effects.TransmittanceEffectType,
            Arguments = {CameraInfo = 'CameraInfo', WorldInfo = 'WorldInfo'},
            Program = TransparentMultibumpTransmittanceSkinnedProgram,
        },
        graphics_effect {
            environmentEffectCommon,
            Program = TransparentEnvironmentSingleLayerAddSkinnedProgram,
        },
        TransDiffractDepthOnlySkinnedEffect
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TransparentMultibumpMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

VideoScreenStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.MediaBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            videoScreenAddEffectCommon,
            Program = VideoScreenAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisVideoScreenStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

VideoScreenSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.MediaBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            videoScreenAddEffectCommon,
            Program = VideoScreenAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisVideoScreenSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

StereographicVideoScreenStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.MediaBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = StereographicOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
            videoScreenAddEffectCommon,
            Program = StereographicVideoScreenAddStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

StereographicVideoScreenSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.MediaBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = StereographicOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
            videoScreenAddEffectCommon,
            Program = StereographicVideoScreenAddSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = StereographicOpaqueEmissiveSingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ChromaKeyVideoScreenStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.ChromaKeyMediaBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            Arguments = {CameraInfo = 'CameraInfo', MediaParams = 'MediaParams', WorldInfo = 'WorldInfo'},
            Type = effects.MediaGbufferMotionEffectType,
            Program = ChromaKeyOpaqueEmissiveSingleLayerBaseStaticProgram,
        },
        graphics_effect {
			videoScreenAddEffectCommon,
            Program = ChromaKeyVideoScreenAddStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = ChromaKeySingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

ChromaKeyVideoScreenSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.ChromaKeyMediaBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            Arguments = {CameraInfo = 'CameraInfo', MediaParams = 'MediaParams', WorldInfo = 'WorldInfo'},
            Type = effects.MediaGbufferMotionEffectType,
            Program = ChromaKeyOpaqueEmissiveSingleLayerBaseSkinnedProgram,
        },
        graphics_effect {
			videoScreenAddEffectCommon,
            Program = ChromaKeyVideoScreenAddSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = ChromaKeySingleLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueSingleLayerDetailStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueSingleLayerDetailStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueSingleLayerDetailFadingStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueSingleLayerDetailStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
        effects.SolidCasterCascadeFadingStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueSingleLayerDetailSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueSingleLayerDetailSkinnedProgram,
            Program = OpaqueSingleLayerDetailSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueSingleLayerDetailFadingSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueSingleLayerDetailSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
        effects.SolidCasterCascadeFadingSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueSingleLayerDetailStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueSingleLayerDetailStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueSingleLayerDetailFadingStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueSingleLayerDetailStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
        effects.SolidCasterCascadeFadingStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueSingleLayerDetailSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueSingleLayerDetailSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueSingleLayerDetailFadingSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueSingleLayerDetailSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
        effects.SolidCasterCascadeFadingSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueMaskSingleLayerDetailStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueMaskSingleLayerDetailStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueMaskDetailShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueMaskDetailShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueMaskSingleLayerDetailStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueMaskSingleLayerDetailMaterialInfo, Material2 = OpaqueMaskSingleLayerDetailMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueMaskSingleLayerDetailSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueMaskSingleLayerDetailSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueMaskDetailShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueMaskDetailShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueMaskSingleLayerDetailSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueMaskSingleLayerDetailMaterialInfo, Material2 = OpaqueMaskSingleLayerDetailMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueMaskSingleLayerDetailStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueMaskSingleLayerDetailStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueMaskDetailShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueMaskDetailShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueMaskSingleLayerDetailStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueMaskSingleLayerDetailMaterialInfo, Material2 = OpaqueMaskSingleLayerDetailMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueMaskSingleLayerDetailSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueFadingBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = TwoSidedOpaqueMaskSingleLayerDetailSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueMaskDetailShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueMaskDetailShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueMaskSingleLayerDetailSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueMaskSingleLayerDetailMaterialInfo, Material2 = OpaqueMaskSingleLayerDetailMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueSubsurfaceSingleLayerDetailStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.SubsurfaceBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueSubsurfaceSingleLayerDetailStaticProgram,
        },
        graphics_effect {
            gatherSubsurfaceEffectCommon,
            Program = GatherSubsurfaceStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueSubsurfaceSingleLayerStaticProgram,
        },
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueSubsurfaceSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueSubsurfaceSingleLayerDetailSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.SubsurfaceBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueSubsurfaceSingleLayerDetailSkinnedProgram,
        },
        graphics_effect {
            gatherSubsurfaceEffectCommon,
            Program = GatherSubsurfaceSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueSubsurfaceSingleLayerSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueSubsurfaceSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueMaskSubsurfaceSingleLayerDetailStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.SubsurfaceBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueMaskSubsurfaceSingleLayerDetailStaticProgram,
        },
        graphics_effect {
            gatherSubsurfaceEffectCommon,
            Program = GatherMaskedSubsurfaceStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueMaskSubsurfaceDetailShadowStaticProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueMaskSubsurfaceDetailShadowCascadeStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueMaskSubsurfaceSingleLayerStaticProgram,
        },
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo, Material2 = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueMaskSubsurfaceSingleLayerDetailSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.SubsurfaceBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterFadingBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueMaskSubsurfaceSingleLayerDetailSkinnedProgram,
        },
        graphics_effect {
            gatherSubsurfaceEffectCommon,
            Program = GatherMaskedSubsurfaceSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowEffectCommon,
            Program = OpaqueMaskSubsurfaceDetailShadowSkinnedProgram,
        },
        graphics_effect {
            opaqueMaskShadowCascadeEffectCommon,
            Program = OpaqueMaskSubsurfaceDetailShadowCascadeSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueMaskSubsurfaceSingleLayerSkinnedProgram,
        },
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo, Material2 = OpaqueMaskSubsurfaceSingleLayerDetailMaterialInfo2, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveSingleLayerDetailStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveSingleLayerDetailBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveSingleLayerDetailAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissiveSingleLayerDetailStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueEmissiveSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueEmissiveSingleLayerDetailSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        skinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueEmissiveSingleLayerDetailBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = OpaqueEmissiveSingleLayerDetailAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisOpaqueEmissiveSingleLayerDetailSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = OpaqueEmissiveSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueEmissiveSingleLayerDetailStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedStaticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueEmissiveSingleLayerDetailBaseStaticProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedOpaqueEmissiveSingleLayerDetailAddStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueEmissiveSingleLayerDetailStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = TwoSidedOpaqueEmissiveSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

TwoSidedOpaqueEmissiveSingleLayerDetailSkinned = effect_table {
    BinSelections = {
        Default = render_bins { effects.EmissiveOpaqueBin, effects.VRVisDepthBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },
        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        twoSidedSkinnedGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = TwoSidedOpaqueEmissiveSingleLayerDetailBaseSkinnedProgram,
        },
        graphics_effect {
            emissiveEffectCommon,
            Program = TwoSidedOpaqueEmissiveSingleLayerDetailAddSkinnedProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.VRVisDepthEffectType,
            Program = DepthVRVisTwoSidedOpaqueEmissiveSingleLayerDetailSkinnedProgram,
        },
        effects.SolidCasterSkinnedEffect,
        effects.SolidCasterCascadeSkinnedEffect,
    },
    DrawParameters = { Model = Common.Core.RiggedModelInfo, Material = TwoSidedOpaqueEmissiveSingleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueDiffuseMultiLayerStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueDiffuseMultiLayerStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueDiffuseMultiLayerFadingStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
        effects.SolidCasterCascadeFadingStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueDiffuseMultiLayerMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}

OpaqueTripleLayerDetailStatic = effect_table {
    BinSelections = {
        Default = render_bins { effects.LitOpaqueBin, effects.PreZBin },
        DepthPrepass = render_bins { effects.PreZBin },
        CastShadowsStatic = render_bins { effects.StaticCasterBin },
        CastShadowsDynamic = render_bins { effects.DynamicCasterBin },

        DefaultFading = render_bins { effects.LitOpaqueFadingBin },
        CastShadowsStaticFading = render_bins { effects.StaticCasterFadingBin },
        CastShadowsDynamicFading = render_bins { effects.DynamicCasterFadingBin },

        Highlight = render_bins { core.HighlightBin },
    },
    GraphicsEffects = flatten {
        staticGraphicsEffectsCommon,
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionEffectType,
            Program = OpaqueTripleLayerDetailStaticProgram,
        },
        graphics_effect {
            gbufferEffectCommon,
            Type = effects.GbufferMotionFadingEffectType,
            Program = OpaqueTripleLayerDetailFadingStaticProgram,
        },
        TrivialDepthOnlyStaticEffect,
        effects.SolidCasterStaticEffect,
        effects.SolidCasterCascadeStaticEffect,
        effects.SolidCasterCascadeFadingStaticEffect,
    },
    DrawParameters = { Model = Common.Core.StaticModelInfo, Material = OpaqueTripleLayerDetailMaterialInfo, ModelHighlightInfo = core.ModelHighlightInfo },
}