#include "Generated/UI_UIViewPanelProgram.hlsli"
#include "Generated/UI_UIInWorldPanelProgram.hlsli"
#include "Generated/UI_UIIndirectPanelProgram.hlsli"

#include "../Common/MotionUtil.hlsli"
#include "../Common/StereoUtil.hlsli"
#include "../Common/ColorUtil.hlsli"
#include "../Common/MathUtil.hlsli"

#ifdef INDIRECT
//#include "../Common/HDRUtil.hlsli"
#endif


struct VertexOutput
{
    float stereoClip : SV_ClipDistance;
    float4 position : SV_Position;
    float2 texcoord : TEXCOORD0;
    //float4 history : TEXCOORD1;
};

float3 InvertToneCurve(float3 x)
{
#ifdef IN_WORLD
    x = saturate(x) * 0.9;  // prevent white stuff from glowing like the sun
    //x = invertAcesFilmicCurve(x, 3.3);
    //x /= UIFrameInfo(Exposure)[0];
#endif
    return x;
}

VertexOutput VertexMain(VertexInput input, uint instanceId : SV_InstanceID, uint vertexId : SV_VertexID)
{
    VertexOutput output;

    bool stereoSide = stereoSideFromInstanceID(instanceId);

    float3 sourcePosition = decodeHalf4ToFloat3ExtraPrecision(input.position);
    float3 positionModelSpace = sourcePosition;
    float3 positionModelSpaceHistory = sourcePosition;
    float3 positionViewSpace = stereoTransformModelToView(stereoSide, UIPanelData(CanvasTransform), positionModelSpace);
    float3 positionViewSpaceHistory = stereoTransformModelToViewHistory(stereoSide, UIPanelData(CanvasTransformHistory), positionModelSpaceHistory);

    //output.position = stereoTransformViewSpaceToClipSpace(asPoint(positionViewSpace), stereoSide, true, output.stereoClip);
    //output.history = stereoTransformViewSpaceToClipSpaceHistory(asPoint(positionViewSpaceHistory), stereoSide, true);
    output.position = stereoTransformViewSpaceToClipSpace(asPoint(positionViewSpace), stereoSide, false, output.stereoClip);
    //output.history = stereoTransformViewSpaceToClipSpaceHistory(asPoint(positionViewSpaceHistory), stereoSide, false);
    output.texcoord = input.texCoord;

    return output;
}

PixelOutput PixelMain(VertexOutput input, float4 screenCoord : SV_Position)
{
#if defined (IN_WORLD) || defined (INDIRECT)
    float2 panelCoord = input.texcoord * CameraInfo(DisplayViewportSize);
    //float2 jitter = CameraInfo(TemporalJitter).xy * float2(0.5, -0.5) * CameraInfo(DisplayViewportSize);
    //panelCoord += jitter;
    float2 uvMotion = (panelCoord) / CameraInfo(DisplayViewportSize).xy;
#else
    float2 uvMotion = input.texcoord;
#endif

#ifdef INDIRECT
    float4 color = UIPanelData(CanvasTexture).Sample(PointSampler, uvMotion);
#else
    float4 color = UIPanelData(CanvasTexture).SampleBias(AnisoSampler, uvMotion, -1.);
#endif

    PixelOutput output;
    output.color0 = float4(srgbToLinear(color.rgb), color.a) * UIPanelData(CanvasColor);
    output.color0.rgb = InvertToneCurve(output.color0.rgb);
    output.color1 = output.color0;

#ifdef INDIRECT
    if (UIFrameInfo(HDRTonemapping))
    {
        //output.color0.rgb = saturate(output.color0.rgb) * 0.9;
        //output.color0.rgb = REC709toREC2020(output.color0.rgb);
        //output.color0.rgb = reinhard_extended_luminance(output.color0.rgb, 30, 80);
        //output.color0.rgb = ApplyREC2084Curve(output.color0.rgb);
    }
#endif

#ifdef IN_WORLD
    // We no longer motion reproject the UI as it is now drawn after taa and upscaling
    //float2 motion = calculatePixelMotion(CameraInfo(RenderViewportOffset), CameraInfo(RenderViewportSize), input.position, input.history);
    //output.color1 = float4(motion.xy, 0, 1);
#endif

    if (output.color0.a == 0.)
    {
        discard;
    }

    return output;
}
