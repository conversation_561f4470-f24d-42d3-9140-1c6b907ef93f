/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHA<PERSON>ABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#include "../Types.h"

#ifdef LLCORE_PLATFORM_WINDOWS

#include "../HardwareInfo.h"
#include "../Log.h"
#include "../Math.h"
#include "../StringUtil.h"
#include "Windows.h"

#include <Devpkey.h>
#include <WbemIdl.h>
#include <comdef.h>
#include <dxgi.h>
#include <immintrin.h>
#include <intrin.h>
#include <setupapi.h>
#include <stdio.h>
#include <tchar.h>

#pragma comment(lib, "wbemuuid.lib")
#pragma comment(lib, "d3d9.lib")
#pragma comment(lib, "SetupApi.lib")

namespace LLCore
{
namespace HardwareInfo
{
    bool SetMaxHardwareThreadCount(int numThreads)
    {
        HANDLE   currentProcess = GetCurrentProcess();
        mem_uint processAffinityMask;
        mem_uint systemAffinityMask;
        ::GetProcessAffinityMask(currentProcess, (PDWORD_PTR)&processAffinityMask, (PDWORD_PTR)&systemAffinityMask);
        const int maxCores = LLCore::CountSetBits(systemAffinityMask);
        if (numThreads < maxCores)
        {
            if (Internal::s_hyperthreadingEnabled)
            {
                // set affinity to every other core
                int numPrimaryThreads = LLCore::Min(maxCores / 2, numThreads);
                int numHyperThreads   = numThreads - numPrimaryThreads;
                processAffinityMask   = 0xAAAA'AAAA'AAAA'AAAA & ((0x1 << (numPrimaryThreads * 2)) - 1);
                processAffinityMask |= 0x5555'5555'5555'5555 & ((0x1 << (numHyperThreads * 2)) - 1);
            }
            else
            {
                processAffinityMask = (0x1 << numThreads) - 1;
            }

            processAffinityMask = processAffinityMask & systemAffinityMask;
            LLCORE_ASSERT(LLCore::CountSetBits(processAffinityMask) == numThreads, "Could not construct processor bit mask for requested number of threads");
            BOOL result = ::SetProcessAffinityMask(currentProcess, processAffinityMask);
            return result != FALSE;
        }

        return false;
    }

    namespace Internal
    {

        int  s_numLogicalHardwareThreads = 0;
        int  s_maxSseVersionSupported    = 0x00;
        int  s_numMonitors               = 0;
        bool s_popCntSupported           = false;
        bool s_hyperthreadingEnabled     = false;
        bool s_hardwareInfoInitialized   = false;

        static LLCore::Array<GpuInfo, 4> s_gpuInfos;

        LLCore::Uuid s_machineGuid;

        int      s_numCacheLevels            = 0;
        mem_uint s_cacheLevelSizes[3]        = {0, 0, 0};
        mem_uint s_halfSizeHighestCacheLevel = 0;

        typedef BOOL(WINAPI* LPFN_GLPI)(
            PSYSTEM_LOGICAL_PROCESSOR_INFORMATION,
            PDWORD);

        void InitGpuInfo()
        {
            if (Internal::s_gpuInfos.isEmpty())
            {
                // copied from windows headers to avoid linking in more libs
                // assumed to be stable
                const GUID DisplayAdapterGuid{0x5b45201d, 0xf2f2, 0x4f3b, {0x85, 0xbb, 0x30, 0xff, 0x1f, 0x95, 0x35, 0x99}};

                HDEVINFO deviceInfoHandle = ::SetupDiGetClassDevs(&DisplayAdapterGuid, nullptr, nullptr, DIGCF_DEVICEINTERFACE | DIGCF_PRESENT);
                if (deviceInfoHandle == nullptr)
                {
                    return;
                }

                SP_DEVINFO_DATA deviceInfoData = {};
                deviceInfoData.cbSize          = sizeof(SP_DEVINFO_DATA);

                int       deviceIndex = 0;
                HINSTANCE dxgiHandle  = ::LoadLibrary(L"dxgi.dll");

                using CreateDxgiFactoryFunc = HRESULT(WINAPI*)(REFIID, void**);
                auto* dxgiFactoryFunc       = (CreateDxgiFactoryFunc)::GetProcAddress(dxgiHandle, "CreateDXGIFactory1");
                if (dxgiFactoryFunc == nullptr)
                {
                    dxgiFactoryFunc = (CreateDxgiFactoryFunc)::GetProcAddress(dxgiHandle, "CreateDXGIFactory");
                }
                IDXGIFactory* dxgiFactory = nullptr;
                if (dxgiFactoryFunc)
                {
                    dxgiFactoryFunc(__uuidof(IDXGIFactory), (void**)&dxgiFactory);
                }

                while (::SetupDiEnumDeviceInfo(deviceInfoHandle, deviceIndex, &deviceInfoData))
                {
                    GpuInfo* info = s_gpuInfos.insertLast();

                    char propertyData[256] = {0};
                    // copied from windows headers to avoid linking in more libs
                    // assumed to be stable

                    const DEVPROPKEY driverVersionKey{{0xa8b865dd, 0x2e3d, 0x4094, {0xad, 0x97, 0xe5, 0x93, 0xa7, 0xc, 0x75, 0xd6}}, 3}; //DEVPKEY_Device_DriverVersion
                    const DEVPROPKEY deviceNameKey{{0xb725f130, 0x47ef, 0x101a, {0xa5, 0xf1, 0x02, 0x60, 0x8c, 0x9e, 0xeb, 0xac}}, 10};  // DEVPKEY_NAME

                    DEVPROPTYPE propertyType;

                    if (::SetupDiGetDeviceProperty(
                            deviceInfoHandle,
                            &deviceInfoData,
                            &deviceNameKey,
                            &propertyType,
                            (PBYTE)propertyData,
                            sizeof(propertyData),
                            nullptr,
                            0))
                    {
                        LLCore::ConvertToUtf8((wchar_t*)propertyData, &info->m_name);
                        // filter out Windows' built-in display driver
                        if (info->m_name.find("Microsoft Basic Display") != -1)
                        {
                            s_gpuInfos.removeLast();
                            deviceIndex++;
                            continue;
                        }
                    }

                    if (::SetupDiGetDeviceProperty(
                            deviceInfoHandle,
                            &deviceInfoData,
                            &driverVersionKey,
                            &propertyType,
                            (PBYTE)propertyData,
                            sizeof(propertyData),
                            nullptr,
                            0))
                    {
                        LLCore::ConvertToUtf8((wchar_t*)propertyData, &info->m_driverVersion);
                    }

                    IDXGIAdapter*     dxgiAdapter = nullptr;
                    DXGI_ADAPTER_DESC description = {};

                    if (dxgiFactory
                        && SUCCEEDED(dxgiFactory->EnumAdapters(deviceIndex, &dxgiAdapter))
                        && SUCCEEDED(dxgiAdapter->GetDesc(&description)))
                    {
                        // filter out Microsoft Basic Render Driver
                        if (StringCompare(description.Description, L"Microsoft Basic Render Driver") == 0)
                        {
                            s_gpuInfos.removeLast();
                            deviceIndex++;
                            continue;
                        }

                        info->m_dedicatedVideoMemoryBytes = description.DedicatedVideoMemory;
                        info->m_sharedVideoMemoryBytes    = description.SharedSystemMemory;
                    }

                    deviceIndex++;
                }

                if (dxgiFactory)
                {
                    dxgiFactory->Release();
                }

                ::SetupDiDestroyDeviceInfoList(deviceInfoHandle);
                ::FreeLibrary(dxgiHandle);
            }
        }

        BOOL CALLBACK IncrementMonitors(HMONITOR, HDC, LPRECT, LPARAM)
        {
            s_numMonitors++;
            return TRUE;
        };

        void InitializeActiveMonitors()
        {
            ::EnumDisplayMonitors(NULL, NULL, &IncrementMonitors, 0);
        }

        void InitializeHardwareCacheInfo()
        {
            BOOL                                  done         = FALSE;
            PSYSTEM_LOGICAL_PROCESSOR_INFORMATION buffer       = nullptr;
            PSYSTEM_LOGICAL_PROCESSOR_INFORMATION ptr          = nullptr;
            DWORD                                 returnLength = 0;
            PCACHE_DESCRIPTOR                     Cache;

            while (!done)
            {
                DWORD rc = ::GetLogicalProcessorInformation(buffer, &returnLength);

                if (FALSE == rc)
                {
                    if (GetLastError() == ERROR_INSUFFICIENT_BUFFER)
                    {
                        if (buffer)
                            free(buffer);

                        buffer = (PSYSTEM_LOGICAL_PROCESSOR_INFORMATION)malloc(
                            returnLength);

                        if (buffer == nullptr)
                        {
                            LLCORE_ASSERT(false, "Could not allocate buffer");
                            return;
                        }
                    }
                    else
                    {

                        LLCORE_ASSERT(false, "GetLogicalProcessorInformation call failed"); // Other error. We're probably toast.
                        return;
                    }
                }
                else
                {
                    done = TRUE;
                }
            }

            ptr = buffer;

            DWORD byteOffset = 0;

            while (byteOffset + sizeof(SYSTEM_LOGICAL_PROCESSOR_INFORMATION) <= returnLength)
            {
                switch (ptr->Relationship)
                {
                    case RelationProcessorCore:
                        // A hyperthreaded core supplies more than one logical processor.
                        if (CountSetBits((mem_uint)ptr->ProcessorMask) > 1)
                        {
                            s_hyperthreadingEnabled = true;
                        }
                        break;

                    case RelationCache:
                        // Cache data is in ptr->Cache, one CACHE_DESCRIPTOR structure for each cache.
                        Cache                               = &ptr->Cache;
                        s_numCacheLevels                    = LLCore::Max(s_numCacheLevels, Cache->Level);
                        s_cacheLevelSizes[Cache->Level - 1] = Cache->Size;

                        break;

                    default:
                        break;
                }
                byteOffset += sizeof(SYSTEM_LOGICAL_PROCESSOR_INFORMATION);
                ptr++;
            }

            free(buffer);

            LLCORE_ASSERT(s_numCacheLevels > 0, "Must have more than 0 cache levels");
            s_halfSizeHighestCacheLevel = (s_cacheLevelSizes[s_numCacheLevels - 1] / 2);

            return;
        }

        int GetMinimumSseLevel(bool* popCntSupported)
        {
            int info[4];
            __cpuid(info, 0x1);

            int ecx = info[2];
            int edx = info[3];

            // Test for popcnt
            if (popCntSupported)
            {
                *popCntSupported = (ecx >> 23) & 1 ? true : false;
            }

            // Test for AVX
            bool osSupportsXsave = (ecx & (1 << 27)) != 0;
            bool cpuAVXSupport   = (ecx & (1 << 28)) != 0;
            if (osSupportsXsave && cpuAVXSupport)
            {
                unsigned __int64 extendedControlRegisters = _xgetbv(_XCR_XFEATURE_ENABLED_MASK);
                if (extendedControlRegisters & 0x6)
                {
                    return 0x50; // AVX Supported
                }
            }

            // Test for SSE 4.2
            if (ecx & (1 << 20))
            {
                return 0x42;
            }
            else if (ecx & (1 << 19)) // SSE 4.1
            {
                return 0x41;
            }
            else if (ecx & (1 << 9)) // SSSE3
            {
                return 0x31;
            }
            else if (ecx & (1 << 0)) // SSE3
            {
                return 0x30;
            }
            else if (edx & (1 << 26)) // SSE2
            {
                return 0x20;
            }
            else if (edx & (1 << 25)) // SSE
            {
                return 0x10;
            }
            else
            {
                return 0x0;
            }
        }

        static bool GetMachineGuid(LLCore::Uuid* machineGuid)
        {
            HKEY hTestKey;

            if (::RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"SOFTWARE\\Microsoft\\Cryptography", 0, KEY_READ, &hTestKey) == ERROR_SUCCESS)
            {
                DWORD   pdwType    = 0;              // _Out_opt_
                wchar_t pvData[37] = {0};            // UUID size plus null terminator
                DWORD   pcbData    = sizeof(pvData); // _Inout_opt_
                LONG    result     = ::RegGetValue(hTestKey, nullptr, L"MachineGuid", RRF_RT_REG_SZ, &pdwType, pvData, &pcbData);
                bool    succeeded  = result == ERROR_SUCCESS;
                if (succeeded)
                {
                    succeeded = ConvertType(pvData, machineGuid);
                }
                RegCloseKey(hTestKey);
                return succeeded;
            }

            return false;
        }

        void InitializeHardwareInfo()
        {
            if (s_hardwareInfoInitialized) return;

            // Initialize hardware thread count
            {
                ::SYSTEM_INFO info;
                ::GetSystemInfo(&info);
                s_numLogicalHardwareThreads = (int)info.dwNumberOfProcessors;
            }

            [[maybe_unused]] int result = GetMachineGuid(&s_machineGuid);
            LLCORE_ASSERT(result, "Could Not Retrieve Machine GUID");

            InitializeHardwareCacheInfo();

            s_maxSseVersionSupported = GetMinimumSseLevel(&s_popCntSupported);

            InitializeActiveMonitors();

            s_hardwareInfoInitialized = true;
        }
    } // namespace Internal

    void GetCpuInfoString(LLCore::String* outString)
    {
        LLCORE_ASSERT(outString != nullptr);
        int      CPUInfo[4] = {-1};
        unsigned nExIds, i = 0;
        char     CPUBrandString[0x40] = {};
        // Get the information associated with each extended ID.
        __cpuid(CPUInfo, 0x80000000);
        nExIds = LLCore::Min(5, CPUInfo[0]);
        for (i = 0x80000000; i <= nExIds; ++i)
        {
            __cpuid(CPUInfo, i);
            // Interpret CPU brand string
            if (i == 0x80000002)
                memcpy(CPUBrandString, CPUInfo, sizeof(CPUInfo));
            else if (i == 0x80000003)
                memcpy(CPUBrandString + 16, CPUInfo, sizeof(CPUInfo));
            else if (i == 0x80000004)
                memcpy(CPUBrandString + 32, CPUInfo, sizeof(CPUInfo));
        }
        //string includes manufacturer, model and clockspeed
        *outString = CPUBrandString;
    }

    void GetGpuInfo(LLCore::Array<GpuInfo>* outInfo)
    {
        Internal::InitGpuInfo();
        outInfo->copy(Internal::s_gpuInfos);
    }

    void GetMemoryInfoString(LLCore::String* outString)
    {
        LLCORE_ASSERT(outString != nullptr);

        uint64 totalMemoryInKilobytes;
        if (::GetPhysicallyInstalledSystemMemory(&totalMemoryInKilobytes))
        {
            double sizeKB = (double)totalMemoryInKilobytes;
            if (sizeKB >= 1024.0)
            {
                double sizeMB = sizeKB / 1024.0;
                if (sizeMB >= 1024.0)
                {
                    double sizeGB = sizeMB / 1024.0;
                    if (sizeGB >= 1024.0)
                    {
                        double sizeTB = sizeGB / 1024.0;
                        outString->format("%gTB", sizeTB);
                    }
                    else
                    {
                        outString->format("%gGB", sizeGB);
                    }
                }
                else
                {
                    outString->format("%gMB", sizeMB);
                }
            }
            else
            {
                outString->format("%gKB", sizeKB);
            }
        }
        else
        {
            *outString = "error";
        }
    }

    void GetOSVersionInfoString(LLCore::String* outString)
    {
        LLCORE_ASSERT(outString != nullptr);
        HKEY versionKey;

        if (::RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", 0, KEY_READ, &versionKey) == ERROR_SUCCESS)
        {
            DWORD type            = 0;
            DWORD majorVersion    = 0;
            DWORD minorVersion    = 0;
            char  buildNumber[16] = {0};
            char  productName[64] = {0};

            DWORD majorVersionSize = sizeof(majorVersion);
            DWORD minorVersionSize = sizeof(minorVersion);
            DWORD buildNumberSize  = sizeof(buildNumber);
            DWORD productNameSize  = sizeof(productName);
            DWORD versionSize      = 7;

            LLCore::StringFixed<8> versionString;
            versionString.ensureCapacity(versionSize);

            ::RegGetValueA(versionKey, nullptr, "ProductName", RRF_RT_REG_SZ, &type, &productName, &productNameSize);
            if (::RegGetValueA(versionKey, nullptr, "CurrentMajorVersionNumber", RRF_RT_REG_DWORD, &type, &majorVersion, &majorVersionSize) == ERROR_SUCCESS
                && ::RegGetValueA(versionKey, nullptr, "CurrentMinorVersionNumber", RRF_RT_REG_DWORD, &type, &minorVersion, &minorVersionSize) == ERROR_SUCCESS)
            {
                LLCore::FormatInto(&versionString, "%d.%d", majorVersion, minorVersion);
            }
            else if (::RegGetValueA(versionKey, nullptr, "CurrentVersion", RRF_RT_REG_SZ, &type, versionString.asWrite(), &versionSize) == ERROR_SUCCESS)
            {
                versionString.fixupLength();
            }
            else
            {
                versionString = "0";
            }

            if (::RegGetValueA(versionKey, nullptr, "CurrentBuild", RRF_RT_REG_SZ, &type, &buildNumber, &buildNumberSize) == ERROR_SUCCESS)
            {
                LLCore::FormatInto(outString, "%s %s.%s", productName, versionString, buildNumber);
            }
            else
            {
                LLCore::FormatInto(outString, "%s %s", productName, versionString);
            }

            RegCloseKey(versionKey);
        }
    }

} // namespace HardwareInfo
} // namespace LLCore

#endif