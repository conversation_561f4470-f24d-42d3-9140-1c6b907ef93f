/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHA<PERSON>ABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#include "D3D11SwapChain.h"

#include "../Portable.h"
#include "LLGpu/HDR.h"
#include "LLGpu/HDRHelpers.cpp"
#include "D3D11Platform.h"
#include "D3D11Texture2d.h"
#include "../../../../App/Libraries/ContentExport/Logging.h"

#include "LLCore/Allocator.h"
#include "LLGpu/HDRHelpers.h"
#include "LLWindow/Win/WinWindow.h"
#include <string>


namespace LLGpu
{
namespace D3D11
{
    SwapChain::SwapChain(LLGraphicsGems::Allocator* const allocator, ID3D11Device* const device, ID3D11DeviceContext* const immediateContext, const LLWindow::Window& window, const Format textureFormat, const Format renderTargetViewFormat, Format optionalDepthStencilFormat, const char* const debugObjectName)
        : LLGraphicsGems::RefCounted(allocator), m_device(device), m_immediateContext(immediateContext), m_renderTargetViewFormat(renderTargetViewFormat), m_depthStencilViewFormat(optionalDepthStencilFormat), m_window(window)
    {
        Microsoft::WRL::ComPtr<IDXGIDevice> dxgiDevice;
        HRESULT                             hr = m_device->QueryInterface<IDXGIDevice>(dxgiDevice.GetAddressOf());

        // Find if display is HDR capable
        //
        // The API for testing if HDR is available doesn't not exist on Windows 7 and will crash if HDR enabled.
        if (!LLCore::IsWindows10())
        {
            window.setIsHdrEnabled(false);
            window.setIsHdrAvailable(false);
        }
        else
        {
            Microsoft::WRL::ComPtr<IDXGIFactory2> m_dxgiFactory;
            CreateDXGIFactory1(IID_PPV_ARGS(&m_dxgiFactory));

            if (m_dxgiFactory->IsCurrent() == false)
            {
                CreateDXGIFactory2(0, IID_PPV_ARGS(&m_dxgiFactory));
            }
            Microsoft::WRL::ComPtr<IDXGIAdapter1> dxgiAdapter;
            m_dxgiFactory->EnumAdapters1(0, &dxgiAdapter);

            UINT                                i = 0;
            Microsoft::WRL::ComPtr<IDXGIOutput> currentOutput;

            bool hdrAvailable = false;
            while (dxgiAdapter->EnumOutputs(i, &currentOutput) != DXGI_ERROR_NOT_FOUND)
            {
                Microsoft::WRL::ComPtr<IDXGIOutput6> dxgiCurrentOutput;
                currentOutput.As(&dxgiCurrentOutput);

                DXGI_OUTPUT_DESC1 dxgi_output_desc;
                dxgiCurrentOutput->GetDesc1(&dxgi_output_desc);
                if (dxgi_output_desc.ColorSpace == DXGI_COLOR_SPACE_RGB_FULL_G2084_NONE_P2020)
                {
                    hdrAvailable = true;
                }

                i++;
            }

            window.setIsHdrAvailable(hdrAvailable);
        }

        if (SUCCEEDED(hr))
        {
            Microsoft::WRL::ComPtr<IDXGIAdapter> adapter;
            hr = dxgiDevice->GetAdapter(adapter.GetAddressOf());
            if (SUCCEEDED(hr))
            {
                Microsoft::WRL::ComPtr<IDXGIFactory1> factory;
                hr = adapter->GetParent(IID_PPV_ARGS(factory.GetAddressOf()));
                if (SUCCEEDED(hr))
                {
                    Microsoft::WRL::ComPtr<IDXGIFactory5> factory5;
                    hr = factory.As(&factory5);
                    if (SUCCEEDED(hr))
                    {
                        BOOL allowTearing = FALSE;
                        hr                = factory5->CheckFeatureSupport(DXGI_FEATURE_PRESENT_ALLOW_TEARING, &allowTearing, sizeof(allowTearing));
                        m_allowTearing    = SUCCEEDED(hr) && allowTearing;
                        enableVSync(m_window.getUseVSync());
                    }

                    const HWND windowHandle = window.getPlatformData()->getWindowHandle();
                    RECT       clientRect;
                    if (GetClientRect(windowHandle, &clientRect))
                    {
                        DXGI_SWAP_CHAIN_DESC swapChainDesc;
                        swapChainDesc.BufferDesc.Width                   = clientRect.right - clientRect.left;
                        swapChainDesc.BufferDesc.Height                  = clientRect.bottom - clientRect.top;
                        swapChainDesc.BufferDesc.RefreshRate.Numerator   = 0;
                        swapChainDesc.BufferDesc.RefreshRate.Denominator = 1;
                        swapChainDesc.BufferDesc.Format                  = Platform::GetFormat(textureFormat);
                        swapChainDesc.BufferDesc.ScanlineOrdering        = DXGI_MODE_SCANLINE_ORDER_UNSPECIFIED;
                        swapChainDesc.BufferDesc.Scaling                 = DXGI_MODE_SCALING_UNSPECIFIED;
                        swapChainDesc.SampleDesc.Count                   = 1;
                        swapChainDesc.SampleDesc.Quality                 = 0;
                        swapChainDesc.BufferUsage                        = DXGI_USAGE_RENDER_TARGET_OUTPUT;
                        swapChainDesc.BufferCount                        = 2; // Oculus sample value
                        swapChainDesc.OutputWindow                       = window.getPlatformData()->getWindowHandle();
                        swapChainDesc.Windowed                           = TRUE;
                        swapChainDesc.Flags                              = m_allowTearing ? DXGI_SWAP_CHAIN_FLAG_ALLOW_TEARING : 0;
                        swapChainDesc.SwapEffect                         = DXGI_SWAP_EFFECT_FLIP_DISCARD;

                        hr = factory->CreateSwapChain(m_device, &swapChainDesc, &m_swapChain);
                        if (SUCCEEDED(hr))
                        {
                            Platform::SetDebugObjectName(m_swapChain, debugObjectName);

                            if (FAILED(factory->MakeWindowAssociation(windowHandle, DXGI_MWA_NO_ALT_ENTER)))
                            {
                                LLCORE_ASSERT(false, "Failed to disable DXGI Alt+Enter fullscreen sequence.");
                                LogErrorHResult(__FUNCTION__, "ID3D11Factory::MakeWindowAssociation", hr);
                            }

                            LLGpu::HDR::setSwapchain(this);
                            enableHDR(window.getIsHdrAvailable());
                        }
                        else
                        {
                            LLCORE_ASSERT(false, "Failed to create D3D11 swap chain.");
                            LogErrorHResult(__FUNCTION__, "IDXGIFactory::CreateSwapChain", hr);
                        }

                    }
                }
                else
                {
                    LogErrorHResult(__FUNCTION__, "IDXGIAdapter::GetParent", hr);
                }
            }
            else
            {
                LogErrorHResult(__FUNCTION__, "IDXGIDevice::GetAdapter", hr);
            }
        }
        else
        {
            LogErrorHResult(__FUNCTION__, "ID3D11Device::QueryInterface<IDXGIDevice>", hr);
        }
    }

    SwapChain::~SwapChain()
    {
        if (m_swapChain)
        {
            m_swapChain->Release();
        }
    }

    void SwapChain::enableHDR(bool hdrEnable)
    {
        if (hdrEnable && LLGpu::HDR::getSwapchain()->m_window.getIsHdrAvailable() && LLGpu::HDR::getSwapchain()->m_window.getEnableHDR())
        {
            const HWND        WindowHandle = LLGpu::HDR::getSwapchain()->m_window.getPlatformData()->getWindowHandle();
            DXGI_OUTPUT_DESC1 dxgi_output_desc;

            Microsoft::WRL::ComPtr<IDXGIFactory2> m_dxgiFactory;
            CreateDXGIFactory1(IID_PPV_ARGS(&m_dxgiFactory));

            if (m_dxgiFactory->IsCurrent() == false)
            {
                CreateDXGIFactory2(0, IID_PPV_ARGS(&m_dxgiFactory));
            }
            Microsoft::WRL::ComPtr<IDXGIAdapter1> dxgiAdapter;
            m_dxgiFactory->EnumAdapters1(0, &dxgiAdapter);

            UINT                                i = 0;
            Microsoft::WRL::ComPtr<IDXGIOutput> currentOutput;
            Microsoft::WRL::ComPtr<IDXGIOutput> bestOutput;
            float                               bestIntersectArea = -1;

            while (dxgiAdapter->EnumOutputs(i, &currentOutput) != DXGI_ERROR_NOT_FOUND)
            {
                RECT clientRect;
                if (GetClientRect(WindowHandle, &clientRect))
                {
                    int ax1 = clientRect.left;
                    int ay1 = clientRect.top;
                    int ax2 = clientRect.right;
                    int ay2 = clientRect.bottom;

                    DXGI_OUTPUT_DESC desc;
                    currentOutput->GetDesc(&desc);
                    RECT r   = desc.DesktopCoordinates;
                    int  bx1 = r.left;
                    int  by1 = r.top;
                    int  bx2 = r.right;
                    int  by2 = r.bottom;

                    int intersectArea = LLCore::Max(0l, LLCore::Min(ax2, bx2) - LLCore::Max(ax1, bx1)) * LLCore::Max(0l, LLCore::Min(ay2, by2) - LLCore::Max(ay1, by1));
                    if (intersectArea > bestIntersectArea)
                    {
                        bestOutput        = currentOutput;
                        bestIntersectArea = static_cast<float>(intersectArea);
                    }
                }

                i++;
            }

            Microsoft::WRL::ComPtr<IDXGIOutput6> dxgiOutput;
            bestOutput.As(&dxgiOutput);

            dxgiOutput->GetDesc1(&dxgi_output_desc);

            // Instead of just generating a color matrix, use display-aware approach
            LLCore::Matrix3 colorMatrix = HDRHelpers::GenerateColorTransformForDisplay(dxgi_output_desc);

            Microsoft::WRL::ComPtr<IDXGISwapChain4> dxgiSwapChain4;
            HRESULT hr = LLGpu::HDR::getSwapchain()->m_swapChain->QueryInterface<IDXGISwapChain4>(&dxgiSwapChain4);

            if (SUCCEEDED(hr))
            {
                const float maxLuminance = LLCore::Min(dxgi_output_desc.MaxLuminance, 1000.0f);
                const float maxFall      = LLCore::Min(dxgi_output_desc.MaxFullFrameLuminance, 1000.0f);

                //float whitePoint = 203.0f; // SDR reference white (~100 nits in scRGB)
                float whitePoint = 253.75f; // Sansar custom curve (~120 nits in scRGB)

                if (maxFall >= 300.0f)
                {
                    whitePoint = 253.75f;
                }
                else if (maxFall > 253.75f)
                {
                    whitePoint = 253.75f + (maxFall - 253.75f) * 0.8f;
                }

                DXGI_HDR_METADATA_HDR10 DXGI_HDR_Metadata;
                for (int y = 0; y < 2; y++)
                {
                    DXGI_HDR_Metadata.RedPrimary[y]   = static_cast<UINT16>(dxgi_output_desc.RedPrimary[y] * 50000.0f);
                    DXGI_HDR_Metadata.GreenPrimary[y] = static_cast<UINT16>(dxgi_output_desc.GreenPrimary[y] * 50000.0f);
                    DXGI_HDR_Metadata.BluePrimary[y]  = static_cast<UINT16>(dxgi_output_desc.BluePrimary[y] * 50000.0f);
                    DXGI_HDR_Metadata.WhitePoint[y]   = static_cast<UINT16>(dxgi_output_desc.WhitePoint[y] * 50000.0f);
                }
                DXGI_HDR_Metadata.MinMasteringLuminance     = static_cast<UINT16>(0);
                DXGI_HDR_Metadata.MaxMasteringLuminance     = static_cast<UINT>(1000 * 10000);
                DXGI_HDR_Metadata.MaxContentLightLevel      = static_cast<UINT16>(maxLuminance);
                DXGI_HDR_Metadata.MaxFrameAverageLightLevel = static_cast<UINT16>(maxFall);

                dxgiSwapChain4->SetColorSpace1(DXGI_COLOR_SPACE_RGB_FULL_G2084_NONE_P2020);
                dxgiSwapChain4->SetHDRMetaData(DXGI_HDR_METADATA_TYPE_HDR10, sizeof(DXGI_HDR_Metadata), &DXGI_HDR_Metadata);

                LLGpu::HDR::getSwapchain()->m_window.setHdrColorMatrix(colorMatrix);
                LLGpu::HDR::getSwapchain()->m_window.setHdrMaxLuminance(maxLuminance);
                LLGpu::HDR::getSwapchain()->m_window.setHdrWhitePoint(whitePoint);
                LLGpu::HDR::getSwapchain()->m_window.setHdrMaxFALL(maxFall);
                LLGpu::HDR::getSwapchain()->m_window.setIsHdrEnabled(true);
            }
        }
        else
        {
            Microsoft::WRL::ComPtr<IDXGISwapChain4> dxgiSwapChain4;
            HRESULT                                 hr = LLGpu::HDR::getSwapchain()->m_swapChain->QueryInterface<IDXGISwapChain4>(&dxgiSwapChain4);
            if (SUCCEEDED(hr))
            {
                dxgiSwapChain4->SetColorSpace1(DXGI_COLOR_SPACE_RGB_FULL_G22_NONE_P709);
                dxgiSwapChain4->SetHDRMetaData(DXGI_HDR_METADATA_TYPE_NONE, sizeof(DXGI_HDR_METADATA_TYPE_NONE), nullptr);
                LLGpu::HDR::getSwapchain()->m_window.setHdrMaxLuminance(0);
                LLGpu::HDR::getSwapchain()->m_window.setHdrWhitePoint(0);
                LLGpu::HDR::getSwapchain()->m_window.setIsHdrEnabled(false);
            }
        }
    }

    void SwapChain::enableVSync(bool vSyncEnable)
    {
        m_presentFlags = (!vSyncEnable && m_allowTearing) ? DXGI_PRESENT_ALLOW_TEARING : 0;
        m_syncInterval = (!vSyncEnable && m_allowTearing) ? 0 : 1;
    }

    void SwapChain::resize(const int width, const int height)
    {
        // Attempt to remove all external references to previous swap targets.
        // ResizeBuffers will fail otherwise.
        m_immediateContext->OMSetRenderTargets(0, nullptr, nullptr);
        m_framebuffer = nullptr;

        HRESULT hr = m_swapChain->ResizeBuffers(0, (uint)width, (uint)height, DXGI_FORMAT_UNKNOWN, m_allowTearing ? DXGI_SWAP_CHAIN_FLAG_ALLOW_TEARING : 0);
        if (SUCCEEDED(hr))
        {
            Microsoft::WRL::ComPtr<ID3D11Texture2D> nativeTexture2d;
            hr = m_swapChain->GetBuffer(0, IID_PPV_ARGS(nativeTexture2d.GetAddressOf()));
            if (SUCCEEDED(hr))
            {
                Platform::SetDebugObjectName(nativeTexture2d.Get(), "SwapChain::resize");

                // create optional depth-stencil if requested...
                LLCore::RefPointer<DepthStencilView> depthStencilView;
                if (m_depthStencilViewFormat != Format::cUnknown)
                {
                    Microsoft::WRL::ComPtr<ID3D11Texture2D> nativeDepthStencilTexture;
                    {
                        D3D11_TEXTURE2D_DESC depthStencilTexture2dDesc;

                        depthStencilTexture2dDesc.Width              = width;
                        depthStencilTexture2dDesc.Height             = height;
                        depthStencilTexture2dDesc.MipLevels          = 1;
                        depthStencilTexture2dDesc.ArraySize          = 1;
                        depthStencilTexture2dDesc.Format             = Platform::GetFormat(m_depthStencilViewFormat);
                        depthStencilTexture2dDesc.SampleDesc.Count   = 1;
                        depthStencilTexture2dDesc.SampleDesc.Quality = 0;
                        depthStencilTexture2dDesc.Usage              = D3D11_USAGE_DEFAULT;
                        depthStencilTexture2dDesc.BindFlags          = D3D11_BIND_DEPTH_STENCIL;
                        depthStencilTexture2dDesc.CPUAccessFlags     = 0;
                        depthStencilTexture2dDesc.MiscFlags          = 0;

                        hr = m_device->CreateTexture2D(&depthStencilTexture2dDesc, nullptr, nativeDepthStencilTexture.GetAddressOf());
                        if (SUCCEEDED(hr))
                        {
                            DepthStencilViewDesc depthStencilViewDesc;
                            depthStencilViewDesc.m_format           = m_depthStencilViewFormat;
                            depthStencilViewDesc.m_viewDimension    = DsvDimension::cTexture2d;
                            LLCore::RefPointer<Texture2d> texture2d = LLCore::GiveRef(GRAPHICS_NEW(getAllocator()) Texture2d(getAllocator(), nativeDepthStencilTexture.Get()));
                            depthStencilView                        = LLCore::GiveRef(GRAPHICS_NEW(getAllocator()) DepthStencilView(getAllocator(), m_device, LLCore::GiveRef(texture2d), depthStencilViewDesc, "SwapChain::resize"));
                        }
                        else
                        {
                            LLCORE_ASSERT(false, "Failed to create depth stencil for swap chain.");
                            LogErrorHResult(__FUNCTION__, "ID3D11Device::CreateTexture2d", hr);
                        }
                    }
                }

                RenderTargetViewDesc renderTargetViewDesc;
                renderTargetViewDesc.m_format                            = m_renderTargetViewFormat;
                renderTargetViewDesc.m_viewDimension                     = RtvDimension::cTexture2d;
                LLCore::RefPointer<Texture2d>        texture2d           = LLCore::GiveRef(GRAPHICS_NEW(getAllocator()) Texture2d(getAllocator(), nativeTexture2d.Get()));
                LLCore::RefPointer<RenderTargetView> renderTargetView    = LLCore::GiveRef(GRAPHICS_NEW(getAllocator()) RenderTargetView(getAllocator(), m_device, texture2d, renderTargetViewDesc, "SwapChain::resize"));
                RenderTargetView*                    renderTargetViews[] = {renderTargetView};
                m_framebuffer                                            = LLCore::GiveRef(GRAPHICS_NEW(getAllocator()) Framebuffer(getAllocator(), renderTargetViews, (uint)LLCore::GetArrayCount(&renderTargetViews), depthStencilView));
            }
            else
            {
                LogErrorHResult(__FUNCTION__, "IDXGISwapChain::GetBuffer", hr);
            }
        }
        else
        {
            LogErrorHResult(__FUNCTION__, "IDXGISwapChain::ResizeBuffers", hr);
        }
    }

    void SwapChain::setFullscreenState(bool enabled)
    {
        HRESULT hr = m_swapChain->SetFullscreenState(enabled, nullptr);
        if (FAILED(hr))
        {
            LLCORE_ASSERT(false, "Failed to set fullscreen state.");
            LogErrorHResult(__FUNCTION__, "IDXGISwapChain::SetFullscreenState", hr);
        }
    }

    LLCore::RefGift<Framebuffer> SwapChain::getNextFramebuffer()
    {
        LLCore::RefPointer<Framebuffer> framebuffer = m_framebuffer;
        return LLCore::GiveRef(framebuffer);
    }

    void SwapChain::present()
    {
        LLPROFILE_AUTO_CPU_MARKER_STATIC("SwapChain::present");

        m_swapChain->Present(m_syncInterval, m_presentFlags);
    }

    void SwapChain::prepareForDestruction()
    {
        setFullscreenState(false);
    }
} // namespace D3D11
} // namespace LLGpu
