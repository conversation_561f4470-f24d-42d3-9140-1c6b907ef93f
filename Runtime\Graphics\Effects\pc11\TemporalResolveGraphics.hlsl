#include "Generated/Render_High_TemporalResolveProgram.hlsli"
#include "Generated/Render_Low_TemporalResolveProgram.hlsli"
#include "../Common/MathUtil.hlsli"
#include "../Common/StereoUtil.hlsli"
#include "../Common/ColorUtil.hlsli"


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//
// Sansar TAA doc https://docs.google.com/document/d/1rFWXZ6p5v3m13-Vsg_-BaeOkG6PXSS4pwhplbemH1_I/edit?usp=sharing
//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

static const float cConvergenceRateMin = 0.04;
static const float cConvergenceRateMax = 0.09;
static const float cNeighborhoodMinDeviations = 0.75;
static const float cNeighborhoodMaxDeviations = 1.33;
static const float cNeighborhoodAbsoluteMax = 0.;
static const float cMotionSensitivity = 100.;
static const float cDissolveFilterLevels = 6.;

static const float cCatmullRomSamplingSharpness = (0.5);


struct VertexOutput
{
    float4 positionSv : SV_Position;
    float2 uv : TEXCOORD0;
};

VertexOutput VertexMain(uint vertexId : SV_VertexID)
{
    VertexOutput output;
    output.positionSv = vertexIdToFullScreenTrianglePosition(vertexId);
    output.uv = output.positionSv.xy * float2(0.5, -0.5) + 0.5;
    return output;
}

float2 lanczos(float2 x, float r)
{
    x = 3.14159265 * min(abs(x), r);
    //return x == 0. ? 1. : r*sin((1./r)*x)*sin(x)/(x*x);
    return x < 1e-6 ? 1. : r * sin((1. / r) * x) * sin(x) / (x * x);
}

float2 lanczos2t2(float2 f)
{
    return 0.591859 - 0.183718 * f + (-0.964178 + 1.92836 * f) * rcp(1.62907 + f * (-1. + f));
}

float2 lanczos2w2(float2 f)
{
    return (3.01709 + f * (1.02984 + -1.02984 * f)) * rcp(3.01738 + f * (-0.893222 + f * (1.89322 + f * (-2. + f))));
}

float2 lanczos2w1(float2 f)
{
    return (f * (-1.25318 + f * (2.72361 + f * (-1.30911 + (-0.539359 + 0.378032 * f) * f)))) / (1.96471 + f * (-0.308047 + f));
}

float2 catmullRom2(float2 f)
{
    return (1.5 * f - 2.5) * f * f + 1.;
}

float2 catmullRom4(float2 f)
{
    return (0.5 * f - 0.5) * f * f;
}

float4 sampleWeighted(Texture2D<float4> tex, float2 uv, float w, in out float wsum)
{
    wsum += w;
    return tex.SampleLevel(LinearSampler, uv, 0) * w;
}

float4 sampleLanczos2(Texture2D<float4> tex, float2 pixelLoc, float2 rcpTextureSize)
{
    float2 f = frac(pixelLoc - 0.5);

    //float2 k1 = lanczos(f + 1., 2.);
    //float2 k2 = lanczos(f + 0., 2.);
    //float2 k3 = lanczos(f - 1., 2.);
    //float2 k4 = lanczos(f - 2., 2.);

    float2 t1 = 0.;
    float2 t2 = lanczos2t2(f); // saturate(k3/(k2 + k3)); // 
    float2 t3 = 1.;

    float2 w1 = lanczos2w1(f); // k1;         // 
    float2 w2 = lanczos2w2(f); // k2 + k3;    // 
    float2 w3 = lanczos2w1(1. - f); // k4;         // 

    float2 p1 = (-f + t1 - 1. + pixelLoc) * rcpTextureSize;
    float2 p2 = (-f + t2 + 0. + pixelLoc) * rcpTextureSize;
    float2 p3 = (-f + t3 + 1. + pixelLoc) * rcpTextureSize;

    float wsum = 0.;
    float4 result = 0.;
    //result += sampleWeighted(tex, float2(p1.x, p1.y), w1.x*w1.y, wsum);
    result += sampleWeighted(tex, float2(p2.x, p1.y), w2.x * w1.y, wsum);
    //result += sampleWeighted(tex, float2(p3.x, p1.y), w3.x*w1.y, wsum);

    result += sampleWeighted(tex, float2(p1.x, p2.y), w1.x * w2.y, wsum);
    result += sampleWeighted(tex, float2(p2.x, p2.y), w2.x * w2.y, wsum);
    result += sampleWeighted(tex, float2(p3.x, p2.y), w3.x * w2.y, wsum);

    //result += sampleWeighted(tex, float2(p1.x, p3.y), w1.x*w3.y, wsum);
    result += sampleWeighted(tex, float2(p2.x, p3.y), w2.x * w3.y, wsum);
    //result += sampleWeighted(tex, float2(p3.x, p3.y), w3.x*w3.y, wsum);

    return result * rcp(wsum);
}

float2 lanczos3t3(float2 f)
{
    return 0.5 + (-1.46217 + 2.92433 * f) * rcp(2.92408 + f * (-1. + f));
}

float2 lanczos3w3(float2 f)
{
    return 0.998157 + (0.866948 - 0.866948 * f) * f;
}

float2 lanczos3w2(float2 f)
{
    return 2.1734 - 1.08916 * f + (-4.09402 + 1.45537 * f) * rcp(1.88435 + f * (-0.451784 + f));
}

float2 lanczos3w1(float2 f)
{
    return -0.432408 + 0.269292 * f + (0.532468 - 0.227442 * f) * rcp(1.23146 + f * (-0.362435 + f));
}

float4 sampleLanczos3(Texture2D<float4> tex, float2 pixelLoc, float2 rcpTextureSize)
{
    float2 f = frac(pixelLoc - 0.5);

    //float2 k1 = lanczos(f + 2., 3.);
    //float2 k2 = lanczos(f + 1., 3.);
    //float2 k3 = lanczos(f + 0., 3.);
    //float2 k4 = lanczos(f - 1., 3.);
    //float2 k5 = lanczos(f - 2., 3.);
    //float2 k6 = lanczos(f - 3., 3.);

    float2 t1 = 0.;
    float2 t2 = 0.;
    float2 t3 = lanczos3t3(f); // saturate(k4/(k4 + k3)); //
    float2 t4 = 1.;
    float2 t5 = 1.;

    float2 w1 = lanczos3w1(f); // k1;         // 
    float2 w2 = lanczos3w2(f); // k2;         // 
    float2 w3 = lanczos3w3(f); // k3 + k4;    // 
    float2 w4 = lanczos3w2(1. - f); // k5;         // 
    float2 w5 = lanczos3w1(1. - f); // k6;         // 

    float2 p1 = (-f + t1 - 2. + pixelLoc) * rcpTextureSize;
    float2 p2 = (-f + t2 - 1. + pixelLoc) * rcpTextureSize;
    float2 p3 = (-f + t3 + 0. + pixelLoc) * rcpTextureSize;
    float2 p4 = (-f + t4 + 1. + pixelLoc) * rcpTextureSize;
    float2 p5 = (-f + t5 + 2. + pixelLoc) * rcpTextureSize;

    float wsum = 0.;
    float4 result = 0.;
    //result += sampleWeighted(tex, float2(p1.x, p1.y), w1.x*w1.y, wsum);
    //result += sampleWeighted(tex, float2(p2.x, p1.y), w2.x*w1.y, wsum);
    result += sampleWeighted(tex, float2(p3.x, p1.y), w3.x * w1.y, wsum);
    //result += sampleWeighted(tex, float2(p4.x, p1.y), w4.x*w1.y, wsum);
    //result += sampleWeighted(tex, float2(p5.x, p1.y), w5.x*w1.y, wsum);

    //result += sampleWeighted(tex, float2(p1.x, p2.y), w1.x*w2.y, wsum);
    result += sampleWeighted(tex, float2(p2.x, p2.y), w2.x * w2.y, wsum);
    result += sampleWeighted(tex, float2(p3.x, p2.y), w3.x * w2.y, wsum);
    result += sampleWeighted(tex, float2(p4.x, p2.y), w4.x * w2.y, wsum);
    //result += sampleWeighted(tex, float2(p5.x, p2.y), w5.x*w2.y, wsum);

    result += sampleWeighted(tex, float2(p1.x, p3.y), w1.x * w3.y, wsum);
    result += sampleWeighted(tex, float2(p2.x, p3.y), w2.x * w3.y, wsum);
    result += sampleWeighted(tex, float2(p3.x, p3.y), w3.x * w3.y, wsum);
    result += sampleWeighted(tex, float2(p4.x, p3.y), w4.x * w3.y, wsum);
    result += sampleWeighted(tex, float2(p5.x, p3.y), w5.x * w3.y, wsum);

    //result += sampleWeighted(tex, float2(p1.x, p4.y), w1.x*w4.y, wsum);
    result += sampleWeighted(tex, float2(p2.x, p4.y), w2.x * w4.y, wsum);
    result += sampleWeighted(tex, float2(p3.x, p4.y), w3.x * w4.y, wsum);
    result += sampleWeighted(tex, float2(p4.x, p4.y), w4.x * w4.y, wsum);
    //result += sampleWeighted(tex, float2(p5.x, p4.y), w5.x*w4.y, wsum);

    //result += sampleWeighted(tex, float2(p1.x, p5.y), w1.x*w5.y, wsum);
    //result += sampleWeighted(tex, float2(p2.x, p5.y), w2.x*w5.y, wsum);
    result += sampleWeighted(tex, float2(p3.x, p5.y), w3.x * w5.y, wsum);
    //result += sampleWeighted(tex, float2(p4.x, p5.y), w4.x*w5.y, wsum);
    //result += sampleWeighted(tex, float2(p5.x, p5.y), w5.x*w5.y, wsum);

    return result * rcp(wsum);
}

float4 sampleCatmullRom(Texture2D<float4> tex, float2 pixelLoc, float2 rcpTextureSize)
{
    float2 position = pixelLoc;
    float2 centerPosition = floor(position - 0.5) + 0.5;
    float2 f = position - centerPosition;
    float2 f2 = f * f;
    float2 f3 = f * f2;

    float c = cCatmullRomSamplingSharpness;
    float2 w0 = -c * f3 + 2.0 * c * f2 - c * f;
    float2 w1 = (2.0 - c) * f3 - (3.0 - c) * f2 + 1.0;
    float2 w2 = -(2.0 - c) * f3 + (3.0 - 2.0 * c) * f2 + c * f;
    float2 w3 = c * f3 - c * f2;

    float2 w12 = w1 + w2;
    float2 tc12 = rcpTextureSize * (centerPosition + w2 / w12);
    float4 centerColor = tex.SampleLevel(LinearSampler, float2(tc12.x, tc12.y), 0);

    float2 tc0 = rcpTextureSize * (centerPosition - 1.0);
    float2 tc3 = rcpTextureSize * (centerPosition + 2.0);
    const float w[] = { w12.x * w0.y, w0.x * w12.y, w12.x * w12.y, w3.x * w12.y, w12.x * w3.y };
    float4 color =
        tex.SampleLevel(LinearSampler, float2(tc12.x, tc0.y), 0) * w[0] +
        tex.SampleLevel(LinearSampler, float2(tc0.x, tc12.y), 0) * w[1] +
        centerColor * w[2] +
        tex.SampleLevel(LinearSampler, float2(tc3.x, tc12.y), 0) * w[3] +
        tex.SampleLevel(LinearSampler, float2(tc12.x, tc3.y), 0) * w[4];
    return color * rcp(w[0] + w[1] + w[2] + w[3] + w[4]);
}

float3 compressRange(float3 c)
{
    c = max(c, -63. / 64.);
    return c / (1. + c);
    //return log2(max(c, 0.) + 0.18);
}

float3 decompressRange(float3 c)
{
    c = clamp(c, 0., 63. / 64.);
    return c / (1. - c);
    //return max(exp2(c) - 0.18, 0.);
}

float4 sampleSource(Texture2D<float4> tex, int2 pixelLoc, float brightnessScale)
{
    float4 result = tex[pixelLoc];
    result.rgb *= brightnessScale;
    result.rgb = compressRange(result.rgb);
    return result;
}

float4 sampleHistory(Texture2D<float4> tex, float2 pixelLoc, float brightnessScale)
{
    float4 result;
#ifdef COMPACT_GBUFFER
    result = sampleLanczos2(tex, pixelLoc, CameraInfo(RcpDisplayTargetSize));
#else
    result = sampleLanczos3(tex, pixelLoc, CameraInfo(RcpDisplayTargetSize));
#endif
    result.rgb *= brightnessScale;
    result.rgb = compressRange(result.rgb);
    return result;
}

void takeNearestZ(float2 loc, in out float nearestZ, in out float2 locOfNearest)
{
    float z = TemporalResolveParams(Depth)[loc].x;
    nearestZ = max(nearestZ, z);
    locOfNearest = (nearestZ == z ? loc : locOfNearest);
}

float filter3x3(int i, int j)
{
    static const float ceoffs[] = { 16. / 196., 24. / 196., 36. / 196. }; // corner, edge, center
    return ceoffs[(i * j == 0) + (i == 0) * (j == 0)];
}

//[numthreads(16, 16, 1)]
PixelOutput PixelMain(VertexOutput input, float4 screenCoord : SV_Position)
{
    float2 enlargement = CameraInfo(DisplayViewportSize) * CameraInfo(RcpRenderViewportSize);
    float2 reduction = CameraInfo(RenderViewportSize) * CameraInfo(RcpDisplayViewportSize);

    float2 jitter = CameraInfo(TemporalJitter).xy * float2(0.5, -0.5) * CameraInfo(RenderViewportSize); // clip space -> render pixel coordinates

    float2 renderCoord = screenCoord.xy * reduction;
    renderCoord += jitter;

    float4 neighborAlphas = TemporalResolveParams(Source).GatherAlpha(LinearSampler, renderCoord * CameraInfo(RcpRenderTargetSize));
    float2 neighborhoodSize = any(neighborAlphas < 1.) ? 1. : reduction;

    float2 motion = 0;
    float currentDepth = -1.1;
    {
        // find motion vector of nearest depth in 3x3 neighborhood
        float2 locOfNearest = renderCoord.xy;
        currentDepth = TemporalResolveParams(Depth)[renderCoord.xy].x;

        takeNearestZ(renderCoord.xy + neighborhoodSize * float2(-1, -1), currentDepth, locOfNearest);
        takeNearestZ(renderCoord.xy + neighborhoodSize * float2(+1, -1), currentDepth, locOfNearest);
        takeNearestZ(renderCoord.xy + neighborhoodSize * float2(+1, +1), currentDepth, locOfNearest);
        takeNearestZ(renderCoord.xy + neighborhoodSize * float2(-1, +1), currentDepth, locOfNearest);

        motion = TemporalResolveParams(Motion)[locOfNearest].xy;
    }
    motion = isfinite(motion) ? motion : 0.; // NaN sneaking in here does very bad things that are hard to track down
    motion *= enlargement;

    float brightnessScale = TemporalResolveParams(Cut) ? 1. : TemporalResolveParams(ExposureInfo)[0];

    float3 mu = 0;
    float3 sigma = 0;
    float3 background;
    {
        float4 accum = 0.;
        float opaqueW = 0.;

        // calculate local variance in 3x3 neighborhood of target sample.
        [unroll]
        for (int i = -1; i <= 1; i++)
        {
            [unroll]
            for (int j = -1; j <= 1; j++)
            {
                float2 loc = renderCoord.xy + float2(i, j) * neighborhoodSize;
                float4 c = sampleSource(TemporalResolveParams(Source), loc, brightnessScale);
                c.rgb = rgbToYCoCg(c.rgb);
                float w = filter3x3(i, j);
                bool isOpaque = (c.a >= 1.);
                opaqueW += isOpaque * w;
                accum += bool4(isOpaque.xxx, !isOpaque) ? c * w : 0.;
                mu += c.rgb * w;
                sigma += c.rgb * c.rgb * w;
            }
        }
        mu /= 1.;
        sigma = sqrt(max(sigma / 1.0 - mu * mu, 0.000001));

        background = accum.rgb / (opaqueW);
    }

    float2 historyLoc = screenCoord.xy - motion.xy;
    // TODO make clamp optional to accommodate pano mode
    //historyLoc = clamp(historyLoc, CameraInfo(DisplayViewportOffset), CameraInfo(DisplayViewportOffset) + CameraInfo(DisplayViewportSize) - 1.);
    bool historyIsVisible = all(historyLoc >= CameraInfo(DisplayViewportOffset)) & all(historyLoc < CameraInfo(DisplayViewportOffset) + CameraInfo(DisplayViewportSize)) & !TemporalResolveParams(Cut);
    //bool historyIsVisible = !TemporalResolveParams(Cut);
    // avoid stereo crosstalk
    {
        bool stereoSide = (screenCoord.x >= CameraInfo(DisplayViewportSize).x * 0.5);
        bool historyStereoSide = (historyLoc.x >= CameraInfo(DisplayViewportSize).x * 0.5);
        historyIsVisible = historyIsVisible & (!stereoIsEnabled() | (stereoSide == historyStereoSide));
    }

    float4 current = sampleSource(TemporalResolveParams(Source), renderCoord.xy, brightnessScale);
    current.rgb = rgbToYCoCg(current.rgb);

    if (current.a == 1.)
    {
        background = current.rgb;
    }
#ifndef COMPACT_GBUFFER
    if (isfinite(background.r))
    {
        current.rgb = lerp(background, current.rgb, (current.a * cDissolveFilterLevels) / (trunc(current.a * (cDissolveFilterLevels - 1.)) + 1.));
    }
#endif

    current.a = rcp(1. + cMotionSensitivity * dotSelf(motion));

    if (historyIsVisible)
    {
        float4 history = sampleHistory(TemporalResolveParams(History), historyLoc, brightnessScale);
        history.rgb = rgbToYCoCg(history.rgb);

        float3 halfExtent = sigma * lerp(cNeighborhoodMinDeviations, cNeighborhoodMaxDeviations, min(current.a, history.a));
        //float3 halfExtent = sigma * cNeighborhoodMaxDeviations;

        //mu = clamp(halfExtent, 1 - halfExtent, mu);
        float3 clamped = (history.rgb - mu) * rcp(halfExtent); // statistical whitening transform
        //clamped *= rsqrt(max(1., dotSelf(clamped)));                    // clip to sphere
        //clamped *= rcp(max(1.0, hmax(abs(clamped))));                   // clip to box
        clamped *= rsqrt(max(1.0, float3(square(clamped.x), dotSelf(clamped.yz).xx))); // clip to cylinder
        clamped = mu + clamped * halfExtent; // back to original domain

        // adjust rate according to luma difference
        float lumaDiff = abs(clamped.x - current.x) / max(max(clamped.x, current.x), 0.2);
        float rate = lerp(cConvergenceRateMin, cConvergenceRateMax, square(1. - lumaDiff));
        current.rgb = lerp(clamped.rgb, current.rgb, rate);
    }
    current.rgb = yCoCgToRgb(current.rgb);
    current.rgb = decompressRange(current.rgb);
    current.rgb /= brightnessScale;

    //current.rg = abs(motion.xy*0.05);

    PixelOutput output;
    output.color0 = current;
    return output;
}