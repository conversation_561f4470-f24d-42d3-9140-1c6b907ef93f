/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#include "EngineRenderApplication.h"

#include "LLCore/Random.h"
#include "LLUI/UISystem.h"

#include "Engine/CameraComponent.h"
#include "Engine/VrSystemInterface.h"
#include "EngineRender/EngineRenderer.h"
#include "EngineRender/FrameContext.h"
#include "EngineRender/RenderWorld.h"
#include "EngineRender/TextureStreamingManager.h"

namespace EngineRenderApplication
{
enum RenderScriptStateFlag : uint8
{
    cRenderScriptStateFlag_Restart      = 1 << 0,
    cRenderScriptStateFlag_Reload       = 1 << 1,
    cRenderScriptStateFlag_Resizing     = 1 << 2,
    cRenderScriptStateFlag_Materials    = 1 << 3,
    cRenderScriptStateFlag_Bindings     = 1 << 4,
    cRenderScriptStateFlag_PauseUpdate  = 1 << 5,

    cRenderScriptStateFlag_Initialize         = (cRenderScriptStateFlag_Bindings | cRenderScriptStateFlag_Reload),
    cRenderScriptStateFlag_RestartRequestMask = (cRenderScriptStateFlag_Restart | cRenderScriptStateFlag_Reload),
};

//////////////////////////////////////////////////////////////////////////
// EngineRenderApplicationBase::Graphics
//////////////////////////////////////////////////////////////////////////

void EngineRenderApplicationBase::Graphics::releaseAll()
{
    m_commonPackage     = nullptr;
    m_uiPackage         = nullptr;
    m_mainWindowSurface = nullptr;
}

//////////////////////////////////////////////////////////////////////////
// EngineRenderApplicationBase::FrameContext
//////////////////////////////////////////////////////////////////////////

EngineRenderApplicationBase::FrameContext::FrameContext(LLGems::ComponentHandle<Engine::CameraComponent> const& camera, OwnedPointer<FrameBuilder>&& frameBuilder, ViewContextArray&& renderViews)
    : m_camera(&camera)
    , m_renderViews(LLCore::Give(renderViews))
    , m_frameBuilder(LLCore::Give(frameBuilder))
    , m_context(m_renderViews, m_frameBuilder)
{
}

EngineRenderApplicationBase::FrameContext& EngineRenderApplicationBase::FrameContext::operator=(FrameContext&& other)
{
    if (this != &other)
    {
        m_camera       = other.m_camera;
        m_renderViews  = LLCore::Give(other.m_renderViews);
        m_frameBuilder = LLCore::Give(other.m_frameBuilder);

        LLCore::ReconstructInPlace(&m_context, m_renderViews, m_frameBuilder);

        other.m_camera = nullptr;
        LLCore::ReconstructInPlace(&other.m_context);
    }
    return *this;
}

//////////////////////////////////////////////////////////////////////////
// EngineRenderApplicationBase
//////////////////////////////////////////////////////////////////////////

EngineRenderApplicationBase::EngineRenderApplicationBase(Config* config)
    : BaseType(config)
    , m_config(config)
    , m_graphics()
    , m_defaultViewRenderScriptId(LLGraphicsScript::cInvalidScriptInstanceId)
    , m_defaultViewRenderScriptContext()
    , m_materialTypeDescriptorCatalog(config->m_graphics.m_defaultBasePath.asRead())
    , m_textureStreamingManager(nullptr)
    , m_renderMode(EngineRender::RenderScriptConfig::cRenderMode_Normal)
    , m_vrSystemInterface(nullptr)
    , m_viewportWidth((float)m_width)
    , m_viewportHeight((float)m_height)
    , m_renderScriptState(cRenderScriptStateFlag_Initialize)
{
}

EngineRenderApplicationBase::~EngineRenderApplicationBase()
{
}

void EngineRenderApplicationBase::setOrClearRenderScriptStateFlags(RenderScriptStateFlag flag, bool state)
{
    m_renderScriptState = LLCore::SetOrClearBits(m_renderScriptState, (uint8)flag, state);
}

LLGraphicsResource::PackageData const* EngineRenderApplicationBase::getUIGraphicsPackage() const
{
    LLCORE_ASSERT(m_graphics.m_uiPackage.notNull(), "UI Package has not been initialized");
    return m_graphics.m_uiPackage.asBase();
}

LLGraphicsResource::PackageData const* EngineRenderApplicationBase::getCommonPackage() const
{
    LLCORE_ASSERT(m_graphics.m_commonPackage.notNull(), "Common Package has not been initialized");
    return m_graphics.m_commonPackage.asBase();
}

LLGraphicsStream::WindowSurfaceHandle EngineRenderApplicationBase::getMainWindowSurfaceHandle() const
{
    return m_graphics.m_mainWindowSurface->getHandle();
}

EngineRender::GraphicsConfig::RenderPipelineType EngineRenderApplicationBase::getCurrentRenderPipelineType() const
{
    switch (isVrActive() ? m_config->m_graphics.m_renderQualityVR : m_config->m_graphics.m_renderQuality)
    {
        case GraphicsConfig::RenderQuality::cRenderQuality_Low:
            return EngineRender::GraphicsConfig::cRenderPipelineType_LowSpec;
        case GraphicsConfig::RenderQuality::cRenderQuality_Mid:
            return EngineRender::GraphicsConfig::cRenderPipelineType_LowSpec;
        case GraphicsConfig::RenderQuality::cRenderQuality_High:
            return EngineRender::GraphicsConfig::cRenderPipelineType_HighSpec;
            LLCORE_NO_DEFAULT_CASE();
    }
}

bool EngineRenderApplicationBase::isVrAvailable() const
{
    bool retVal = false;
    if (m_vrSystemInterface != nullptr)
    {
        retVal = (m_vrSystemInterface->getCurrentState() == Engine::VrState::cAvailable || m_vrSystemInterface->getCurrentState() == Engine::VrState::cActive);
    }
    return retVal;
}

bool EngineRenderApplicationBase::isVrActive() const
{
    bool retVal = false;
    if (m_vrSystemInterface != nullptr)
    {
        retVal = (m_vrSystemInterface->getCurrentState() == Engine::VrState::cActive);
    }
    return retVal;
}

LLGraphics::VRDisplay const* EngineRenderApplicationBase::getVrDisplay() const
{
    return isVrActive() ? m_vrSystemInterface->getVrDisplay() : nullptr;
}

void EngineRenderApplicationBase::resizeViewport(int width, int height)
{
    // Do not restart the script until resizing is complete
    if (LLCore::CheckAllBitsSet(m_renderScriptState, (uint8)cRenderScriptStateFlag_Resizing))
    {
        return;
    }
    const float newWidth  = (float)width;
    const float newHeight = (float)height;
    if (LLCore::RoundUpToNearest(newWidth, 8.f) != LLCore::RoundUpToNearest(m_viewportWidth, 8.f)
        || LLCore::RoundUpToNearest(newHeight, 8.f) != LLCore::RoundUpToNearest(m_viewportHeight, 8.f)) // (don't bother unless tile boundary crossed)
    {
        m_viewportWidth  = newWidth;
        m_viewportHeight = newHeight;
        requestRenderScriptRestart("Resizing viewport");
    }
}

void EngineRenderApplicationBase::onWindowResizeStart(LLWindow::WindowStartResizeEvent const&)
{
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Resizing, true);
}

void EngineRenderApplicationBase::onWindowResizeEnd(LLWindow::WindowEndResizeEvent const&)
{
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Resizing, false);
    resizeViewport(m_width, m_height);
}

bool EngineRenderApplicationBase::startRenderer(LLResource::ResourceStoreManager* resourceStoreManager, LLResource::ResourceLoader* const resourceLoader)
{
    if (m_config->m_enableRenderer == false)
    {
        return false;
    }

    // This needs to be initialized before the call to BaseType::startRenderer()
    if (m_config->m_enableVR)
    {
        m_vrSystemInterface = createVrSystem();
    }

    BaseType::startRenderer();

    if (rendererRunning() == false)
    {
        return false;
    }

    int const threadCount = LLCore::Clamp(LLCore::HardwareInfo::GetHardwareThreadCount() - 2, 4, 16); // (Umbra appears to have an internal limit of 16)
    m_jobManager.startThreads(threadCount, "EngineRender.Job");

    getEventQueue().addCallback(this, &EngineRenderApplicationBase::onWindowResizeStart);
    getEventQueue().addCallback(this, &EngineRenderApplicationBase::onWindowResizeEnd);

    m_materialTypeDescriptorCatalog.load(EngineRender::MaterialTypeDescriptor::Catalog::CatalogType::cCatalogType_Materials, m_config->m_graphics.m_materialTypeDescriptorPath.asRead());
    m_materialTypeDescriptorCatalog.load(EngineRender::MaterialTypeDescriptor::Catalog::CatalogType::cCatalogType_SystemMaterials, m_config->m_graphics.m_systemMaterialTypeDescriptorPath.asRead());

    {
        LLGraphicsResource::ColorFormat colorFormat        = m_config->m_graphics.m_windowSurface.m_colorFormat;
        LLGraphicsResource::ColorFormat depthStencilFormat = m_config->m_graphics.m_windowSurface.m_depthStencilFormat;
        m_graphics.m_mainWindowSurface                     = m_graphicsSystem.createWindowSurface(LLCore::RefPointer<LLWindow::Window>(getWindow()), colorFormat, depthStencilFormat);
    }

    m_graphics.m_commonPackage = m_graphicsSystem.getOrLoadPackage(LLGraphics::ResolvePackageName(LLCore::Path("RUNTIME:Graphics/Output/"), "Common", m_config->m_graphics.m_useDebugPackages), resourceStoreManager);
    m_graphics.m_uiPackage     = m_graphicsSystem.getOrLoadPackage(LLGraphics::ResolvePackageName(LLCore::Path("RUNTIME:Graphics/Output/"), "UISystem", m_config->m_graphics.m_useDebugPackages), resourceStoreManager);

    m_textureStreamingManager = new EngineRender::TextureStreamingManager(&m_config->m_graphics.m_textureStreaming.asRead(), resourceStoreManager, &m_graphicsSystem, resourceLoader, &m_materialTypeDescriptorCatalog, &m_jobManager);

    requestRenderScriptReload("Startup");
    return true;
}

void EngineRenderApplicationBase::shutdownRenderer()
{
    if (m_config->m_enableRenderer == false)
    {
        return;
    }

    getEventQueue().removeCallback(this, &EngineRenderApplicationBase::onWindowResizeStart);
    getEventQueue().removeCallback(this, &EngineRenderApplicationBase::onWindowResizeEnd);

    delete m_vrSystemInterface;
    m_vrSystemInterface = nullptr;

    delete m_textureStreamingManager;
    m_textureStreamingManager = nullptr;

    m_defaultViewRenderScriptId = LLGraphicsScript::cInvalidScriptInstanceId;
    m_defaultViewRenderScriptContext.wipe();

    m_graphics.releaseAll();

    BaseType::stopRenderer();
    m_jobManager.stopThreads();
}

void EngineRenderApplicationBase::initializeUiRenderer(LLUIRender::UIRenderControl* uiRenderControl)
{
    if (m_config->m_enableRenderer == false)
    {
        return;
    }

    static_cast<EngineRender::EngineRenderer*>(m_renderer)->initializeUiRenderer(uiRenderControl);
}

void EngineRenderApplicationBase::destroyUiRenderer()
{
    if (m_config->m_enableRenderer == false)
    {
        return;
    }

    LLCORE_ASSERT(BaseType::m_rendererStarted == false, "Do not destroy UI renderer while renderer is running");
    static_cast<EngineRender::EngineRenderer*>(m_renderer)->destroyUiRenderer();
}

float EngineRenderApplicationBase::getViewportWidth() const
{
    return isVrActive() ? (float)m_vrSystemInterface->getVrDisplay()->getStaticState().m_combinedViewport.m_width : m_viewportWidth;
}

float EngineRenderApplicationBase::getViewportHeight() const
{
    return isVrActive() ? (float)m_vrSystemInterface->getVrDisplay()->getStaticState().m_combinedViewport.m_height : m_viewportHeight;
}

void EngineRenderApplicationBase::reloadMaterials(LLResource::ResourceStoreManager* resourceStoreManager, EngineRender::RenderWorld* renderWorld)
{
    LLCORE_ASSERT(resourceStoreManager != nullptr, "Parameter 'resourceStoreManager' cannot be null");
    LLCORE_ASSERT(renderWorld != nullptr, "Parameter 'renderWorld' cannot be null");

    m_materialTypeDescriptorCatalog.load(EngineRender::MaterialTypeDescriptor::Catalog::cCatalogType_Materials, m_config->m_graphics.m_materialTypeDescriptorPath.asRead());
    m_materialTypeDescriptorCatalog.load(EngineRender::MaterialTypeDescriptor::Catalog::cCatalogType_SystemMaterials, m_config->m_graphics.m_systemMaterialTypeDescriptorPath.asRead());
    renderWorld->reloadMaterials(resourceStoreManager, getCurrentRenderPipelineType());
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Materials, false);

    requestRenderScriptReload("Reloading materials");
}

void EngineRenderApplicationBase::requestRenderMaterialsReload(char const* const reason)
{
    LLCore::LogInfo<"RenderScript"_ll_tag>("EngineRenderApplication", LLCore::Format("Requesting render materials reload. Reason: %s", reason));
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Materials, true);
}

void EngineRenderApplicationBase::pauseRenderScriptRestartReload(bool pauseUpdate)
{
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_PauseUpdate, pauseUpdate);
}

void EngineRenderApplicationBase::requestRenderScriptRestart(char const* const reason)
{
    LLCore::LogInfo<"RenderScript"_ll_tag>("EngineRenderApplication", LLCore::Format("Requesting render script restart. Reason: %s", reason));
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Restart, true);
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Bindings, true);
}

void EngineRenderApplicationBase::requestRenderScriptReload(char const* const reason)
{
    LLCore::LogInfo<"RenderScript"_ll_tag>("EngineRenderApplication", LLCore::Format("Requesting render script reload. Reason: %s", reason));
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Reload, true);
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Bindings, true);
}

inline bool ShouldRestartRenderScript(LLGraphicalApplication::GraphicalApplicationBase const* const client, const uint8 renderScriptStateFlags, LLResource::ResourceStoreManager const* const resourceStoreManager, EngineRender::RenderWorld const* const renderWorld)
{
    const bool isRendererRunning           = client->rendererRunning();
    const bool isRestartOrReloadRequested  = LLCore::CheckAnyBitsSet(renderScriptStateFlags, (uint8)cRenderScriptStateFlag_RestartRequestMask);
    const bool isValidResourceStoreManager = resourceStoreManager != nullptr;
    const bool isValidRenderWorld          = renderWorld != nullptr;

    const bool retVal = isRendererRunning && isValidResourceStoreManager && isValidRenderWorld && isRestartOrReloadRequested;
    return retVal;
}

inline bool ShouldRenewGraphicsBindings(LLGraphicalApplication::GraphicalApplicationBase const* const client, const uint8 renderScriptStateFlags, LLResource::ResourceStoreManager const* const resourceStoreManager, EngineRender::RenderWorld const* const renderWorld)
{
    const bool shouldRestartScript      = ShouldRestartRenderScript(client, renderScriptStateFlags, resourceStoreManager, renderWorld);
    const bool isRenewBindingsRequested = LLCore::CheckAllBitsSet(renderScriptStateFlags, (uint8)cRenderScriptStateFlag_Bindings);

    const bool retVal = shouldRestartScript && isRenewBindingsRequested;
    return retVal;
}

EngineRenderApplicationBase::FrameContext EngineRenderApplicationBase::beginBuildingRenderFrame(LLGems::ComponentHandle<Engine::CameraComponent> const& camera)
{
    LLResource::ResourceStoreManager* resourceStoreManager = getResourceStoreManager();
    EngineRender::RenderWorld*        renderWorld          = getRenderWorld();

    if (renderWorld != nullptr && resourceStoreManager != nullptr && LLCore::CheckAllBitsSet(m_renderScriptState, (uint8)cRenderScriptStateFlag_Materials))
    {
        reloadMaterials(resourceStoreManager, renderWorld);
    }


    //FIXME: this is called only occasionally, not at least once per renderworld, so can't rely on it to configure texture memory
    if (ShouldRenewGraphicsBindings(this, m_renderScriptState, resourceStoreManager, renderWorld))
    {
        renewGraphicsBindings();
    }

    if (renderWorld != nullptr)
    {
        renderWorld->setBrightnessAdjustment(isVrActive() ? 0.f : m_config->m_graphics.m_desktopBrightnessAdjustment);
    }

    FrameContext::ViewContextArray                               renderViews;
    LLGraphicsGems::OwnedPointer<LLGraphicsStream::FrameBuilder> frameBuilder(m_streamQueue.buildFrame());
    initializeRenderViews(camera, frameBuilder, &renderViews);
    return FrameContext(camera, LLCore::Give(frameBuilder), LLCore::Give(renderViews));
}

void EngineRenderApplicationBase::endBuildingRenderFrame(FrameContext&& frameContextRef, LLGraphics::Target2d const** screenshotSourceOut)
{
    LLCORE_ASSERT(frameContextRef.m_context.m_viewsCount == frameContextRef.m_renderViews.getCount(), "Invalid context");
    FrameContext frameContext(LLCore::Give(frameContextRef));
    evaluateRenderScripts(frameContext.m_context, screenshotSourceOut);
    m_graphicsSystem.submitFrame(LLCore::Give(frameContext.m_frameBuilder));
}

void EngineRenderApplicationBase::renewGraphicsBindings()
{
    if (LLCore::CheckAllBitsSet(m_renderScriptState, (uint8)cRenderScriptStateFlag_PauseUpdate))
    {
        return;
    }

    LLResource::ResourceStoreManager* resourceStoreManager = getResourceStoreManager();
    EngineRender::RenderWorld*        renderWorld          = getRenderWorld();
    LLCORE_ASSERT(LLCore::CheckAllBitsSet(m_renderScriptState, (uint8)cRenderScriptStateFlag_Bindings), "Use flags to trigger graphics bindings renewal");
    LLCORE_ASSERT(ShouldRestartRenderScript(this, m_renderScriptState, resourceStoreManager, renderWorld), "A script restart should also be pending");

    if (LLCore::CheckAllBitsSet(m_renderScriptState, (uint8)cRenderScriptStateFlag_Reload))
    {
        // clear out any resources held by the old script instance
        renderWorld->unbindGraphics();
        m_defaultViewRenderScriptContext.wipe();
        m_defaultViewRenderScriptId = LLGraphicsScript::cInvalidScriptInstanceId;
        m_graphicsSystem.submitResourceFlush(m_streamQueue.buildCleanup());
    }

    EngineRender::GraphicsConfig::SceneConfig sceneConfig(m_config->m_graphics.m_scene);
    switch (isVrActive() ? m_config->m_graphics.m_renderQualityVR : m_config->m_graphics.m_renderQuality)
    {
        case GraphicsConfig::cRenderQuality_Low:
            sceneConfig.m_lightingConfig.m_shadowConfig.m_spotlightShadowsMax           = 0;
            sceneConfig.m_lightingConfig.m_shadowConfig.m_spotlightShadowmapAtlasHeight = 256;
            sceneConfig.m_lightingConfig.m_shadowConfig.m_cascadeCount                  = 0;
            break;
        case GraphicsConfig::cRenderQuality_Mid:
            sceneConfig.m_lightingConfig.m_shadowConfig.m_spotlightShadowsMax           = 4;
            sceneConfig.m_lightingConfig.m_shadowConfig.m_spotlightShadowmapAtlasHeight = 2048;
            sceneConfig.m_lightingConfig.m_shadowConfig.m_cascadeCount                  = 2;
            sceneConfig.m_lightingConfig.m_shadowConfig.m_cascadeResolution             = 1024;
            sceneConfig.m_lightingConfig.m_shadowConfig.m_cascadeNearDistance           = 7.f;
            sceneConfig.m_lightingConfig.m_shadowConfig.m_cascadeFarDistance            = 35.f;
            break;
        case GraphicsConfig::cRenderQuality_High:
            sceneConfig.m_lightingConfig.m_shadowConfig.m_spotlightShadowsMax.resetToDefault();
            sceneConfig.m_lightingConfig.m_shadowConfig.m_spotlightShadowmapAtlasHeight.resetToDefault();
            sceneConfig.m_lightingConfig.m_shadowConfig.m_cascadeCount.resetToDefault();
            sceneConfig.m_lightingConfig.m_shadowConfig.m_cascadeResolution.resetToDefault();
            sceneConfig.m_lightingConfig.m_shadowConfig.m_cascadeNearDistance.resetToDefault();
            sceneConfig.m_lightingConfig.m_shadowConfig.m_cascadeFarDistance.resetToDefault();

            break;
        case GraphicsConfig::cRenderQuality_Custom:
            break;
            LLCORE_NO_DEFAULT_CASE();
    }

    renderWorld->configureGraphics(sceneConfig, getCurrentRenderPipelineType(), resourceStoreManager);
    setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Bindings, false);
}

void EngineRenderApplicationBase::populateRenderScriptConfig(EngineRender::RenderScriptConfig* renderScriptConfig, EngineRender::GraphicsConfig::ScriptWorldParams* scriptWorldParams, EngineRender::RenderScriptParams* renderScriptParams, LLGraphicsScript::Configuration* config)
{
    LLCORE_ASSERT(renderScriptConfig != nullptr, "Parameter 'renderScriptConfig' cannot be null");
    LLCORE_ASSERT(scriptWorldParams != nullptr, "Parameter 'scriptWorldParams' cannot be null");
    LLCORE_ASSERT(renderScriptParams != nullptr, "Parameter 'renderScriptParams' cannot be null");
    LLCORE_ASSERT(config != nullptr, "Parameter 'config' cannot be null");

    const bool vrActive = isVrActive();

    // Render script config
    {
        renderScriptConfig->m_mode                 = m_renderMode;
        renderScriptConfig->m_pipelineType         = getCurrentRenderPipelineType();
        renderScriptConfig->m_platform             = EngineRender::RenderScriptConfig::cPlatform_pc11;
        renderScriptConfig->m_configuration        = m_config->m_graphics.m_useDebugPackages ? EngineRender::RenderScriptConfig::cConfiguration_Debug : EngineRender::RenderScriptConfig::cConfiguration_Release;
        renderScriptConfig->m_enableEditorFeatures = false;

        adjustRenderScriptCustomConfig(renderScriptConfig);
    }

    if (EngineRender::RenderWorld* renderWorld = getRenderWorld())
    {
        renderWorld->populateScriptParams(scriptWorldParams);
    }

    // Render script params
    {
        
        // Convert matrix to float array
        float matrixArray[9];
        for (int col = 0; col < 3; col++)
        {
            for (int row = 0; row < 3; row++)
            {
                // Matrix is column-major, array should be also column-major for shader
                matrixArray[col * 3 + row] = m_config->m_window.m_hdrColorMatrix.getColumn(col).getScalarAt(row).asFloat();
            }
        }

        renderScriptParams->m_scriptConfig             = renderScriptConfig; // these config block pointers are not held by the script instance, just passed this way
        renderScriptParams->m_worldParams              = scriptWorldParams;
        renderScriptParams->m_enableShadowedAtmosphere = vrActive ? m_config->m_graphics.m_enableVrShadowedAtmospherics : m_config->m_graphics.m_enableShadowedAtmospherics;
        renderScriptParams->m_enableAmbientOcclusion   = vrActive ? m_config->m_graphics.m_enableVrAmbientOcclusion : m_config->m_graphics.m_enableAmbientOcclusion;
        renderScriptParams->m_enableReflections        = vrActive ? m_config->m_graphics.m_enableVrReflections : m_config->m_graphics.m_enableReflections;
        renderScriptParams->m_mirrorVrDisplay          = m_config->m_graphics.m_mirrorVrDisplay;
        renderScriptParams->m_viewSegmentCount         = 1u;
        renderScriptParams->m_samplesPerPixel          = 1.0f;
        renderScriptParams->m_displayGamma             = m_config->m_graphics.m_displayGamma;
        renderScriptParams->m_hdrMaxFALL               = m_config->m_window.m_hdrMaxFALL;
        renderScriptParams->m_hdrMaxLuminance          = m_config->m_window.m_hdrMaxLuminance;
        renderScriptParams->m_hdrOsEnabled             = (uint)m_config->m_window.m_isHdrAvailable;
        renderScriptParams->m_hdrTonemapping           = vrActive ? 0 : (uint)m_config->m_window.m_isHdrEnabled;
        renderScriptParams->m_hdrWhitePoint            = m_config->m_window.m_hdrWhitePoint;
        renderScriptParams->m_HdrPqNormalizationRcp    = m_config->m_window.m_hdrWhitePoint / 10000.0;
        renderScriptParams->m_useFSRUpscaler           = m_config->m_graphics.m_useFSRUpscaler;

        if (m_config->m_window.m_isHdrEnabled)
        {
            renderScriptParams->m_displayGammaMode = 0;
            renderScriptParams->m_displayGamma     = 2.2;
        }
        else
        {
            switch (m_config->m_graphics.m_displayGamma)
            {
                case GraphicsConfig::cDisplayGamma_Enhanced24:
                    renderScriptParams->m_displayGammaMode = 1;
                    renderScriptParams->m_displayGamma     = 2.4;
                    break;
                case GraphicsConfig::cDisplayGamma_Enhanced22:
                    renderScriptParams->m_displayGammaMode = 1;
                    renderScriptParams->m_displayGamma     = 2.2;
                    break;
                case GraphicsConfig::cDisplayGamma_Reference24:
                    renderScriptParams->m_displayGammaMode = 0;
                    renderScriptParams->m_displayGamma     = 2.4;
                    break;
                case GraphicsConfig::cDisplayGamma_Reference22:
                    renderScriptParams->m_displayGammaMode = 0;
                    renderScriptParams->m_displayGamma     = 2.2;
                    break;
                    LLCORE_NO_DEFAULT_CASE();
            }
        }

        switch (vrActive ? m_config->m_graphics.m_renderQualityVR : m_config->m_graphics.m_renderQuality)
        {
            case GraphicsConfig::cRenderQuality_Low:
                renderScriptParams->m_enableShadowedAtmosphere = false;
                renderScriptParams->m_enableAmbientOcclusion   = false;
                renderScriptParams->m_enableReflections        = false;
                break;
            case GraphicsConfig::cRenderQuality_Mid:
                renderScriptParams->m_enableAmbientOcclusion = false;
                break;
            case GraphicsConfig::cRenderQuality_High:
                break;
                LLCORE_NO_DEFAULT_CASE();
        }

        switch (vrActive ? m_config->m_graphics.m_renderResolutionVR : m_config->m_graphics.m_renderResolution)
        {
            case GraphicsConfig::cRenderResolution_Low:
                renderScriptParams->m_samplesPerPixel = 0.33;
                break;
            case GraphicsConfig::cRenderResolution_Mid:
                renderScriptParams->m_samplesPerPixel = 0.5;
                break;
            case GraphicsConfig::cRenderResolution_High:
                break;
                LLCORE_NO_DEFAULT_CASE();
        }

        adjustRenderScriptCustomParams(renderScriptParams);
    }

    // Graphics script configuration
    {
        config->m_vrDisplay     = vrActive ? getVrDisplay() : nullptr;
        config->m_custom        = renderScriptParams;
        config->m_maxViews      = renderScriptParams->m_viewSegmentCount;
        config->m_surfaceWidth  = vrActive ? 0 : (uint)m_viewportWidth;
        config->m_surfaceHeight = vrActive ? 0 : (uint)m_viewportHeight;

        adjustGraphicsScriptConfiguration(config);
    }
}

void EngineRenderApplicationBase::restartRenderScript(LLCore::Array<LLGraphicsScript::View> const& views)
{
    if (LLCore::CheckAllBitsSet(m_renderScriptState, (uint8)cRenderScriptStateFlag_PauseUpdate))
    {
        return;
    }

    LLPROFILE_AUTO_CPU_MARKER_STATIC("EngineRenderApplication::restartRenderScript");

    LLResource::ResourceStoreManager* resourceStoreManager = getResourceStoreManager();
    LLCORE_ASSERT(ShouldRestartRenderScript(this, m_renderScriptState, resourceStoreManager, getRenderWorld()), "Should not be restarting");
    LLCORE_ASSERT(LLCore::CheckAllBitsClear(m_renderScriptState, (uint8)cRenderScriptStateFlag_Bindings), "Bindings should have already been renewed");

    const bool isInitialStartup = m_defaultViewRenderScriptContext.getCurrentId() == 0;
    LLCore::RefPointer<LLGraphicsScript::ScriptImage> defaultViewRenderScriptBinary;
    if (LLCore::CheckAllBitsSet(m_renderScriptState, (uint8)cRenderScriptStateFlag_Reload))
    {
        LLCore::LogInfo<"RenderScript"_ll_tag>("EngineRenderApplication", isInitialStartup ? "Loading render script" : "Reloading render script");

        LLCore::Path const& renderScriptPath = m_config->m_graphics.m_renderScript;
        defaultViewRenderScriptBinary        = LLGraphicsScript::ScriptImage::LoadAndCreate(renderScriptPath);
        setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Reload, false);

        LLCORE_ASSERT(defaultViewRenderScriptBinary.notNull(), "Failed to load render script");
        if (defaultViewRenderScriptBinary.isNull())
        {
            LLCore::LogError("EngineRenderApplication", LLCore::Format("Failed to load render script: %s", renderScriptPath));
            return;
        }
    }

    EngineRender::RenderScriptConfig                renderScriptConfig;
    EngineRender::GraphicsConfig::ScriptWorldParams scriptWorldParams;
    EngineRender::RenderScriptParams                renderScriptParams;
    LLGraphicsScript::Configuration                 config(m_graphics.m_mainWindowSurface);

    populateRenderScriptConfig(&renderScriptConfig, &scriptWorldParams, &renderScriptParams, &config);

    // restart the script
    LLCore::LogInfo<"RenderScript"_ll_tag>("EngineRenderApplication", isInitialStartup ? "Starting render script" : "Restarting render script");

    bool startRestartSuccess = false;
    if (defaultViewRenderScriptBinary.notNull())
    {
        // Reload the script with fresh binary data
        auto defaultViewRenderScriptId = m_defaultViewRenderScriptContext.startScript(defaultViewRenderScriptBinary, views, config, &m_graphicsSystem, resourceStoreManager);
        startRestartSuccess = defaultViewRenderScriptId != LLGraphicsScript::cInvalidScriptInstanceId;
        LLCORE_ASSERT(startRestartSuccess, "Failed to start script");
        if (startRestartSuccess == true)
        {
            m_defaultViewRenderScriptId = defaultViewRenderScriptId;
        }
        else
        {
            LLCore::LogError("EngineRenderApplication", "Failed to start the render script");
        }
    }
    else
    {
        // Restart existing script instance
        LLCORE_ASSERT(m_defaultViewRenderScriptId != LLGraphicsScript::cInvalidScriptInstanceId, "Script instance never started");
        startRestartSuccess = m_defaultViewRenderScriptContext.restartScript(m_defaultViewRenderScriptId, views, config, &m_graphicsSystem, resourceStoreManager);
        LLCORE_ASSERT(startRestartSuccess, "Failed to restart the script");
        if (startRestartSuccess)
        {
            m_graphicsSystem.submitResourceFlush(m_streamQueue.buildCleanup());
        }
        else
        {
            LLCore::LogError("EngineRenderApplication", "Failed to restart the render script");
        }
    }

    if (startRestartSuccess)
    {
        setOrClearRenderScriptStateFlags(cRenderScriptStateFlag_Restart, false);
    }
}

void EngineRenderApplicationBase::setRenderMode(EngineRender::RenderScriptConfig::RenderMode mode, char const* const reason)
{
    if (m_renderMode != mode)
    {
        m_renderMode = mode;
        requestRenderScriptRestart(reason);
    }
}

void EngineRenderApplicationBase::setRenderMode(EngineRender::SetRenderModeEvent const& event)
{
    setRenderMode(event.m_mode, event.m_reason.asRead());
}

LLGraphicsRender::IRenderer* EngineRenderApplicationBase::createRenderer(LLGraphicsGems::Allocator* graphicsAllocator)
{
    return new EngineRender::EngineRenderer(graphicsAllocator, &m_jobManager, m_vrSystemInterface ? &m_vrSystemInterface->getHeadsetManager() : nullptr, m_config->m_enableVideostream);
}

void EngineRenderApplicationBase::initializeScriptViews(EngineRender::ViewContext const* view, LLCore::Array<LLGraphicsScript::View>* scriptViews)
{
    const bool useCameraComponentTransform = view->m_cameraComponent->asBase() != nullptr && LLCore::CheckAllBitsSet(view->m_flags, (uint8)FrameContext::cRenderViewFlag_UseCameraComponentTransform);
    const bool disableLateVRSampling       = LLCore::CheckAllBitsSet(view->m_flags, (uint8)FrameContext::cRenderViewFlag_DisableLateVRSampling);
    for (uint segmentIndex = 0; segmentIndex < view->m_segmentCount; segmentIndex++)
    {
        LLGraphicsScript::View&       scriptView = *scriptViews->insertLast();
        LLGraphicsGems::Camera const& cullingCam = view->m_renderStreamBuilder.getCullingInfo(segmentIndex).m_camera;
        if (useCameraComponentTransform)
        {
            scriptView.m_camera.m_localToWorld = view->m_cameraComponent->asBase()->getTransform();
        }
        else
        {
            scriptView.m_camera.m_localToWorld = LLGraphicsGems::MathUtil::PositionForwardUpToMTransform(cullingCam.m_position, cullingCam.m_forward, cullingCam.m_up);
        }
        scriptView.m_camera.m_horizontalFovHalfTangent = cullingCam.m_rightTan;
        scriptView.m_camera.m_verticalFovHalfTangent   = cullingCam.m_upTan;
        scriptView.m_camera.m_vrTrackingSpaceToWorld   = view->m_vrTrackingSpaceToWorld;
        scriptView.m_camera.m_allowLateVRSampling      = !disableLateVRSampling;
        scriptView.m_viewport                          = view->m_viewports[segmentIndex];
    }
}

void EngineRenderApplicationBase::evaluateRenderScripts(EngineRender::FrameContext const& renderFrameContext, LLGraphics::Target2d const** screenshotSourceOut)
{
    LLPROFILE_AUTO_CPU_MARKER_FUNCTION
    EngineRender::RenderWorld*        renderWorld          = getRenderWorld();
    LLResource::ResourceStoreManager* resourceStoreManager = getResourceStoreManager();
    bool                              restartScript        = ShouldRestartRenderScript(this, m_renderScriptState, resourceStoreManager, renderWorld);

    for (EngineRender::ViewContext const* view = renderFrameContext.m_views; view < renderFrameContext.m_views + renderFrameContext.m_viewsCount; view++)
    {
        if (view->m_renderScriptId == nullptr)
        {
            continue;
        }

        LLCore::Array<LLGraphicsScript::View, 16> scriptViews;
        initializeScriptViews(view, &scriptViews);

        if (scriptViews.isEmpty())
        {
            continue;
        }

        if (restartScript)
        {
            restartRenderScript(scriptViews);
            restartScript = false;
        }

        // Do this AFTER restart to ensure view is pointing to latest script ID
        LLCORE_ASSERT(&m_defaultViewRenderScriptId == view->m_renderScriptId, "Invalid render script ID");
        if (*view->m_renderScriptId == LLGraphicsScript::cInvalidScriptInstanceId)
        {
            continue;
        }

        m_defaultViewRenderScriptContext.evaluateScript(*view->m_renderScriptId, scriptViews, view->m_renderStreamBuilder, screenshotSourceOut);

        const bool disablePresentationToWindow = LLCore::CheckAllBitsSet(view->m_flags, (uint8)FrameContext::cRenderViewFlag_DisablePresentationToWindow);
        if (disablePresentationToWindow)
        {
            view->m_renderStreamBuilder.setAffectedWindowSurface(LLGraphicsStream::WindowSurfaceHandle{});
        }
    }
}
} // namespace EngineRenderApplication