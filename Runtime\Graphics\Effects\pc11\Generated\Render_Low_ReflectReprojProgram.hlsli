//
// Generated by GraphicsBuild. Do not modify.
//

#if !defined(INCLUDED_Render_Low_ReflectReprojProgram) && defined(COMPILING_Render_Low_ReflectReprojProgram)
#define INCLUDED_Render_Low_ReflectReprojProgram


// Effect-provided macros
//

#define COMPACT_GBUFFER

// Accessor macros (for HLSL/GLSL/Metal shader source compatibility)
//

#define CameraInfo(member)                          CameraInfo_##member
#define DeferredBuffers(member)                     DeferredBuffers_##member
#define ReflectReprojParams(member)                 ReflectReprojParams_##member
#define WorldInfo(member)                           WorldInfo_##member
#define VertexInput(member)                         VertexInput_.member
#define PixelOutput(member)                         PixelOutput_.member

// Samplers
//

SamplerState                        LinearClampSampler                  : register(s0);

SamplerState                        PointClampSampler                   : register(s1);

// Parameter blocks
//

//     Type Core::CameraInfo
//

cbuffer                             CameraInfo                          : register(b0)
{
    float4x4                        CameraInfo(HeadToWorld);
    float4x4                        CameraInfo(HeadToWorldHistory);
    float4x4                        CameraInfo(WorldToHead);
    float4x4                        CameraInfo(LeftEyeToHead);
    float4x4                        CameraInfo(RightEyeToHead);
    float4x4                        CameraInfo(LeftHeadToEye);
    float4x4                        CameraInfo(RightHeadToEye);
    float4x4                        CameraInfo(ClipToLeftEye);
    float4x4                        CameraInfo(ClipToRightEye);
    float4x4                        CameraInfo(LeftEyeToClip);
    float4x4                        CameraInfo(RightEyeToClip);
    float4x4                        CameraInfo(LeftEyeToClipHistory);
    float4x4                        CameraInfo(RightEyeToClipHistory);
    float                           CameraInfo(LeftEyeToHeadOffset);
    float                           CameraInfo(RightEyeToHeadOffset);
    float2                          CameraInfo(RenderViewportOffset);
    float2                          CameraInfo(RenderViewportSize);
    float2                          CameraInfo(RcpRenderViewportSize);
    float2                          CameraInfo(DisplayViewportOffset);
    float2                          CameraInfo(DisplayViewportSize);
    float2                          CameraInfo(RcpDisplayViewportSize);
    float2                          CameraInfo(RenderTargetSize);
    float2                          CameraInfo(RcpRenderTargetSize);
    float2                          CameraInfo(DisplayTargetSize);
    float2                          CameraInfo(RcpDisplayTargetSize);
    float2                          CameraInfo(TemporalJitter);
    float                           CameraInfo(GlobalMipBias);
    uint                            CameraInfo(FrameNumber);
};

Buffer<float4>                      DeferredBuffers(ProbeCoeffsL1)      : register(t0);
Buffer<float4>                      DeferredBuffers(SkyCoeffsL2)        : register(t1);
Texture2D<float4>                   DeferredBuffers(Gbuffer1)           : register(t2);
Texture2D<float4>                   DeferredBuffers(Gbuffer2)           : register(t3);
Texture2D<float>                    DeferredBuffers(Depth)              : register(t4);
Texture2D<float4>                   DeferredBuffers(Visibility)         : register(t5);
Texture2D<float4>                   DeferredBuffers(DielectricLookup)   : register(t6);
Texture2D<float4>                   DeferredBuffers(Noise)              : register(t7);

Texture2D<float4>                   ReflectReprojParams(PrevReproj)     : register(t8);
Texture2D<float4>                   ReflectReprojParams(ReflectTrace)   : register(t9);
Texture2D<float4>                   ReflectReprojParams(ReflectSolve)   : register(t10);
Texture2D<float2>                   ReflectReprojParams(Motion)         : register(t11);

//     Type Core::WorldInfo
//

cbuffer                             WorldInfo                           : register(b1)
{
    float                           WorldInfo(WorldTimeSeconds);
    float                           WorldInfo(WorldDeltaSeconds);
    bool                            WorldInfo(EnableDistanceFading);
    uint                            WorldInfo(FrameNumber);
};

// Vertex inputs
//

struct                              VertexInput
{
};

// Pixel outputs
//

struct                              PixelOutput
{
    float4                          color0          : SV_Target0;
};


#endif
