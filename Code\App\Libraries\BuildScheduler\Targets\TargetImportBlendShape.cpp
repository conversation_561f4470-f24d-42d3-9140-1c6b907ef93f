/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#include "TargetImportBlendShape.h"

#include "../Errors.h"

#include "LLApplication/CommandLineConfig.h"
#include "LLFbx/BlendShape.h"
#include "LLFbx/ErrorCodes.h"
#include "LLFbx/LLFbx.h"
#include "LLFbx/Manager.h"
#include "LLFbx/Scene.h"
#include "LLFbx/Mesh.h"

namespace BuildScheduler
{
uint8 ImportBlendShape::ExternalTask::setInput(TaskArgs* input)
{
    TaskExe::setInput(input);

    InputArgT<Build::Variable::cPath, TaskArg::cCollection>  inputFbxFiles;
    InputArgT<Build::Variable::cResId, TaskArg::cCollection> importedMeshSkeletons;
    InputArgT<Build::Variable::cText, TaskArg::cCollection>  meshScales;
    InputArgT<Build::Variable::cPath, TaskArg::cSingle>      blendShapesInfoFolder;
    InputArgT<Build::Variable::cText, TaskArg::cSingle>      doCombineMeshes;

    uint8 errorCode = getInputArgs(&inputFbxFiles, &importedMeshSkeletons, &meshScales, &blendShapesInfoFolder, &doCombineMeshes);
    if (errorCode != (uint8)Errors::Succeed)
    {
        return errorCode;
    }

    if (inputFbxFiles->getCount() != meshScales->getCount() || inputFbxFiles->getCount() != importedMeshSkeletons->getCount())
    {
        LLCore::LogError("BuildScheduler.ImportBlendShapes", "Input arrays do not have matching counts");
        return (uint8)Errors::ArrayValueMultipleMissMatch;
    }

    // make sure we have a single skeleton
    // sometimes we get the same skeleton multiple times, if that's the case, treat as single skeleton
    bool allSameSkeletons = true;
    for (mem_int i = 1; i < importedMeshSkeletons->getCount(); ++i)
    {
        if (importedMeshSkeletons->getId((int)i) != importedMeshSkeletons->getId(0))
        {
            allSameSkeletons = false;
            break;
        }
    }

    if (!allSameSkeletons)
    {
        //
        // This isn't the most optiomal solution, but attempts to work-around the issue where
        // regular multiple mesh scene uploads will fail if multiple meshes contain skeletons.
        //
        // A more optimal solution would avoid loading the mesh twice during
        // the import process but might not be practical to avoid this.
        //
        // However, by only performing this additional check when required
        // will ameliorate most of the extra cost

        // Check if any of the input files have blend shapes
        bool anyHasBlendShapes = false;
        for (mem_int i = 0; i < inputFbxFiles->getCount(); ++i)
        {
            // Create an FBX manager and scene
            LLFbx::Manager fbxManager;
            LLFbx::Scene   fbxScene(fbxManager);

            // Try to load the FBX file
            const LLCore::Path& inputFbxPath = inputFbxFiles->getPath((int)i);
            LLFbx::ErrorCodes   fbxError     = fbxScene.load(inputFbxPath, false);
            if (fbxError == LLFbx::ErrorCodes::Succeed)
            {
                // Get the mesh from the scene
                LLFbx::Mesh fbxMesh(fbxScene.getMesh(0));
                if (!fbxMesh.isNull())
                {
                    // Check for blend shapes
                    LLFbx::BlendShape blendShapesInfo(fbxMesh.get());
                    if (blendShapesInfo.isValid())
                    {
                        blendShapesInfo.build();
                        if (blendShapesInfo.hasBlendShapes())
                        {
                            anyHasBlendShapes = true;
                            LLCore::LogInfo("BuildScheduler.ImportBlendShapes", "File '", inputFbxPath.getFilename(), "' contains blend shapes");
                            break; // Found at least one file with blend shapes
                        }
                    }
                }
            }
        }
        //

        if (anyHasBlendShapes)
        {
            LLCore::LogError("BuildScheduler.ImportBlendShapes", "Multiple skeletons found!");
            return (uint8)Errors::MultipleRigDetected;
        }
    }

    m_config.m_resourceFolder        = getResourceFolder();
    m_config.m_recordResourceWriting = true;

#if defined(LLCORE_PLATFORM_WINDOWS)
    LLCore::String fbxString;
    LLCore::String scalesString;
    for (int i = 0; i < inputFbxFiles->getCount(); ++i)
    {
        fbxString.append(inputFbxFiles->at(0)->getPath().getFilename(), " ");
        scalesString.append(LLCore::ConvertType<LLCore::Vector4>(meshScales->getText((int)i)), "/");
    }

    m_config.m_inputFbxFiles.add(inputFbxFiles->getPath(0));
    m_config.m_meshScales.add(LLCore::ConvertType<LLCore::Vector4>(meshScales->getText(0)));
    writeFbxParams(fbxString, LLCore::String("ImportContentFbxBlendPaths"));
    writeFbxParams(scalesString, LLCore::String("ImportContentFbxBlendScales"));
#else
    for (mem_int i = 0; i < inputFbxFiles->getCount(); ++i)
    {
        m_config.m_inputFbxFiles.add(inputFbxFiles->getPath((int)i));
        m_config.m_meshScales.add(LLCore::ConvertType<LLCore::Vector4>(meshScales->getText((int)i)));
    }
#endif


    if (!importedMeshSkeletons->getId(0).isNull())
    {
        m_config.m_bindPose = importedMeshSkeletons->getId(0);
    }

    if (!EnsureOutputFolder(blendShapesInfoFolder->getPath()))
    {
        LLCore::LogError("BuildScheduler.ImportBlendShape", "Cannot create output folder '", blendShapesInfoFolder->getPath(), "'");
        return (uint8)Errors::CannotCreateOutputFolder;
    }
    m_config.m_blendShapesInfoFolder = blendShapesInfoFolder->getPath();

    m_doCombineMeshes           = LLCore::ConvertType<bool>(doCombineMeshes->getText());
    m_config.m_doCombineMeshes  = m_doCombineMeshes;


    m_config.fillWith(*m_parentConfig);

    m_appHandle = LLApplication::CreateChildApplication<::ImportBlendShape::ImportBlendShapeApplication>(m_config);

    return (uint8)Errors::Succeed;
}

bool ImportBlendShape::ExternalTask::writeFbxParams(const LLCore::String& fbxString, const LLCore::String& pathSuffix)
{
    const LLCore::Path buildPath = m_config.m_resourceFolder.asRead().m_path;
    LLCore::String     buildPathString;
    buildPath.getPath(&buildPathString);
    buildPathString.append(pathSuffix);

    LLCore::FileSystem::Error   fileOpenError;
    const LLCore::FileReference argFileReference = LLCore::FileReference(buildPathString);
    LLCore::File*               argFile          = argFileReference.open(LLCore::FileSystem::cOpenMode_WriteReplace, LLCore::FileSystem::cShareMode_Shared, &fileOpenError);

    if (fileOpenError == LLCore::FileSystem::Error::cError_None)
    {
        argFile->writeString(fbxString);
        argFile->flush();
        return true;
    }
    else
    {
        LLCore::LogError("AssetConversion", "Unable to open file '", buildPathString, "'");
        return false;
    }
}

uint8 ImportBlendShape::ExternalTask::exeCompleted(uint8 returnCode)
{
    if (returnCode)
    {
        return returnCode;
    }

    //BLEND SHAPES
    if (m_doCombineMeshes)
    {
        LLCore::Path blendShapesInfoPath;
        if (!TryParseOutput(m_textOutput, "BlendShapesInfo"_ll, &blendShapesInfoPath))
        {
            LLCore::LogError("BuildScheduler.ImportBlendShape", "Cannot parse for BlendShapesInfo");
            return (uint8)Errors::CannotParseOutput;
        }

        m_output.add("BlendShapesInfo")->add(blendShapesInfoPath);
    }
    else
    {
        LLCore::Array<LLCore::String> blendShapesInfoPathStrs;
        if (TryParseOutput(m_textOutput, "BlendShapesInfo"_ll, &blendShapesInfoPathStrs))
        {
            TaskArg* arg = m_output.add("BlendShapesInfo");
            for (const LLCore::String& blendShapeInfoPathStr : blendShapesInfoPathStrs)
            {
                LLCore::Path blendShapeInfoPath;
                blendShapeInfoPath.setPath(blendShapeInfoPathStr);
                arg->add(blendShapeInfoPath);
            }
        }
        else
        {
            LLCore::LogError("BuildScheduler.ImportBlendShape", "Cannot parse for BlendShapesInfo");
            return (uint8)Errors::CannotParseOutput;
        }
    }


    return (uint8)Errors::Succeed;
}
} // namespace BuildScheduler