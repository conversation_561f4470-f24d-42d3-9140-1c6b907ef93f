﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Android">
      <Configuration>Debug</Configuration>
      <Platform>Android</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Production|Android">
      <Configuration>Production</Configuration>
      <Platform>Android</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|_Windows">
      <Configuration>Debug</Configuration>
      <Platform>_Windows</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Production|_Windows">
      <Configuration>Production</Configuration>
      <Platform>_Windows</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Linux">
      <Configuration>Debug</Configuration>
      <Platform>Linux</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Production|Linux">
      <Configuration>Production</Configuration>
      <Platform>Linux</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|iOS">
      <Configuration>Debug</Configuration>
      <Platform>iOS</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Production|iOS">
      <Configuration>Production</Configuration>
      <Platform>iOS</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="AnimationComponent.cpp" />
    <ClCompile Include="AnimationComponentManager.cpp" />
    <ClCompile Include="AnimationComponentMessages.cpp" />
    <ClCompile Include="AnimationStateListener.cpp" />
    <ClCompile Include="ArrayStreamWriter.cpp" />
    <ClCompile Include="AVBDIntegrationBridge.cpp" />
    <ClCompile Include="BehaviorAssetLoader.cpp" />
    <ClCompile Include="BehaviorAttachment.cpp" />
    <ClCompile Include="BehaviorProjectData.cpp" />
    <ClCompile Include="BodyCollisionFilter.cpp" />
    <ClCompile Include="CameraViewer.cpp" />
    <ClCompile Include="CharacterComponent.cpp" />
    <ClCompile Include="CharacterComponentManager.cpp" />
    <ClCompile Include="CharacterNodePhysicsInterface.cpp" />
    <ClCompile Include="CharacterPrediction.cpp" />
    <ClCompile Include="CharacterProxyController.cpp" />
    <ClCompile Include="CharacterProxyState.cpp" />
    <ClCompile Include="FrameSynchronizer.cpp" />
    <ClCompile Include="HavokAnimationUtils.cpp" />
    <ClCompile Include="HavokDebugDisplay.cpp" />
    <ClCompile Include="HavokResource.cpp" />
    <ClCompile Include="IKBodyComponent.cpp" />
    <ClCompile Include="IKBodyComponentManager.cpp" />
    <ClCompile Include="IKBodyShapes.cpp" />
    <ClCompile Include="IKBodyShapeViewer.cpp" />
    <ClCompile Include="LayerManager.cpp" />
    <ClCompile Include="SGAnimationControls.cpp" />
    <ClCompile Include="SourceSpaceCollisionFilter.cpp" />
    <ClCompile Include="PhysicsEventTranslator.cpp" />
    <ClCompile Include="RigidBodyAuthorityViewer.cpp" />
    <ClCompile Include="SGCharacterBridge.cpp" />
    <ClCompile Include="PickComponent.cpp" />
    <ClCompile Include="Picking.cpp" />
    <ClCompile Include="SpeechGraphicsResource.cpp" />
    <ClCompile Include="System.cpp" />
    <ClCompile Include="HavokAssetUtil.cpp" />
    <ClCompile Include="KeyframedBoneComponent.cpp" />
    <ClCompile Include="KeyframedBoneComponentManager.cpp" />
    <ClCompile Include="KeyframedBoneViewer.cpp" />
    <ClCompile Include="HavokAnimationContext.cpp" />
    <ClCompile Include="HavokPhysicsContext.cpp" />
    <ClCompile Include="PhysicsCharacterInterface.cpp" />
    <ClCompile Include="PhysicsConstraintComponent.cpp" />
    <ClCompile Include="PhysicsConstraintComponentManager.cpp" />
    <ClCompile Include="PoseComponent.cpp" />
    <ClCompile Include="PoseComponentManager.cpp" />
    <ClCompile Include="ProjectAssetManager.cpp" />
    <ClCompile Include="ProxyAffectManager.cpp" />
    <ClCompile Include="RigidBodyComponent.cpp" />
    <ClCompile Include="RigidBodyComponentManager.cpp" />
    <ClCompile Include="ScriptAssetLoader.cpp" />
    <ClCompile Include="SerializeTypeOverload.cpp" />
    <ClCompile Include="ServerCharacterProxyViewer.cpp" />
    <ClCompile Include="ServerShapeViewer.cpp" />
    <ClCompile Include="SimulationMessages.cpp" />
    <ClCompile Include="VisualDebugger.cpp" />
    <ClCompile Include="SimulationWorld.cpp" />
    <ClCompile Include="XSensDatagram.cpp" />
    <ClCompile Include="XSensManager.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AnimationComponent.h" />
    <ClInclude Include="AnimationComponentManager.h" />
    <ClInclude Include="AnimationComponentMessages.h" />
    <ClInclude Include="AnimationComponentMessagesAutoConvert.h" />
    <ClInclude Include="AnimationComponentMessagesConvert.h" />
    <ClInclude Include="AnimationComponentReflectionDefinitions.h" />
    <ClInclude Include="AnimationEvents.h" />
    <ClInclude Include="AnimationHavokWrappers.h" />
    <ClInclude Include="AnimationStateListener.h" />
    <ClInclude Include="AnimationUtils.h" />
    <ClInclude Include="ArrayStreamWriter.h" />
    <ClInclude Include="BehaviorAssetLoader.h" />
    <ClInclude Include="BehaviorAttachment.h" />
    <ClInclude Include="BehaviorProjectData.h" />
    <ClInclude Include="BodyCollisionFilter.h" />
    <ClInclude Include="CameraViewer.h" />
    <ClInclude Include="CharacterComponent.h" />
    <ClInclude Include="CharacterComponentManager.h" />
    <ClInclude Include="CharacterNodePhysicsInterface.h" />
    <ClInclude Include="CharacterPrediction.h" />
    <ClInclude Include="CharacterProxyController.h" />
    <ClInclude Include="CharacterProxyState.h" />
    <ClInclude Include="EngineSimulationDeprecatedTypes.h" />
    <ClInclude Include="EngineSimulationConstants.h" />
    <ClInclude Include="FrameSynchronizer.h" />
    <ClInclude Include="HavokAnimationUtils.h" />
    <ClInclude Include="HavokAssetUtil.h" />
    <ClInclude Include="HavokDebugDisplay.h" />
    <ClInclude Include="HavokResource.h" />
    <ClInclude Include="IKBodyComponent.h" />
    <ClInclude Include="IKBodyComponentManager.h" />
    <ClInclude Include="IKBodyShapes.h" />
    <ClInclude Include="IKBodyShapeViewer.h" />
    <ClInclude Include="LayerManager.h" />
    <ClInclude Include="SGAnimationControls.h" />
    <ClInclude Include="SimulationEvents.h" />
    <ClInclude Include="SourceSpaceCollisionFilter.h" />
    <ClInclude Include="PhysicsEvents.h" />
    <ClInclude Include="PhysicsEventTranslator.h" />
    <ClInclude Include="PickComponent.h" />
    <ClInclude Include="Picking.h" />
    <ClInclude Include="RigidBodyAuthorityViewer.h" />
    <ClInclude Include="SGCharacterBridge.h" />
    <ClInclude Include="SGHavokCommon.h" />
    <ClInclude Include="SpeechGraphicsInterface.h" />
    <ClInclude Include="SpeechGraphicsResource.h" />
    <ClInclude Include="System.h" />
    <ClInclude Include="HavokTypes.h" />
    <ClInclude Include="KeyframedBoneComponent.h" />
    <ClInclude Include="KeyframedBoneComponentManager.h" />
    <ClInclude Include="KeyframedBoneViewer.h" />
    <ClInclude Include="HavokAnimationContext.h" />
    <ClInclude Include="HavokPhysicsContext.h" />
    <ClInclude Include="MathUtils.h" />
    <ClInclude Include="PhysicsCharacterInterface.h" />
    <ClInclude Include="PhysicsConstraintComponent.h" />
    <ClInclude Include="PhysicsConstraintComponentManager.h" />
    <ClInclude Include="PoseComponent.h" />
    <ClInclude Include="PoseComponentManager.h" />
    <ClInclude Include="ProjectAssetManager.h" />
    <ClInclude Include="ProxyAffectManager.h" />
    <ClInclude Include="ResourceWriter.h" />
    <ClInclude Include="RigidBodyComponent.h" />
    <ClInclude Include="RigidBodyComponentManager.h" />
    <ClInclude Include="ScriptAssetLoader.h" />
    <ClInclude Include="SerializeTypeOverload.h" />
    <ClInclude Include="SerializeUtils.h" />
    <ClInclude Include="ServerCharacterProxyViewer.h" />
    <ClInclude Include="ServerShapeViewer.h" />
    <ClInclude Include="SimulationMessages.h" />
    <ClInclude Include="SimulationMessagesAutoConvert.h" />
    <ClInclude Include="SimulationMessagesConvert.h" />
    <ClInclude Include="VisualDebugger.h" />
    <ClInclude Include="SimulationWorld.h" />
    <ClInclude Include="XSensDatagram.h" />
    <ClInclude Include="XSensManager.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Common\Libraries\LLCore\LLCore.vcxproj">
      <Project>{0300fc37-9a39-470f-83ff-ead3189f30b4}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Common\Libraries\LLHavok\LLHavok.vcxproj">
      <Project>{24acb5e8-746e-418c-832a-d77edcf8b603}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Common\Libraries\LLNetwork\LLNetwork.vcxproj">
      <Project>{7d12fc49-d3bd-492b-835a-36fd4c3a56dc}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Common\Libraries\LLResource\LLResource.vcxproj">
      <Project>{e21fb0a3-a274-4cb1-b207-43b4006c36d6}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Common\Libraries\LLSerializeXml\LLSerializeXml.vcxproj">
      <Project>{574a985a-4930-44ed-81fb-7c9f76f489da}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Common\Libraries\LLZlib\LLZlib.vcxproj">
      <Project>{efbc9adc-310e-4b27-b051-e79a20415847}</Project>
    </ProjectReference>
    <ProjectReference Include="..\EngineCommon\EngineCommon.vcxproj">
      <Project>{322b6fb1-8a55-4531-85ee-94ed6795b78c}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="AnimationComponent.inl" />
    <None Include="AnimationComponentMessages.inl" />
    <None Include="SerializeUtils.inl" />
    <None Include="SimulationMessages.inl" />
  </ItemGroup>
  <ItemGroup>
    <MessageSchema Include="AnimationComponentMessages.msg" />
    <MessageSchema Include="SimulationMessages.msg" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{39ED0824-61F8-45E5-99EB-4C4269761F92}</ProjectGuid>
    <Keyword Condition="'$(Platform)'=='_Windows'">Win32Proj</Keyword>
    <Keyword Condition="'$(Platform)'=='Android'">Android</Keyword>
    <RootNamespace>Havok</RootNamespace>
    <WindowsTargetPlatformVersion>10.0.20348.0</WindowsTargetPlatformVersion>
    <ApplicationType Condition="'$(Platform)'=='Android' and '$(DesignTimebuild)'!='true'">Android</ApplicationType>
    <ApplicationTypeRevision Condition="'$(Platform)'=='Android'">3.0</ApplicationTypeRevision>
  </PropertyGroup>
  <Import Project="..\FindRoot.props" />
  <Import Project="$(LLRootDir)Build\MSBuild\Linden.Cpp.Default.props" />
  <PropertyGroup Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset Condition="'$(Platform)'!='Android'">v142</PlatformToolset>
    <PlatformToolset Condition="'$(Platform)'=='Android'">Clang_5_0</PlatformToolset>
    <UseNativeEnvironment>true</UseNativeEnvironment>
    <EnableUnitySupport Condition="'$(EnableUnitySupport)' == ''">true</EnableUnitySupport>
  </PropertyGroup>
  <Import Project="$(LLRootDir)Build\MSBuild\Linden.Cpp.props" />
  <Import Project="$(LLAdditionalCppProps)" Condition="'$(LLAdditionalCppProps)' != ''" />
  <ImportGroup Label="PropertySheets">
    <Import Project="..\..\..\Common\Libraries\LLHavok\Havok.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <Import Project="$(LLRootDir)Build\MSBuild\Linden.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>