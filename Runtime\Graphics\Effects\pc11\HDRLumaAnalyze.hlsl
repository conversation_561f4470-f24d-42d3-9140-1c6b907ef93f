#include "Generated/Render_High_HDRLumaAnalyzeProgram.hlsli"
#include "Generated/Render_Low_HDRLumaAnalyzeProgram.hlsli"

#include "../Common/MathUtil.hlsli"
#include "../Common/ColorUtil.hlsli"


// Shared memory for local computations
groupshared float2 localData[256]; // [average sum, max value] per thread

// Helper to calculate perceptual luminance (Rec. 709)
float CalculateLuminance(float3 color)
{
    return dot(color, float3(0.2126, 0.7152, 0.0722));
}

// First pass - analyze tiles of the image
[numthreads(16, 16, 1)]
void ComputeMain(uint3 groupId : SV_GroupID, uint3 dispatchThreadId : SV_DispatchThreadID, uint3 groupThreadId : SV_GroupThreadID, uint groupIndex : SV_GroupIndex)
{
    uint2 pixel = dispatchThreadId.xy;
    float2 result = float2(0.0, 0.0); // [luminance sum, max luminance]
    
    // Read pixel value if within texture bounds
    if (all(pixel < HDRLumaAnalysisParams(TextureSize)))
    {
        float3 color = HDRLumaAnalysisParams(InputTexture)[pixel].xyz * HDRLumaAnalysisParams(ExposureInfo)[0].xxx;
        float lum = CalculateLuminance(color);
        
        // Store sum for average and max for peak luminance
        result = float2(lum, lum);
    }
    
    // Store in shared memory
    localData[groupIndex] = result;
    
    // Synchronize threads in the group
    GroupMemoryBarrierWithGroupSync();
    
    // Parallel reduction within tile
    for (uint stride = 256/2; stride > 0; stride >>= 1)
    {
        if (groupIndex < stride)
        {
            // Sum for average, max for peak
            localData[groupIndex].x += localData[groupIndex + stride].x;
            localData[groupIndex].y = max(localData[groupIndex].y, localData[groupIndex + stride].y);
        }
        
        GroupMemoryBarrierWithGroupSync();
    }
    
    // Write results to intermediate buffer
    if (groupIndex == 0)
    {
        uint tileIndex = groupId.y * (HDRLumaAnalysisParams(TextureSize).x / 16 + (HDRLumaAnalysisParams(TextureSize).x % 16 > 0 ? 1 : 0)) + groupId.x;
        HDRLumaAnalysisParams(TileStatistics)[tileIndex] = float4(localData[0].x / 256.0, localData[0].y, 0.0, 0.0);
    }
}