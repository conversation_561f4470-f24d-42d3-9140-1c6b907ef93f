//
// Generated by GraphicsBuild. Do not modify.
//

#if !defined(INCLUDED_Render_High_HDRLumaReduceProgram) && defined(COMPILING_Render_High_HDRLumaReduceProgram)
#define INCLUDED_Render_High_HDRLumaReduceProgram


// Effect-provided macros
//


// Accessor macros (for HLSL/GLSL/Metal shader source compatibility)
//

#define HDRLumaAnalysisParams(member)               HDRLumaAnalysisParams_##member

// Samplers
//

SamplerState                        LinearSampler                       : register(s0);

// Parameter blocks
//

//     Type Render::HDRLumaAnalysisParams
//

cbuffer                             HDRLumaAnalysisParams               : register(b0)
{
    uint4                           HDRLumaAnalysisParams(TextureSize);
    uint                            HDRLumaAnalysisParams(TileCount);
};
RWBuffer<float4>                    HDRLumaAnalysisParams(TileStatistics): register(u0);
RWBuffer<float4>                    HDRLumaAnalysisParams(OutputStatistics): register(u1);
Buffer<float>                       HDRLumaAnalysisParams(ExposureInfo) : register(t0);
Texture2D<float4>                   HDRLumaAnalysisParams(InputTexture) : register(t1);


#endif
