﻿#pragma checksum "ChatSessionControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "C8E26431957EC0BECD1028E4AE4B15E9BBA6EE8DA82BA5D16BC9B54F4B0550FA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using Microsoft.Expression.Interactivity.Core;
using Microsoft.Expression.Interactivity.Input;
using Microsoft.Expression.Interactivity.Layout;
using Microsoft.Expression.Interactivity.Media;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// ChatSessionControl
    /// </summary>
    public partial class ChatSessionControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 12 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.ChatSessionControl Root;
        
        #line default
        #line hidden
        
        
        #line 62 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ChatContainer;
        
        #line default
        #line hidden
        
        
        #line 72 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton GoBackButton;
        
        #line default
        #line hidden
        
        
        #line 99 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DirectChatHeader;
        
        #line default
        #line hidden
        
        
        #line 130 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl Session;
        
        #line default
        #line hidden
        
        
        #line 497 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid Input_Textbox;
        
        #line default
        #line hidden
        
        
        #line 498 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MessageContainer;
        
        #line default
        #line hidden
        
        
        #line 505 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ToBeSentMessage;
        
        #line default
        #line hidden
        
        
        #line 519 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.EmoticonAutoCompleteBehavior EmoticonAutoComplete;
        
        #line default
        #line hidden
        
        
        #line 561 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox EmoticonSuggestions;
        
        #line default
        #line hidden
        
        
        #line 608 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.EmoticonsControl EmoticonControl;
        
        #line default
        #line hidden
        
        
        #line 613 "ChatSessionControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.ImageToggleButton EmojiBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/social/chatsessioncontrol.xaml", System.UriKind.Relative);
            
            #line 1 "ChatSessionControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Root = ((LindenLab.ChatSessionControl)(target));
            return;
            case 2:
            this.ChatContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.GoBackButton = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            return;
            case 4:
            this.DirectChatHeader = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            this.Session = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 6:
            this.Input_Textbox = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.MessageContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 8:
            this.ToBeSentMessage = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.EmoticonAutoComplete = ((LindenLab.EmoticonAutoCompleteBehavior)(target));
            return;
            case 10:
            this.EmoticonSuggestions = ((System.Windows.Controls.ListBox)(target));
            return;
            case 11:
            this.EmoticonControl = ((LindenLab.EmoticonsControl)(target));
            return;
            case 12:
            this.EmojiBtn = ((LindenLab.ImageToggleButton)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

