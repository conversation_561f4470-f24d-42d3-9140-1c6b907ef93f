#ifndef Common_ColorUtil_hlsli
#define Common_ColorUtil_hlsli

// Gamma reference
// Windows 11 Hdr    - piecewise gamma = 2.2
// Sansar internally - piecewise gamma = 2.4

// Precompute constant reciprocals for better instruction scheduling
static const float kSrgbThresholdRcp = 1.0 / 0.04045;
static const float kSrgbScale = 1.0 / 12.92;
static const float kSrgbDenominator = 1.0 / 1.055;
//static const float kLinearThresholdRcp = 1.0 / 0.0031308;
static const float kGamma2_4Rcp = 1.0 / 2.4;

float3 srgbToLinear(float3 srgb) // Piecewise sRGB to linear RGB conversion
{
    float3 isAboveThreshold = step(0.04045, srgb);
    
    float3 linearLow = srgb * kSrgbScale;
    float3 linearHigh = pow(srgb * kSrgbDenominator + 0.055 * kSrgbDenominator, 2.4);

    return lerp(linearLow, linearHigh, isAboveThreshold);
}

float3 linearToSrgb(float3 c) // Linear RGB to piecewise sRGB conversion
{
    float3 isAboveThreshold = step(0.0031308, c);
    
    float3 srgbLow = c * 12.92;
    float3 srgbHigh = pow(c, kGamma2_4Rcp) * 1.055 - 0.055;
    
    return lerp(srgbLow, srgbHigh, isAboveThreshold);
}

// Optimized reference implementations
float3 srgbToLinearReference(float3 srgb, float gamma) // Piecewise sRGB to linear RGB conversion
{
    float3 isAboveThreshold = step(0.04045, srgb);
    float3 linearLow = srgb * kSrgbScale;
    float3 linearHigh = pow(srgb * kSrgbDenominator + 0.055 * kSrgbDenominator, gamma);
    return lerp(linearLow, linearHigh, isAboveThreshold);
}

float srgbToLinearReference(float c, float gamma) // Scalar version
{
    float isAboveThreshold = step(0.04045, c);
    float linearLow = c * kSrgbScale;
    float linearHigh = pow(c * kSrgbDenominator + 0.055 * kSrgbDenominator, gamma);
    return lerp(linearLow, linearHigh, isAboveThreshold);
}

float3 linearToSrgbReference(float3 c, float gammaRcp) // Linear RGB to piecewise sRGB conversion
{
    float3 isAboveThreshold = step(0.0031308, c);
    float3 srgbLow = c * 12.92;
    float3 srgbHigh = pow(c, gammaRcp) * 1.055 - 0.055;
    return lerp(srgbLow, srgbHigh, isAboveThreshold);
}

float linearToSrgbReference(float i, float gammaRcp) // Scalar version
{
    float isAboveThreshold = step(0.0031308, i);
    float srgbLow = i * 12.92;
    float srgbHigh = pow(i, gammaRcp) * 1.055 - 0.055;
    return lerp(srgbLow, srgbHigh, isAboveThreshold);
}

// https://entropymine.com/imageworsener/srgbformula/
// Constants for enhanced version
static const float kEnhancedSrgbThreshold = 0.0404482362771082;
static const float kEnhancedLinearThreshold = 0.00313066844250063;
static const float kRecip1055 = 1.0 / 1.055;

float3 srgbToLinearEnhanced(float3 srgb, float gamma) // Hybrid piecewise linear/non-linear sRGB to linear RGB conversion
{
    float3 linear_low = srgb * kSrgbScale;
    float3 scaled = (srgb + 0.055) * kRecip1055;
    float3 linear_high = pow(scaled, gamma);
    float3 is_high = step(kEnhancedSrgbThreshold, srgb);
    return lerp(linear_low, linear_high, is_high);
}

float srgbToLinearEnhanced(float i, float gamma) // Scalar version with reduced operations
{
    float linear_low = i * kSrgbScale;
    float scaled = (i + 0.055) * kRecip1055;
    float linear_high = pow(scaled, gamma);
    float is_high = step(kEnhancedSrgbThreshold, i);
    return lerp(linear_low, linear_high, is_high);
}

// https://entropymine.com/imageworsener/srgbformula/
float3 linearToSrgbEnhanced(float3 c, float gammaRcp) // Linear RGB to hybrid piecewise linear/non-linear sRGB conversion
{
    float3 linear_low = c * 12.92;
    float3 scaled = 1.055 * c;
    float3 linear_high = pow(scaled, gammaRcp) - 0.055;
    float3 is_high = step(kEnhancedLinearThreshold, c);
    return lerp(linear_low, linear_high, is_high);
}

float linearToSrgbEnhanced(float c, float gammaRcp) // Scalar version with reduced operations
{
    float linear_low = c * 12.92;
    float scaled = 1.055 * c;
    float linear_high = pow(scaled, gammaRcp) - 0.055;
    float is_high = step(kEnhancedLinearThreshold, c);
    return lerp(linear_low, linear_high, is_high);
}

float3 rgbToYCoCg(float3 rgb)
{
    float rPlusB = rgb.r + rgb.b;
    float rMinusB = rgb.r - rgb.b;
    float halfRPlusB = rPlusB * 0.25;
    
    return float3(
        halfRPlusB + rgb.g * 0.5,  // Y
        rgb.g * 0.5 - halfRPlusB,   // Co
        rMinusB * 0.5               // Cg
    );
}

float3 yCoCgToRgb(float3 ycocg)
{
    // Optimized form to reduce register pressure
    float tmp = ycocg.r - ycocg.g;
    return float3(
        tmp + ycocg.b,       // R
        ycocg.r + ycocg.g,   // G
        tmp - ycocg.b        // B
    );
}

// Precompute constants for HSV conversion
static const float4 kHSV_K1 = float4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
static const float4 kHSV_K2 = float4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
static const float kHSV_Epsilon = 1.0e-10;

float3 rgb2hsv(float3 c)
{
    float cg_step_cb = step(c.b, c.g);
    float4 p = lerp(float4(c.bg, kHSV_K1.wz), float4(c.gb, kHSV_K1.xy), cg_step_cb);
    
    float px_step_cr = step(p.x, c.r);
    float4 q = lerp(float4(p.xyw, c.r), float4(c.r, p.yzx), px_step_cr);
    
    float d = q.x - min(q.w, q.y);
    float h = abs(q.z + (q.w - q.y) / (6.0 * d + kHSV_Epsilon));
    float s = d / (q.x + kHSV_Epsilon);
    
    return float3(h, s, q.x);
}

float3 hsv2rgb(float3 c)
{
    float3 p = abs(frac(c.xxx + kHSV_K2.xyz) * 6.0 - kHSV_K2.www);
    float3 rgb = lerp(kHSV_K2.xxx, saturate(p - kHSV_K2.xxx), c.y);
    return c.z * rgb;
}

// Precompute luminance coefficients vector
static const float3 kLuminanceWeights = float3(0.2126, 0.7152, 0.0722);

float linearRgbToLuminance(float3 rgb)
{
    return dot(rgb, kLuminanceWeights);
}

float3 compressRgbRange(float3 c, float metric)
{
    float rcpMetric = 1.0 / (1.0 + metric);
    return c * rcpMetric;
}

float3 decompressRgbRange(float3 c, float metric)
{
    float rcpMetric = 1.0 / (1.0 - metric);
    return c * rcpMetric;
}

// Precompute constant matrices for ACES
static const float3x3 kACES_M1_inv = float3x3(
    1.76474, -0.147028, -0.0363368,
    -0.675778, 1.16025, -0.162436,
    -0.0889633, -0.0132237, 1.19877
);
static const float3x3 kACES_M2_inv = float3x3(
    0.643038, 0.0592687, 0.0059619,
    0.311187, 0.931436, 0.063929,
    0.0457755, 0.00929492, 0.930118
);
static const float3x3 kACES_M1 = float3x3(
    0.59719, 0.07600, 0.02840,
    0.35458, 0.90834, 0.13383,
    0.04823, 0.01566, 0.83777
);
static const float3x3 kACES_M2 = float3x3(
    1.60475, -0.10208, -0.00327,
    -0.53108, 1.10813, -0.07276,
    -0.07367, -0.00605, 1.07602
);
static const float kACES_NormFactor = 0.00009053/0.238081;

float3 invertAcesFilmicCurve(float3 x, float bias)
{
    float3 tx = mul(x, kACES_M2_inv);
    tx -= kACES_NormFactor;
    
    float3 denom = 1.01654 - tx;
    float3 numer = -0.0124926 + 0.220056 * tx;
    float3 radical = 0.000249621 + (0.240432 - 0.193594 * tx) * tx;
    
    tx = (numer + sqrt(radical)) / denom;
    tx = mul(tx, kACES_M1_inv);
    
    return max(tx/bias, 0.0);
}

// see https://www.shadertoy.com/view/XsGfWV
float3 acesFilmicCurve(float3 x, float bias)
{
    x *= bias;
    
    float3 v = mul(x, kACES_M1);
    
    float3 v2 = v * v;
    float3 a = v2 + v * 0.0245786 - 0.000090537;
    float3 b = v2 * 0.983729 + v * 0.4329510 + 0.238081;
    
    float3 result = mul(kACES_NormFactor + a / b, kACES_M2);
    return saturate(result);
}

#endif
