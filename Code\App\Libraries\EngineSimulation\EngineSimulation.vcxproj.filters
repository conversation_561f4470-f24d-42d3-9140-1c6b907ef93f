﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="HavokAssetUtil.cpp" />
    <ClCompile Include="VisualDebugger.cpp" />
    <ClCompile Include="SimulationWorld.cpp" />
    <ClCompile Include="AnimationComponent.cpp" />
    <ClCompile Include="AnimationComponentManager.cpp" />
    <ClCompile Include="CharacterComponent.cpp" />
    <ClCompile Include="CharacterComponentManager.cpp" />
    <ClCompile Include="KeyframedBoneComponent.cpp" />
    <ClCompile Include="KeyframedBoneComponentManager.cpp" />
    <ClCompile Include="RigidBodyComponent.cpp" />
    <ClCompile Include="RigidBodyComponentManager.cpp" />
    <ClCompile Include="PoseComponent.cpp" />
    <ClCompile Include="PhysicsConstraintComponent.cpp" />
    <ClCompile Include="AnimationComponentMessages.cpp" />
    <ClCompile Include="CameraViewer.cpp" />
    <ClCompile Include="PhysicsCharacterInterface.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="CharacterProxyState.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="PhysicsConstraintComponentManager.cpp" />
    <ClCompile Include="ArrayStreamWriter.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="ServerShapeViewer.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="ProxyAffectManager.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="CharacterProxyController.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="ServerCharacterProxyViewer.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="KeyframedBoneViewer.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="SimulationMessages.cpp" />
    <ClCompile Include="FrameSynchronizer.cpp" />
    <ClCompile Include="SerializeTypeOverload.cpp" />
    <ClCompile Include="BehaviorProjectData.cpp" />
    <ClCompile Include="BehaviorAssetLoader.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="ScriptAssetLoader.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="ProjectAssetManager.cpp">
      <Filter>Internal</Filter>
    </ClCompile>
    <ClCompile Include="CharacterPrediction.cpp" />
    <ClCompile Include="System.cpp" />
    <ClCompile Include="HavokAnimationContext.cpp" />
    <ClCompile Include="HavokPhysicsContext.cpp" />
    <ClCompile Include="HavokDebugDisplay.cpp" />
    <ClCompile Include="Picking.cpp" />
    <ClCompile Include="PickComponent.cpp" />
    <ClCompile Include="SGCharacterBridge.cpp" />
    <ClCompile Include="PoseComponentManager.cpp" />
    <ClCompile Include="SpeechGraphicsResource.cpp" />
    <ClCompile Include="PhysicsEventTranslator.cpp" />
    <ClCompile Include="BehaviorAttachment.cpp" />
    <ClCompile Include="LayerManager.cpp" />
    <ClCompile Include="IKBodyComponent.cpp" />
    <ClCompile Include="IKBodyComponentManager.cpp" />
    <ClCompile Include="IKBodyShapes.cpp" />
    <ClCompile Include="IKBodyShapeViewer.cpp" />
    <ClCompile Include="SourceSpaceCollisionFilter.cpp" />
    <ClCompile Include="RigidBodyAuthorityViewer.cpp" />
    <ClCompile Include="CharacterNodePhysicsInterface.cpp" />
    <ClCompile Include="HavokResource.cpp" />
    <ClCompile Include="SGAnimationControls.cpp" />
    <ClCompile Include="BodyCollisionFilter.cpp" />
    <ClCompile Include="HavokAnimationUtils.cpp" />
    <ClCompile Include="AnimationStateListener.cpp" />
    <ClCompile Include="XSensManager.cpp" />
    <ClCompile Include="XSensDatagram.cpp" />
    <ClCompile Include="AVBDIntegrationBridge.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="System.h" />
    <ClInclude Include="HavokAssetUtil.h" />
    <ClInclude Include="MathUtils.h" />
    <ClInclude Include="VisualDebugger.h" />
    <ClInclude Include="SimulationWorld.h" />
    <ClInclude Include="AnimationComponent.h" />
    <ClInclude Include="CharacterComponent.h" />
    <ClInclude Include="KeyframedBoneComponent.h" />
    <ClInclude Include="RigidBodyComponent.h" />
    <ClInclude Include="PoseComponent.h" />
    <ClInclude Include="PhysicsConstraintComponent.h" />
    <ClInclude Include="SerializeUtils.h" />
    <ClInclude Include="AnimationComponentMessages.h" />
    <ClInclude Include="AnimationComponentMessagesAutoConvert.h" />
    <ClInclude Include="AnimationComponentMessagesConvert.h" />
    <ClInclude Include="CameraViewer.h" />
    <ClInclude Include="AnimationComponentManager.h" />
    <ClInclude Include="PhysicsCharacterInterface.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="CharacterProxyState.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="CharacterComponentManager.h" />
    <ClInclude Include="RigidBodyComponentManager.h" />
    <ClInclude Include="KeyframedBoneComponentManager.h" />
    <ClInclude Include="PhysicsConstraintComponentManager.h" />
    <ClInclude Include="ArrayStreamWriter.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="ServerShapeViewer.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="ProxyAffectManager.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="CharacterProxyController.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="ServerCharacterProxyViewer.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="HavokTypes.h" />
    <ClInclude Include="HavokResource.h" />
    <ClInclude Include="KeyframedBoneViewer.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="AnimationEvents.h" />
    <ClInclude Include="SimulationMessages.h" />
    <ClInclude Include="SimulationMessagesAutoConvert.h" />
    <ClInclude Include="SimulationMessagesConvert.h" />
    <ClInclude Include="FrameSynchronizer.h" />
    <ClInclude Include="SerializeTypeOverload.h" />
    <ClInclude Include="BehaviorProjectData.h" />
    <ClInclude Include="ResourceWriter.h" />
    <ClInclude Include="BehaviorAssetLoader.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="ScriptAssetLoader.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="ProjectAssetManager.h">
      <Filter>Internal</Filter>
    </ClInclude>
    <ClInclude Include="CharacterPrediction.h" />
    <ClInclude Include="HavokAnimationContext.h" />
    <ClInclude Include="HavokPhysicsContext.h" />
    <ClInclude Include="AnimationComponentReflectionDefinitions.h" />
    <ClInclude Include="HavokDebugDisplay.h" />
    <ClInclude Include="PickComponent.h" />
    <ClInclude Include="Picking.h" />
    <ClInclude Include="SGCharacterBridge.h" />
    <ClInclude Include="SGHavokCommon.h" />
    <ClInclude Include="PoseComponentManager.h" />
    <ClInclude Include="SpeechGraphicsResource.h" />
    <ClInclude Include="SpeechGraphicsInterface.h" />
    <ClInclude Include="PhysicsEventTranslator.h" />
    <ClInclude Include="PhysicsEvents.h" />
    <ClInclude Include="BehaviorAttachment.h" />
    <ClInclude Include="LayerManager.h" />
    <ClInclude Include="CharacterNodePhysicsInterface.h" />
    <ClInclude Include="BodyCollisionFilter.h" />
    <ClInclude Include="IKBodyComponent.h" />
    <ClInclude Include="IKBodyComponentManager.h" />
    <ClInclude Include="IKBodyShapes.h" />
    <ClInclude Include="IKBodyShapeViewer.h" />
    <ClInclude Include="SourceSpaceCollisionFilter.h" />
    <ClInclude Include="RigidBodyAuthorityViewer.h" />
    <ClInclude Include="SimulationEvents.h" />
    <ClInclude Include="SGAnimationControls.h" />
    <ClInclude Include="EngineSimulationConstants.h" />
    <ClInclude Include="HavokAnimationUtils.h" />
    <ClInclude Include="AnimationUtils.h" />
    <ClInclude Include="EngineSimulationDeprecatedTypes.h" />
    <ClInclude Include="AnimationStateListener.h" />
    <ClInclude Include="AnimationHavokWrappers.h" />
    <ClInclude Include="XSensManager.h" />
    <ClInclude Include="XSensDatagram.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="SerializeUtils.inl" />
    <None Include="AnimationComponentMessages.inl" />
    <None Include="AnimationComponent.inl" />
    <None Include="SimulationMessages.inl" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Internal">
      <UniqueIdentifier>{12bcc85c-06fd-4259-a2c2-3dbaa4a697ea}</UniqueIdentifier>
    </Filter>
    <Filter Include="Ikinema License">
      <UniqueIdentifier>{e0fd6286-8440-43ef-82f6-38048c7fb1cf}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <MessageSchema Include="AnimationComponentMessages.msg" />
    <MessageSchema Include="SimulationMessages.msg" />
  </ItemGroup>
</Project>