#ifndef Common_HDRUtil_hlsli
#define Common_HDRUtil_hlsli


/*
 * HDR VALUE NORMALIZATION STANDARDS
 * --------------------------------
 * This codebase uses the following conventions:
 *
 * 1. Functions with "Normalized" inputs expect values where:
 *    - 0.0 = Black
 *    - 1.0 = 10,000 nits
 *
 * 2. Functions with "Nits" parameters expect absolute nit values
 *    (not normalized)
 *
 * 3. scRGB values use the convention where:
 *    - 0.0 = Black
 *    - 1.0 = 80 nits (SDR reference white)
 */

static const float3 luminanceRec2020Coeff = float3(0.2627, 0.6780, 0.0593); // Rec2020 luminance coefficients

static const float3x3 scRgbToBt2020Matrix =
{
    0.00501923123f, 0.00263426429f, 0.000346504530f,
    0.000552778306f, 0.00735632330f, 0.0000908985239f,
    0.000131131513f, 0.000704106467f, 0.00716476188f
};

// Precomputed conversion matrix
static const float3x3 kREC709toREC2020_Mat =
{
    0.627402, 0.329292, 0.043306,
    0.069095, 0.919544, 0.011360,
    0.016394, 0.088028, 0.895578
};

float3 REC709toREC2020(float3 RGB709)
{
    return mul(kREC709toREC2020_Mat, RGB709);
}

static const float3x3 kREC2020toREC709_Mat =
{
    1.660496, -0.587656, -0.072840,
        -0.124547, 1.132895, -0.008348,
        -0.018154, -0.100597, 1.118751
};

float3 REC2020toREC709(float3 RGB2020)
{
    return mul(kREC2020toREC709_Mat, RGB2020);
}



// https://64.github.io/tonemapping/
float3 change_luminance(float3 c_in, float l_in, float l_out)
{
    float scale = (l_in > 0.0f) ? l_out / l_in : 0.0f;
    return c_in * scale;
}

float3 reinhard_extended_luminance(float3 v, float l_old, float max_white_l)
{
    float max_white_l_sq = max_white_l * max_white_l;
    
    float l_old_ratio = l_old / max_white_l_sq;
    float numerator = l_old * (1.0f + l_old_ratio);
    float l_new = numerator / (1.0f + l_old);
    
    return change_luminance(v, l_old, l_new);
}

float3 calibrationReinhardTonemapper(
    float outputMax, // Display's maximum luminance in scRGB values, e.g. 1.0 = 80 nits.
    float3 input // scRGB color.
)
{
    float inputScale = 0.08; // 1.0f / 12.5 stop of light;
    float3 normalizedInput = input * inputScale;
    float3 mapped = normalizedInput / (1.0f + normalizedInput);
    return mapped * outputMax;
}


// Precomputed constants for ACES Rec2020
static const float kACES_REC2020_A = 15.8f;
static const float kACES_REC2020_B = 2.12f;
static const float kACES_REC2020_C = 1.2f;
static const float kACES_REC2020_D = 5.92f;
static const float kACES_REC2020_E = 1.9f;

float3 ACESFilmRec2020(float3 x)
{

    float3 num = x * (kACES_REC2020_A * x + kACES_REC2020_B);

    float3 den = x * (kACES_REC2020_C * x + kACES_REC2020_D) + kACES_REC2020_E;
    
    return num / den;
}


// HDRCOLOR
// Precomputed constants for REC2084
static const float kREC2084_M1 = 2610.0 / 4096.0 / 4.0;
static const float kREC2084_M2 = 2523.0 / 4096.0 * 128.0;
static const float kREC2084_C1 = 3424.0 / 4096.0;
static const float kREC2084_C2 = 2413.0 / 4096.0 * 32.0;
static const float kREC2084_C3 = 2392.0 / 4096.0 * 32.0;

// https://github.com/Microsoft/DirectX-Graphics-Samples/blob/master/MiniEngine/Core/Shaders/ColorSpaceUtility.hlsli
float3 ApplyREC2084Curve(float3 L)
{
    float3 Lp = pow(L, kREC2084_M1);

    float3 num = kREC2084_C1 + kREC2084_C2 * Lp;
    float3 den = 1.0 + kREC2084_C3 * Lp;

    return pow(num / den, kREC2084_M2);
}

float3 scRGBtoBt2020(float3 Colour)
{
    return mul(scRgbToBt2020Matrix, Colour);
}


// Calculate determinant of 3x3 matrix
float determinant3x3(float3x3 m)
{
    return m[0][0] * (m[1][1] * m[2][2] - m[1][2] * m[2][1])
         - m[0][1] * (m[1][0] * m[2][2] - m[1][2] * m[2][0])
         + m[0][2] * (m[1][0] * m[2][1] - m[1][1] * m[2][0]);
}

// Calculate inverse of 3x3 matrix
float3x3 inverse3x3(float3x3 m)
{
    float det = determinant3x3(m);
    
    // Guard against division by zero
    if (abs(det) < 1e-6)
    {
        return float3x3(1,0,0, 0,1,0, 0,0,1); // Identity in case of singularity
    }
    
    float invDet = 1.0 / det;
    
    float3x3 result;
    
    // Calculate cofactors divided by determinant
    result[0][0] = invDet * (m[1][1] * m[2][2] - m[1][2] * m[2][1]);
    result[0][1] = invDet * (m[0][2] * m[2][1] - m[0][1] * m[2][2]);
    result[0][2] = invDet * (m[0][1] * m[1][2] - m[0][2] * m[1][1]);
    
    result[1][0] = invDet * (m[1][2] * m[2][0] - m[1][0] * m[2][2]);
    result[1][1] = invDet * (m[0][0] * m[2][2] - m[0][2] * m[2][0]);
    result[1][2] = invDet * (m[0][2] * m[1][0] - m[0][0] * m[1][2]);
    
    result[2][0] = invDet * (m[1][0] * m[2][1] - m[1][1] * m[2][0]);
    result[2][1] = invDet * (m[0][1] * m[2][0] - m[0][0] * m[2][1]);
    result[2][2] = invDet * (m[0][0] * m[1][1] - m[0][1] * m[1][0]);
    
    return result;
}

// More accurate matrix transformation that preserves color integrity
float3x3 TransformMatrixForDisplayPrimaries(float3x3 originalMatrix, float3x3 displayColorMatrix)
{
    // Need to account for the transformation sequence: 
    // 1. Colors are in display-specific space after DisplayColorMatrix
    // 2. Our operation needs to work in this new space
    
    // This creates a properly balanced transform that won't shift green
    return mul(originalMatrix, inverse3x3(displayColorMatrix));
}

// More accurate coefficient transformation
float3 TransformCoefficientsForDisplayPrimaries(float3 coeffs, float3x3 displayColorMatrix)
{
    // Ensure proper functional inverse rather than just mathematical inverse
    float3x3 invDisplayMatrix = inverse3x3(displayColorMatrix);
    
    // For dot products (like luminance calculation), we need this transformation
    return mul(transpose(invDisplayMatrix), coeffs);
}

// LinearRec2020ToACESBt2020

float3 LinearRec2020ToACESBt2020(float3 x)
{
    // Pre-compute squared input once
    float3 x2 = x * x;

    // Constants for ACES tone mapping
    const float a = kACES_REC2020_A;
    const float b = kACES_REC2020_B;
    const float c = kACES_REC2020_C;
    const float d = kACES_REC2020_D;
    const float e = kACES_REC2020_E;

    float3 num = a * x2 + b * x;
    float3 den = c * x2 + d * x + e;

    float3 acesMapped = num / den;

    return mul(scRgbToBt2020Matrix, acesMapped);
}
// LinearRec2020ToACESBt2020


float3 acesFilmicCurveRec2020(float3 x, float bias)
{
    x *= bias;

    // Much more aggressive parameter adjustments for Rec.2020
    const float a = 1.8f;     // More aggressively reduced from 2.51
    const float b = 0.03f;    // Keep toe behavior
    const float c = 2.2f;     // Same as before
    const float d = 0.6f;     // Slightly increased for stronger rolloff
    const float e = 0.2f;     // Increased for darker shadows

    return saturate((x * (a * x + b)) / (x * (c * x + d) + e));
}

float3 adaptiveToneMapperNormalized(float displayMaxNits, float hdrWhitePoint, float3 colorNormalized)
{
    // Constants for HDR range normalization
    const float HDR_MAX_NITS = 10000.0; // 1.0 in normalized scale = 10000 nits

    // Convert displayMaxNits to normalized range
    float displayMax = displayMaxNits / HDR_MAX_NITS;

    // Get current luminance in normalized space
    float luma = dot(colorNormalized, luminanceRec2020Coeff);

    // Get white point in normalized space
    float whitePoint = hdrWhitePoint / HDR_MAX_NITS;

    // Only compress values above white point
    if (luma <= whitePoint)
        return colorNormalized; // No change to pixels below white point

    // How much display headroom we have above white point
    float headroom = displayMax - whitePoint;

    // How much this pixel exceeds the white point
    float excess = luma - whitePoint;

    if (headroom <= 0.0)
    {
        // No headroom - hard clamp to white point
        return colorNormalized * (whitePoint / luma);
    }

    //Modified Reinhard curve with soft transition
    float compressionFactor = 0.5; // Controls how aggressively we compress
    float compressedExcess = headroom * (1.0 - exp(-excess / (headroom * compressionFactor)));

    // Compute target luminance after compression
    float targetLuma = whitePoint + compressedExcess;

    // Scale RGB proportionally to preserve colorNormalized
    return colorNormalized * (targetLuma / luma);
}

float3 EnhancedDisplayDithering(float3 color, float2 uv, float2 screenCoord, Texture2D<float4> blueNoise, int displayType)
{
    float ditherStrength = 1.0;

    // Adjust dithering strength based on display type
    [branch]
    switch (displayType)
    {
        case 0: // OLED
            ditherStrength = 1.5;
            break;
        case 1: // LCD
            ditherStrength = 0.8;
            break;
        default:
            ditherStrength = 1.0;
            break;
    }

    // Generate dither value
    float dither = blueNoise.Sample(PointSampler, frac(screenCoord * CameraInfo(FrameNumber) * CameraInfo(RcpDisplayTargetSize))).x * ditherStrength;

    // HDR-aware dithering approach:

    // 1. Scale color to a range suitable for dithering (treating HDR as exposure)
    const float HDR_EXPOSURE_ADJUST = 0.0001; // 1.0 in normalized = 10,000 nits, 0.0001 = 1 nit
    float3 exposureAdjustedColor = color * HDR_EXPOSURE_ADJUST;

    // 2. Calculate luminance-adaptive dithering amount (stronger in midtones, gentler in shadows/highlights)
    float luminance = dot(exposureAdjustedColor, luminanceRec2020Coeff);
    float ditherAmount = (1.0 / 127.0) * smoothstep(0.0, 0.1, luminance) * (1.0 - smoothstep(0.7, 1.0, luminance));

    // 3. Apply dither in an appropriate space
    // We don't need full sRGB conversion for HDR - just enough perceptual correction
    float3 perceptualColor = pow(exposureAdjustedColor, 0.45); // Approximate perceptual curve
    perceptualColor += dither * ditherAmount;

    // 4. Convert back to linear and restore HDR range
    float3 ditheredColor = pow(max(perceptualColor, 0.0), 1.0 / 0.45) * (1.0 / HDR_EXPOSURE_ADJUST);

    return ditheredColor;
}

// Remaps HDR content to avoid banding based on frame average light level
float3 RemapFrameAverageLightLevelNormalized(float3 hdrColorNormalized, float contentFALLNits, float maxFALLNits)
{
    contentFALLNits = max(contentFALLNits, 1);
    maxFALLNits = max(maxFALLNits, 1);

    if (contentFALLNits <= maxFALLNits)
        return hdrColorNormalized;

    float ratio = float(maxFALLNits) / float(contentFALLNits);

    float3 compressedColor = hdrColorNormalized * ratio;

    float knee = 0.7; // Adjust knee point for compression curve
    float3 rolloff = 1.0 - exp(-hdrColorNormalized / (knee * ratio));

    float3 blendFactor = saturate(hdrColorNormalized * 2.0);
    float3 remappedColor = lerp(compressedColor, rolloff, blendFactor);

    return remappedColor;
}


#endif