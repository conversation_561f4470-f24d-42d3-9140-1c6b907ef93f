<!-- These are default values for strongly typed fields -->
<MaterialFieldTypes>
	<TextureFields>
		<Albedo Type="Albedo" Name="Albedo Map" AltName="" Layer="1" ParameterBindName="AlbedoMap" DefaultFilename="albedo"/>
		<Normal Type="Normal" Name="Normal Map" AltName="" Layer="1" ParameterBindName="NormalMap" DefaultFilename="normal"/>
		<Roughness Type="Roughness" Name="Roughness Map" AltName="" Layer="1" ParameterBindName="RoughnessMap" DefaultFilename="roughness"/>
		<Metalness Type="Metalness" Name="Metalness Map" AltName="" Layer="1" ParameterBindName="MetalnessMap" DefaultFilename="metalness"/>
		<Emissive Type="Emissive" Name="Emissive Map" AltName="" Layer="1" ParameterBindName="EmissiveMap" DefaultFilename="emissive"/>
		<Displacement Type="Displacement" Name="Displacement Map" AltName="" Layer="1" ParameterBindName="DisplacementMap" DefaultFilename="displacement"/>
		<VAT Type="VAT" Name="VAT Map" AltName="" Layer="1" ParameterBindName="VATMap" DefaultFilename="vat"/>
		<DetailNormal Type="DetailNormal" Name="Detail Normal Map" AltName="" Layer="1" ParameterBindName="DetailNormalMap" DefaultFilename="detailNormal"/>
		<DetailRoughness Type="DetailRoughness" Name="Detail Roughness Map" AltName="" Layer="1" ParameterBindName="DetailRoughnessMap" DefaultFilename="detailRoughness"/>
		<AmbientOcclusion Type="AmbientOcclusion" Name="Ambient Occlusion" AltName="" Layer="1" ParameterBindName="AmbientOcclusionMap" DefaultFilename="ambientOcclusion"/>
		<Blend Type="Blend" Name="Blend Map" AltName="" Layer="1" ParameterBindName="BlendMap" DefaultFilename="blend"/>
	</TextureFields>
	<FloatFields>
		<UvScale Type="UvScale" Name="Uv Scale" AltName="" ParameterBindName="UvScale" DefaultValue="1.0" MinValue="0.001" MaxValue="100.0"/>
		<UvRotate Type="UvRotate" Name="Uv Rotate" AltName="" ParameterBindName="UvRotate" DefaultValue="0.0" MinValue="-360.0" MaxValue="360.0"/>
		<UvFluidDistortion Type="UvFluidDistortion" Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
		<EmissiveIntensity Type="EmissiveIntensity" Name="Emissive Intensity" AltName="" ParameterBindName="EmissiveIntensity" DefaultValue="3.0" MinValue="0.0" MaxValue="32.0"/>
		<MaskThreshold Type="MaskThreshold" Name="Mask Threshold" AltName="" ParameterBindName="MaskThreshold" DefaultValue="0.5" MinValue="0.0" MaxValue="1.0"/>
		<MaskSoftness Type="MaskSoftness" Name="Mask Softness" AltName="" ParameterBindName="MaskSoftness" DefaultValue="0.5" MinValue="0.0" MaxValue="1.0"/>
	</FloatFields>
	<VectorFields>
		<UvScales Type="UvScales" Name="Uv Scales" AltName="Uv Scale" ParameterBindName="UvScales" MinValue="0.01" MaxValue="1000.0" Dimensions="4">
			<DefaultValue X="1" Y="1" Z="1" W="1"/>
		</UvScales>
		<UvOffsets Type="UvOffsets" Name="Uv Offsets" AltName="Uv Offset" ParameterBindName="UvOffsets" MinValue="-1.0" MaxValue="1.0" Dimensions="2">
			<DefaultValue X="0" Y="0" Z="0" W="0"/>
		</UvOffsets>
		<DetailScale Type="DetailScale" Name="Detail Scale" AltName="" ParameterBindName="DetailScale" MinValue="0.01" MaxValue="1000.0" Dimensions="3">
			<DefaultValue X="1" Y="1" Z="1" W="1"/>
		</DetailScale>
		<DisplacementScale Type="DisplacementScale" Name="Displacement Scale" AltName="Displacement Scale" ParameterBindName="DisplacementScale" MinValue="-250.0" MaxValue="250.0" Dimensions="3">
			<DefaultValue X="1" Y="1" Z="1"/>
		</DisplacementScale>
		<VATScale Type="VATScale" Name="VAT Scale" AltName="VAT Scale" ParameterBindName="VATScale" DefaultValue="1.0" MinValue="-25.0" MaxValue="25.0" Dimensions="3">
			<DefaultValue X="1" Y="1" Z="1"/>
		</VATScale>
	</VectorFields>
	<ColorFields>
		<Tint Type="Tint" Name="Tint" AltName="" ParameterBindName="Tint" MinValue="0.0" MaxValue="1.0" Dimensions="4">
			<DefaultValue X="1" Y="1" Z="1" W="1"/>
		</Tint>
	</ColorFields>
	<BitFields/>
</MaterialFieldTypes>

<!-- ========================================================= -->
<!-- Specifying a strongly typed field type will auto-populate that field with the defaults -->
<!-- Any attribute provided after specifying a strongly typed field type will override the default value -->
<!-- Example: <Texture Type="Albedo" DefaultFilename="albedo2.dds" -->
<!-- Example: <Color Type="Tint"><DefaultValue X="0" Y="1" Z="0" W="0"/></Tint> -->
<!-- ========================================================= -->
<!-- Custom/Uncommon field types can be additionally provided, do not provide a 'Type' attribute but ensure all other attributes are provided NOTE: Custom Texture2DFields field types currently not supported-->
<!-- Example: <RangedFloat Name="SampleBias" AltName="" ParameterBindName="SampleBias" DefaultValue="1.0" MinValue="0.01" MaxValue="1.0"/> -->
<!-- ========================================================= -->

<MaterialTypes>

	<MaterialType Name="OpaqueSingleLayer" DisplayName="Standard">
		<Properties>
			<Default Value="true"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueEnvironmentSingleLayer" DisplayName="Standard + Environment">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Name="Skybox Intensity" AltName="" ParameterBindName="SkyboxIntensity" DefaultValue="1.0" MinValue="0.0" MaxValue="2.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueEnvironmentSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueEnvironmentSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueSingleLayer" DisplayName="Standard Two Sided">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueMaskSingleLayer" DisplayName="Standard + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueEnvironmentMaskSingleLayer" DisplayName="Standard + Environment + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
			<RangedFloat Name="Skybox Intensity" AltName="" ParameterBindName="SkyboxIntensity" DefaultValue="1.0" MinValue="0.0" MaxValue="2.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueEnvironmentMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueEnvironmentMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueMaskSingleLayer" DisplayName="Standard Two Sided + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueEmissiveSingleLayer" DisplayName="Standard + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Apply UV Offset to Emissive" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueEmissiveSingleLayer" DisplayName="Standard Two Sided + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Apply UV Offset to Emissive" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="Billboard" DisplayName="Billboard">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Emissive" Name="Emissive Map" ParameterBindName="EmissiveMap" Layer="1"/>
			<Texture Type="Emissive" Name="Mask" ParameterBindName="MaskMap" Layer="2"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Brightness" AltName="" ParameterBindName="Brightness" DefaultValue="1.0" MinValue="0.0" MaxValue="10.0"/>
			<RangedFloat Name="Absorption" AltName="" ParameterBindName="Absorption" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Lollipop" AltName="" ParameterBindName="Lollipop" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Inherit Orientation" AltName="" ParameterBindName="InheritOrientation" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="2"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="3"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="8"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="9"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="BillboardStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="BillboardSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueSingleLayerDetail" DisplayName="Standard + Detail">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="DetailNormal"/>
			<Texture Type="DetailRoughness"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
			<RangedVector Type="DetailScale"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueSingleLayerDetailStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueSingleLayerDetailSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueSingleLayerDetail" DisplayName="Standard Two Sided + Detail">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="DetailNormal"/>
			<Texture Type="DetailRoughness"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
			<RangedVector Type="DetailScale"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueSingleLayerDetailStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueSingleLayerDetailSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueMaskSingleLayerDetail" DisplayName="Standard + Detail + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="DetailNormal"/>
			<Texture Type="DetailRoughness"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
			<RangedVector Type="DetailScale"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueMaskSingleLayerDetailStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueMaskSingleLayerDetailSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueMaskSingleLayerDetail" DisplayName="Standard Two Sided + Detail + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="DetailNormal"/>
			<Texture Type="DetailRoughness"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
			<RangedVector Type="DetailScale"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueMaskSingleLayerDetailStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueMaskSingleLayerDetailSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueSubsurfaceSingleLayerDetail" DisplayName="Subsurface + Detail">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="DetailNormal"/>
			<Texture Type="DetailRoughness"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Name="BleedDistance" AltName="" ParameterBindName="BleedDistance" DefaultValue="1.0" MinValue="0.01" MaxValue="5.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DetailScale"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
			<Color Name="BleedTint" AltName="" ParameterBindName="BleedTint" MinValue="0.001" MaxValue="1.0" Dimensions="3">
				<DefaultValue X="1" Y="0.55" Z="0.525" W="1"/>
			</Color>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueSubsurfaceSingleLayerDetailStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueSubsurfaceSingleLayerDetailSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueMaskSubsurfaceSingleLayerDetail" DisplayName="Subsurface + Detail + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="DetailNormal"/>
			<Texture Type="DetailRoughness"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Name="BleedDistance" AltName="" ParameterBindName="BleedDistance" DefaultValue="1.0" MinValue="0.01" MaxValue="5.0"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DetailScale"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
			<Color Name="BleedTint" AltName="" ParameterBindName="BleedTint" MinValue="0.001" MaxValue="1.0" Dimensions="3">
				<DefaultValue X="1" Y="0.55" Z="0.525" W="1"/>
			</Color>
		</ColorFields>
		<BitFields>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueMaskSubsurfaceSingleLayerDetailStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueMaskSubsurfaceSingleLayerDetailSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueEmissiveSingleLayerDetail" DisplayName="Standard + Detail + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Emissive"/>
			<Texture Type="DetailNormal"/>
			<Texture Type="DetailRoughness"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
			<RangedVector Type="DetailScale"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Apply UV Offset to Emissive" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueEmissiveSingleLayerDetailStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueEmissiveSingleLayerDetailSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueEmissiveSingleLayerDetail" DisplayName="Standard Two Sided + Detail + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="DetailNormal"/>
			<Texture Type="DetailRoughness"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
			<RangedVector Type="DetailScale"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Apply UV Offset to Emissive" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueEmissiveSingleLayerDetailStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueEmissiveSingleLayerDetailSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueDiffuseMultiLayer" DisplayName="Four Layer Non-Specular">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="false"/>
			<Layers Value="4"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Blend"/>
			<Texture Type="Albedo" Name="Albedo Map 1" ParameterBindName="AlbedoMap1" Layer="1"/>
			<Texture Type="Albedo" Name="Albedo Map 2" ParameterBindName="AlbedoMap2" Layer="2"/>
			<Texture Type="Albedo" Name="Albedo Map 3" ParameterBindName="AlbedoMap3" Layer="3"/>
			<Texture Type="Albedo" Name="Albedo Map 4" ParameterBindName="AlbedoMap4" Layer="4"/>
			<Texture Type="Normal" Name="Normal Map 1" ParameterBindName="NormalMap1" Layer="1"/>
			<Texture Type="Normal" Name="Normal Map 2" ParameterBindName="NormalMap2" Layer="2"/>
			<Texture Type="Normal" Name="Normal Map 3" ParameterBindName="NormalMap3" Layer="3"/>
			<Texture Type="Normal" Name="Normal Map 4" ParameterBindName="NormalMap4" Layer="4"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvScales" Dimensions="4"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueDiffuseMultiLayerStatic" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueTripleLayerDetail" DisplayName="Standard Three Layer + Detail">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="false"/>
			<Layers Value="3"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Blend"/>
			<Texture Type="Albedo" Name="Albedo Map 1" ParameterBindName="AlbedoMap1" Layer="1"/>
			<Texture Type="Albedo" Name="Albedo Map 2" ParameterBindName="AlbedoMap2" Layer="2"/>
			<Texture Type="Albedo" Name="Albedo Map 3" ParameterBindName="AlbedoMap3" Layer="3"/>
			<Texture Type="Normal" Name="Normal Map 1" ParameterBindName="NormalMap1" Layer="1"/>
			<Texture Type="Normal" Name="Normal Map 2" ParameterBindName="NormalMap2" Layer="2"/>
			<Texture Type="Normal" Name="Normal Map 3" ParameterBindName="NormalMap3" Layer="3"/>
			<Texture Type="Roughness" Name="Roughness Map 1" ParameterBindName="RoughnessMap1" Layer="1"/>
			<Texture Type="Roughness" Name="Roughness Map 2" ParameterBindName="RoughnessMap2" Layer="2"/>
			<Texture Type="Roughness" Name="Roughness Map 3" ParameterBindName="RoughnessMap3" Layer="3"/>
			<Texture Type="Metalness" Name="Metalness Map 1" ParameterBindName="MetalnessMap1" Layer="1"/>
			<Texture Type="Metalness" Name="Metalness Map 2" ParameterBindName="MetalnessMap2" Layer="2"/>
			<Texture Type="Metalness" Name="Metalness Map 3" ParameterBindName="MetalnessMap3" Layer="3"/>
			<Texture Type="DetailNormal" Name="Detail Normal Map 1" ParameterBindName="DetailNormalMap1" Layer="1"/>
			<Texture Type="DetailNormal" Name="Detail Normal Map 2" ParameterBindName="DetailNormalMap2" Layer="2"/>
			<Texture Type="DetailNormal" Name="Detail Normal Map 3" ParameterBindName="DetailNormalMap3" Layer="3"/>
			<Texture Type="DetailRoughness" Name="Detail Roughness Map 1" ParameterBindName="DetailRoughnessMap1" Layer="1"/>
			<Texture Type="DetailRoughness" Name="Detail Roughness Map 2" ParameterBindName="DetailRoughnessMap2" Layer="2"/>
			<Texture Type="DetailRoughness" Name="Detail Roughness Map 3" ParameterBindName="DetailRoughnessMap3" Layer="3"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvScales" Dimensions="3"/>
			<RangedVector Type="DetailScale" Dimensions="3"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueTripleLayerDetailStatic" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="Emissive" DisplayName="Emissive + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Emissive" Name="Emissive Map" ParameterBindName="EmissiveMap" Layer="1"/>
			<Texture Type="Emissive" Name="Mask" ParameterBindName="MaskMap" Layer="2"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Afterimage Decay Rate" AltName="" ParameterBindName="TrailsDecay" DefaultValue="0.5" MinValue="0.1" MaxValue="0.9"/>
			<RangedFloat Name="Absorption" AltName="" ParameterBindName="Absorption" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="2"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="3"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="10"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="8"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="9"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
			<BitField Name="Draw Afterimages" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="EmissiveStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="EmissiveSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedEmissive" DisplayName="Emissive Two Sided + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Emissive" Name="Emissive Map" ParameterBindName="EmissiveMap" Layer="1"/>
			<Texture Type="Emissive" Name="Mask" ParameterBindName="MaskMap" Layer="2"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Afterimage Decay Rate" AltName="" ParameterBindName="TrailsDecay" DefaultValue="0.5" MinValue="0.1" MaxValue="0.9"/>
			<RangedFloat Name="Absorption" AltName="" ParameterBindName="Absorption" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="2"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="3"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="10"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="8"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="9"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
			<BitField Name="Draw Afterimages" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedEmissiveStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedEmissiveSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TransmissiveEmissive" DisplayName="Transmissive + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Emissive"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Type="UvScale"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TransmissiveEmissiveStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TransmissiveEmissiveSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="StereographicOpaqueEmissiveSingleLayer" DisplayName="Standard + Emissive + Stereographic">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Rotation Factor" AltName="" ParameterBindName="RotationFactor" DefaultValue="0.0" MinValue="0.0" MaxValue="4.0"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="StereographicOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="StereographicOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="ScrollingOpaqueEmissiveSingleLayer" DisplayName="Standard + Emissive + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Emissive Mask" ParameterBindName="EmissiveMask" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="3"/>
			<BitField Name="Scroll Emissive Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="ScrollingOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="ScrollingOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedScrollingOpaqueEmissiveSingleLayer" DisplayName="Standard Two Sided + Emissive + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Emissive Mask" ParameterBindName="EmissiveMask" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="3"/>
			<BitField Name="Scroll Emissive Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedScrollingOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedScrollingOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<!-- Smearing weapon of mass destruction, TODO
    <MaterialType Name="ScrollingOpaqueMaskEmissiveSingleLayer" DisplayName="Standard + AlphaMask + Emissive + UV Animation">
        <Properties>
            <Default Value="false"/>
            <Occluder Value="false"/>
            <OpacityMasked Value="false"/>
            <TwoSided Value="false"/>
            <Video Value="false"/>
            <GiBaked Value="false"/>
            <SupportsStaticMesh Value="true"/>
            <SupportsRiggedMesh Value="true"/>
            <Layers Value="1"/>
        </Properties>
        <Texture2DFields>
            <Texture Type="Albedo"/>
            <Texture Type="Normal"/>
            <Texture Type="Roughness"/>
            <Texture Type="Metalness"/>
            <Texture Type="Emissive"/>
        </Texture2DFields>
        <RangedFloatFields>
            <RangedFloat Type="MaskThreshold"/>
            <RangedFloat Type="MaskSoftness"/>
            <RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
            <RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
            <RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
            <RangedFloat Type="EmissiveIntensity"/>
        </RangedFloatFields>
        <RangedVectorFields>
            <RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
                <DefaultValue X="1" Y="1" Z="0" W="0"/>
            </RangedVector>
            <RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
                <DefaultValue X="0" Y="0" Z="0" W="0"/>
            </RangedVector>
        </RangedVectorFields>
        <ColorFields>
            <Color Type="Tint"/>
        </ColorFields>
        <BitFields>
            <BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
            <BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
            <BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
            <BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="3"/>
            <BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
        </BitFields>
        <Bindings>
            <Binding Tag="StaticMesh" EffectTableName="ScrollingOpaqueMaskEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
            <Binding Tag="RiggedMesh" EffectTableName="ScrollingOpaqueMaskEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
        </Bindings>
    </MaterialType>
    -->

	<MaterialType Name="ScrollingOpaqueMaskSingleLayer" DisplayName="Standard + Alpha Mask + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="ScrollingOpaqueMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="ScrollingOpaqueMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedScrollingOpaqueMaskSingleLayer" DisplayName="Standard Two Sided + Alpha Mask + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedScrollingOpaqueMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedScrollingOpaqueMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="VideoScreen" DisplayName="Media: Media Surface">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="true"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo" DefaultFilename="video"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness" DefaultFilename="video"/>
			<Texture Type="Emissive" DefaultFilename="video"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity" DefaultValue="0.3" MinValue="0.0" MaxValue="32.0"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="VideoScreenStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="VideoScreenSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="StereographicVideoScreen" DisplayName="Media: Media Surface + Stereographic">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="true"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo" DefaultFilename="video"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness" DefaultFilename="video"/>
			<Texture Type="Emissive" DefaultFilename="video"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity" DefaultValue="0.3" MinValue="0.0" MaxValue="32.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Rotation Factor" AltName="" ParameterBindName="RotationFactor" DefaultValue="0.0" MinValue="0.0" MaxValue="4.0"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="StereographicVideoScreenStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="StereographicVideoScreenSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="ChromaKeyVideoScreen" DisplayName="Media: Media Surface + ChromaKey">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="true"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo" DefaultFilename="video"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness" DefaultFilename="video"/>
			<Texture Type="Emissive" DefaultFilename="video"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity" DefaultValue="0.3" MinValue="0.0" MaxValue="32.0"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Hue Threshold (Transparency)" AltName="" ParameterBindName="HueThreshold" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Saturation Threshold (Transparency)" AltName="" ParameterBindName="SaturationThreshold" DefaultValue="0.4" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Value Threshold (Transparency)" AltName="" ParameterBindName="ValueThreshold" DefaultValue="0.4" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Hue Threshold (Glare)" AltName="" ParameterBindName="HueThresholdGlare" DefaultValue="0.15" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Saturation Threshold (Glare)" AltName="" ParameterBindName="SaturationThresholdGlare" DefaultValue="0.45" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Value Threshold (Glare)" AltName="" ParameterBindName="ValueThresholdGlare" DefaultValue="0.45" MinValue="0.0" MaxValue="1.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
			<Color Name="Chroma Key Color" AltName="" ParameterBindName="ChromaKeyColor" Conversion="RGBtoHSV">
				<DefaultValue X="0.0" Y="1.0" Z="0.0" W="0.0"/>
			</Color>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="ChromaKeyVideoScreenStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="ChromaKeyVideoScreenSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="MediaScrollingOpaqueEmissiveSingleLayer" DisplayName="Media: Displacement Media + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Emissive Mask" ParameterBindName="EmissiveMask" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Name="Max Displacement Clamp" AltName="" ParameterBindName="MaxDisplacementClamp" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="3"/>
			<BitField Name="Scroll Emissive Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Use Media Stream for Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="12"/>
			<BitField Name="Use Media Stream for Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="13"/>
			<BitField Name="Split Color And Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="14"/>
			<BitField Name="Use Gamma Curve for Media Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="15"/>
			<BitField Name="Use Luma for Media Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="16"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="MediaScrollingOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="MediaScrollingOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedMediaScrollingOpaqueEmissiveSingleLayer" DisplayName="Media: Two Sided Displacement Media + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Emissive Mask" ParameterBindName="EmissiveMask" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Name="Max Displacement Clamp" AltName="" ParameterBindName="MaxDisplacementClamp" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="3"/>
			<BitField Name="Scroll Emissive Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Use Media Stream for Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="12"/>
			<BitField Name="Use Media Stream for Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="13"/>
			<BitField Name="Split Color And Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="14"/>
			<BitField Name="Use Gamma Curve for Media Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="15"/>
			<BitField Name="Use Luma for Media Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="16"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedMediaScrollingOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedMediaScrollingOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>


	<MaterialType Name="Transparent" DisplayName="Transparent">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="UvScale"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Adv. Albedo Blending" AltName="" ParameterBindName="BlendFlags" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Skip motion vectors for reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Skip accumulation for reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="false" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TransparentStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TransparentSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TransparentMultibump" DisplayName="Transparent + Multibump">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Bump Scale" AltName="" ParameterBindName="BumpScale" DefaultValue="1.0" MinValue="0.01" MaxValue="2.0"/>
			<RangedFloat Name="Octave Wavelength Decay" AltName="" ParameterBindName="WavelengthDecay" DefaultValue="0.618033989" MinValue="0.3" MaxValue="0.9"/>
			<RangedFloat Name="Octave Bump Decay" AltName="" ParameterBindName="BumpScaleDecay" DefaultValue="0.5" MinValue="0.3" MaxValue="0.9"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-10.0" MaxValue="10.0" Dimensions="2">
				<DefaultValue X="0.3" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Adv. Albedo Blending" AltName="" ParameterBindName="BlendFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Skip motion vectors for reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Skip accumulation for reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="false" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TransparentMultibumpStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TransparentMultibumpSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TransparentMultibumpDiffract" DisplayName="Diffractive: Transparent + Multibump">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Diffractive Index" AltName="" ParameterBindName="DiffractiveIndex" DefaultValue="-1.3" MinValue="-2.5" MaxValue="2.5"/>
			<RangedFloat Name="Bump Scale" AltName="" ParameterBindName="BumpScale" DefaultValue="1.0" MinValue="0.01" MaxValue="2.0"/>
			<RangedFloat Name="Octave Wavelength Decay" AltName="" ParameterBindName="WavelengthDecay" DefaultValue="0.618033989" MinValue="0.3" MaxValue="0.9"/>
			<RangedFloat Name="Octave Bump Decay" AltName="" ParameterBindName="BumpScaleDecay" DefaultValue="0.5" MinValue="0.3" MaxValue="0.9"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-10.0" MaxValue="10.0" Dimensions="2">
				<DefaultValue X="0.3" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Skip motion vectors for reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Skip accumulation for reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="false" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TransparentMultibumpDiffractStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TransparentMultibumpDiffractSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TransparentMultibumpEnvironmentDiffract" DisplayName="Diffractive: Transparent + Multibump + Environment">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Diffractive Index" AltName="" ParameterBindName="DiffractiveIndex" DefaultValue="-1.3" MinValue="-2.5" MaxValue="2.5"/>
			<RangedFloat Name="Bump Scale" AltName="" ParameterBindName="BumpScale" DefaultValue="1.0" MinValue="0.01" MaxValue="2.0"/>
			<RangedFloat Name="Octave Wavelength Decay" AltName="" ParameterBindName="WavelengthDecay" DefaultValue="0.618033989" MinValue="0.3" MaxValue="0.9"/>
			<RangedFloat Name="Octave Bump Decay" AltName="" ParameterBindName="BumpScaleDecay" DefaultValue="0.5" MinValue="0.3" MaxValue="0.9"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Skybox Intensity" AltName="" ParameterBindName="SkyboxIntensity" DefaultValue="1.0" MinValue="0.0" MaxValue="2.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-10.0" MaxValue="10.0" Dimensions="2">
				<DefaultValue X="0.3" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Skip motion vectors for reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Skip accumulation for reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="false" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TransparentMultibumpEnvironmentDiffractStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TransparentMultibumpEnvironmentDiffractSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueDiffractionMaskSingleLayer" DisplayName="Diffractive: Standard + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Diffractive Index" AltName="" ParameterBindName="DiffractiveIndex" DefaultValue="-1.3" MinValue="-2.5" MaxValue="2.5"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueDiffractMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueDiffractMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueDiffractionMaskSingleLayer" DisplayName="Diffractive: Standard Two Sided + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Diffractive Index" AltName="" ParameterBindName="DiffractiveIndex" DefaultValue="-1.3" MinValue="-2.5" MaxValue="2.5"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueDiffractMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueDiffractMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="ScrollingOpaqueDiffractionMaskSingleLayer" DisplayName="Diffractive: Standard + Alpha Mask + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Diffractive Index" AltName="" ParameterBindName="DiffractiveIndex" DefaultValue="-1.3" MinValue="-2.5" MaxValue="2.5"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="ScrollingOpaqueDiffractMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="ScrollingOpaqueDiffractMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedScrollingOpaqueDiffractionMaskSingleLayer" DisplayName="Diffractive: Standard Two Sided + Alpha Mask + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Diffractive Index" AltName="" ParameterBindName="DiffractiveIndex" DefaultValue="-1.3" MinValue="-2.5" MaxValue="2.5"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedScrollingOpaqueDiffractMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedScrollingOpaqueDiffractMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueEmissiveFresnelSingleLayer" DisplayName="Fresnel: Standard + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Albedo" Name="Outside Albedo" ParameterBindName="AlbedoMap2" Layer="2"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Outside Emissive" ParameterBindName="EmissiveMap2" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Fresnel Intensity" AltName="" ParameterBindName="FresnelIntensity" DefaultValue="1.0" MinValue="0.0" MaxValue="32.0"/>
			<RangedFloat Name="Fresnel Exponent" AltName="" ParameterBindName="FresnelExponent" DefaultValue="1.0" MinValue="0.1" MaxValue="2.0"/>
			<RangedFloat Name="Fresnel Bias" AltName="" ParameterBindName="FresnelBias" DefaultValue="0.0" MinValue="-1.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
			<Color Name="Fresnel Tint" AltName="" ParameterBindName="FresnelTint" MinValue="0.0" MaxValue="1.0" Dimensions="4"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Invert fresnel" AltName="" ParameterBindName="FresnelFlags" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Blend Outside Albedo Texture Into Fresnel" AltName="" ParameterBindName="FresnelFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Blend Outside Emissive Texture Into Fresnel" AltName="" ParameterBindName="FresnelFlags" DefaultValue="false" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueEmissiveFresnelSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueEmissiveFresnelSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueEmissiveFresnelSingleLayer" DisplayName="Fresnel: Standard Two Sided + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Albedo" Name="Outside Albedo" ParameterBindName="AlbedoMap2" Layer="2"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Outside Emissive" ParameterBindName="EmissiveMap2" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Fresnel Intensity" AltName="" ParameterBindName="FresnelIntensity" DefaultValue="1.0" MinValue="0.0" MaxValue="32.0"/>
			<RangedFloat Name="Fresnel Exponent" AltName="" ParameterBindName="FresnelExponent" DefaultValue="1.0" MinValue="0.1" MaxValue="2.0"/>
			<RangedFloat Name="Fresnel Bias" AltName="" ParameterBindName="FresnelBias" DefaultValue="0.0" MinValue="-1.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
			<Color Name="Fresnel Tint" AltName="" ParameterBindName="FresnelTint" MinValue="0.0" MaxValue="1.0" Dimensions="4"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Invert fresnel" AltName="" ParameterBindName="FresnelFlags" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Blend Outside Albedo Texture Into Fresnel" AltName="" ParameterBindName="FresnelFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Blend Outside Emissive Texture Into Fresnel" AltName="" ParameterBindName="FresnelFlags" DefaultValue="false" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueEmissiveFresnelSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueEmissiveFresnelSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="BillboardPixelize" DisplayName="Pixelize: Billboard">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Emissive" Name="Emissive Map" ParameterBindName="EmissiveMap" Layer="1"/>
			<Texture Type="Emissive" Name="Mask" ParameterBindName="MaskMap" Layer="2"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Name="Pixel Size" AltName="" ParameterBindName="PixelSize" DefaultValue="50.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Name="Brightness" AltName="" ParameterBindName="Brightness" DefaultValue="1.0" MinValue="0.0" MaxValue="10.0"/>
			<RangedFloat Name="Absorption" AltName="" ParameterBindName="Absorption" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Lollipop" AltName="" ParameterBindName="Lollipop" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Inherit Orientation" AltName="" ParameterBindName="InheritOrientation" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="2"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="3"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="BillboardPixelizeStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="BillboardPixelizeSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

		<MaterialType Name="ScrollingOpaqueMaskPixelSingleLayer" DisplayName="Pixelize: Billboard + Alpha Mask + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Lollipop" AltName="" ParameterBindName="Lollipop" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Inherit Orientation" AltName="" ParameterBindName="InheritOrientation" DefaultValue="false" BitIndex="0"/>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="ScrollingOpaqueMaskPixelSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="ScrollingOpaqueMaskPixelSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueEmissivePixelizeSingleLayer" DisplayName="Pixelize: Standard + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Pixel Size" AltName="" ParameterBindName="PixelSize" DefaultValue="50.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueEmissivePixelizeSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueEmissivePixelizeSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueEmissivePixelizeSingleLayer" DisplayName="Pixelize: Standard Two Sided + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Pixel Size" AltName="" ParameterBindName="PixelSize" DefaultValue="50.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueEmissivePixelizeSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueEmissivePixelizeSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaquePixelizeMaskSingleLayer" DisplayName="Pixelize: Standard + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Pixel Size" AltName="" ParameterBindName="PixelSize" DefaultValue="50.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaquePixelizeMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaquePixelizeMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaquePixelizeMaskSingleLayer" DisplayName="Pixelize: Standard Two Sided + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Pixel Size" AltName="" ParameterBindName="PixelSize" DefaultValue="50.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaquePixelizeMaskSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaquePixelizeMaskSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="ScrollingOpaqueEmissivePixelizeSingleLayer" DisplayName="Pixelize: Standard + Emissive + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Emissive Mask" ParameterBindName="EmissiveMask" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Pixel Size" AltName="" ParameterBindName="PixelSize" DefaultValue="50.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="3"/>
			<BitField Name="Scroll Emissive Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="ScrollingOpaqueEmissivePixelizeSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="ScrollingOpaqueEmissivePixelizeSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedScrollingOpaqueEmissivePixelizeSingleLayer" DisplayName="Pixelize: Standard Two Sided + Emissive + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Emissive Mask" ParameterBindName="EmissiveMask" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Pixel Size" AltName="" ParameterBindName="PixelSize" DefaultValue="50.0" MinValue="1.0" MaxValue="256.0"/>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="3"/>
			<BitField Name="Scroll Emissive Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedScrollingOpaqueEmissivePixelizeSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="ScreenspaceOpaqueEmissiveSingleLayer" DisplayName="Screenspace: Standard + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="ScreenspaceOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="ScreenspaceOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedScreenspaceOpaqueEmissiveSingleLayer" DisplayName="Screenspace: Two Sided Standard + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
		</RangedFloatFields>
		<RangedVectorFields/>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields/>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedScreenspaceOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedScreenspaceOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="ScrollingScreenspaceOpaqueEmissiveSingleLayer" DisplayName="Screenspace: Standard + Emissive + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Emissive Mask" ParameterBindName="EmissiveMask" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Screenspace UV Scale" AltName="" ParameterBindName="ScreenSpaceUVScale" DefaultValue="1.0" MinValue="0.0" MaxValue="10.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="3"/>
			<BitField Name="Scroll Emissive Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Ping-Pong Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="20"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="ScrollingScreenspaceOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="ScrollingScreenspaceOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayer" DisplayName="Screenspace: Standard Two Sided + Emissive + UV Animation">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="Emissive" Name="Emissive Mask" ParameterBindName="EmissiveMask" Layer="2"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="Displacement"/>
			<Texture Type="Displacement" Name="Displacement Factor" ParameterBindName="DisplacementFactor" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Screenspace UV Scale" AltName="" ParameterBindName="ScreenSpaceUVScale" DefaultValue="1.0" MinValue="0.0" MaxValue="10.0"/>
			<RangedFloat Name="Fluid Distortion Speed" AltName="" ParameterBindName="UvFluidDistortion" DefaultValue="0.0" MinValue="0.0" MaxValue="8.0"/>
			<RangedFloat Name="Fluid Distortion Scale" AltName="" ParameterBindName="UvFluidDistortionScale" DefaultValue="25.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Fluid Distortion Amplitude" AltName="" ParameterBindName="UvFluidDistortionAmp" DefaultValue="0.1" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Name="Flipbook - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="255.0"/>
			<RangedFloat Name="Flipbook - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
			<RangedFloat Name="Flipbook - Loop" AltName="" ParameterBindName="MaxFrames" DefaultValue="256.0" MinValue="1.0" MaxValue="256.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="DisplacementScale" Dimensions="3"/>
			<RangedVector Name="Flipbook - Uv Frames" AltName="" ParameterBindName="UvFrames" MinValue="1.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="1" Y="1" Z="0" W="0"/>
			</RangedVector>
			<RangedVector Name="Uv Scroll - Rate" AltName="" ParameterBindName="ScrollRate" MinValue="-100.0" MaxValue="100.0" Dimensions="2">
				<DefaultValue X="0" Y="0" Z="0" W="0"/>
			</RangedVector>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Scroll Albedo" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Scroll Normal/Roughness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Scroll Metalness" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="2"/>
			<BitField Name="Scroll Emissive" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="3"/>
			<BitField Name="Scroll Emissive Mask" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="4"/>
			<BitField Name="Scroll Ambient Occlusion" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="11"/>
			<BitField Name="Scroll Displacement" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="7"/>
			<BitField Name="Scroll Displacement Factor" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="8"/>
			<BitField Name="Blend Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="9"/>
			<BitField Name="Blend On Displacement Loop" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="19"/>
			<BitField Name="Loop Displacement Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="17"/>
			<BitField Name="Loop Flipbook" AltName="" ParameterBindName="ScrollFlags" DefaultValue="true" BitIndex="18"/>
			<BitField Name="Displacement in LocalSpace" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="10"/>
			<BitField Name="Fix Shimmering" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="5"/>
			<BitField Name="Clamp Flipbook UVs" AltName="" ParameterBindName="ScrollFlags" DefaultValue="false" BitIndex="6"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedScrollingScreenspaceOpaqueEmissiveSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueVATSingleLayer" DisplayName="VAT: Standard">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="VAT"/>
			<Texture Type="VAT" Name="VAT Normals" ParameterBindName="VATNormalsMap" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Name="VAT - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="4096.0"/>
			<RangedFloat Name="VAT - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="VATScale" Dimensions="3"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Loop Animation" AltName="" ParameterBindName="VatFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Ping-Pong Animation" AltName="" ParameterBindName="VatFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Blend Animation Loop" AltName="" ParameterBindName="VatFlags" DefaultValue="false" BitIndex="2"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Shadow casting" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="3"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueVATSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueVATSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueEmissiveVATSingleLayer" DisplayName="VAT: Standard + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="false"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="2"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="AmbientOcclusion"/>
			<Texture Type="VAT"/>
			<Texture Type="VAT" Name="VAT Normals" ParameterBindName="VATNormalsMap" Layer="2"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Name="VAT - Frame" AltName="" ParameterBindName="Frame" DefaultValue="0.0" MinValue="0.0" MaxValue="4096.0"/>
			<RangedFloat Name="VAT - Step Rate" AltName="" ParameterBindName="StepRate" DefaultValue="0.0" MinValue="0.0" MaxValue="100.0"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="VATScale" Dimensions="3"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Loop Animation" AltName="" ParameterBindName="VatFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Ping-Pong Animation" AltName="" ParameterBindName="VatFlags" DefaultValue="false" BitIndex="1"/>
			<BitField Name="Blend Animation Loop" AltName="" ParameterBindName="VatFlags" DefaultValue="false" BitIndex="2"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Shadow casting" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="3"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueEmissiveVATSingleLayerStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueEmissiveVATSingleLayerSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueSingleLayerNoShadow" DisplayName="No Shadow: Standard">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueSingleLayerNoShadowStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueSingleLayerNoShadowSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueEmissiveSingleLayerNoShadow" DisplayName="No Shadow: Standard + Emissive">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="true"/>
			<OpacityMasked Value="false"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="Emissive"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Type="EmissiveIntensity"/>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Apply UV Offset to Emissive" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="2"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueEmissiveSingleLayerNoShadowStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueEmissiveSingleLayerNoShadowSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="OpaqueMaskSingleLayerNoShadow" DisplayName="No Shadow: Standard + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="false"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="OpaqueMaskSingleLayerNoShadowStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="OpaqueMaskSingleLayerNoShadowSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

	<MaterialType Name="TwoSidedOpaqueMaskSingleLayerNoShadow" DisplayName="No Shadow: Standard Two Sided + Alpha Mask">
		<Properties>
			<Default Value="false"/>
			<Occluder Value="false"/>
			<OpacityMasked Value="true"/>
			<TwoSided Value="true"/>
			<Video Value="false"/>
			<GiBaked Value="true"/>
			<SupportsStaticMesh Value="true"/>
			<SupportsRiggedMesh Value="true"/>
			<Layers Value="1"/>
		</Properties>
		<Texture2DFields>
			<Texture Type="Albedo"/>
			<Texture Type="Normal"/>
			<Texture Type="Roughness"/>
			<Texture Type="Metalness"/>
			<Texture Type="AmbientOcclusion"/>
		</Texture2DFields>
		<RangedFloatFields>
			<RangedFloat Name="Ambient Occlusion Strength" AltName="" ParameterBindName="AmbientOcclusionStrength" DefaultValue="1.0" MinValue="0.0" MaxValue="1.0"/>
			<RangedFloat Type="UvScale"/>
			<RangedFloat Type="UvRotate"/>
			<RangedFloat Type="MaskThreshold"/>
			<RangedFloat Type="MaskSoftness"/>
		</RangedFloatFields>
		<RangedVectorFields>
			<RangedVector Type="UvOffsets" Dimensions="2"/>
		</RangedVectorFields>
		<ColorFields>
			<Color Type="Tint"/>
		</ColorFields>
		<BitFields>
			<BitField Name="Apply UV Offset to Diffuse layers" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="0"/>
			<BitField Name="Apply UV Offset to Ambient Occlusion" AltName="" ParameterBindName="OffsetFlags" DefaultValue="true" BitIndex="1"/>
			<BitField Name="Receive reflections" AltName="" ParameterBindName="RenderFlags" DefaultValue="true" BitIndex="0"/>
		</BitFields>
		<Bindings>
			<Binding Tag="StaticMesh" EffectTableName="TwoSidedOpaqueMaskSingleLayerNoShadowStatic" PackageName="MaterialDefinitions"/>
			<Binding Tag="RiggedMesh" EffectTableName="TwoSidedOpaqueMaskSingleLayerNoShadowSkinned" PackageName="MaterialDefinitions"/>
		</Bindings>
	</MaterialType>

</MaterialTypes>

