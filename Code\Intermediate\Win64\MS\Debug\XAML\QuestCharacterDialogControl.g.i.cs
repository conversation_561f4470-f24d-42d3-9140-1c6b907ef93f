﻿#pragma checksum "QuestCharacterDialogControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "F1F97F4E41A943E31E734034056F0DA19FF2685B898624CFC510DF273DC5BA3A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using Microsoft.Expression.Interactivity.Core;
using Microsoft.Expression.Interactivity.Input;
using Microsoft.Expression.Interactivity.Layout;
using Microsoft.Expression.Interactivity.Media;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// QuestCharacterDialogControl
    /// </summary>
    public partial class QuestCharacterDialogControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 10 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.QuestCharacterDialogControl QuestCharacterDialogRoot;
        
        #line default
        #line hidden
        
        
        #line 33 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid QuestCharacterDialogViewport;
        
        #line default
        #line hidden
        
        
        #line 135 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid QuestCharacterDialogContent;
        
        #line default
        #line hidden
        
        
        #line 150 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid QuestSplash;
        
        #line default
        #line hidden
        
        
        #line 170 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid QuestProgress;
        
        #line default
        #line hidden
        
        
        #line 187 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid QuestComplete;
        
        #line default
        #line hidden
        
        
        #line 207 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualStateGroup VisualStateGroup;
        
        #line default
        #line hidden
        
        
        #line 212 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState ShowOfferQuest;
        
        #line default
        #line hidden
        
        
        #line 234 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState ShowQuestProgress;
        
        #line default
        #line hidden
        
        
        #line 256 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState ShowCompleteQuest;
        
        #line default
        #line hidden
        
        
        #line 278 "QuestCharacterDialogControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.VisualState ShowCharacterDialog;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/quests/character/questcharacterdialogcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "QuestCharacterDialogControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.QuestCharacterDialogRoot = ((LindenLab.QuestCharacterDialogControl)(target));
            return;
            case 2:
            this.QuestCharacterDialogViewport = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.QuestCharacterDialogContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 4:
            this.QuestSplash = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.QuestProgress = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.QuestComplete = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.VisualStateGroup = ((System.Windows.VisualStateGroup)(target));
            return;
            case 8:
            this.ShowOfferQuest = ((System.Windows.VisualState)(target));
            return;
            case 9:
            this.ShowQuestProgress = ((System.Windows.VisualState)(target));
            return;
            case 10:
            this.ShowCompleteQuest = ((System.Windows.VisualState)(target));
            return;
            case 11:
            this.ShowCharacterDialog = ((System.Windows.VisualState)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

