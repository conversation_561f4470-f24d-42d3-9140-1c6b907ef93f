﻿#pragma checksum "ExperienceScrollingListControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "4F964944E3A4FDB93D41BC4979B9372EAFD514ACA0907DDBD74132CE70B91E82"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using Microsoft.Expression.Interactivity.Core;
using Microsoft.Expression.Interactivity.Input;
using Microsoft.Expression.Interactivity.Layout;
using Microsoft.Expression.Interactivity.Media;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// ExperienceScrollingListControl
    /// </summary>
    public partial class ExperienceScrollingListControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 10 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.ExperienceScrollingListControl ExpScrollingLIstControlRoot;
        
        #line default
        #line hidden
        
        
        #line 16 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LayoutRoot;
        
        #line default
        #line hidden
        
        
        #line 60 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleBtnDropShadow;
        
        #line default
        #line hidden
        
        
        #line 68 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleBtnTxt;
        
        #line default
        #line hidden
        
        
        #line 76 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SeeAllBtnDropShadow;
        
        #line default
        #line hidden
        
        
        #line 84 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SeeAllBtnTxt;
        
        #line default
        #line hidden
        
        
        #line 111 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RowDefinition MainRow;
        
        #line default
        #line hidden
        
        
        #line 120 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.ImageButton ScrollLeftButton;
        
        #line default
        #line hidden
        
        
        #line 138 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl CollectionItemsControl;
        
        #line default
        #line hidden
        
        
        #line 184 "ExperienceScrollingListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.ImageButton ScrollRightButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/atlas/experiencescrollinglistcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "ExperienceScrollingListControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ExpScrollingLIstControlRoot = ((LindenLab.ExperienceScrollingListControl)(target));
            return;
            case 2:
            this.LayoutRoot = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.TitleBtnDropShadow = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TitleBtnTxt = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.SeeAllBtnDropShadow = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.SeeAllBtnTxt = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.MainRow = ((System.Windows.Controls.RowDefinition)(target));
            return;
            case 8:
            this.ScrollLeftButton = ((LindenLab.ImageButton)(target));
            return;
            case 9:
            this.CollectionItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 10:
            this.ScrollRightButton = ((LindenLab.ImageButton)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

