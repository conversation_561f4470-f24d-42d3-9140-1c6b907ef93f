/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#ifndef EDITENGINE_LIGHTTRANSPORTBAKER_H
#define EDITENGINE_LIGHTTRANSPORTBAKER_H

#include "LightTransportBakerSettings.h"

#include "LLEmbree/Device.h"
#include "LLEmbree/Scene.h"
#include "LLGraphicsGems/SpatialIndex.h"
#include "LLGraphicsGems/SphericalHarmonics.h"

namespace LLGems
{
class Mesh;
}

namespace Engine
{
class JobQueue;
}

namespace EngineRender
{
class LightTransportBakeData;
class LightComponentDefinition;
;
struct LightTransportChunk;
} // namespace EngineRender

namespace EditEngine
{
    // AABB extents for GI
    static inline const LLCore::Vector4 extent_AABB_Min = {-4095.0f, -4095.0f, -4095.0f, 0.0f};
    static inline const LLCore::Vector4 extent_AABB_Max = {4095.0f, 4095.0f, 4095.0f, 0.0f};

class LightTransportBaker
{
public:
    struct MaterialInfo
    {
        enum
        {
            cMaxMaps = 5
        };
        EngineRender::LightTransportBakeData const* m_maps[cMaxMaps]    = {};
        LLCore::Vector4                             m_tint              = LLCore::VectorConstants::cOne;
        float                                       m_emissiveIntensity = 1.0f;
        float                                       m_uvScale           = 1.0f;
        float                                       m_uvOffset          = 0.0f;
        LLCore::Vector4                             m_uvOffsets         = LLCore::VectorConstants::cZero;
        float                                       m_uvRotate          = 0.0f;
        bool                                        m_alphaOpacity      = false;
        bool                                        m_isVideo           = false;
        bool                                        m_isTwoSided        = false;
    };
    typedef void (*SampleFunc)(float u, float v, MaterialInfo const& materialInfo, LLCore::Vector4& albedoOut, LLCore::Vector4& emissionOut);

    static SampleFunc const s_simpleAlbedoSampler;
    static SampleFunc const s_simpleAlbedoAndEmissionSampler;
    static SampleFunc const s_blendedAlbedoSampler;
    static SampleFunc const s_grayAlbedoSampler;
    static SampleFunc const s_blackAlbedoSampler;

    explicit LightTransportBaker(float voxelCellSize);

    void addModel(
        LLCore::Matrix4 const& modelToWorld,
        float const*           positions,
        float const*           uvs,
        uint const*            indices,
        uint                   vertexCount,
        uint                   triangleCount,
        bool                   twoSided,
        MaterialInfo const&    materialInfo,
        SampleFunc             albedoSampleFunc);

    void addLight(LLCore::Matrix4 const& lightToWorld, EngineRender::LightComponentDefinition const& def);

    void buildLightTransportChunk(
        LightTransportBakerSettings const&        settings,
        uint                                      numLevels,
        LLGraphicsGems::MultilevelVoxelSet const* voxelMask,
        LLCore::Function<bool()> const&           cancellationPredicate,
        LLCore::Function<void(float)> const&      progressCallback,
        Engine::JobQueue*                         jobQueue,
        EngineRender::LightTransportChunk*        result);

public:
    class PruneProbesJob;
    class ComputeProbesJob;
    class GatherIndirectJob;

    LLGraphicsGems::VoxelSet m_builderVoxels;
    LLEmbree::Device         m_embreeDevice;
    LLEmbree::Scene          m_embreeScene;
    struct GeometryInfo
    {
        bool                 m_twoSided;
        MaterialInfo         m_materialInfo;
        SampleFunc           m_sampleFunc;
        LLCore::Array<float> m_u;
        LLCore::Array<float> m_v;
    };
    struct LightInfo
    {
        LLCore::Vector4 m_loc;
        LLCore::Vector4 m_dir;
        LLCore::Vector4 m_intensity; // 1/radius^2 in w
        float           m_coneCosine;
        float           m_angularFalloff;
        float           m_nearClip;
        bool            m_isShadowCaster;

        LLCore::Vector4 evaluate(LLCore::Vector4_ConstParameter toLight, LLCore::Vector4_ConstParameter norm, LLCore::Vector4_ConstParameter albedo) const;
    };

    LLCore::Array<GeometryInfo> m_geometryInfos;
    LLCore::Array<LightInfo>    m_lightInfos;
    LightInfo                   m_sunInfo;
};

} // namespace EditEngine

#endif
