﻿#pragma checksum "StatMultiGraph.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "51AC119EB61BAD8CB240C4CFC98538863C5569AD08FB64F9FB120B1F6BBF87F3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// StatMultiGraph
    /// </summary>
    public partial class StatMultiGraph : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 1 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.StatMultiGraph Root;
        
        #line default
        #line hidden
        
        
        #line 34 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GroupNameText;
        
        #line default
        #line hidden
        
        
        #line 50 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Stat0LabelText;
        
        #line default
        #line hidden
        
        
        #line 53 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Stat0ValueText;
        
        #line default
        #line hidden
        
        
        #line 56 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Stat1LabelText;
        
        #line default
        #line hidden
        
        
        #line 59 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Stat1ValueText;
        
        #line default
        #line hidden
        
        
        #line 62 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Stat2LabelText;
        
        #line default
        #line hidden
        
        
        #line 65 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Stat2ValueText;
        
        #line default
        #line hidden
        
        
        #line 68 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Stat3LabelText;
        
        #line default
        #line hidden
        
        
        #line 71 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Stat3ValueText;
        
        #line default
        #line hidden
        
        
        #line 77 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas GraphCanvas;
        
        #line default
        #line hidden
        
        
        #line 110 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.StatGraphTrace Stat0Trace;
        
        #line default
        #line hidden
        
        
        #line 111 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.StatGraphTrace Stat1Trace;
        
        #line default
        #line hidden
        
        
        #line 112 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.StatGraphTrace Stat2Trace;
        
        #line default
        #line hidden
        
        
        #line 113 "StatMultiGraph.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.StatGraphTrace Stat3Trace;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/diagnostics/statmultigraph.xaml", System.UriKind.Relative);
            
            #line 1 "StatMultiGraph.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Root = ((LindenLab.StatMultiGraph)(target));
            return;
            case 2:
            this.GroupNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.Stat0LabelText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.Stat0ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.Stat1LabelText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.Stat1ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.Stat2LabelText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.Stat2ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.Stat3LabelText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.Stat3ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.GraphCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 12:
            this.Stat0Trace = ((LindenLab.StatGraphTrace)(target));
            return;
            case 13:
            this.Stat1Trace = ((LindenLab.StatGraphTrace)(target));
            return;
            case 14:
            this.Stat2Trace = ((LindenLab.StatGraphTrace)(target));
            return;
            case 15:
            this.Stat3Trace = ((LindenLab.StatGraphTrace)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

