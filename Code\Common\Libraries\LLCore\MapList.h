/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#pragma once

#include "DynamicPool.h"
#include "FixedPool.h"
#include "IMapList.h"
#include "MultiPool.h"
#include "RefCounted.h"

namespace LLCore
{


//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// The T_FIXED template parameter controls how memory is managed for the container.  Generally speaking, T_FIXED represents
// the number of objects that the container can hold in the fixed-buffer that is embedded inside the container class itself.
// This embedded space effectively allows the first T_FIXED objects inserted into the container to occur without any additional
// allocations.  Additionally, there are special values of T_FIXED (negative values) that cause the container to use alternate
// allocation schemes.  The values are as follows:
//
//     T_FIXED = -1 = allocations go straight to/from the heap via CoreAllocator or the allocator-override (see setAllocator)
//     T_FIXED >= 0 = Pooled mode with fixed space.  There is enough space for T_FIXED objects embedded right into the container class
//
// The T_TABLE_SIZE parameter controls how large the hash-table itself is.  If it is a positive number, then that that is the size
// of the hash table itself and that spaced is embedded into the container object itself.  If it is 0, then the size of the hash table
// is dynamic and grows as the contents of the container grows (using a growth policy to avoid expensive resizing as much as reasonable)
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

template<typename T_KEY, typename T, int T_TABLE_SIZE = 0, int T_FIXED = -1>
class MapList;
template<typename T_KEY, typename T, int T_TABLE_SIZE = 0, int T_FIXED = -1>
class RefMapList; // identical to MapList, except the container itself is RefCounted.

template<typename T_KEY, typename T, int T_TABLE_SIZE, int T_FIXED>
RangeRef(const MapList<T_KEY, T, T_TABLE_SIZE, T_FIXED>&, mem_int = 0, mem_int = 0) -> RangeRef<const T>;

template<typename T_KEY, typename T, int T_TABLE_SIZE, int T_FIXED>
RangeRef(MapList<T_KEY, T, T_TABLE_SIZE, T_FIXED>&, mem_int = 0, mem_int = 0) -> RangeRef<T>;

template<typename T_KEY, typename T, int T_TABLE_SIZE, int T_FIXED>
RangeRef(const RefMapList<T_KEY, T, T_TABLE_SIZE, T_FIXED>&, mem_int = 0, mem_int = 0) -> RangeRef<const T>;

template<typename T_KEY, typename T, int T_TABLE_SIZE, int T_FIXED>
RangeRef(RefMapList<T_KEY, T, T_TABLE_SIZE, T_FIXED>&, mem_int = 0, mem_int = 0) -> RangeRef<T>;


template<typename T_KEY, typename T, int T_TABLE_SIZE>
class MapList<T_KEY, T, T_TABLE_SIZE, -1>
{
public:
    typedef T_KEY KeyType;
    typedef T     ValueType;

    struct InitType
    {
        T_KEY m_key;
        T     m_value;
    };

    MapList();
    MapList(std::initializer_list<InitType> source);
    MapList(const MapList& source);
    virtual ~MapList();

    MapList& operator=(const MapList& source);
    MapList& operator=(std::initializer_list<InitType> source);

    operator RangeRef<T>();
    operator RangeRef<const T>() const;

    template<typename T_RESULT, Requires<LLCore::IsConvertibleType<RangeRef<T>, RangeRef<T_RESULT>>()> = 0>
    operator RangeRef<T_RESULT>();

    template<typename T_RESULT, Requires<LLCore::IsConvertibleType<RangeRef<T>, RangeRef<T_RESULT>>()> = 0>
    operator RangeRef<const T_RESULT>() const;

    template<typename T_SOURCE>
    bool overlaps(RangeRef<T_SOURCE> range);

    // list-based iteration
    T&       at(mem_int i, int prefetchDistance = 16);
    const T& at(mem_int i, int prefetchDistance = 16) const;
    T&       operator[](mem_int i);
    const T& operator[](mem_int i) const;

    // very fast O(1) lookup
    mem_int      getIndex(const T* obj) const;
    const T_KEY& getKey(const T* obj) const;

    T*       getFirst();
    const T* getFirst() const;
    T*       getLast();
    const T* getLast() const;
    T*       getNext(const T* obj);
    const T* getNext(const T* obj) const;
    T*       getPrev(const T* obj);
    const T* getPrev(const T* obj) const;

    T&       atFirst();
    const T& atFirst() const;
    T&       atLast();
    const T& atLast() const;

    T*       getRange(mem_int startIndex, mem_int* rangeCount);
    const T* getRange(mem_int startIndex, mem_int* rangeCount) const;


    // map-based lookup
    template<typename T_FIND_KEY>
    T* find(const T_FIND_KEY& key); // finds entry matching specified key (not guaranteed to be first entry in a series of matching entries, but in practice it is for this container.  Exists for consistency with OrderedMap interface)
    template<typename T_FIND_KEY>
    const T* find(const T_FIND_KEY& key) const;
    template<typename T_FIND_KEY>
    T* findFirst(const T_FIND_KEY& key); // identical to find
    template<typename T_FIND_KEY>
    const T* findFirst(const T_FIND_KEY& key) const;
    T*       findNext(const T* obj); // find the next entry matching key of 'obj' (returns nullptr if next entry does not have a matching key)
    const T* findNext(const T* obj) const;

    // inserting
    template<typename T_INSERT_KEY, typename... T_PARAMS>
    T* insertLastMulti(T_INSERT_KEY&& key, T_PARAMS&&... params);
    template<typename T_INSERT_KEY, typename... T_PARAMS>
    T* insertLastOrAssert(T_INSERT_KEY&& key, T_PARAMS&&... params);
    template<typename T_INSERT_KEY, typename... T_PARAMS>
    T* insertLastOrReplace(T_INSERT_KEY&& key, T_PARAMS&&... params);
    template<typename T_INSERT_KEY, typename... T_PARAMS>
    T* insertLastOrFind(T_INSERT_KEY&& key, T_PARAMS&&... params);
    template<typename T_INSERT_KEY, typename... T_PARAMS>
    T* insertOrderedMulti(T_INSERT_KEY&& key, mem_int index, T_PARAMS&&... params);
    template<typename T_INSERT_KEY, typename... T_PARAMS>
    T* insertOrderedOrAssert(T_INSERT_KEY&& key, mem_int index, T_PARAMS&&... params);
    template<typename T_INSERT_KEY, typename... T_PARAMS>
    T* insertOrderedOrReplace(T_INSERT_KEY&& key, mem_int index, T_PARAMS&&... params);
    template<typename T_INSERT_KEY, typename... T_PARAMS>
    T* insertOrderedOrFind(T_INSERT_KEY&& key, mem_int index, T_PARAMS&&... params);
    template<typename T_INSERT_KEY>
    byte* placementInsertLastMulti(T_INSERT_KEY&& key);
    template<typename T_INSERT_KEY>
    byte* placementInsertOrderedMulti(T_INSERT_KEY&& key, mem_int index);


    // removing
    void removeFast(T* obj);
    void removeFast(mem_int index);
    void removeOrdered(T* obj);
    void removeOrdered(mem_int index);
    void removeLast(mem_int removeCount = 1);
    void removeAll();
    template<typename T_FIND_KEY>
    void removeAll(const T_FIND_KEY& key);

    // moving (more efficient than removing and reinserting)
    template<typename T_NEW_KEY>
    void move(T* obj, T_NEW_KEY&& newKey);

    // swaps elements between the two indexes specified.  The actual location of the objects does not change in memory, so pointers to objects remain valid.
    // note: this function is used by the IndexedContainerSort algorithm to allow for sorting of a List without the internal objects every being moved or copied.
    void indexSwap(mem_int index1, mem_int index2);

    mem_int getCount() const;
    bool    isEmpty() const;

    void       setAllocator(Allocator* allocator);
    Allocator* getAllocator() const;

    // aggressive means the underlying array is sized to exactly what is needed, non-aggressive means it uses a growth policy to pick a size (see Array for more details)
    void ensureCapacity(mem_int count, bool aggressive = true);
    void compact(bool aggressive = false);

    // range-based for support
    Internal::ListRangeForIterator<MapList<T_KEY, T, T_TABLE_SIZE, -1>>       begin() { return this; }
    Internal::ListRangeForIterator<const MapList<T_KEY, T, T_TABLE_SIZE, -1>> begin() const { return this; }
    Internal::ListRangeForEndIterator                                         end() const { return {}; }

protected:
    struct Node : public PlacementNewOnly
    {
        alignas(T) byte m_value[sizeof(T)];
        IListLink             m_listLink;
        IMapLink<T_KEY, Node> m_hashLink;
    };

    Node* getNode(const T* obj) const
    {
        static_assert(offsetof(Node, m_value) == 0, "m_value MUST be first member of this class for this to work");
        Node* node = reinterpret_cast<Node*>(const_cast<T*>(obj));
        LLCORE_ASSERT(m_mapList.contains(node), "Object not in container, possibly using separate copy of stored value?");
        return node;
    }
    T* getValue(const Node* node) const { return reinterpret_cast<T*>(const_cast<byte*>(node->m_value)); }

    byte* allocNode();
    void  freeNode(byte* p);

    IMapList<T_KEY, Node, &Node::m_hashLink, &Node::m_listLink, T_TABLE_SIZE, -1> m_mapList;

    void initFromInitializerList(std::initializer_list<InitType>);

public:
    enum
    {
        cTotalSpace = sizeof(Node)
    };
    template<int T_FIXED_ELEMENT_COUNT>
    using FixedAllocatorType = MultiPool<FixedPool<MapList<T_KEY, T, T_TABLE_SIZE, -1>::cTotalSpace, T_FIXED_ELEMENT_COUNT, alignof(Node)>,                                                             // handle allocation of map nodes
                                         FixedPool<(T_FIXED_ELEMENT_COUNT * sizeof(void*)), (T_FIXED_ELEMENT_COUNT == Internal::CalculateMapTableSize(T_FIXED_ELEMENT_COUNT)) ? 2 : 1, alignof(void*)>, // allocation of list container
                                                                                                                                                                                                        //(handles case where list and hash array are same size and need to be served from same pool
                                         FixedPool<(T_FIXED_ELEMENT_COUNT == Internal::CalculateMapTableSize(T_FIXED_ELEMENT_COUNT))
                                                       ? 1
                                                       : (Internal::CalculateMapTableSize(T_FIXED_ELEMENT_COUNT) * sizeof(void*)),
                                                   1,
                                                   alignof(void*)>, // allocation of hash array
                                         CoreAllocator>;
};

template<typename T_KEY, typename T, int T_TABLE_SIZE, int T_FIXED>
class MapList : public MapList<T_KEY, T, T_TABLE_SIZE, -1>
{
public:
    using InitType = typename MapList<T_KEY, T, T_TABLE_SIZE, -1>::InitType;

    enum
    {
        cTFixed = T_FIXED
    };

    MapList()
    {
        this->setAllocator(&m_pool);
        this->m_mapList.ensureCapacity(T_FIXED);
    }
    MapList(const MapList& source)
    {
        this->setAllocator(&m_pool);
        this->m_mapList.ensureCapacity(T_FIXED);
        operator=(source);
    }
    MapList(std::initializer_list<InitType> source)
    {
        this->setAllocator(&m_pool);
        this->m_mapList.ensureCapacity(T_FIXED);
        this->initFromInitializerList(source);
    }

    virtual ~MapList()
    {
        this->removeAll();
        this->setAllocator(nullptr);
    }
    MapList& operator=(const MapList& source)
    {
        MapList<T_KEY, T, T_TABLE_SIZE, -1>::operator=((const MapList<T_KEY, T, T_TABLE_SIZE, -1>&)source);
        return *this;
    }
    MapList& operator=(const MapList<T_KEY, T, T_TABLE_SIZE, -1>& source)
    {
        MapList<T_KEY, T, T_TABLE_SIZE, -1>::operator=(source);
        return *this;
    }
    MapList& operator=(std::initializer_list<InitType> source)
    {
        MapList<T_KEY, T, T_TABLE_SIZE, -1>::operator=(source);
        return *this;
    }

    void compactPool() { m_pool.getAllocator().compact(); }

protected:
    typename MapList<T_KEY, T, T_TABLE_SIZE, -1>::template FixedAllocatorType<T_FIXED> m_pool;
};

template<typename T_KEY, typename T, int T_TABLE_SIZE>
class RefMapList<T_KEY, T, T_TABLE_SIZE, -1> : public MapList<T_KEY, T, T_TABLE_SIZE, -1>
    , public RefCounted
{
public:
    using InitType = typename MapList<T_KEY, T, T_TABLE_SIZE, -1>::InitType;

    using MapList<T_KEY, T, T_TABLE_SIZE, -1>::MapList;

    RefMapList& operator=(const RefMapList& source)
    {
        MapList<T_KEY, T, T_TABLE_SIZE, -1>::operator=((const MapList<T_KEY, T, T_TABLE_SIZE, -1>&)source);
        return *this;
    }
    RefMapList& operator=(const MapList<T_KEY, T, T_TABLE_SIZE, -1>& source)
    {
        MapList<T_KEY, T, T_TABLE_SIZE, -1>::operator=(source);
        return *this;
    }
    RefMapList& operator=(std::initializer_list<InitType> source)
    {
        MapList<T_KEY, T, T_TABLE_SIZE, -1>::operator=(source);
        return *this;
    }
};

template<typename T_KEY, typename T, int T_TABLE_SIZE, int T_FIXED>
class RefMapList : public RefMapList<T_KEY, T, T_TABLE_SIZE, -1>
{
public:
    using InitType = typename RefMapList<T_KEY, T, T_TABLE_SIZE, -1>::InitType;

    enum
    {
        cTFixed = T_FIXED
    };

    RefMapList()
    {
        this->setAllocator(&m_pool);
        this->m_mapList.ensureCapacity(T_FIXED);
    }
    RefMapList(const RefMapList& source)
    {
        this->setAllocator(&m_pool);
        this->m_mapList.ensureCapacity(T_FIXED);
        operator=(source);
    }
    RefMapList(std::initializer_list<InitType> source)
    {
        this->setAllocator(&m_pool);
        this->m_mapList.ensureCapacity(T_FIXED);
        this->initFromInitializerList(source);
    }
    virtual ~RefMapList()
    {
        this->removeAll();
        this->setAllocator(nullptr);
    }
    RefMapList& operator=(const RefMapList& source)
    {
        MapList<T_KEY, T, T_TABLE_SIZE, -1>::operator=((const MapList<T_KEY, T, T_TABLE_SIZE, -1>&)source);
        return *this;
    }
    RefMapList& operator=(const MapList<T_KEY, T, T_TABLE_SIZE, -1>& source)
    {
        MapList<T_KEY, T, T_TABLE_SIZE, -1>::operator=(source);
        return *this;
    }
    RefMapList& operator=(std::initializer_list<InitType> source)
    {
        MapList<T_KEY, T, T_TABLE_SIZE, -1>::operator=(source);
        return *this;
    }

    void compactPool() { m_pool.getAllocator().compact(); }

protected:
    typename MapList<T_KEY, T, T_TABLE_SIZE, -1>::template FixedAllocatorType<T_FIXED> m_pool;
};


///////////////////////////////////////////////////////////////////////////////////////
// inline implementation
///////////////////////////////////////////////////////////////////////////////////////
template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline MapList<T_KEY, T, T_TABLE_SIZE>::MapList()
{
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline MapList<T_KEY, T, T_TABLE_SIZE>::MapList(std::initializer_list<InitType> source)
{
    initFromInitializerList(source);
}


template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::initFromInitializerList(std::initializer_list<InitType> source)
{
    ensureCapacity(source.size());
    for (const InitType& item : source)
    {
        insertLastMulti(item.m_key, item.m_value);
    }
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline MapList<T_KEY, T, T_TABLE_SIZE>::MapList(const MapList& source)
{
    *this = source;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline MapList<T_KEY, T, T_TABLE_SIZE>::~MapList()
{
    removeAll();
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
MapList<T_KEY, T, T_TABLE_SIZE>::operator RangeRef<T>()
{
    //FIXME: need faster way to get ptr to array
    RangeRef<Node> listSequence = m_mapList;

    RangeRef<T> s;
    s.m_blockArray = listSequence.m_blockArray;
    s.m_blockBits  = 0;
    s.m_first      = 0;
    s.m_count      = getCount();
    s.m_stride     = sizeof(T);
    s.m_offset     = 0;

    return s;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
MapList<T_KEY, T, T_TABLE_SIZE>::operator RangeRef<const T>() const
{
    //FIXME: need faster way to get ptr to array
    RangeRef<const Node> listSequence = m_mapList;

    RangeRef<const T> s;
    s.m_blockArray = listSequence.m_blockArray;
    s.m_blockBits  = 0;
    s.m_first      = 0;
    s.m_count      = getCount();
    s.m_stride     = sizeof(T);
    s.m_offset     = 0;

    return s;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_RESULT, Requires<LLCore::IsConvertibleType<RangeRef<T>, RangeRef<T_RESULT>>()>>
MapList<T_KEY, T, T_TABLE_SIZE>::operator RangeRef<T_RESULT>()
{
    RangeRef<T> s(*this);
    return s;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_RESULT, Requires<LLCore::IsConvertibleType<RangeRef<T>, RangeRef<T_RESULT>>()>>
MapList<T_KEY, T, T_TABLE_SIZE>::operator RangeRef<const T_RESULT>() const
{
    RangeRef<const T> s(*this);
    return s;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline MapList<T_KEY, T, T_TABLE_SIZE>& MapList<T_KEY, T, T_TABLE_SIZE>::operator=(const MapList<T_KEY, T, T_TABLE_SIZE>& source)
{
    if (this != &source)
    {
        removeAll();
        ensureCapacity(source.getCount());
        for (mem_int i = 0; i < source.getCount(); ++i)
        {
            const T* obj = &source[i];
            this->insertLastMulti(source.getKey(obj), *obj);
        }
    }
    return *this;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline MapList<T_KEY, T, T_TABLE_SIZE>& MapList<T_KEY, T, T_TABLE_SIZE>::operator=(std::initializer_list<InitType> source)
{
    removeAll();
    ensureCapacity(source.getCount());
    for (const InitType& initType : source)
    {
        this->insertLastMulti(initType.m_key, initType.m_value);
    }

    return *this;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_SOURCE>
inline bool MapList<T_KEY, T, T_TABLE_SIZE, -1>::overlaps(RangeRef<T_SOURCE> range)
{
    return m_mapList.overlaps(range);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline T& MapList<T_KEY, T, T_TABLE_SIZE>::at(mem_int i, int prefetchDistance)
{
    return *getValue(&m_mapList.at(i, prefetchDistance));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline const T& MapList<T_KEY, T, T_TABLE_SIZE>::at(mem_int i, int prefetchDistance) const
{
    return *getValue(&m_mapList.at(i, prefetchDistance));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline T& MapList<T_KEY, T, T_TABLE_SIZE>::operator[](mem_int i)
{
    return *getValue(&m_mapList.operator[](i));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline const T& MapList<T_KEY, T, T_TABLE_SIZE>::operator[](mem_int i) const
{
    return *getValue(&m_mapList.operator[](i));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
T* MapList<T_KEY, T, T_TABLE_SIZE>::getFirst()
{
    return getValue(m_mapList.getFirst());
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
const T* MapList<T_KEY, T, T_TABLE_SIZE>::getFirst() const
{
    return getValue(m_mapList.getFirst());
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
T* MapList<T_KEY, T, T_TABLE_SIZE>::getLast()
{
    return getValue(m_mapList.getLast());
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
const T* MapList<T_KEY, T, T_TABLE_SIZE>::getLast() const
{
    return getValue(m_mapList.getLast());
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
T* MapList<T_KEY, T, T_TABLE_SIZE>::getNext(const T* obj)
{
    return getValue(m_mapList.getNext(getNode(obj)));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
const T* MapList<T_KEY, T, T_TABLE_SIZE>::getNext(const T* obj) const
{
    return getValue(m_mapList.getNext(getNode(obj)));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
T* MapList<T_KEY, T, T_TABLE_SIZE>::getPrev(const T* obj)
{
    return getValue(m_mapList.getPrev(getNode(obj)));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
const T* MapList<T_KEY, T, T_TABLE_SIZE>::getPrev(const T* obj) const
{
    return getValue(m_mapList.getPrev(getNode(obj)));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline T& MapList<T_KEY, T, T_TABLE_SIZE>::atFirst()
{
    return *getValue(&m_mapList.atFirst());
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline const T& MapList<T_KEY, T, T_TABLE_SIZE>::atFirst() const
{
    return *getValue(&m_mapList.atFirst());
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline T& MapList<T_KEY, T, T_TABLE_SIZE>::atLast()
{
    return *getValue(&m_mapList.atLast());
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline const T& MapList<T_KEY, T, T_TABLE_SIZE>::atLast() const
{
    return *getValue(&m_mapList.atLast());
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::getRange(mem_int startIndex, mem_int* rangeCount)
{
    return getValue(m_mapList.getRange(startIndex, rangeCount));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline const T* MapList<T_KEY, T, T_TABLE_SIZE>::getRange(mem_int startIndex, mem_int* rangeCount) const
{
    return getValue(m_mapList.getRange(startIndex, rangeCount));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline mem_int MapList<T_KEY, T, T_TABLE_SIZE>::getIndex(const T* obj) const
{
    return m_mapList.getIndex(getNode(obj));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline const T_KEY& MapList<T_KEY, T, T_TABLE_SIZE>::getKey(const T* obj) const
{
    return m_mapList.getKey(getNode(obj));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_FIND_KEY>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::find(const T_FIND_KEY& key)
{
    return getValue(m_mapList.find(key));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_FIND_KEY>
inline const T* MapList<T_KEY, T, T_TABLE_SIZE>::find(const T_FIND_KEY& key) const
{
    return getValue(m_mapList.find(key));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_FIND_KEY>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::findFirst(const T_FIND_KEY& key)
{
    return getValue(m_mapList.findFirst(key));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_FIND_KEY>
inline const T* MapList<T_KEY, T, T_TABLE_SIZE>::findFirst(const T_FIND_KEY& key) const
{
    return getValue(m_mapList.findFirst(key));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::findNext(const T* obj)
{
    return getValue(m_mapList.findNext(getNode(obj)));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline const T* MapList<T_KEY, T, T_TABLE_SIZE>::findNext(const T* obj) const
{
    return getValue(m_mapList.findNext(getNode(obj)));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY, typename... T_PARAMS>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::insertLastMulti(T_INSERT_KEY&& key, T_PARAMS&&... params)
{
    return UncheckedPlacementNew<T>(placementInsertLastMulti(static_cast<T_INSERT_KEY&&>(key)), static_cast<T_PARAMS&&>(params)...);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY, typename... T_PARAMS>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::insertLastOrAssert(T_INSERT_KEY&& key, T_PARAMS&&... params)
{
    LLCORE_ASSERT(find(key) == nullptr, "Attempted to insert a non-unique key");

    return insertLastMulti(static_cast<T_INSERT_KEY&&>(key), static_cast<T_PARAMS&&>(params)...);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY, typename... T_PARAMS>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::insertLastOrReplace(T_INSERT_KEY&& key, T_PARAMS&&... params)
{
    T* res = find(static_cast<T_INSERT_KEY&&>(key));
    if (res != nullptr)
    {
        LLCore::ReconstructInPlace(res, static_cast<T_PARAMS&&>(params)...);
    }
    else
    {
        res = insertLastMulti(key, static_cast<T_PARAMS&&>(params)...);
    }
    return res;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY, typename... T_PARAMS>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::insertLastOrFind(T_INSERT_KEY&& key, T_PARAMS&&... params)
{
    T* res = find(static_cast<T_INSERT_KEY&&>(key));
    if (res == nullptr)
    {
        res = insertLastMulti(key, static_cast<T_PARAMS&&>(params)...);
    }
    return res;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY, typename... T_PARAMS>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::insertOrderedMulti(T_INSERT_KEY&& key, mem_int index, T_PARAMS&&... params)
{
    return UncheckedPlacementNew<T>(placementInsertOrderedMulti(static_cast<T_INSERT_KEY&&>(key), index), static_cast<T_PARAMS&&>(params)...);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY, typename... T_PARAMS>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::insertOrderedOrAssert(T_INSERT_KEY&& key, mem_int index, T_PARAMS&&... params)
{
    LLCORE_ASSERT(find(key) == nullptr, "Attempted to insert a non-unique key");

    return insertOrderedMulti(static_cast<T_INSERT_KEY&&>(key), index, static_cast<T_PARAMS&&>(params)...);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY, typename... T_PARAMS>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::insertOrderedOrReplace(T_INSERT_KEY&& key, mem_int index, T_PARAMS&&... params)
{
    T* res = find(static_cast<T_INSERT_KEY&&>(key));
    if (res != nullptr)
    {
        LLCore::ReconstructInPlace(res, static_cast<T_PARAMS&&>(params)...);
    }
    else
    {
        res = insertOrderedMulti(static_cast<T_INSERT_KEY&&>(key), index, static_cast<T_PARAMS&&>(params)...);
    }
    return res;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY, typename... T_PARAMS>
inline T* MapList<T_KEY, T, T_TABLE_SIZE>::insertOrderedOrFind(T_INSERT_KEY&& key, mem_int index, T_PARAMS&&... params)
{
    T* res = find(static_cast<T_INSERT_KEY&&>(key));
    if (res == nullptr)
    {
        res = insertOrderedMulti(static_cast<T_INSERT_KEY&&>(key), index, static_cast<T_PARAMS&&>(params)...);
    }
    return res;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY>
inline byte* MapList<T_KEY, T, T_TABLE_SIZE>::placementInsertLastMulti(T_INSERT_KEY&& key)
{
    byte* p = allocNode(); // do not construct T here, that is done afterward by the caller
    m_mapList.insertLastMulti(static_cast<T_INSERT_KEY&&>(key), UncheckedPlacementNew<Node>(p));
    return p;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_INSERT_KEY>
inline byte* MapList<T_KEY, T, T_TABLE_SIZE>::placementInsertOrderedMulti(T_INSERT_KEY&& key, mem_int index)
{
    byte* p = allocNode(); // do not construct T here, that is done afterward by the caller
    m_mapList.insertOrderedMulti(static_cast<T_INSERT_KEY&&>(key), index, UncheckedPlacementNew<Node>(p));
    return p;
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::removeFast(T* obj)
{
    Node* node = m_mapList.removeFast(getNode(obj));
    obj->~T();
    node->~Node();
    freeNode((byte*)(void*)node);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::removeFast(mem_int index)
{
    Node* node = m_mapList.removeFast(index);
    getValue(node)->~T();
    node->~Node();
    freeNode((byte*)(void*)node);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::removeOrdered(T* obj)
{
    Node* node = m_mapList.removeOrdered(getNode(obj));
    obj->~T();
    node->~Node();
    freeNode((byte*)(void*)node);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::removeOrdered(mem_int index)
{
    Node* node = m_mapList.removeOrdered(index);
    getValue(node)->~T();
    node->~Node();
    freeNode((byte*)(void*)node);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::removeLast(mem_int removeCount)
{
    while (removeCount--)
    {
        Node* node = m_mapList.removeLast();
        LLCORE_ASSERT(node != nullptr, "Attempt to removeLast on an empty MapList container");
        getValue(node)->~T();
        node->~Node();
        freeNode((byte*)(void*)node);
    }
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::removeAll()
{
    while (getCount() > 0)
    {
        removeLast();
    }
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_FIND_KEY>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::removeAll(const T_FIND_KEY& key)
{
    Node* node = m_mapList.findFirst(key);
    while (node != nullptr)
    {
        Node* next = m_mapList.findNext(node);
        removeFast(getValue(node));
        node = next;
    }
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::indexSwap(mem_int index1, mem_int index2)
{
    m_mapList.indexSwap(index1, index2);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
template<typename T_NEW_KEY>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::move(T* obj, T_NEW_KEY&& newKey)
{
    m_mapList.move(getNode(obj), static_cast<T_NEW_KEY&&>(newKey));
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline mem_int MapList<T_KEY, T, T_TABLE_SIZE>::getCount() const
{
    return m_mapList.getCount();
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline bool MapList<T_KEY, T, T_TABLE_SIZE>::isEmpty() const
{
    return m_mapList.isEmpty();
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::setAllocator(Allocator* allocator)
{
    m_mapList.setAllocator(allocator);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline Allocator* MapList<T_KEY, T, T_TABLE_SIZE>::getAllocator() const
{
    return m_mapList.getAllocator();
}

// aggressive means the underlying array is sized to exactly what is needed, non-aggressive means it uses a growth policy to pick a size (see Array for more details)
template<typename T_KEY, typename T, int T_TABLE_SIZE>
void MapList<T_KEY, T, T_TABLE_SIZE>::ensureCapacity(mem_int count, bool aggressive)
{
    m_mapList.ensureCapacity(count, aggressive);
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
void MapList<T_KEY, T, T_TABLE_SIZE>::compact(bool aggressive)
{
    m_mapList.compact(aggressive);
}


template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline byte* MapList<T_KEY, T, T_TABLE_SIZE>::allocNode()
{
    Allocator* allocator = m_mapList.getAllocator();
    return (allocator != nullptr) ? (byte*)allocator->alloc<Node>() : (byte*)LLCore::Alloc<Node>();
}

template<typename T_KEY, typename T, int T_TABLE_SIZE>
inline void MapList<T_KEY, T, T_TABLE_SIZE>::freeNode(byte* p)
{
    Allocator* allocator = m_mapList.getAllocator();
    (allocator != nullptr) ? allocator->free(p, cTotalSpace, alignof(Node)) : LLCore::Free(p, cTotalSpace, alignof(Node));
}

} // namespace LLCore