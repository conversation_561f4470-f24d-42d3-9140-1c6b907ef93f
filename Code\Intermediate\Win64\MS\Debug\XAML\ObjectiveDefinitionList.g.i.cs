﻿#pragma checksum "ObjectiveDefinitionList.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "BACEEF85C3FA213618763536C2D8E8280025482C6E3AF242D4E261AF1FD7636B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using Microsoft.Expression.Interactivity.Core;
using Microsoft.Expression.Interactivity.Input;
using Microsoft.Expression.Interactivity.Layout;
using Microsoft.Expression.Interactivity.Media;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// ObjectiveDefinitionListControl
    /// </summary>
    public partial class ObjectiveDefinitionListControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 10 "ObjectiveDefinitionList.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.ObjectiveDefinitionListControl ObjectiveDefinitionList;
        
        #line default
        #line hidden
        
        
        #line 20 "ObjectiveDefinitionList.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.DragAdornerBehavior DragAdorner;
        
        #line default
        #line hidden
        
        
        #line 33 "ObjectiveDefinitionList.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetQuestProgressBtnCreator;
        
        #line default
        #line hidden
        
        
        #line 48 "ObjectiveDefinitionList.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateNewObjectiveDefinitionBtn;
        
        #line default
        #line hidden
        
        
        #line 58 "ObjectiveDefinitionList.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl ObjectiveDefinitionItemsControl;
        
        #line default
        #line hidden
        
        
        #line 140 "ObjectiveDefinitionList.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border DraggedItem;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/quests/questcreator/objectivedefinitionlist.xaml", System.UriKind.Relative);
            
            #line 1 "ObjectiveDefinitionList.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ObjectiveDefinitionList = ((LindenLab.ObjectiveDefinitionListControl)(target));
            return;
            case 2:
            this.DragAdorner = ((LindenLab.DragAdornerBehavior)(target));
            return;
            case 3:
            this.ResetQuestProgressBtnCreator = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.CreateNewObjectiveDefinitionBtn = ((System.Windows.Controls.Button)(target));
            return;
            case 5:
            this.ObjectiveDefinitionItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 6:
            this.DraggedItem = ((System.Windows.Controls.Border)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

