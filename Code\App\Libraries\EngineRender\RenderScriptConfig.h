/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#ifndef ENGINERENDER_RENDER_SCRIPT_CONFIG_H
#define ENGINERENDER_RENDER_SCRIPT_CONFIG_H

#include "GraphicsConfig.h"

#include "LLGems/ConfigBlock.h"
namespace EngineRender
{
struct RenderScriptConfig : public LLGems::ConfigBlock<RenderScriptConfig>
{
    enum RenderMode : uint
    {
        cRenderMode_Normal = 0,
        cRenderMode_EditorLighting,
        cRenderMode_VisualizeNormals,
        cRenderMode_Debug,
        cRenderMode_VisualizeTriangleDensity,
        cRenderMode_Overdraw,
        cRenderMode_Wireframe,
        cRenderMode_LightingComplexity,
        cRenderMode_VisibleBackfaces,
        cRenderMode_Radiance,
        cRenderMode_Count
    };
    enum Platform : uint
    {
        cPlatform_pc11 = 0,
    };
    enum Configuration : uint
    {
        cConfiguration_Release = 0,
        cConfiguration_Debug,
    };
    Optional<uint> m_pipelineType         = {{this, "pipelineType"}, GraphicsConfig::cRenderPipelineType_HighSpec};
    Optional<uint> m_mode                 = {{this, "mode"}, cRenderMode_Normal};
    Optional<bool> m_enableEditorFeatures = {{this, "enableEditorFeatures"}, false};
    Optional<uint> m_platform             = {{this, "platform"}, cPlatform_pc11};
    Optional<uint> m_configuration        = {{this, "configuration"}, cConfiguration_Release};
};

struct RenderScriptParams : public LLGems::ConfigBlock<RenderScriptParams>
{
    Optional<bool>                           m_enableShadowedAtmosphere   = {{this, "enableShadowedAtmosphere"}, true};
    Optional<bool>                           m_enableAmbientOcclusion     = {{this, "enableAmbientOcclusion"}, true};
    Optional<bool>                           m_enableReflections          = {{this, "enableReflections"}, true};
    Optional<bool>                           m_enableTemporalAntialiasing = {{this, "enableTemporalAntialiasing"}, true};
    Optional<bool>                           m_mirrorVrDisplay            = {{this, "mirrorVrDisplay"}, true};
    Optional<uint>                           m_viewSegmentCount           = {{this, "viewSegmentCount"}, 1};
    Optional<float>                          m_samplesPerPixel            = {{this, "samplesPerPixel"}, 1.0f};
    Optional<float>                          m_displayGamma               = {{this, "displayGamma"}, 0.0f};
    Optional<uint>                           m_displayGammaMode           = {{this, "displayGammaMode"}, 0};
    Optional<uint>                           m_hdrOsEnabled               = {{this, "hdrOsEnabled"}, 0};
    Optional<uint>                           m_hdrTonemapping             = {{this, "hdrTonemapping"}, 0};
    Optional<float>                          m_hdrMaxFALL                 = {{this, "hdrMaxFALL"}, 0.0f};
    Optional<float>                          m_hdrMaxLuminance            = {{this, "hdrMaxLuminance"}, 0.0f};
    Optional<float>                          m_hdrWhitePoint              = {{this, "hdrWhitePoint"}, 0.0f};
    Optional<float>                          m_HdrPqNormalizationRcp      = {{this, "HdrPqNormalizationRcp"}, 0.0f};
    Optional<bool>                           m_useFSRUpscaler             = {{this, "useFSRUpscaler"}, false};
    Optional<LLGems::ConfigBlockBase const*> m_scriptConfig               = {{this, "scriptConfig"}};
    Optional<LLGems::ConfigBlockBase const*> m_worldParams                = {{this, "worldParams"}};
};

struct SetRenderModeEvent
{
    SetRenderModeEvent(RenderScriptConfig::RenderMode mode, char const* const reason)
        : m_mode(mode)
        , m_reason(reason)
    {
    }
    const RenderScriptConfig::RenderMode m_mode;
    const LLCore::StringFixed<64>        m_reason;
};

struct RestartRenderScriptEvent
{
    RestartRenderScriptEvent(bool needReload, char const* const reason)
        : m_needReload(needReload)
        , m_reason(reason)
    {
    }

    bool                          m_needReload;
    const LLCore::StringFixed<64> m_reason;
};

} // namespace EngineRender

#endif //ENGINERENDER_RENDER_SCRIPT_CONFIG_H