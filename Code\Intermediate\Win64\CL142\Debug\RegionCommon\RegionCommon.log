﻿  unity_9ISEH9YOOH7H9O2U.cpp
C:\localDev\sansar\Code\Common\Libraries\LLCore\Vector4.h(17,1): error C2011: 'LLCore::Vector4': 'class' type redefinition
C:\localDev\sansar\Code\Common\Libraries\LLCore\Vector4.inl(14): message : see declaration of 'LLCore::Vector4'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(15,1): error C2011: 'LLCore::Quaternion': 'class' type redefinition
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(153,21): error C2084: function 'LLCore::PartialOrder LLCore::OrderTypeOverload(const LLCore::Quaternion &,const LLCore::Quaternion &)' already has a body
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(153): message : see previous definition of 'OrderTypeOverload'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(155,25): error C2027: use of undefined type 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(155,42): error C2027: use of undefined type 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(155,54): error C2661: 'LLCore::OrderType': no overloaded function takes 1 arguments
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(22,34): error C2061: syntax error: identifier 'NewPlacement'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(22,67): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(22,67): warning C4183: 'HK_DECLARE_CLASS': missing return type; assumed to be a member function returning 'int'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(23,18): error C2653: 'hk': is not a class or namespace name
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(23,22): error C2061: syntax error: identifier 'ExactTypeFunction'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(23,65): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(23,65): warning C4183: 'HK_RECORD_ATTR': missing return type; assumed to be a member function returning 'int'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(31,19): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(31,19): error C2143: syntax error: missing ';' before '*'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(32,2): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(32,2): error C2059: syntax error: '{'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(32,2): error C2143: syntax error: missing ';' before '{'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkBaseObject.h(32,2): error C2447: '{': missing function header (old-style formal list?)
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(53,46): error C2061: syntax error: identifier 'New'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(53,50): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(53,50): warning C4183: 'HK_DECLARE_CLASS': missing return type; assumed to be a member function returning 'int'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(54,46): error C2061: syntax error: identifier 'Reflect'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(54,54): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(54,54): error C2535: 'int hkReferencedObject::HK_DECLARE_CLASS(hkReferencedObject)': member function already defined or declared
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(53): message : see declaration of 'hkReferencedObject::HK_DECLARE_CLASS'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(54,54): warning C4183: 'HK_DECLARE_CLASS': missing return type; assumed to be a member function returning 'int'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(57,19): error C2653: 'hk': is not a class or namespace name
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(57,23): error C2061: syntax error: identifier 'AddOptional'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(57,127): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(57,127): warning C4183: 'HK_RECORD_ATTR': missing return type; assumed to be a member function returning 'int'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(58,19): error C2653: 'hk': is not a class or namespace name
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(58,23): error C2061: syntax error: identifier 'AddOptional'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(58,123): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(58,123): error C2535: 'int hkReferencedObject::HK_RECORD_ATTR(void)': member function already defined or declared
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(57): message : see declaration of 'hkReferencedObject::HK_RECORD_ATTR'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(58,123): warning C4183: 'HK_RECORD_ATTR': missing return type; assumed to be a member function returning 'int'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(141,30): error C3646: 'HK_ATTR': unknown override specifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(141,38): error C2653: 'hk': is not a class or namespace name
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(141,42): error C2065: 'Untracked': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(141,51): error C2096: 'm_memSizeAndFlags': A data member cannot be initialized with a parenthesized initializer
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(141,37): error C2131: expression did not evaluate to a constant
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(141,42): message : a non-constant (sub-)expression was encountered
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(143,23): error C3646: 'HK_ATTR': unknown override specifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(143,31): error C2653: 'hk': is not a class or namespace name
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(143,35): error C2065: 'Untracked': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(143,44): error C2096: 'm_refCount': A data member cannot be initialized with a parenthesized initializer
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(143,30): error C2131: expression did not evaluate to a constant
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(143,35): message : a non-constant (sub-)expression was encountered
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(109,54): error C2760: syntax error: unexpected token ',', expected 'expression'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(107,23): message : This diagnostic occurred in the compiler generated function 'void hkReferencedObject::addReferences(const T *const *,int,int)'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(118,54): error C2760: syntax error: unexpected token ',', expected 'expression'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.h(116,23): message : This diagnostic occurred in the compiler generated function 'void hkReferencedObject::removeReferences(const T *const *,int,int)'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.inl(9,37): error C2027: use of undefined type 'hkMemoryRouter'
C:\localDev\sansar\Code\App\Libraries\EngineSimulation\System.h(19): message : see declaration of 'hkMemoryRouter'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.inl(54,2): error C3861: 'HK_ASSERT': identifier not found
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Object\hkReferencedObject.inl(63,2): error C3861: 'HK_ASSERT': identifier not found
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(67,43): error C2760: syntax error: unexpected token '<', expected 'declaration'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(75,19): error C2653: 'hkTrait': is not a class or namespace name
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(115): message : see reference to class template instantiation 'hkEndian::EndianType<NativeType,LittleEndian>' being compiled
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(75,43): error C2143: syntax error: missing ';' before '<'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(75,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(75,57): error C2039: 'IntType': is not a member of '`global namespace''
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(75,49): error C2238: unexpected token(s) preceding ';'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(76,19): error C2653: 'hkTrait': is not a class or namespace name
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(76,37): error C2143: syntax error: missing ';' before '<'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(76,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(76,71): error C2039: 'Type': is not a member of '`global namespace''
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(76,1): error C2238: unexpected token(s) preceding ';'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(77,20): error C3646: '_swap': unknown override specifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(77,26): error C2065: 'Storage': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(77,34): error C2146: syntax error: missing ')' before identifier 'src'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(77,39): error C2653: 'hkTrait': is not a class or namespace name
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(77,1): error C2334: unexpected token(s) preceding '{'; skipping apparent function body
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(78,20): error C3646: '_swap': unknown override specifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(78,26): error C2065: 'Storage': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(78,34): error C2146: syntax error: missing ')' before identifier 'src'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(78,39): error C2653: 'hkTrait': is not a class or namespace name
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(78,1): error C2334: unexpected token(s) preceding '{'; skipping apparent function body
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(79,20): error C3646: '_swap': unknown override specifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(79,26): error C2065: 'Storage': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(79,34): error C2146: syntax error: missing ')' before identifier 'src'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(79,1): error C2334: unexpected token(s) preceding '{'; skipping apparent function body
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(114,10): error C3646: 'm_val': unknown override specifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(114,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(96,3): error C3861: 'm_val': identifier not found
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(94,7): message : This diagnostic occurred in the compiler generated function 'void hkEndian::EndianType<NativeType,LittleEndian>::operator =(T)'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(96,18): error C2065: 'hkBitCast': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(94,7): message : This diagnostic occurred in the compiler generated function 'void hkEndian::EndianType<NativeType,LittleEndian>::operator =(T)'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(96,18): error C3861: 'hkBitCast': identifier not found
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(94,7): message : This diagnostic occurred in the compiler generated function 'void hkEndian::EndianType<NativeType,LittleEndian>::operator =(T)'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(96,28): error C3861: 'Storage': identifier not found
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(94,7): message : This diagnostic occurred in the compiler generated function 'void hkEndian::EndianType<NativeType,LittleEndian>::operator =(T)'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(96,38): error C3861: 'hkLosslessCast': identifier not found
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(94,7): message : This diagnostic occurred in the compiler generated function 'void hkEndian::EndianType<NativeType,LittleEndian>::operator =(T)'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(101,10): error C3861: 'm_val': identifier not found
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(107,11): error C2760: syntax error: unexpected token 'identifier', expected ';'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(105,9): message : This diagnostic occurred in the compiler generated function 'hkEndian::EndianType<NativeType,LittleEndian>::operator T(void) const'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,48): error C3856: 'IsSigned': symbol is not a class template
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,71): error C2065: 'NT': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,74): error C2065: 'LE': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,60): error C2923: 'hkEndian::EndianType': 'NT' is not a valid template type argument for parameter 'NativeType'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121): message : see declaration of 'NT'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,33): error C2975: 'LittleEndian': invalid template argument for 'hkEndian::EndianType', expected compile-time constant expression
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(71): message : see declaration of 'LittleEndian'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,60): error C2143: syntax error: missing ';' before 'hkEndian::EndianType'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,78): error C2059: syntax error: '>'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,82): error C2059: syntax error: 'public'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,98): error C2065: 'NT': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,89): error C2923: 'hkTrait::IsSigned': 'NT' is not a valid template type argument for parameter 'NT'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121): message : see declaration of 'NT'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,89): error C2976: 'hkTrait::IsSigned': too few template arguments
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121): message : see declaration of 'hkTrait::IsSigned'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,102): error C2143: syntax error: missing ';' before '{'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(121,102): error C2447: '{': missing function header (old-style formal list?)
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2059: syntax error: ','
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2374: 'HK_REFLECTIONOF_SPECIALIZE': redefinition; multiple initialization
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137): message : see declaration of 'HK_REFLECTIONOF_SPECIALIZE'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C3856: 'ReflectionOf': symbol is not a class template
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C3083: 'Typedef': the symbol to the left of a '::' must be a type
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2039: 'hkInt8_Tag': is not a member of 'hkReflect'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137): message : see declaration of 'hkReflect'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2065: 'hkInt8_Tag': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2065: 'LE': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2923: 'hkEndian::EndianType': 'hkInt8_Tag' is not a valid template type argument for parameter 'NativeType'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137): message : see declaration of 'hkInt8_Tag'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2975: 'LittleEndian': invalid template argument for 'hkEndian::EndianType', expected compile-time constant expression
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(71): message : see declaration of 'LittleEndian'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2143: syntax error: missing ';' before 'hkEndian::EndianType'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2059: syntax error: '>'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2059: syntax error: 'public'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2993: 'hkEndian::EndianType<hkInt8,false>': is not a valid type for non-type template parameter 'LE'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2143: syntax error: missing ';' before '{'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137,1): error C2447: '{': missing function header (old-style formal list?)
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138,1): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138,1): error C2374: 'HK_REFLECTIONOF_SPECIALIZE': redefinition; multiple initialization
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(137): message : see declaration of 'HK_REFLECTIONOF_SPECIALIZE'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138,1): error C2059: syntax error: ','
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138,1): error C3083: 'Typedef': the symbol to the left of a '::' must be a type
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138,1): error C2039: 'hkUint8_Tag': is not a member of 'hkReflect'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138): message : see declaration of 'hkReflect'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138,1): error C2065: 'hkUint8_Tag': undeclared identifier
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138,1): error C2923: 'hkEndian::EndianType': 'hkUint8_Tag' is not a valid template type argument for parameter 'NativeType'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138): message : see declaration of 'hkUint8_Tag'
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Types\hkEndian.h(138,1): fatal error C1003: error count exceeds 100; stopping compilation
