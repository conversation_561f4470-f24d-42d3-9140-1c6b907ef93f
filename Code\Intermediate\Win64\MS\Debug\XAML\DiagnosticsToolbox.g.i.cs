﻿#pragma checksum "DiagnosticsToolbox.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "9F577FD9B3EB8B92B7BCC47724AE8D0AE59DA82363867F0F3490659ABB939B80"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// DiagnosticsToolboxControl
    /// </summary>
    public partial class DiagnosticsToolboxControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 10 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.DiagnosticsToolboxControl Root;
        
        #line default
        #line hidden
        
        
        #line 24 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FrameRateBorder;
        
        #line default
        #line hidden
        
        
        #line 34 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FrameRate;
        
        #line default
        #line hidden
        
        
        #line 47 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel FrameDuration;
        
        #line default
        #line hidden
        
        
        #line 67 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border DrawsBorder;
        
        #line default
        #line hidden
        
        
        #line 77 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel Draws;
        
        #line default
        #line hidden
        
        
        #line 90 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel Primitives;
        
        #line default
        #line hidden
        
        
        #line 110 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FootprintBorder;
        
        #line default
        #line hidden
        
        
        #line 120 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel GeometryFootprint;
        
        #line default
        #line hidden
        
        
        #line 133 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel TextureFootprint;
        
        #line default
        #line hidden
        
        
        #line 155 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem Normal;
        
        #line default
        #line hidden
        
        
        #line 156 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem LightingComplexity;
        
        #line default
        #line hidden
        
        
        #line 157 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem OverdrawComplexity;
        
        #line default
        #line hidden
        
        
        #line 158 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem TriangleDensity;
        
        #line default
        #line hidden
        
        
        #line 159 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem Backfaces;
        
        #line default
        #line hidden
        
        
        #line 160 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem Wireframe;
        
        #line default
        #line hidden
        
        
        #line 161 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem Unlit;
        
        #line default
        #line hidden
        
        
        #line 162 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem Normals;
        
        #line default
        #line hidden
        
        
        #line 163 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem Scale;
        
        #line default
        #line hidden
        
        
        #line 164 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBoxItem Radiance;
        
        #line default
        #line hidden
        
        
        #line 168 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SetTriangleDensityModeBorder;
        
        #line default
        #line hidden
        
        
        #line 169 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton SetTriangleDensityMode;
        
        #line default
        #line hidden
        
        
        #line 181 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SetLightingComplexityModeBorder;
        
        #line default
        #line hidden
        
        
        #line 182 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton SetLightingComplexityMode;
        
        #line default
        #line hidden
        
        
        #line 194 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SetLightingVisibilityBorder;
        
        #line default
        #line hidden
        
        
        #line 195 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton SetLightingVisibility;
        
        #line default
        #line hidden
        
        
        #line 210 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SetSensitivityBorder;
        
        #line default
        #line hidden
        
        
        #line 211 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton SetSensitivity;
        
        #line default
        #line hidden
        
        
        #line 233 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup FrameRateToolTip;
        
        #line default
        #line hidden
        
        
        #line 238 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup FrameDurationToolTip;
        
        #line default
        #line hidden
        
        
        #line 243 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup DrawCallsToolTip;
        
        #line default
        #line hidden
        
        
        #line 248 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup PrimitivesToolTip;
        
        #line default
        #line hidden
        
        
        #line 255 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup GometryFootprintToolTip;
        
        #line default
        #line hidden
        
        
        #line 260 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup TextureFootprintToolTip;
        
        #line default
        #line hidden
        
        
        #line 265 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SetLightingVisibilityToolTip;
        
        #line default
        #line hidden
        
        
        #line 269 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SetLightingVisibilityPopup;
        
        #line default
        #line hidden
        
        
        #line 287 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SetSensitivityToolTip;
        
        #line default
        #line hidden
        
        
        #line 291 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SetSensitivityPopup;
        
        #line default
        #line hidden
        
        
        #line 309 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SetTriangleDensityModeToolTip;
        
        #line default
        #line hidden
        
        
        #line 312 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SetTriangleDensityModePopup;
        
        #line default
        #line hidden
        
        
        #line 320 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem ScreenSpaceTriangleDensity;
        
        #line default
        #line hidden
        
        
        #line 326 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem WorldSpaceTriangleDensity;
        
        #line default
        #line hidden
        
        
        #line 337 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SetLightingComplexityModeToolTip;
        
        #line default
        #line hidden
        
        
        #line 340 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup SetLightingComplexityModePopup;
        
        #line default
        #line hidden
        
        
        #line 348 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem AllLights;
        
        #line default
        #line hidden
        
        
        #line 354 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem AllShadowCasters;
        
        #line default
        #line hidden
        
        
        #line 360 "DiagnosticsToolbox.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBoxItem ActiveShadowCasters;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/diagnostics/diagnosticstoolbox.xaml", System.UriKind.Relative);
            
            #line 1 "DiagnosticsToolbox.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Root = ((LindenLab.DiagnosticsToolboxControl)(target));
            return;
            case 2:
            this.FrameRateBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.FrameRate = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            this.FrameDuration = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            this.DrawsBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.Draws = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.Primitives = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.FootprintBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.GeometryFootprint = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.TextureFootprint = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.Normal = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 12:
            this.LightingComplexity = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 13:
            this.OverdrawComplexity = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 14:
            this.TriangleDensity = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 15:
            this.Backfaces = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 16:
            this.Wireframe = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 17:
            this.Unlit = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 18:
            this.Normals = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 19:
            this.Scale = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 20:
            this.Radiance = ((System.Windows.Controls.ComboBoxItem)(target));
            return;
            case 21:
            this.SetTriangleDensityModeBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 22:
            this.SetTriangleDensityMode = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 23:
            this.SetLightingComplexityModeBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 24:
            this.SetLightingComplexityMode = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 25:
            this.SetLightingVisibilityBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 26:
            this.SetLightingVisibility = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 27:
            this.SetSensitivityBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 28:
            this.SetSensitivity = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 29:
            this.FrameRateToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 30:
            this.FrameDurationToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 31:
            this.DrawCallsToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 32:
            this.PrimitivesToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 33:
            this.GometryFootprintToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 34:
            this.TextureFootprintToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 35:
            this.SetLightingVisibilityToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 36:
            this.SetLightingVisibilityPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 37:
            this.SetSensitivityToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 38:
            this.SetSensitivityPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 39:
            this.SetTriangleDensityModeToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 40:
            this.SetTriangleDensityModePopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 41:
            this.ScreenSpaceTriangleDensity = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 42:
            this.WorldSpaceTriangleDensity = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 43:
            this.SetLightingComplexityModeToolTip = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 44:
            this.SetLightingComplexityModePopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 45:
            this.AllLights = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 46:
            this.AllShadowCasters = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            case 47:
            this.ActiveShadowCasters = ((System.Windows.Controls.ListBoxItem)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

