﻿  unity_JX1AKI9GDPH5GFLL.cpp
C:\localDev\sansar\Code\Common\Libraries\LLCore\Vector4.h(17,1): error C2011: 'LLCore::Vector4': 'class' type redefinition
C:\localDev\sansar\Code\Common\Libraries\LLCore\Vector4.inl(14): message : see declaration of 'LLCore::Vector4'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(15,1): error C2011: 'LLCore::Quaternion': 'class' type redefinition
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(153,21): error C2084: function 'LLCore::PartialOrder LLCore::OrderTypeOverload(const LLCore::Quaternion &,const LLCore::Quaternion &)' already has a body
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(153): message : see previous definition of 'OrderTypeOverload'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(155,25): error C2027: use of undefined type 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(155,42): error C2027: use of undefined type 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(14): message : see declaration of 'LLCore::Quaternion'
C:\localDev\sansar\Code\Common\Libraries\LLCore\Quaternion.inl(155,54): error C2661: 'LLCore::OrderType': no overloaded function takes 1 arguments
C:\localDev\sansar\Code\External\Havok\Source\Common\Base\Thread\TaskQueue\Default\hkDefaultTaskQueue.h(14,10): fatal error C1083: Cannot open include file: 'Common/Base/Thread/TaskQueue/hkTaskQueue.h': No such file or directory
