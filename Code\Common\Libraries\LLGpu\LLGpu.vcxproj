﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Android">
      <Configuration>Debug</Configuration>
      <Platform>Android</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Production|Android">
      <Configuration>Production</Configuration>
      <Platform>Android</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|_Windows">
      <Configuration>Debug</Configuration>
      <Platform>_Windows</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Production|_Windows">
      <Configuration>Production</Configuration>
      <Platform>_Windows</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Linux">
      <Configuration>Debug</Configuration>
      <Platform>Linux</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Production|Linux">
      <Configuration>Production</Configuration>
      <Platform>Linux</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|iOS">
      <Configuration>Debug</Configuration>
      <Platform>iOS</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Production|iOS">
      <Configuration>Production</Configuration>
      <Platform>iOS</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Buffer.h" />
    <ClInclude Include="Bundle.h" />
    <ClInclude Include="CommandAllocator.h" />
    <ClInclude Include="CommandSignature.h" />
    <ClInclude Include="ComputePipelineState.h" />
    <ClInclude Include="D3D11\D3D11Buffer.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Bundle.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11CommandAllocator.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11CommandQueue.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11CommandSignature.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11ComputePipelineState.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11ConstantBufferView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11DepthStencilView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Device.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Fence.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Framebuffer.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11GpuTracker.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11GraphicsCommandList.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11GraphicsPipelineState.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11ResourceBarrier.h" />
    <ClInclude Include="D3D11\D3D11PipelineState.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Platform.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11QueryHeap.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Resource.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11ResourceCommandList.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11RootSignature.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11SamplerState.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11ShaderResourceView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11SwapChain.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Texture2d.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Texture3d.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11UnorderedAccessView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11VertexArray.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11RenderTargetView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11DescriptorHeap.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Buffer.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Bundle.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12CommandAllocator.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12CommandQueue.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12CommandSignature.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ComputePipelineState.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ConstantBufferView.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12DepthStencilView.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12DescriptorHeap.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Device.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Fence.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Framebuffer.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12GpuRefCounted.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12GpuTracker.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12GraphicsCommandList.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12GraphicsPipelineState.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ResourceBarrier.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12PipelineState.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Platform.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12QueryHeap.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12RenderTargetView.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Resource.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ResourceCommandList.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12RootSignature.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12SamplerState.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ShaderResourceView.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12SwapChain.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Texture2d.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Texture3d.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12UnorderedAccessView.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12VertexArray.h">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="DescriptorHeap.h" />
    <ClInclude Include="Framebuffer.h" />
    <ClInclude Include="GL\GLBundle.h" />
    <ClInclude Include="GL\GLCommandSignature.h" />
    <ClInclude Include="GL\GLConstantBufferView.h" />
    <ClInclude Include="GL\GLFence.h" />
    <ClInclude Include="GL\GLGpuRefCounted.h" />
    <ClInclude Include="GL\GLGpuTracker.h" />
    <ClInclude Include="GL\GLResourceBarrier.h" />
    <ClInclude Include="GL\GLPipelineState.h" />
    <ClInclude Include="GL\GLQueryHeap.h" />
    <ClInclude Include="GL\Linux\GLLinuxSwapChain.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="HDR.h" />
    <ClInclude Include="HDRHelpers.h" />
    <ClInclude Include="Metal\MetalBundle.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalCommandSignature.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalConstantBufferView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalFence.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalResourceBarrier.h" />
    <ClInclude Include="Metal\MetalPipelineState.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalQueryHeap.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="ResourceBarrier.h" />
    <ClInclude Include="StatKeeper.h" />
    <ClInclude Include="Texture3d.h" />
    <ClInclude Include="Typedefs.h" />
    <ClInclude Include="Device.h" />
    <ClInclude Include="GL\GLBuffer.h" />
    <ClInclude Include="GL\GLCommandAllocator.h" />
    <ClInclude Include="GL\GLCommandQueue.h" />
    <ClInclude Include="GL\GLComputePipelineState.h" />
    <ClInclude Include="GL\GLDepthStencilView.h" />
    <ClInclude Include="GL\GLDescriptorHeap.h" />
    <ClInclude Include="GL\GLDevice.h" />
    <ClInclude Include="GL\GLFramebuffer.h" />
    <ClInclude Include="GL\GLGraphicsCommandList.h" />
    <ClInclude Include="GL\GLGraphicsPipelineState.h" />
    <ClInclude Include="GL\GLPlatform.h" />
    <ClInclude Include="GL\GLRenderTargetView.h" />
    <ClInclude Include="GL\GLResource.h" />
    <ClInclude Include="GL\GLResourceCommandList.h" />
    <ClInclude Include="GL\GLRootSignature.h" />
    <ClInclude Include="GL\GLSamplerState.h" />
    <ClInclude Include="GL\GLShaderResourceView.h" />
    <ClInclude Include="GL\GLSwapChain.h" />
    <ClInclude Include="GL\GLTexture2d.h" />
    <ClInclude Include="GL\GLTexture3d.h" />
    <ClInclude Include="GL\GLUnorderedAccessView.h" />
    <ClInclude Include="GL\GLVertexArray.h" />
    <ClInclude Include="GL\Ios\GLIosSwapChain.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="GL\Win\GLWinSwapChain.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="GraphicsPipelineState.h" />
    <ClInclude Include="Metal\MetalBuffer.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalCommandAllocator.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalCommandQueue.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalComputePipelineState.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalDepthStencilView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalDescriptorHeap.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalDevice.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalFramebuffer.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalGpuRefCounted.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalGpuTracker.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalGraphicsCommandList.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalGraphicsPipelineState.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalPlatform.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalRenderTargetView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalResource.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalResourceCommandList.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalRootSignature.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalSamplerState.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalShaderResourceView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalSwapChain.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalTexture2d.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalTexture3d.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalUnorderedAccessView.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Metal\MetalVertexArray.h">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </ClInclude>
    <ClInclude Include="Portable.h" />
    <ClInclude Include="GpuResource.h" />
    <ClInclude Include="SamplerState.h" />
    <ClInclude Include="ShaderResourceView.h" />
    <ClInclude Include="SwapChain.h" />
    <ClInclude Include="Texture2d.h" />
    <ClInclude Include="VertexArray.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LLGraphicsGems\LLGraphicsGems.vcxproj">
      <Project>{96025863-f1f6-48d9-bbbc-3954746de91c}</Project>
    </ProjectReference>
    <ProjectReference Include="..\LLProfile\LLProfile.vcxproj">
      <Project>{5c55a1ec-5337-484d-a7ac-591c7527be82}</Project>
    </ProjectReference>
    <ProjectReference Include="..\LLWindow\LLWindow.vcxproj">
      <Project>{fe81548f-f603-4c96-8e2b-667e53100143}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D3D11\D3D11Buffer.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Bundle.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11CommandAllocator.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11CommandQueue.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11CommandSignature.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11ComputePipelineState.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11ConstantBufferView.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11DepthStencilView.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Device.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Fence.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Framebuffer.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11GpuTracker.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11GraphicsCommandList.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11GraphicsPipelineState.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11PipelineState.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Platform.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11QueryHeap.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Resource.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11ResourceCommandList.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11RootSignature.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11SamplerState.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11ShaderResourceView.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11SwapChain.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Texture2d.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Texture3d.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11UnorderedAccessView.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11VertexArray.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11RenderTargetView.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11DescriptorHeap.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Buffer.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Bundle.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12CommandAllocator.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12CommandQueue.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12CommandSignature.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12ComputePipelineState.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12ConstantBufferView.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12DepthStencilView.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12DescriptorHeap.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Device.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Fence.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Framebuffer.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12GpuRefCounted.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12GpuTracker.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12GraphicsCommandList.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12GraphicsPipelineState.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12PipelineState.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Platform.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12QueryHeap.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12RenderTargetView.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Resource.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12ResourceCommandList.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12RootSignature.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12SamplerState.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12ShaderResourceView.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12SwapChain.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Texture2d.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Texture3d.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12UnorderedAccessView.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12VertexArray.cpp">
      <ExcludedFromBuild Condition="'$(D3D12Exists)'=='false'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="GL\GLBuffer.cpp" />
    <ClCompile Include="GL\GLBundle.cpp" />
    <ClCompile Include="GL\GLCommandAllocator.cpp" />
    <ClCompile Include="GL\GLCommandQueue.cpp" />
    <ClCompile Include="GL\GLCommandSignature.cpp" />
    <ClCompile Include="GL\GLComputePipelineState.cpp" />
    <ClCompile Include="GL\GLConstantBufferView.cpp" />
    <ClCompile Include="GL\GLDepthStencilView.cpp" />
    <ClCompile Include="GL\GLDescriptorHeap.cpp" />
    <ClCompile Include="GL\GLDevice.cpp" />
    <ClCompile Include="GL\GLFence.cpp" />
    <ClCompile Include="GL\GLFramebuffer.cpp" />
    <ClCompile Include="GL\GLGlewWrapper.cpp" />
    <ClCompile Include="GL\GLGpuRefCounted.cpp" />
    <ClCompile Include="GL\GLGpuTracker.cpp" />
    <ClCompile Include="GL\GLGraphicsCommandList.cpp" />
    <ClCompile Include="GL\GLGraphicsPipelineState.cpp" />
    <ClCompile Include="GL\GLPipelineState.cpp" />
    <ClCompile Include="GL\GLPlatform.cpp" />
    <ClCompile Include="GL\GLQueryHeap.cpp" />
    <ClCompile Include="GL\GLRenderTargetView.cpp" />
    <ClCompile Include="GL\GLResource.cpp" />
    <ClCompile Include="GL\GLResourceCommandList.cpp" />
    <ClCompile Include="GL\GLRootSignature.cpp" />
    <ClCompile Include="GL\GLSamplerState.cpp" />
    <ClCompile Include="GL\GLShaderResourceView.cpp" />
    <ClCompile Include="GL\GLSwapChain.cpp" />
    <ClCompile Include="GL\GLTexture2d.cpp" />
    <ClCompile Include="GL\GLTexture3d.cpp" />
    <ClCompile Include="GL\GLUnorderedAccessView.cpp" />
    <ClCompile Include="GL\GLVertexArray.cpp" />
    <ClCompile Include="GL\Linux\GLLinuxSwapChain.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="HDR.cpp" />
    <ClCompile Include="HDRHelpers.cpp" />
    <ClCompile Include="Portable.cpp" />
    <ClCompile Include="StatKeeper.cpp" />
    <None Include="..\..\..\..\Runtime\d3dcompiler_47.dll">
      <DeploymentContent>true</DeploymentContent>
      <DeploymentContent Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">false</DeploymentContent>
      <DeploymentContent Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">false</DeploymentContent>
    </None>
    <None Include="Metal\MetalBundle.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalCommandAllocator.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalCommandSignature.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalComputePipelineState.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalConstantBufferView.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalDepthStencilView.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalDescriptorHeap.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalFence.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalPipelineState.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalPlatform.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="GL\Ios\GLIosSwapChain.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <ClCompile Include="GL\Win\GLWinSwapChain.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </ClCompile>
    <None Include="Metal\MetalBuffer.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalCommandQueue.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalDevice.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalFramebuffer.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalGpuRefCounted.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalGpuTracker.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalGraphicsCommandList.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalGraphicsPipelineState.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalRenderTargetView.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalResource.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalResourceCommandList.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalRootSignature.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalSamplerState.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalShaderResourceView.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalSwapChain.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalTexture2d.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalTexture3d.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalUnorderedAccessView.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
    </None>
    <None Include="Metal\MetalVertexArray.mm">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|Linux'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">true</ExcludedFromBuild>
    </None>
    <None Include="Portable.inl" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E126F994-C764-4341-AC75-43ECC6B8B650}</ProjectGuid>
    <Keyword Condition="'$(Platform)'=='_Windows'">Win32Proj</Keyword>
    <Keyword Condition="'$(Platform)'=='Android'">Android</Keyword>
    <RootNamespace>LLGpu</RootNamespace>
    <WindowsTargetPlatformVersion>10.0.20348.0</WindowsTargetPlatformVersion>
    <ApplicationType Condition="'$(Platform)'=='Android' and '$(DesignTimebuild)'!='true'">Android</ApplicationType>
    <ApplicationTypeRevision Condition="'$(Platform)'=='Android'">3.0</ApplicationTypeRevision>
  </PropertyGroup>
  <Import Project="..\FindRoot.props" />
  <Import Project="$(LLRootDir)Build\MSBuild\Linden.Cpp.Default.props" />
  <PropertyGroup Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset Condition="'$(Platform)'!='Android'">v142</PlatformToolset>
    <PlatformToolset Condition="'$(Platform)'=='Android'">Clang_5_0</PlatformToolset>
    <UseNativeEnvironment>true</UseNativeEnvironment>
    <EnableUnitySupport Condition="'$(EnableUnitySupport)' == ''">true</EnableUnitySupport>
  </PropertyGroup>
  <Import Project="$(LLRootDir)Build\MSBuild\Linden.Cpp.props" />
  <Import Project="$(LLAdditionalCppProps)" Condition="'$(LLAdditionalCppProps)' != ''" />
  <ImportGroup Label="PropertySheets">
    <Import Project="D3D12.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug' and '$(Platform)'=='_Windows'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'!='Debug' and '$(Platform)'=='_Windows'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <PreprocessorDefinitions>GLEW_STATIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Lib>
    </Lib>
    <Lib>
      <AdditionalDependencies Condition="'$(Configuration)|$(Platform)'=='Debug|iOS'">Metal.framework;OpenGLES.framework</AdditionalDependencies>
    </Lib>
    <Lib>
      <AdditionalDependencies Condition="'$(Configuration)|$(Platform)'=='Production|iOS'">Metal.framework;OpenGLES.framework</AdditionalDependencies>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Platform)'=='_Windows'">
    <Lib>
      <AdditionalDependencies>opengl32.lib</AdditionalDependencies>
    </Lib>
    <ClCompile>
    </ClCompile>
    <ClCompile>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Debug|_Windows'">$(LLRootDir)External\glew\include\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <ClCompile>
    </ClCompile>
    <ClCompile>
      <AdditionalIncludeDirectories Condition="'$(Configuration)|$(Platform)'=='Production|_Windows'">$(LLRootDir)External\glew\include\;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <ClCompile>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Platform)'=='Linux'">
    <Lib>
      <AdditionalDynamicDependencies>GL;GLEWmx;glut;X11;dl</AdditionalDynamicDependencies>
    </Lib>
    <ClCompile>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Platform)'=='iOS'">
    <Lib>
      <AdditionalDependencies>OpenGLES.framework;QuartzCore.framework;CoreGraphics.framework</AdditionalDependencies>
    </Lib>
    <ClCompile>
    </ClCompile>
  </ItemDefinitionGroup>
  <Import Project="$(LLRootDir)Build\MSBuild\Linden.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>