#include "Generated/Render_High_PostProcessProgram.hlsli"
#include "Generated/Render_High_PostProcessHDRProgram.hlsli"
#include "Generated/Render_Low_PostProcessProgram.hlsli"
#include "Generated/Render_Low_PostProcessHDRProgram.hlsli"
#include "../Common/MathUtil.hlsli"
#include "../Common/ColorUtil.hlsli"

#ifdef HDR
#include "../Common/HDRUtil.hlsli"
#include "../Common/ICCCorrection.hlsli"
#endif

struct VertexOutput
{
    float4 positionSv : SV_Position;
    float2 uv : TEXCOORD0;
};

VertexOutput VertexMain(uint vertexId : SV_VertexID)
{
    VertexOutput output;
    output.positionSv = vertexIdToFullScreenTrianglePosition(vertexId);
    output.uv = output.positionSv.xy * float2(0.5, -0.5) + 0.5;
    return output;
}

inline float2 CalcualteFluidDistortion(float2 uv, float depth)
{
    float localTimeSeconds = WorldInfo(WorldTimeSeconds) * min(1 - depth, 1);

    float X = uv.x * 25.0 + localTimeSeconds;
    float Y = uv.y * 25.0 + localTimeSeconds;
    uv.y += cos(X + Y) * depth * cos(Y);
    uv.x += sin(X - Y) * depth * sin(Y);

    return uv;
}

float triangleNoise(float2 pixelLoc, uint seed)
{
    float result;
    result = PostProcessParams(Noise)[frac(pixelLoc.xy / PostProcessParams(NoiseResolution) + generateWeyl(0., (seed - 0) % 256)) * PostProcessParams(NoiseResolution)].a;
    result -= PostProcessParams(Noise)[frac(pixelLoc.xy / PostProcessParams(NoiseResolution) + generateWeyl(0., (seed - 1) % 256)) * PostProcessParams(NoiseResolution)].a;
    return result;
}

float3 aces(float3 x)
{
    x *= 1.5; // legacy built-in exposure bias
    float a = 2.51f;
    float b = 0.03f;
    float c = 2.43f;
    float d = 0.59f;
    float e = 0.14f;
    return saturate((x * (a * x + b)) / (x * (c * x + d) + e));
}

float3 smoothClamp(float3 x, float3 a, float3 b)
{
    return lerp(a, b, smoothstep(a, b, x));
}

float3 performDithering(float2 uv, float2 screenCoord, float3 color) // kill banding...
{
    float dither = triangleNoise(screenCoord, CameraInfo(FrameNumber));

    color = linearToSrgb(color);
    dither *= (0.5 + 0.5 * saturate(127. * 2. * min(color, 1.))).x; // handle near 0 to avoid losing blacks
    color += dither / 127.;
    color = srgbToLinear(max(color, 0.));

    color *= (abs(uv.x - 0.5) >= PostProcessParams(SeparatorWidth));

    return color;
}


PixelOutput PixelMain(VertexOutput input, float4 screenCoord : SV_Position)
{
    PixelOutput output;

    half4 source = PostProcessParams(Source).Load(int3(screenCoord.xy, 0));


    // Emissive Trails effect
    half4 trails = PostProcessParams(Trails).Load(int3(screenCoord.xy, 0));
    source.rgb = lerp(source.rgb, trails.rgb, trails.a);


    // PostFX volume underwater effect
    if (PostProcessParams(WaterEffect))
    {
        float2 postfxUv = input.uv;
        const float zFar = 20;
        const float zNear = 0.002;
        float depth = PostProcessParams(Depth).Load(int3(screenCoord.xy, 0)).x;
        float linearDepth = 2.0 * zNear * zFar / (zFar + zNear - (2.0 * depth - 1.0) * (zFar - zNear));

        if (!PostProcessParams(vrDisplay))
            postfxUv = CalcualteFluidDistortion(postfxUv, linearDepth);

        float3 swirl = 0.5 + 0.2 * cos(WorldInfo(WorldTimeSeconds) + postfxUv.xyx * 5.0 + float3(0, 2, 4));
        swirl = lerp((swirl.r).xxx * (swirl.g).xxx + (swirl.b).xxx, (1).xxx, 0.20);

        float fogDepth = min(depth * 128, 1);
        float fog = exp(-max(0., CameraInfo(RcpDisplayViewportSize).y / fogDepth));
        source.rgb *= lerp(float3(0.2, 0.4, 0.9) * swirl, (fog).xxx, fogDepth);
    }


    // Bloom
#ifdef CHEAPER
    float4 bloom = PostProcessParams(Bloom).Sample(LinearSampler, screenCoord.xy * CameraInfo(RcpDisplayTargetSize), 0);
    output.color0 = source;
    output.color0.rgb = lerp(output.color0.rgb, bloom.rgb, BloomParams(BloomStrength)*BloomParams(BloomStrength)); //0.35
#else
    float4 bloom1 = 0;
    bloom1 += 0.25 * PostProcessParams(Bloom).Sample(LinearSampler, (screenCoord.xy + 2 * float2(-0.5, -0.5)) * CameraInfo(RcpDisplayTargetSize), 0);
    bloom1 += 0.25 * PostProcessParams(Bloom).Sample(LinearSampler, (screenCoord.xy + 2 * float2(+0.5, -0.5)) * CameraInfo(RcpDisplayTargetSize), 0);
    bloom1 += 0.25 * PostProcessParams(Bloom).Sample(LinearSampler, (screenCoord.xy + 2 * float2(-0.5, +0.5)) * CameraInfo(RcpDisplayTargetSize), 0);
    bloom1 += 0.25 * PostProcessParams(Bloom).Sample(LinearSampler, (screenCoord.xy + 2 * float2(+0.5, +0.5)) * CameraInfo(RcpDisplayTargetSize), 0);

    float3 lowPass = 0;
    lowPass += 0.25 * PostProcessParams(Source).Sample(LinearSampler, (screenCoord.xy + float2(-0.25, -0.25)) * CameraInfo(RcpDisplayTargetSize), 0).rgb;
    lowPass += 0.25 * PostProcessParams(Source).Sample(LinearSampler, (screenCoord.xy + float2(+0.25, -0.25)) * CameraInfo(RcpDisplayTargetSize), 0).rgb;
    lowPass += 0.25 * PostProcessParams(Source).Sample(LinearSampler, (screenCoord.xy + float2(-0.25, +0.25)) * CameraInfo(RcpDisplayTargetSize), 0).rgb;
    lowPass += 0.25 * PostProcessParams(Source).Sample(LinearSampler, (screenCoord.xy + float2(+0.25, +0.25)) * CameraInfo(RcpDisplayTargetSize), 0).rgb;

    output.color0 = source;

    // sharpen
    output.color0.rgb += smoothClamp(PostProcessParams(Sharpen) * (source.rgb - lowPass.rgb),
        -output.color0.rgb * PostProcessParams(SharpenShockAllowance),
        +output.color0.rgb * PostProcessParams(SharpenShockAllowance));

    output.color0.rgb = lerp(output.color0.rgb, bloom1.rgb, BloomParams(BloomStrength) * BloomParams(BloomStrength)); //0.35

#endif

    output.color0.rgb *= PostProcessParams(ExposureInfo)[0];

    // saturation boost
    //float desat = (hsum(output.color0.rgb) - hmin(output.color0.rgb))*0.5;
    float desat;
    {
        float3 tmp = log2(output.color0.rgb + 0.00001);
        desat = exp2((hsum(tmp) - hmin(tmp)) * 0.5) - 0.00001;
    }
    if (PostEffectParams(SaturationBoost) < 0)
    {
        output.color0.rgb = lerp(desat, output.color0.rgb, PostEffectParams(SaturationBoost) + 1.);
    }
    else
    {
        output.color0.rgb = lerp(output.color0.rgb, lerp(desat, output.color0.rgb, 1. + PostEffectParams(SaturationBoost)), rcp(desat + 1.));
    }

    // brightness adjustment
    {
        float adjustment = (1. / 32.) * PostEffectParams(BrightnessAdjustment);
        float scaleFactor = rsqrt(1. + abs(adjustment) / desat);
        output.color0.rgb *= adjustment < 0. ? scaleFactor : rcp(scaleFactor);
    }


    //{
    //    //float gamma = pow(hproduct(1.0 - square(unormToSnorm(screenCoord.xy * CameraInfo(RcpDisplayTargetSize)))), -PostProcessParams(VignetteStrength));
    //    float gamma = rcp(1.0 - 0.5*PostProcessParams(VignetteStrength)*dotSelf(unormToSnorm(screenCoord.xy * CameraInfo(RcpDisplayTargetSize))));
    //    output.color0.rgb *= pow(hmin(output.color0.rgb), gamma - 1.);
    //}

    float3 tonemappedSDR, tonemappedHDR = (0).xxx;
    tonemappedSDR = acesFilmicCurve(max(output.color0.rgb, 0.), 3.3);
    tonemappedSDR = performDithering(input.uv, screenCoord.xy, tonemappedSDR);
#ifdef HDR
    tonemappedHDR = acesFilmicCurve(max(output.color0.rgb, 0.), 1.6);
    tonemappedHDR = performDithering(input.uv, screenCoord.xy, tonemappedHDR);

    tonemappedHDR = ACESFilmRec2020(tonemappedHDR);
    tonemappedHDR = scRGBtoBt2020(tonemappedHDR);

    // Adjust to whitepoint and renormalize
    tonemappedHDR *= PostProcessParams(HdrWhitePoint) * PostProcessParams(HdrPqNormalizationRcp);

    // Calculate the frame light level parameters
    float2 statistics = PostProcessParams(OutputStatistics)[0].xy;
    int contentFALL = int(statistics.x);
    int maxFALL = int(statistics.y);

    // FALL (Frame Average Light Level) adjustment
    tonemappedHDR = RemapFrameAverageLightLevelNormalized(tonemappedHDR, contentFALL, min(maxFALL, PostProcessParams(HdrMaxFALL)));

    // Pre-dithering adaptation to display's actual peak brightness
    float displayMaxNits = PostProcessParams(HdrMaxLuminance); // Convert from scRGB scale to nits
    if (maxFALL > displayMaxNits)
    {
        tonemappedHDR = adaptiveToneMapperNormalized(displayMaxNits, PostProcessParams(HdrWhitePoint), tonemappedHDR);
    }

    // Apply dithering to reduce banding artifacts
    const int displayType = 0; // 0 = OLED, 1 = LCD
    tonemappedHDR = EnhancedDisplayDithering(tonemappedHDR, input.uv, screenCoord, PostProcessParams(BlueNoise), displayType);

    // Final PQ encoding
    tonemappedHDR = ApplyREC2084Curve(tonemappedHDR);
#else
    tonemappedHDR = tonemappedSDR;
#endif

    output.color0 = half4(tonemappedHDR, 1.0f);
    output.color1 = half4(tonemappedSDR, 1.0f);
    return output;
}