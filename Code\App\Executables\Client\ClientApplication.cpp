/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANT<PERSON>ILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

// current library
#include "ClientApplication.h"

#include "EditMode.h"
#include "EngineSettingsManager.h"
#include "HomeSpace.h"
#include "UserInputHintController.h"

// other libraries
#include "LLAppService/AppServiceRegistry.h"
#include "LLApplication/ApplicationEmbeddedFileSystem.h"
#include "LLAssetSystem/AssetManager.h"
#include "LLAudio/AudioSystem.h"
#include "LLClientServer/Channel.h"
#include "LLCore/AsyncTimer.h"
#include "LLCore/HardwareInfo.h"
#include "LLCore/Parser.h"
#include "LLCore/ThreadMonitor.h"
#include "LLCore/Time.h"
#include "LLCore/UsingMath.h"
#include "LLCrashReporter/CrashReporter.h"
#include "LLDataModel/DataSyncRoot.h"
#include "LLGems/DebugDisplayManager.h"
#include "LLGems/TomlConfig.h"
#include "LLGraphicsGems/MathUtil.h"
#include "LLGraphicsRender/Metrics.h"
#include "LLInput/GamepadDeviceMapper.h"
#include "LLInput/GamepadInputManager.h"
#include "LLInput/InputBindingManager.h"
#include "LLInput/InputEvent.h"
#include "LLInput/KeyboardDeviceMapper.h"
#include "LLInput/WandDeviceMapper.h"
#include "LLLibpng/ImagePngWrapper.h"
#include "LLMedia/MediaThread.h"
#include "LLNetwork/TelnetServer.h"
#include "LLNvEncoder/NvEncoder.h"
#include "LLOodle/LLOodle.h"
#include "LLProfile/PerformanceStatsManager.h"
#include "LLSteamworks/SteamworksManager.h"
#include "LLTextureProcessing/PlatformTextureProcessing.h"
#include "LLUI/Clipboard.h"
#include "LLUI/UIEvents.h"
#include "LLUI/UISystem.h"
#include "LLUINoesis/NoesisBackend.h" // This is only here to support a HACK involving ClientUIManager.
#include "LLWindow/CursorDisplayManager.h"
#include "LLWindow/MouseDeviceMapper.h"
#include "LLCore/System.h"

#include "AppCore/SansarVersion.h"
#include "ClientAudio/ClientAudio.h"
#include "ClientEditLocal/LocalWorkspace.h"
#include "ClientEditLocal/LocalWorkspaceEvents.h"
#include "ClientInventory/BuiltInInventory.h"
#include "ClientInventory/Inventory.h"
#include "ClientInventory/LicenseService.h"
#include "ClientInventory/Marketplace.h"
#include "ClientInventory/OrdersService.h"
#include "ClientLogin/AuthenticationManager.h"
#include "ClientLogin/ClientVersionService.h"
#include "ClientLogin/CodeVerifierCache.h"
#include "ClientLogin/ExperimentConfig.h"
#include "ClientLogin/GoldFiz.h"
#include "ClientLogin/GoogleAnalytics.h"
#include "ClientLogin/LoginManager.h"
#include "ClientLogin/RegistrationMetrics.h"
#include "ClientLogin/RemoteConfig.h"
#include "ClientLogin/SubscriptionManager.h"
#include "ClientLogin/SubscriptionService.h"
#include "ClientServices/AssetImport.h"
#include "ClientServices/Atlas3Definitions.h"
#include "ClientServices/AtlasMetricEvents.h"
#include "ClientServices/ClientMetricsManager.h"
#include "ClientServices/CodexNotificationsManager.h"
#include "ClientServices/ExperienceManager.h"
#include "ClientServices/FtueControlsManager.h"
#include "ClientServices/GiftingManager.h"
#include "ClientServices/HttpConfig.h"
#include "ClientServices/KafkaManager.h"
#include "ClientServices/NotificationEvents.h"
#include "ClientServices/RelationshipManager.h"
#include "ClientServices/ScriptCompileClient.h"
#include "ClientServices/SocialEvents.h"
#include "ClientServices/ThumbnailManager.h"
#include "ClientServices/TippingManager.h"
#include "ClientServices/UserBalanceManager.h"
#include "ClientServices/UserProfileManager.h"
#include "ClientServices/VisitExperienceService.h"
#include "ClientServices/WebAtlasService.h"
#include "ClientServices/WebFrontend.h"
#include "ClientServices/WebImageManager.h"
#include "ClientUI/ClientUIEvents.h"
#include "ClientUI/ClientUIManager.h"
#include "ClientUI/ScreenshotCaptureEvents.h"
#include "ClientUI/ViewModel/Diagnostics/DiagnosticsEvents.h"
#include "ClientUI/ViewModel/Marketplace/MarketplaceCapabilitiesConverter.h"
#include "ClientUI/Views/ClientUIViewFactory.h"
#include "ClientVr/ClientVrEvents.h"
#include "ClientVr/ComfortZoneFilter.h"
#include "ClientVr/VrSystem.h"
#include "ClientVr/VrVisualizationLayer.h"
#include "EditAudio/EditWorldAudioEvents.h"
#include "EditCommon/PropertyReflection/DescriptorRegistry.h"
#include "EditWorkspace/EditWorkspace.h"
#include "Engine/CharacterController.h"
#include "Engine/DebugConsoleCommandManager.h"
#include "Engine/DebugEvents.h"
#include "Engine/Engine.h"
#include "Engine/EngineStreamRouter.h"
#include "Engine/FirstPersonCameraController.h"
#include "Engine/FlyCameraController.h"
#include "Engine/IKinemaDebugCommands.h"
#include "Engine/LocalAgentControllerManager.h"
#include "Engine/LocalAgentMessageHandler.h"
#include "Engine/OrbitCameraController.h"
#include "Engine/Reactions.h"
#include "Engine/RoomScaleCameraController.h"
#include "Engine/RuntimeCommonAssetManager.h"
#include "Engine/RuntimeWorld.h"
#include "Engine/ScriptCameraController.h"
#include "Engine/UserCharacter.h"
#include "EngineAudio/AudioSystem.h"
#include "EngineAudio/AudioWorld.h"
#include "EngineCommon/Capabilities.h"
#include "EngineRender/EngineRenderer.h"
#include "EngineRender/FrameContext.h"
#include "EngineRender/RenderWorld.h"
#include "EngineRender/TextureStreamingManager.h"
#include "EngineSimulation/RigidBodyComponentManager.h"
#include "EngineSimulation/VisualDebugger.h"
#include "RegionCommon/AvatarSkin.h"
#include "RegionCommon/ClientRegionMessages.h"
#include "RegionCommon/DebugServiceRequest.h"
#include "SansarHttp/SansarHttpManager.h"
#include "Twitch/TwitchEvents.h"

#ifdef CLIENT_USE_FFMPEG
#include "Streaming/StreamingMuxer.h"
#endif
#include "ClientScripts/ServerScriptsDefinitions.h"

#if defined(_WINDOWS_)
#error You accidentally included windows.h in some public header.  Please do not do that. -richard
#endif

//HACK to avoid pulling in Noesis headers
namespace ClientUI
{
namespace AudioTrigger
{
    void configureAudioTrigger(LLCore::EventQueue* eventQueue, LLUI::UIWorkspaceManager* workspaceManager);
}
namespace MetricsHeatmap
{
    void configureMetricsHeatmap(const LLCore::FileReference& heatmapJsonFile, LLUI::UIWorkspaceManager* workspaceManager);
    void toggleMetricsHeatmap();
    void destroyMetricsHeatmap();
} // namespace MetricsHeatmap
} // namespace ClientUI


namespace Client
{

void ClientApplication::UpdatePersonaRoot(const Identity::Persona* persona)
{
    LLCore::StringFixed<128> personaCache("USERCACHE:Persona-");
    if (persona == nullptr)
    {
        personaCache.append("Default/");
    }
    else
    {
        LLCore::StringFixed<64> personaId;
        persona->getId().asPackedString(&personaId);
        personaId.append("/");
        personaCache.append(personaId);
    }
    LLCore::CoreNativeFileSystem::GetInstance().addVirtualRoot("PERSONACACHE", LLCore::Path(personaCache));
    LLCore::CoreNativeFileSystem::GetInstance().createDirectory(LLCore::Path("PERSONACACHE:"_ll));
}

class ClientEmbeddedFileSystem : public LLCore::SingletonFacade<ClientEmbeddedFileSystem, LLApplication::ApplicationEmbeddedFileSystem>
{
};

LLCORE_REGISTER_SINGLETON_FACADE_CPP(LLCore::CoreSingletonRegistry, ClientEmbeddedFileSystem, LLApplication::ApplicationEmbeddedFileSystem);

bool ClientApplication::Configure(const LLCore::CommandLineArguments& args, LLCore::Allocator* configAllocator, Config* config)
{
    config->m_configSaveFile  = LLCore::Path("USERDATA:SansarClient.toml"_ll);
    config->m_configSaveDelay = 5_ll_sec;

    if (BaseType::Configure(args, configAllocator, config))
    {
        if (config->m_ui.m_enableUi == true && config->m_enableRenderer == true)
        {
            // note, depth-stencil is for Neosis UI
            config->m_graphics.m_windowSurface.m_depthStencilFormat = LLGraphicsResource::ColorFormat::cD32FloatS8X24Uint;
        }


        //HACK: slam window position to screen-centered with hardcoded dimensions
        if (config->m_enableRenderer && config->m_ignoreWindowPositionAndSize)
        {
            config->m_window.m_placement.m_x.m_relative = 0.f;
            config->m_window.m_placement.m_y.m_relative = 0.f;
            config->m_window.m_placement.m_width        = 1600;
            config->m_window.m_placement.m_height       = 900;
        }

        // APPCACHE is an OS level USERCACHE but not a sansar USER cache.
        // Only content which is guaranteed to be the same regardless of the grid connected to should be stored here
        LLCore::CoreNativeFileSystem::GetInstance().addVirtualRoot("APPCACHE", LLCore::Path("USERCACHE:App/"));

        // Add grid requested to USERCACHE and rebuild the path roots
        // This changes the definition of user in USERCACHE into something which is more representative of its usage in Sansar
        // Use USERCACHE for content which may differ depending on the grid connected, but will not differ between
        //  user identities who eventually log in
        if (config->m_apiService.m_grid.asRead().isEmpty() == false)
        {
            LLCore::StringFixed<64> gridCache("USERCACHE:");
            gridCache += config->m_apiService.m_grid;
            gridCache.append("/");
            LLCore::CoreNativeFileSystem::GetInstance().addVirtualRoot("USERCACHE", LLCore::Path(gridCache));
            LLCore::CoreNativeFileSystem::GetInstance().addVirtualRoot("LOG", LLCore::Path("USERCACHE:Log/"));
        }

        // PERSONACACHE is a different root depending on which persona is actually logged into Sansar
        // Use for content which may be different for each user logged in, cacheing user specific data for example
        UpdatePersonaRoot(nullptr);

        if (config->m_application.m_logging.m_logFilePath.isProvided() == false || config->m_stateLogFilePath.isProvided() == false)
        {
            LLCore::DateTime        nowDateTime;
            LLCore::StringFixed<64> dateTimePathBase;

            LLCore::ConvertSystemToDateTime(LLCore::SystemTime::Now(), &nowDateTime, false);

            // use a filesystem-safe, lexographical string representation of a datetime
            dateTimePathBase.format("LOG:%04d_%02d_%02d-%02d_%02d_%02d", nowDateTime.m_year, nowDateTime.m_month, nowDateTime.m_day, nowDateTime.m_hour, nowDateTime.m_minute, nowDateTime.m_second);

            if (config->m_application.m_logging.m_logFilePath.isProvided() == false)
            {
                LLCore::StringFixed<64> dateTimePath;
                dateTimePath.format("%s_%s.log ", dateTimePathBase, config->m_appName);
                config->m_application.m_logging.m_logFilePath = LLCore::Path(dateTimePath);
            }

            if (config->m_stateLogFilePath.isProvided() == false)
            {
                LLCore::StringFixed<64> dateTimePath;
                dateTimePath.format("%s_%sState.log ", dateTimePathBase, config->m_appName);
                config->m_stateLogFilePath = LLCore::Path(dateTimePath);
            }
        }

        // RememberMe is only enabled if the render is enabled and the config does not specify a userName or password.
        const bool isRememberMeEnabled = config->m_enableRenderer
                                         && config->m_login.m_userName.asRead().isEmpty()
                                         && config->m_login.m_password.asRead().isEmpty();
        config->m_rememberMe.m_isEnabled = config->m_rememberMe.m_isEnabled && isRememberMeEnabled;

        // only allow reading from RUNTIME
        LLCore::CoreNativeFileSystem::GetInstance().removeVirtualRoot("RUNTIME");
        LLCore::CoreNativeFileSystem::GetInstance().addVirtualRoot("RUNTIME", config->m_application.m_runtimeDirectory, LLCore::FileSystem::cAccessMode_Read);

        ClientEmbeddedFileSystem::GetInstance().addVirtualRoot("EMBEDDED", LLCore::Path{"!:"});
        LLApplication::ApplicationFileSystem::GetInstance().addFileSystemLast(ClientEmbeddedFileSystem::GetInstancePtr());

        return true;
    }

    return false;
}

void ClientApplication::checkForLowGraphics()
{
    if (m_config->m_enableRenderer == true)
    {
        bool hasDiscreteRam = false;
        bool hasLimitedDiscreteRam = false;

        LLCore::HardwareInfo::HardwareSnapshot hardwareSnapshot;
        LLCore::HardwareInfo::GetSnapshot(&hardwareSnapshot);
        for (mem_int i = 0; i < hardwareSnapshot.m_gpuInfo.getCount(); ++i)
        {
            LLCore::LogInfo("GraphicsCheck", "Testing card ", hardwareSnapshot.m_gpuInfo[i].m_name, " Ram ", hardwareSnapshot.m_gpuInfo[i].m_dedicatedVideoMemoryBytes);
            if (hardwareSnapshot.m_gpuInfo[i].m_dedicatedVideoMemoryBytes >= (uint64)1024 * 1024 * 1024)
            {
                LLCore::LogInfo("GraphicsCheck", "Device has sufficient VRAM");
                hasDiscreteRam = true;
                break;
            }
            if (hardwareSnapshot.m_gpuInfo[i].m_dedicatedVideoMemoryBytes == 0 && hardwareSnapshot.m_gpuInfo[i].m_sharedVideoMemoryBytes == 0)
            {
                LLCore::LogInfo("GraphicsCheck", "No VRAM detected, assuming this is an error and accepting the device");
                //basically, we have no idea what it is, so assume it's fine
                hasDiscreteRam = true;
                break;
            }
            else if (hardwareSnapshot.m_gpuInfo[i].m_dedicatedVideoMemoryBytes <= (uint64)8192 * 1024 * 1024)
            {
                LLCore::LogInfo("GraphicsCheck", "Device has limited VRAM");
                hasLimitedDiscreteRam = true;
                break;
            }

        }
        //m_config->m_hasDiscreteRam = true;
        //hasDiscreteRam = false;

        if (m_config->m_hasLimitedDiscreteRam != hasLimitedDiscreteRam)
        {
            LLCore::LogInfo("GraphicsCheck", "Detected change in video memory (or initial check)");
            //that is, a card was either added or removed.
            //The first time, it will act as if a card was removed if no discrete card detected
            if (!hasLimitedDiscreteRam)
            {
                LLCore::LogInfo("GraphicsCheck", "Disabling reflections");
                m_config->m_graphics.m_enableReflections          = false;
                m_config->m_graphics.m_enableVrReflections        = false;
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cReflectionsDesktop, false);
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cReflectionsVR, false);
            }
            m_config->m_hasLimitedDiscreteRam = hasLimitedDiscreteRam;
        }

        if (m_config->m_hasDiscreteRam != hasDiscreteRam)
        {
            //LLCore::LogInfo("GraphicsCheck", "Detected change in video memory (or initial check)");
            //that is, a card was either added or removed.
            //The first time, it will act as if a card was removed if no discrete card detected
            if (!hasDiscreteRam)
            {
                LLCore::LogInfo("GraphicsCheck", "Setting to low quality graphics settings");
                m_showLowGraphicsDialog                           = true;
                m_config->m_graphics.m_renderQuality              = 0;
                m_config->m_graphics.m_renderQualityVR            = 0;
                m_config->m_graphics.m_renderResolution           = 0;
                m_config->m_graphics.m_renderResolutionVR         = 0;
                m_config->m_graphics.m_enableShadowedAtmospherics = false;
                m_config->m_graphics.m_enableAmbientOcclusion     = false;
                m_config->m_graphics.m_enableReflections          = false;
                m_config->m_graphics.m_enableVrReflections        = false;
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cRenderQuality, 0);
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cRenderQualityVR, 0);
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cRenderResolution, 0);
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cRenderResolutionVR, 0);
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cAmbientOcclusionDesktop, false);
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cShadowedAtmosphericsDesktop, false);
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cReflectionsDesktop, false);
                getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cReflectionsVR, false);
            }
            m_config->m_hasDiscreteRam = hasDiscreteRam;
        }
    }
}

// budget values specified here are default values. ClientPerformanceStatsConfig.toml overrides.
constexpr float cFrameTime = 1.0 / 90.0;
LLPROFILE_STAT_DECLARE(ApplicationTiming, Frame, LLProfile::PerformanceStat::cTimeValueSeconds, cFrameTime);
LLPROFILE_STAT_DECLARE(ApplicationTiming, StepWorld, LLProfile::PerformanceStat::cTimeValueSeconds, cFrameTime * 0.15f);
LLPROFILE_STAT_DECLARE(ApplicationTiming, UpdateView, LLProfile::PerformanceStat::cTimeValueSeconds, cFrameTime * 0.5f);
LLPROFILE_STAT_DECLARE(ApplicationTiming, InputAndEvents, LLProfile::PerformanceStat::cTimeValueSeconds, cFrameTime * 0.01f);
LLPROFILE_STAT_DECLARE(ApplicationTiming, Misc, LLProfile::PerformanceStat::cTimeValueSeconds, cFrameTime * 0.04f);

ClientApplication::ClientApplication(ClientApplication::Config* config)
    : BaseType(config)
    , m_automation(config->m_automation)
    , m_config(config)
    , m_chatManager(nullptr)
    , m_editModeView(config->m_edit.m_mode)
    , m_lacm(nullptr)
    , m_userCamera(&m_inputBindingManager)
    , m_smiteUser(false)
    , m_queuedDisconnect(false)
    , m_queuedReconnect(false)
    , m_heartbeatContext(false)
    , m_currentLoadingPhaseTimer(false)
    , m_connectToRegionStateMachine(this)
    , m_metricsUpdatePeriod(config->m_metricsUpdatePeriod)
{
    m_appServiceRegistry = new LLAppService::AppServiceRegistry();

    m_rememberMe = new ClientLogin::RememberMe(m_config->m_rememberMe);

    LLCore::CoreNativeFileSystem::GetInstance().createDirectory(LLCore::Path("USERCACHE:"_ll));

    // store hard link in sansar user directory to latest log file in Log subdir
    LLCore::StringFixed<128> nativeLogFilePath;
    LLCore::CoreNativeFileSystem::GetInstance().getNativePath(config->m_application.m_logging.m_logFilePath.asRead().m_path, &nativeLogFilePath);
    LLCore::StringFixed<128> nativeLogLinkPath;
    LLCore::Path             logLinkPath("USERCACHE:SansarClient.log");
    LLCore::CoreNativeFileSystem::GetInstance().getNativePath(logLinkPath, &nativeLogLinkPath);

    LLCore::CoreNativeFileSystem::GetInstance().deleteFile(logLinkPath);
    LLCore::CoreNativeFileSystem::GetInstance().linkNativeFile(nativeLogFilePath, nativeLogLinkPath);
    // LLAudio::Initialize must come before WinMM.dll is loaded by other systems
    if (m_config->m_enableAudio)
    {
        LLAudio::SystemCinfo audioCinfo(&m_config->m_audio);
        audioCinfo.m_physicsThreadCreateCallback  = &EngineSimulation::InitializeThread;
        audioCinfo.m_physicsThreadDestroyCallback = &EngineSimulation::TerminateThread;
        audioCinfo.m_appEventQueue                = &getEventQueue();
        audioCinfo.m_enableLoopback               = m_config->m_media.m_enabled;
        LLAudio::Initialize(audioCinfo); // LLAudio::Initialize must come before Engine::Initialize, which is called by EditWorkspace::Initialize
    }

    if (m_config->m_enableSteamLogin)
    {
        m_steamworksManager                       = new LLSteamworks::SteamworksManager(m_config->m_steamAppId, &getEventQueue());
        m_config->m_login.m_isSteamLoginAvailable = m_steamworksManager->isSteamInitializedAndRunning();
    }

    LLResource::Initialize();
    EditWorkspace::Initialize(); // LLAudio::Initialize must come before Engine::Initialize, which is called by EditWorkspace::Initialize
    initializeAssetSystem();

    startRendering();

    if (m_config->m_ui.m_enableUi && rendererRunning())
    {
        ClientUI::ClientUIManager::CInfo cinfo = {
            getEventQueue(),
            &m_config->m_ui.asWrite(),
            &m_graphicsSystem,
            &m_keyboardInputManager,
            &m_inputFocusManager,
            m_assetManager,
            BaseType::getUIGraphicsPackage(),
            getWindow(),
            m_config->m_regions.m_regionList.asRead(),
            LLCore::GetThreadId(),
            /*enableBindingLogs=*/m_config->m_ui.m_enableNoesisBindingLog,
            m_config->m_ui.m_allowScriptedShowWorldDetails};
        // TODO: The ClientApplication should not own the ClientUIManager (wrong thread).
        m_clientUIManager = new ClientUI::ClientUIManager(cinfo);

        LLUI::UISystem* UISystem = m_clientUIManager->getUISystem();
        UISystem->setStreamingMode(config->m_ui.m_streamingMode);

        if (m_config->m_ui.m_heatmapDataFile.isProvided())
        {
            LLCore::FileReference heatmapFile(m_config->m_ui.m_heatmapDataFile);
            ClientUI::MetricsHeatmap::configureMetricsHeatmap(heatmapFile, getUISystem()->getWorkspaceManager());
        }

        LLCORE_ASSERT(m_clientUIManager->getUISystem() != nullptr, "UI system failed to properly initialize");

        postUIEvent<LLUI::LoadThemeRequest>(LLCore::Path("UI_ASSETS:Common/SansarStyle.xaml"));

        BaseType::initializeUiRenderer(m_clientUIManager->getUIRenderControl());

        if (m_vrSystem != nullptr)
        {
            m_vrSystem->onUiSystemInitialized(getUIEventQueue());
        }
    }
    else
    {
        m_fallbackUiEventQueue.construct();
    }

    m_settingsManager = new EngineSettingsManager(m_config, &getEventQueue(), getUIEventQueue());
    getEventQueue().addCallback(this, &ClientApplication::onEngineSettingsChanged);

    LLHttp::HttpManager::Config httpConfig;
    httpConfig.m_clientsPerHost = 3;
    httpConfig.m_verifyPeers    = !m_config->m_connection.m_conductor.m_insecureHost;
    httpConfig.m_useV4Socket    = m_config->m_networking.m_useV4Socket;
    m_httpManager               = new LLHttp::HttpManager(httpConfig);

    m_depDupSessionData              = new SansarHttp::DeprecatedDuplicateSessionData();
    m_depDupSessionData->m_userAgent = "SansarClient/1.0"_ll; // HACK this now comes from ApiLocatorService

    m_apiLocatorService = new SansarHttp::ApiLocatorService(m_config->m_apiService, &getEventQueue(), m_httpManager);
    getEventQueue().addCallback(this, &ClientApplication::onApiLocatorLoadedEvent);

    m_dataSyncRoot = new LLDataModel::DataSyncRoot(getUIEventQueue(), "dataSyncRoot"_ll);

    m_appData = new AppData(&m_config->m_appData, &getEventQueue(), getUIEventQueue(), m_dataSyncRoot, m_httpManager, m_apiLocatorService);

    m_metricsManager    = new ClientServices::ClientMetricsManager(config->m_stateLogFilePath);
    m_oldMetricsManager = LLMetrics::MetricsManager::SwapImplementation(m_metricsManager);

    m_metricsManager->setReleaseVersion(APPCORE_SANSAR_VERSION_STRING);

    LLCore::HardwareInfo::GetSnapshot(&m_clientHardwareInfo);

    if (getCrashReporter() != nullptr)
    {
        if (getCrashReporter()->checkForPreviousRunCrash())
        {
            LLMetrics::RecordMetric(ClientServices::PreviousRunCrashedMetricsEvent());
        }

        // total 1MB limit
        {
            // main log file has 1MB size limit
            getCrashReporter()->addLogFilePath(m_config->m_application.m_logging.m_logFilePath, 1022 * 1024);
            // state log has 2KB size limit
            getCrashReporter()->addLogFilePath(m_config->m_stateLogFilePath, 2 * 1024);
        }
    }

    LLProfile::PerformanceStatsManagerSF::GetInstance().initialize(config->m_performanceStats.m_config);

    if (config->m_xsens.m_active)
    {
        EngineSimulation::XSensManagerCinfo xsensCinfo;
        xsensCinfo.m_port = config->m_xsens.m_port;
        xsensCinfo.m_useV4Socket = config->m_networking.m_useV4Socket;
        m_xsensManager    = new EngineSimulation::XSensManager(xsensCinfo);
    }

    // this needs to get to Noesis config!
    // config->m_performanceStats.m_displayPreferences

    ClientUI::DiagnosticsView::ConfigurePreferences(config->m_performanceStats.m_displayPreferences);

    m_ftueControlsManager = new ClientServices::FtueControlsManager(&getEventQueue(), getUIEventQueue(), m_vrSystem);

    m_inputBindingManager.addDeviceMapper<LLInput::KeyboardDeviceMapper>();
    m_inputBindingManager.addDeviceMapper<LLWindow::MouseDeviceMapper>();
    m_inputBindingManager.addDeviceMapper<LLInput::GamepadDeviceMapper>();
    m_inputBindingManager.addDeviceMapper<LLInput::WandDeviceMapper>();

    registerInputBindings();

    //EditEngine::EditParameterDescriptorRegistry::Initialize();

    if (m_config->m_networking.m_telnetEnabled)
    {
        m_telnetServer = new LLNetwork::TelnetServer(&getEventQueue(), m_config->m_networking.m_telnetPort);
        getEventQueue().addCallback(this, &ClientApplication::handleTelnetEvent);
    }

    LLCore::LogInfo<"Initialize"_ll_tag>("ClientApplication", "Initializing Engine...");

    if (m_config->m_enableAudio)
    {
        if (m_vrSystem != nullptr)
        {
            LLAudio::AudioSystem::GetInstance().updateDefaultDeviceNames(m_vrSystem->getHeadsetManager());
            LLAudio::AudioSystem::GetInstance().setVrMode(isVrActive(), m_vrSystem->getHeadsetManager());
        }

        ClientUI::AudioTrigger::configureAudioTrigger(&getEventQueue(), getUISystem()->getWorkspaceManager());
        m_clientAudio = new ClientAudio::ClientAudio(&getEventQueue());

        EngineAudio::AudioSystem::GetInstance().setEventQueue(&getEventQueue());
    }

    LLTextureProcessing::Platform::Initialize();

    m_gamepadManager = new LLInput::GamepadInputManager(&getEventQueue());

    if (m_vrSystem)
    {
        m_vrSystem->setGamepadInputManager(m_gamepadManager);
    }

    getEventQueue().addCallback(this, &ClientApplication::onWindowResized);
    getEventQueue().addCallback(this, &ClientApplication::onWindowStartResize);
    getEventQueue().addCallback(this, &ClientApplication::onWindowEndResize);
    getEventQueue().addCallback(this, &ClientApplication::onSetWindowTitleRequest);
    getEventQueue().addCallback(this, &ClientApplication::handleConnectToRegionRequest);
    getEventQueue().addCallback(this, &ClientApplication::handleEditSceneRequest);
    getEventQueue().addCallback(this, &ClientApplication::onWindowFocusLost);
    getEventQueue().addCallback(this, &ClientApplication::onWindowFocusGained);
    getEventQueue().addCallback(this, &ClientApplication::onVrStateChanged);
    getEventQueue().addCallback(this, &ClientApplication::onVrCalibrationComplete);
    getEventQueue().addCallback(this, &ClientApplication::handleSwitchToEditModeRequest);
    getEventQueue().addCallback(this, &ClientApplication::handleEditModeViewSwitched);
    getEventQueue().addCallback(this, &ClientApplication::handleSwitchToRegionSelectModeRequest);
    getEventQueue().addCallback(this, &ClientApplication::handleIsDeveloperModeRequest);
    getEventQueue().addCallback(this, &ClientApplication::onScreenshotCaptureRequest);
    getEventQueue().addCallback(this, &ClientApplication::onMarketPlaceCapabilityMapRequest);
    getEventQueue().addCallback(this, &ClientApplication::onCompiledSceneEvent);
    getEventQueue().addCallback(this, &ClientApplication::onPublishedSceneEvent);
    getEventQueue().addCallback(this, &ClientApplication::onRefreshAudioDevicesEvent);
    getEventQueue().addCallback(this, &ClientApplication::onConnectedToAccountEvent);
    getEventQueue().addCallback(this, &ClientApplication::handleTeleportToFriendRequest);
    getEventQueue().addCallback(this, &ClientApplication::handleApplicationMenuToggledEvent);
    getEventQueue().addCallback(this, &ClientApplication::handleCancelSceneLoadEvent);
    getEventQueue().addCallback(this, &ClientApplication::handleGlobalKeyPressedEvent);
    getEventQueue().addCallback(this, &ClientApplication::handleCopySceneUriToClipboard);

    getEventQueue().addCallbackWithOwner(this, this, &ClientApplication::handlePlayEmote);
    getEventQueue().addCallbackWithOwner(this, this, &ClientApplication::handleStopEmote);
    getEventQueue().addCallbackWithOwner(this, this, &ClientApplication::handleClientBanNotification);

    getEventQueue().addCallback(this, &ClientApplication::handleScreenshotByEditorEvent);

    if (m_config->m_connection.m_conductor.m_insecureHost)
    {
        LLCore::LogWarning("ClientApplication", "Allowing insecure https connections");
    }

    m_operationQueue = new ClientHttp::Dep::OperationQueue();

    bool haveKafka = (m_config->m_connection.m_useConductor || m_config->m_account.m_kafkaConnectorAddress.isProvided());
    if (haveKafka)
    {
        m_accountConnector = new ClientServices::KafkaManager(&getEventQueue(), m_config->m_networking.m_useTcp, m_config->m_networking.m_useV4Socket, LLCore::Duration::FromMilliseconds(m_config->m_networking.m_latencyTesting.m_simulateLatencyMs));

        // Script console only works if we have kafka and an account connector.
        getEventQueue().addCallback(this, &ClientApplication::onScriptConsoleBeginRequest);
        getEventQueue().addCallback(this, &ClientApplication::onScriptConsoleEndRequest);
    }

    m_relationshipManager = new ClientServices::RelationshipManager(&getEventQueue(),
                                                                    getUIEventQueue(),
                                                                    (m_accountConnector != nullptr) ? &(m_accountConnector->getClientKafkaChannel()) : nullptr);

    m_chatManager = new Chat::ChatManager(m_config->m_chat, &getEventQueue(), getUIEventQueue(), (m_accountConnector != nullptr) ? m_accountConnector->getClientKafkaApi() : m_dummyKafkaApi);


    m_notificationManager = new ClientServices::NotificationManager(&getEventQueue(),
                                                                    (m_accountConnector != nullptr) ? m_accountConnector->getClientKafkaApi() : m_dummyKafkaApi);

    m_usingLocalRegionConductor = (m_config->m_connection.m_useConductor == false);

    m_gridStatusService = new ClientLogin::HttpGridStatusService(m_config->m_gridStatusService, &getEventQueue(), m_apiLocatorService, m_httpManager);
    m_appServiceRegistry->install(ClientLogin::GridStatusServiceApi::GetInterfaceId(), m_gridStatusService);

    m_latestClientVersionService = new ClientLogin::HttpLatestClientVersionService(&getEventQueue(), m_apiLocatorService, m_httpManager, m_config->m_appVersion);
    m_appServiceRegistry->install(ClientLogin::LatestClientVersionServiceApi::GetInterfaceId(), m_latestClientVersionService);

    m_registrationService = new ClientLogin::HttpRegistrationService(m_apiLocatorService, m_httpManager);
    m_appServiceRegistry->install(ClientLogin::RegistrationServiceApi::GetInterfaceId(), m_registrationService);

    m_remoteConfig = new ClientLogin::HttpRemoteConfigService(m_apiLocatorService, m_httpManager);
    m_appServiceRegistry->install(ClientLogin::RemoteConfigApi::GetInterfaceId(), m_remoteConfig);

    m_googleAnalytics = new ClientLogin::GoogleAnalytics(m_dataSyncRoot, m_apiLocatorService, m_httpManager, m_remoteConfig);
    m_appServiceRegistry->install(ClientLogin::GoogleAnalyticsApi::GetInterfaceId(), m_googleAnalytics);

    m_experimentConfig = new ClientLogin::HttpExperimentConfig(&getEventQueue(), m_dataSyncRoot, m_apiLocatorService, m_httpManager, m_googleAnalytics);
    m_appServiceRegistry->install(ClientLogin::ExperimentConfigApi::GetInterfaceId(), m_experimentConfig);

    m_registrationMetrics = new ClientLogin::RegistrationMetrics(&getEventQueue(), m_config->m_appVersion, m_apiLocatorService, m_googleAnalytics);
    m_appServiceRegistry->install(ClientLogin::RegistrationMetricsApi::GetInterfaceId(), m_registrationMetrics);

    m_visitExperienceService = new ClientServices::VisitExperienceService(m_config->m_visitExperienceService, &getEventQueue(), getUIEventQueue(), m_apiLocatorService, m_httpManager);
    m_appServiceRegistry->install(ClientServices::VisitExperienceServiceApi::GetInterfaceId(), m_visitExperienceService);

    m_streamRouter = new Engine::EngineStreamRouter();

    if (!isShutdownRequested() && m_config->m_sceneUri.isProvided())
    {
        LLHttp::HttpUrl                 sceneUri = m_config->m_sceneUri;
        AppCore::SansarUriBase::UriType uriType  = AppCore::SansarUriBase::FindUriType(sceneUri);

        if (uriType == AppCore::SansarUriBase::UriType::cExperience || uriType == AppCore::SansarUriBase::UriType::cEvent)
        {
            if (m_requestedExperienceUri.parseUrl(sceneUri))
            {
                LLCore::LogInfo("ClientApplication", "Command-line Experience URI provided: ", sceneUri);
                if (m_requestedExperienceUri.getGrid() != m_apiLocatorService->getGrid())
                {
                    LLCore::LogError("ClientApplication", "sceneUri.grid:", m_requestedExperienceUri.getGrid(), " does not match apiLocatorService.grid:", m_apiLocatorService->getGrid());
                    m_commandLineSceneUriError = true;
                }
            }
            else
            {
                m_commandLineSceneUriError = true;
            }
        }
        else if (uriType == AppCore::SansarUriBase::UriType::cSceneEdit)
        {
            if (m_requestedSceneEditUri.parseUrl(sceneUri))
            {
                // These settings don't exist in scene URIs
                m_sceneInventoryItemToEdit.setNull();
                m_experienceAssociatedWithSceneEdit.setNull();
                m_editModeView = EditWorkspace::WorkspaceEditView::cWorkspaceEditView_Layout;

                LLCore::LogInfo("ClientApplication", "Command-line Scene Edit URI provided: ", sceneUri);

                if (m_requestedSceneEditUri.getGrid() != m_apiLocatorService->getGrid())
                {
                    LLCore::LogError("ClientApplication", "sceneUri.grid:", m_requestedSceneEditUri.getGrid(), " does not match apiLocatorService.grid:", m_apiLocatorService->getGrid());
                    m_commandLineSceneUriError = true;
                }
            }
            else
            {
                m_commandLineSceneUriError = true;
            }
        }
        else if (uriType == AppCore::SansarUriBase::UriType::cEmpty)
        {
            // Nothing to do here. The sceneUri may contain query params.
        }
        else
        {
            m_commandLineSceneUriError = true;
        }
    }

    m_frameSleep           = LLCore::Duration::FromMilliseconds(LLCore::FloorToInt(1000.0f / static_cast<float>(m_config->m_graphics.m_maxFps)));
    m_frameSleepVr         = LLCore::Duration::FromMilliseconds(LLCore::FloorToInt(1000.0f / 120.0f));
    m_frameSleepBackground = LLCore::Duration::FromMilliseconds(LLCore::FloorToInt(1000.0f / static_cast<float>(m_config->m_graphics.m_maxFpsBackground)));
    m_frameSleepMinimized  = LLCore::Duration::FromMilliseconds(LLCore::FloorToInt(1000.0f / static_cast<float>(m_config->m_graphics.m_maxFpsMinimized)));

    m_sceneInventoryItemToEdit.setNull();

    m_codeVerifierCache = new ClientLogin::CodeVerifierCache();

    m_authManager                               = new ClientLogin::AuthenticationManager(&getEventQueue(), m_apiLocatorService);
    m_depDupSessionData->m_requestAuthenticator = m_authManager;

    m_identityManager = new Identity::IdentityManager(m_config->m_account.m_identity, &getEventQueue(), m_httpManager, m_depDupSessionData, m_apiLocatorService, m_authManager);

    m_subscriptionService = new ClientLogin::HttpSubscriptionService(m_httpManager, m_apiLocatorService, m_authManager, m_identityManager);
    m_subscriptionManager = new ClientLogin::SubscriptionManager(&getEventQueue(), getUIEventQueue(), m_subscriptionService);

    ClientServices::HttpCInfo clientHttpConfig{m_httpManager, m_apiLocatorService, m_authManager, m_identityManager, m_config->m_connection.m_conductor.m_accessGroup.asRead()};

    m_worldInstanceManager = new ClientServices::WorldInstanceManager(&getEventQueue(), getUIEventQueue(), clientHttpConfig);

    m_accountConfigurationManager = new Identity::AccountConfigurationManager(m_config->m_account, &getEventQueue(), m_apiLocatorService, m_httpManager, m_authManager, m_identityManager);

    int32 primaryDisplayWidth  = 0;
    int32 primaryDisplayHeight = 0;
    if (m_mainWindow)
    {
        m_mainWindow->getPrimaryDisplayDimension(&primaryDisplayWidth, &primaryDisplayHeight);
    }

    m_goldFizService = new ClientLogin::HttpGoldFizService(m_apiLocatorService, m_httpManager, m_remoteConfig, m_authManager, primaryDisplayWidth, primaryDisplayHeight);
    m_appServiceRegistry->install(ClientLogin::GoldFizApi::GetInterfaceId(), m_goldFizService);

    m_orderService = new ClientInventory::HttpOrderService(m_apiLocatorService, m_httpManager, m_authManager, m_identityManager);
    m_appServiceRegistry->install(ClientInventory::OrderServiceApi::GetInterfaceId(), m_orderService);


    EditWorkspace::AssetUploadManagerCInfo assetUploadManagerCInfo;
    {
        assetUploadManagerCInfo.m_config               = &m_config->m_asset.m_assetUpload;
        assetUploadManagerCInfo.m_httpManager          = m_httpManager;
        assetUploadManagerCInfo.m_depDupSessionData    = m_depDupSessionData;
        assetUploadManagerCInfo.m_resourceStoreManager = m_resourceStoreManager;
        assetUploadManagerCInfo.m_apiLocatorService    = m_apiLocatorService;
    };

    m_marketplace = new Marketplace::MarketplaceManager(m_httpManager, m_depDupSessionData, m_operationQueue, m_apiLocatorService, &getEventQueue());

    m_inventoryManager = new Inventory::InventoryManager(m_config->m_inventory, m_httpManager, m_depDupSessionData, m_assetManager, assetUploadManagerCInfo, m_authManager, m_apiLocatorService, m_marketplace, m_config->m_networking.m_useV4Socket);
    m_inventoryManager->listenTo(&getEventQueue());
    m_inventoryManager->broadcastOn(&getEventQueue());

    m_licenseService = new ClientInventory::LicenseService(m_inventoryManager, &getEventQueue(), m_resourceStoreManager, m_engineResourceLoader);

    if (rendererRunning())
    {
        m_inventoryManager->broadcastOn(getUIEventQueue());
    }


    m_builtInInventory = new Inventory::BuiltInInventory(m_resourceStoreManager);
    m_inventoryManager->setBuiltInInventory(m_builtInInventory);

    if (rendererRunning())
    {
        m_builtInInventory->broadcastOn(getUIEventQueue());
    }

    m_atlasManager = new ClientServices::AtlasManager(m_config->m_atlasManager, m_httpManager, m_depDupSessionData, m_apiLocatorService, m_authManager, m_identityManager);
    m_atlasManager->setAppEventQueue(&getEventQueue());
    m_atlasManager->setUIEventQueue(getUIEventQueue());

    m_codexNotificationsManager = new ClientServices::CodexNotificationsManager(&getEventQueue(), getUIEventQueue());

    m_thumbnailManager = new ClientServices::ThumbnailManager(&getEventQueue(), assetUploadManagerCInfo, m_assetManager, m_config->m_networking.m_useV4Socket);

    m_loginManager = new ClientLogin::LoginManager(m_config->m_login,
                                                   &getEventQueue(),
                                                   getUIEventQueue(),
                                                   m_dataSyncRoot,
                                                   m_rememberMe,
                                                   m_codeVerifierCache,
                                                   m_apiLocatorService,
                                                   m_authManager,
                                                   m_identityManager,
                                                   m_latestClientVersionService,
                                                   m_accountConfigurationManager,
                                                   m_subscriptionManager,
                                                   m_steamworksManager,
                                                   m_goldFizService,
                                                   m_googleAnalytics,
                                                   m_experimentConfig,
                                                   m_remoteConfig,
                                                   m_registrationService);

    m_authManager->setOAuthTokenChangeHandler(m_loginManager);
    m_identityManager->setIdentityChangeHandler(m_loginManager);

    m_giftingManager = new ClientServices::GiftingManager(&getEventQueue(), getUIEventQueue(), m_orderService);
    m_tippingManager = new ClientServices::TippingManager(&getEventQueue(), getUIEventQueue());

    m_supportRequestManager = new ClientServices::SupportRequestManager(m_config->m_supportRequestManager, m_httpManager, m_depDupSessionData, m_authManager, m_identityManager, m_apiLocatorService);
    m_supportRequestManager->listenTo(&getEventQueue());

    ClientServices::ScriptCompileClient::Config clientConfig;
    {
        clientConfig.m_listenQueue         = &getEventQueue();
        clientConfig.m_compileLocally      = m_config->m_scriptCompilerConfig.m_compileLocally;
        clientConfig.m_httpManager         = m_httpManager;
        clientConfig.m_depDupSessionData   = m_depDupSessionData;
        clientConfig.m_authManager         = m_authManager;
        clientConfig.m_identityManager     = m_identityManager;
        clientConfig.m_apiLocatorService   = m_apiLocatorService;
        clientConfig.m_compilerAccessGroup = m_config->m_connection.m_conductor.m_accessGroup;
    }
    m_scriptCompiler = new ClientScripts::Compiler(&m_config->m_scriptCompilerConfig, m_resourceStoreManager);
    m_compilerClient = new ClientServices::ScriptCompileClient(clientConfig);

    m_personaCatalog = new ClientServices::PersonaCatalog(m_config->m_personaCatalog, m_httpManager, m_depDupSessionData, m_authManager, m_apiLocatorService, &getEventQueue());
    getEventQueue().addCallback(this, &ClientApplication::onPersonaDataResponse);

    m_userProfileManager = new ClientServices::UserProfileManager(m_usingLocalRegionConductor == false, &getEventQueue(), getUIEventQueue());

    m_experienceManager = new ClientServices::ExperienceManager(m_usingLocalRegionConductor == false, &getEventQueue());

    m_webImageManager = new ClientServices::WebImageManager(&getEventQueue(), getUIEventQueue(), m_httpManager);

    if (!m_usingLocalRegionConductor)
    {
        m_regionConductor = new ClientServices::RegionConductor(&m_config->m_connection.m_conductor, m_httpManager, m_depDupSessionData, m_apiLocatorService);
        m_regionConductor->listenTo(&getEventQueue());
    }

    m_assetImportManager = new ClientServices::AssetImport::Manager(m_config, m_config->m_asset.m_importContentExecutable, assetUploadManagerCInfo, m_inventoryManager, m_compilerClient, m_engineResourceLoader, m_identityManager, m_config->m_networking.m_useV4Socket);
    m_assetImportManager->listenTo(&getEventQueue());

    m_webFrontend = new ClientServices::WebFrontend(&getEventQueue(), m_apiLocatorService, m_remoteConfig);

    m_clientHttpManager = new SansarHttp::SansarHttpManager(&getEventQueue(), m_httpManager, m_apiLocatorService, m_authManager, m_identityManager);

    if (m_config->m_enableRenderer)
    {
        Engine::RuntimeCommonAssetManager::Cinfo commonAssetCinfo;
        {
            commonAssetCinfo.m_assetManager   = m_assetManager;
            commonAssetCinfo.m_resourceLoader = m_engineResourceLoader;
        }
        m_runtimeCommonAssetManager = new Engine::RuntimeCommonAssetManager(commonAssetCinfo);
        m_runtimeCommonAssetManager->loadAssets(m_config->m_asset.m_runtimeCommonAssets.m_config);
    }

    m_clientStateMachine.initialize(this, isShutdownRequested() ? ApplicationStateId::cShutdown : ApplicationStateId::cContactServices);

    if (m_config->m_ui.m_inputHintsEnabled)
    {
        m_inputHintController = new UserInputHintController(config->m_tutorial.m_userInputHintsConfig, config->m_tutorial.m_userInputHintsProgress, m_vrSystem, &getEventQueue(), getUIEventQueue());
        if (config->m_tutorial.m_resetUserInputHints)
        {
            m_inputHintController->resetProgress();
        }

        m_inputHintController->setEnabled(config->m_tutorial.m_enableUserInputHints);
    }

    m_userEmotes = new ClientServices::UserEmotes(m_inventoryManager, &m_inputBindingManager, &getEventQueue(), getUIEventQueue());

    m_debugConsoleCommandManager = new Engine::DebugConsoleCommandManager(&getEventQueue(), getUIEventQueue());

    m_cachedIsMicMutedState = m_config->m_muteMicOnStart;

    parseLoginFromSceneUri(m_config->m_sceneUri);

    m_stopwatch.reset();
    m_stopwatch.start();
    m_heartbeatContext.start();

    m_config->setBaseline();
    checkForLowGraphics();

    // Start all app services. This should be done at the end of the application constructor.
    m_appServiceRegistry->startAll();

    if (config->m_test.m_ftue)
    {
        m_ftueTest = new FtueTest(&getEventQueue(), getUIEventQueue());
    }
#ifdef CLIENT_USE_FFMPEG
    if (config->m_enableVideostream)
    {
        m_streamMuxer = new StreamingMuxer(m_renderer);
    }
#endif
}

LLUI::UISystem* ClientApplication::getUISystem()
{
    LLUI::UISystem* retVal = nullptr;
    if (m_clientUIManager != nullptr)
    {
        retVal = m_clientUIManager->getUISystem();
    }
    return retVal;
}

LLCore::EventQueue* ClientApplication::getUIEventQueue()
{
    LLCore::EventQueue* retVal = nullptr;
    if (m_clientUIManager != nullptr)
    {
        retVal = &m_clientUIManager->getUIEventQueue();
    }
    else
    {
        LLCORE_ASSERT(m_fallbackUiEventQueue.isConstructed(), "Fallback queue should have been constructed");
        retVal = m_fallbackUiEventQueue;
    }
    return retVal;
}

/// @brief Creates a session duration time limit timer if it was requested
void ClientApplication::createSessionDurationLimitTimer()
{
    // Create a session duration time limit if it was requested.
    if (m_config->m_application.m_sessionDurationLimit != LLCore::Duration::cForever && !m_sessionDurationLimitTimer.isConstructed())
    {
        LLCore::LogInfo("Application", "Setting session duration limit to ", m_config->m_application.m_sessionDurationLimit, " seconds");

        // Create a timer that will call requestShutdown after notifying the user.
        m_sessionDurationLimitTimer.construct(m_config->m_application.m_sessionDurationLimit, [this](LLCore::AsyncTimer*){

            // Post an alert to the UI event queue to inform the user that the session duration limit has been reached.
            if (this->getUIEventQueue() != nullptr)
            {
                this->getUIEventQueue()->postEvent<ClientServices::LocalAlertEvent>("Your session time limit has been reached. You will be logged out in 20 seconds.");
            }

            // Create a timer that will call requestShutdown after a delay to allow the user to read the alert.
            this->m_shutdownDelayTimer.construct(20_ll_sec, [this](LLCore::AsyncTimer*){
                LLCore::LogInfo("Application", "Shutting down application due to session duration time limit");
                this->requestShutdown();
            });

            // Start the timer.
            LLCore::CoreAsyncTimerManager::GetInstance().start(&m_shutdownDelayTimer.asBase());
        });

        // Start the timer.
        LLCore::CoreAsyncTimerManager::GetInstance().start(&m_sessionDurationLimitTimer.asBase());
    }
}

void ClientApplication::switchUIWorkspace(uint32 workspaceHash, uint32 workspaceFlags /* = 0 */)
{
    postUIEvent<LLUI::WorkspaceWillChangeEvent>(workspaceHash, workspaceFlags);
    postUIEvent<LLUI::ChangeWorkspaceEvent>(workspaceHash, workspaceFlags);
}

void ClientApplication::reloadUIWorkspace()
{
    postUIEvent<LLUI::ReloadWorkspaceEvent>();
}

void ClientApplication::reloadUIStylesAndWorkspace()
{
    postUIEvent<LLUI::ReloadStylesAndWorkspaceEvent>();
}

void ClientApplication::setSafeToDisplayVrUi(bool safeToDisplay)
{
    if (m_clientUIManager != nullptr)
    {
        m_clientUIManager->setSafeToDisplayVrUi(safeToDisplay);
    }
}

void ClientApplication::enteredInWorld()
{
    LLCore::RefPointer<ConnectingToRegionAndVoiceServers> regionConnectedState = m_connectToRegionStateMachine.getCurrentState<ConnectingToRegionAndVoiceServers>();
    if (regionConnectedState)
    {
        regionConnectedState->enteredInWorld();
    }

    m_tippingManager->onWorldEnter(m_currentExperienceId);
}

void ClientApplication::exitedInWorld()
{
    m_tippingManager->onWorldExit();
}

void ClientApplication::logLoadStatistics() const
{
    LLAssetSystem::AssetManager::Statistics stats;
    m_assetManager->getStatistics(&stats);
    stats = stats - m_loadStatisticsStart;
    LLCore::LogInfo<"SceneLoad"_ll_tag>("SceneLoad", "DefaultHttp:    Rate=", stats.m_httpStats.m_defaultStats.m_totalBytesFetched / stats.m_httpStats.m_defaultStats.m_totalBusyTime.asFloat(), " Complete=", stats.m_httpStats.m_defaultStats.m_completedRequests, " BusyTime=", stats.m_httpStats.m_defaultStats.m_totalBusyTime.asFloat());
    LLCore::LogInfo<"SceneLoad"_ll_tag>("SceneLoad", "SmallHttp:      Rate=", stats.m_httpStats.m_smallStats.m_totalBytesFetched / stats.m_httpStats.m_smallStats.m_totalBusyTime.asFloat(), " Complete=", stats.m_httpStats.m_smallStats.m_completedRequests, " BusyTime=", stats.m_httpStats.m_smallStats.m_totalBusyTime.asFloat());
    LLCore::LogInfo<"SceneLoad"_ll_tag>("SceneLoad", "LowPriortyHttp: Rate=", stats.m_httpStats.m_lowPriorityStats.m_totalBytesFetched / stats.m_httpStats.m_lowPriorityStats.m_totalBusyTime.asFloat(), " Complete=", stats.m_httpStats.m_lowPriorityStats.m_completedRequests, " BusyTime=", stats.m_httpStats.m_lowPriorityStats.m_totalBusyTime.asFloat());
}

void ClientApplication::onConnectedToAccountEvent(const AccountConnectorState::ConnectedToAccountEvent& event)
{
    m_metricsManager->setKafkaManager(m_accountConnector);

    // Record metric of local overrides by user.
    // If you don't call ClientMetricsManager::setKafkaManager() before this, you'll need to hold onto localOnlyConfig beyond RecordMetric()
    {
        LLGems::ConfigInclude include;
        include.m_path         = LLCore::FileReference{m_config->m_configSaveFile};
        include.m_allowMissing = true;

        LLGems::ConfigWithIncludes<Config> localOnlyConfig;
        localOnlyConfig.m_configFiles = {include};
        localOnlyConfig.applyFixups();

        ClientServices::ClientConfigurationMetricsEvent configMetrics(&localOnlyConfig);
        LLMetrics::RecordMetric(configMetrics);
    }

    m_accountConnectorRetryAttempts = 0;
    getEventQueue().addCallback(this, &ClientApplication::handleUnexpectedKafkaDisconnect);
}

// TODO: Everything in this function should be refactored!
void ClientApplication::onApiLocatorLoadedEvent(const SansarHttp::ApiLocatorLoadedEvent& event)
{
    // TODO: AssetManager should get this Url like any other service. It can cache it too since it will never change over the life of the application.
    if (!m_config->m_asset.m_http.m_urlTemplate.isProvided())
    {
        LLHttp::HttpUrl assetTemplate;
        m_apiLocatorService->getApiUrl(&assetTemplate, "backend"_ll, "asset-template"_ll, 1);
        if (m_assetManager)
        {
            m_assetManager->setHttpUrlTemplate(assetTemplate);
        }
        m_config->m_asset.m_http.m_urlTemplate.asWrite(false) = assetTemplate;
    }
}

void ClientApplication::setMouseInputEnabled(bool enabled)
{
    m_mouseInputManager.setMouseInputEnabled(enabled);
}

void ClientApplication::onVrStateChanged(const ClientVr::VrStateChangedEvent& event)
{
    const bool isEnteringVr = (event.m_state == Engine::VrState::cActive);

    // Forward to UI Event Queue
    postUIEvent<ClientVr::VrStateChangedEvent>(event);
    if (LLUI::UISystem* uiSystem = getUISystem())
    {
        getUISystem()->setVrMode(isEnteringVr); // this closes all open ui panels
    }

    if ((getRuntimeWorld() != nullptr) && m_vrVisualizationLayer && isEnteringVr)
    {
        ClientVr::VrVisualizationLayer::InitInfo initInfo = {};
        initInfo.m_vrSystem                               = m_vrSystem;
        initInfo.m_runtimeWorld                           = getRuntimeWorld();
        initInfo.m_renderWorld                            = getRuntimeWorld()->getRenderWorld();
        initInfo.m_resourceStoreManager                   = m_resourceStoreManager;
        initInfo.m_resourceLoader                         = m_engineResourceLoader;
        initInfo.m_assetManager                           = m_assetManager;
        initInfo.m_uiWorkspaceManager                     = getUISystem()->getWorkspaceManager();
        initInfo.m_isEditMode                             = (m_clientStateMachine.getCurrentState() == ApplicationStateId::cEditMode);

        m_vrVisualizationLayer->init(initInfo);
    }

    if (m_lacm)
    {
        m_lacm->onVrStateChanged(event.m_state);
    }

    // if we started hmd mode, hide the mouse and trap it in the window (so the client won't lose focus by clicking off the window)
    if (isEnteringVr)
    {
        if (m_config->m_vr.m_constrainMouseInHmd)
        {
            m_mainWindow->constrainMouse();
        }
        LLWindow::CursorDisplayManagerSF::GetInstance().hideDesktopCursor();
        setMouseInputEnabled(false);

        m_metricsManager->setDisplayModeVr();
        m_registrationMetrics->setDisplayModeVr();
    }
    else // we switched to non-hmd
    {
        m_mainWindow->deconstrainMouse();
        LLWindow::CursorDisplayManagerSF::GetInstance().showDesktopCursor();
        setMouseInputEnabled(true);

        m_metricsManager->setDisplayModeDesktop();
        m_registrationMetrics->setDisplayModeDesktop();
    }
    if (m_config->m_enableAudio)
    {
        LLAudio::AudioSystem::GetInstance().updateDefaultDeviceNames(m_vrSystem->getHeadsetManager());
        LLAudio::AudioSystem::GetInstance().setVrMode(isEnteringVr, m_vrSystem->getHeadsetManager());
    }
    if (m_homeSpace)
    {
        m_homeSpace->onVrStateChanged();
    }

    requestRenderScriptRestart("VR state changed");
    requestRenderMaterialsReload("VR state changed");
}

void ClientApplication::onVrCalibrationComplete(const Engine::VrCalibratedEvent& event)
{
    if (m_vrSystem)
    {
        m_config->m_vr.m_lastMeasuredUserHeight = m_vrSystem->getUnscaledUserHeight();
    }
}

template<typename... T_FUNCTION_ARGS>
void ClientApplication::registerVrModeOnlyCallback(const LLCore::String& eventName, T_FUNCTION_ARGS&&... args)
{
    registerVrModeOnlyCallbackImpl(eventName, LLCore::Function(static_cast<T_FUNCTION_ARGS&&>(args)...));
}

template<typename T_EVENT>
void ClientApplication::registerVrModeOnlyCallbackImpl(const LLCore::String& eventName, LLCore::Function<void(const T_EVENT& event)>&& callback)
{
    m_inputBindingManager.setCallback(eventName, [this, storedCallback = LLCore::Give(callback)](const T_EVENT& event) {
        if (isVrActive())
        {
            storedCallback(event);
        }
    });
}

template<typename... T_FUNCTION_ARGS>
void ClientApplication::registerVrModeOnlyOneTimeCallback(const LLCore::String& eventName, T_FUNCTION_ARGS&&... args)
{
    registerVrModeOnlyOnetimeCallbackImpl(eventName, LLCore::Function(static_cast<T_FUNCTION_ARGS&&>(args)...));
}

template<typename T_EVENT>
void ClientApplication::registerVrModeOnlyOneTimeCallbackImpl(const LLCore::String& eventName, LLCore::Function<void(const T_EVENT& event)>&& callback)
{
    m_inputBindingManager.setCallback(eventName, [this, storedCallback = LLCore::Give(callback)](const T_EVENT& event) {
        if (isVrActive())
        {
            storedCallback(event);
        }
    });
}


//////////////////////////////////////////////////////////////////////////////////
// BEGIN Production bindings - Do not change unless you have production approval!!!
//////////////////////////////////////////////////////////////////////////////////
void ClientApplication::registerProductionBindings()
{
    const auto cDirect = LLInput::InputEdgeUpdateBehavior::BehaviorType::cDirect;

    LLInput::InputBindingGroup* bindings = m_inputBindingManager.getActiveBindingGroup();

    // Register keyboard key bindings
    auto* keyboardMapper = m_inputBindingManager.getDeviceMapper<LLInput::KeyboardDeviceMapper>();
    using namespace LLInput::KeyCode;

    // TODO: uncomment these when we have support for global shortcuts
    //bindings->bind("toggleHMD"_ll, keyboardMapper, cKey_F9);
    //bindings->bind("showHelp"_ll, keyboardMapper, cKey_F1);
    //bindings->bind("toggleUiHeatmapDisplay"_ll, keyboardMapper, LLInput::KeyData(cKey_F8));
    //bindings->bind("toggleEscMenu"_ll, keyboardMapper, cKey_Escape);

    bindings->bind("toggleFullscreen"_ll, keyboardMapper, LLInput::KeyData({cKey_Alt, cKey_Enter}));
    bindings->bind("resetHeadsetOrientation"_ll, keyboardMapper, cKey_F2);
    bindings->bind("toggleDiagnosticsPanel"_ll, keyboardMapper, LLInput::KeyData({cKey_Alt, cKey_Shift, cKey_D}));
    bindings->bind("copySceneUriToClipboard"_ll, keyboardMapper, LLInput::KeyData({cKey_Alt, cKey_Shift, cKey_C}));
    bindings->bind("debugConsole"_ll, keyboardMapper, LLInput::KeyData({cKey_Control, cKey_D}));
    bindings->bind("toggleChatPanel"_ll, keyboardMapper, LLInput::KeyData({cKey_Control, cKey_T}));
    bindings->bind("toggleEmotePanel"_ll, keyboardMapper, LLInput::KeyData({cKey_Control, cKey_E}));
    bindings->bind("openQuests"_ll, keyboardMapper, LLInput::KeyData({cKey_Control, cKey_Q}));
    bindings->bind("toggleMicrophone"_ll, keyboardMapper, cKey_M);
    bindings->bind("pushToTalk"_ll, keyboardMapper, cKey_V);


    // Script bindings: user scripts can subscribe to these. See https://docs.google.com/document/d/1ifXRAkc3rTeSWT1Eb85_uVA4Lxcm68EfRSzXMmwqyaI
    bindings->bind("PrimaryAction"_ll, keyboardMapper, cKey_F, cDirect, false);
    bindings->bind("SecondaryAction"_ll, keyboardMapper, cKey_R, cDirect, false);
    bindings->bind("Modifier"_ll, keyboardMapper, cKey_Shift, cDirect, false);

    bindings->bind("Action1"_ll, keyboardMapper, cKey_1, cDirect, false);
    bindings->bind("Action2"_ll, keyboardMapper, cKey_2, cDirect, false);
    bindings->bind("Action3"_ll, keyboardMapper, cKey_3, cDirect, false);
    bindings->bind("Action4"_ll, keyboardMapper, cKey_4, cDirect, false);
    bindings->bind("Action5"_ll, keyboardMapper, cKey_5, cDirect, false);
    bindings->bind("Action6"_ll, keyboardMapper, cKey_6, cDirect, false);
    bindings->bind("Action7"_ll, keyboardMapper, cKey_7, cDirect, false);
    bindings->bind("Action8"_ll, keyboardMapper, cKey_8, cDirect, false);
    bindings->bind("Action9"_ll, keyboardMapper, cKey_9, cDirect, false);
    bindings->bind("Action0"_ll, keyboardMapper, cKey_0, cDirect, false);

    bindings->bind("SelectLeft"_ll, keyboardMapper, cKey_Left, cDirect, false);
    bindings->bind("SelectRight"_ll, keyboardMapper, cKey_Right, cDirect, false);
    bindings->bind("SelectUp"_ll, keyboardMapper, cKey_Up, cDirect, false);
    bindings->bind("SelectDown"_ll, keyboardMapper, cKey_Down, cDirect, false);
    bindings->bind("Confirm"_ll, keyboardMapper, cKey_Enter, cDirect, false);
    bindings->bind("Cancel"_ll, keyboardMapper, cKey_Escape, cDirect, false);

    // Keypad0 - Keypad9
    bindings->bind("KeyPad0"_ll, keyboardMapper, cKey_Pad0, cDirect, false);
    bindings->bind("KeyPad1"_ll, keyboardMapper, cKey_Pad1, cDirect, false);
    bindings->bind("KeyPad2"_ll, keyboardMapper, cKey_Pad2, cDirect, false);
    bindings->bind("KeyPad2"_ll, keyboardMapper, cKey_S, cDirect, false);
    bindings->bind("KeyPad3"_ll, keyboardMapper, cKey_Pad3, cDirect, false);
    bindings->bind("KeyPad4"_ll, keyboardMapper, cKey_Pad4, cDirect, false);
    bindings->bind("KeyPad4"_ll, keyboardMapper, cKey_A, cDirect, false);
    bindings->bind("KeyPad5"_ll, keyboardMapper, cKey_Pad5, cDirect, false);
    bindings->bind("KeyPad5"_ll, keyboardMapper, cKey_S, cDirect, false);
    bindings->bind("KeyPad6"_ll, keyboardMapper, cKey_Pad6, cDirect, false);
    bindings->bind("KeyPad6"_ll, keyboardMapper, cKey_D, cDirect, false);
    bindings->bind("KeyPad7"_ll, keyboardMapper, cKey_Pad7, cDirect, false);
    bindings->bind("KeyPad8"_ll, keyboardMapper, cKey_Pad8, cDirect, false);
    bindings->bind("KeyPad8"_ll, keyboardMapper, cKey_W, cDirect, false);
    bindings->bind("KeyPad9"_ll, keyboardMapper, cKey_Pad9, cDirect, false);
    bindings->bind("KeypadEnter"_ll, keyboardMapper, cKey_PadEnter, cDirect, false);

    {
        auto* mouseMapper      = m_inputBindingManager.getDeviceMapper<LLWindow::MouseDeviceMapper>();
        auto* wandDeviceMapper = m_inputBindingManager.getDeviceMapper<LLInput::WandDeviceMapper>();

        // SAN-9389 As of 2018-11-30 commands tied to multiple devices share device state which leads to bad events
        // The ultimate goal is still separation of device from command knowledge.
        // To support that any commands shared across both hands get separate commands in addition to the base command.
        bindings->bind("Trigger"_ll, mouseMapper, LLWindow::cMouseButton_Left);
        bindings->bind("TriggerLeftWand"_ll, wandDeviceMapper, LLInput::WandHand::cLeft, LLInput::cWandTrigger_Primary);
        bindings->bind("TriggerRightWand"_ll, wandDeviceMapper, LLInput::WandHand::cRight, LLInput::cWandTrigger_Primary);

        bindings->bind("PrimaryActionLeftWand"_ll, wandDeviceMapper, LLInput::WandHand::cLeft, LLInput::cWandButton_Grip);
        bindings->bind("PrimaryActionRightWand"_ll, wandDeviceMapper, LLInput::WandHand::cRight, LLInput::cWandButton_Grip);

        bindings->bind("SecondaryActionLeftWand"_ll, wandDeviceMapper, LLInput::WandHand::cLeft, LLInput::cWandButton_X);
        bindings->bind("SecondaryActionRightWand"_ll, wandDeviceMapper, LLInput::WandHand::cRight, LLInput::cWandButton_A);

        // Experimental Vive bindings.
        if (m_config->m_experimentalViveBinding)
        {
            bindings->bind("SecondaryActionLeftWand"_ll, wandDeviceMapper, LLInput::WandHand::cLeft, LLInput::cWandButton_Virtual_Center);
            bindings->bind("SecondaryActionRightWand"_ll, wandDeviceMapper, LLInput::WandHand::cRight, LLInput::cWandButton_Virtual_Center);
        }

        bindings->bind("Cancel"_ll, wandDeviceMapper, LLInput::WandHand::cLeft, LLInput::cWandButton_Y);
        bindings->bind("Confirm"_ll, wandDeviceMapper, LLInput::WandHand::cRight, LLInput::cWandButton_B);
        bindings->bind("ToggleApplicationMenu"_ll, wandDeviceMapper, LLInput::WandHand::cLeft, LLInput::cWandButton_Y); // TODO: Double event
        bindings->bind("ToggleApplicationMenu"_ll, wandDeviceMapper, LLInput::WandHand::cLeft, LLInput::cWandButton_ApplicationMenu);

        auto*        gamepadMapper = m_inputBindingManager.getDeviceMapper<LLInput::GamepadDeviceMapper>();
        const uint32 gamepadId     = 0;

        bindings->bind("Trigger"_ll, gamepadMapper, gamepadId, LLInput::cGamepadTrigger_Right);
        bindings->bind("PrimaryAction"_ll, gamepadMapper, gamepadId, LLInput::cGamepadButton_Bottom);
        bindings->bind("SecondaryAction"_ll, gamepadMapper, gamepadId, LLInput::cGamepadTrigger_Left);

        bindings->bind("pushToTalk"_ll, gamepadMapper, gamepadId, LLInput::cGamepadButton_LeftShoulder);
    }
}

void ClientApplication::registerProductionCallbacks()
{
    m_inputBindingManager.setCallback("toggleEscMenu"_ll, [this](const LLInput::InputTriggeredEvent&) {
        postUIEvent(LLUI::ToggleModalRequest("EscMenu"_ll));
    });

    m_inputBindingManager.setCallback("toggleFullscreen"_ll, [this](const LLInput::InputTriggeredEvent&) {
        m_config->m_window.m_fullscreen = !m_config->m_window.m_fullscreen;
        getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cFullscreen, m_config->m_window.m_fullscreen);
    });

    m_inputBindingManager.setCallback("toggleHMD"_ll, [this](const LLInput::InputTriggeredEvent&) {
        toggleHMD();
    });

    m_inputBindingManager.setCallback("showHelp"_ll, [this](const LLInput::InputTriggeredEvent&) {
        postUIEvent(LLUI::ShowModalRequest("Help"_ll));
    });

    m_inputBindingManager.setCallback("debugConsole"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (m_clientStateMachine.getCurrentState() == ApplicationStateId::cInWorld && !isVrActive() && m_config->m_script.m_debugConsole)
        {
            postUIEvent(LLUI::TogglePanelRequest("DebugConsole"_ll));
        }
    });

    m_inputBindingManager.setCallback("toggleChatPanel"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if ((m_clientStateMachine.getCurrentState() == ApplicationStateId::cInWorld)
            || (m_clientStateMachine.getCurrentState() == cEditMode)
            || (m_clientStateMachine.getCurrentState() == ApplicationStateId::cHomeSpace)
            || (m_clientStateMachine.getCurrentState() == ApplicationStateId::cConnectToRegion))
        {
            postUIEvent(LLUI::TogglePanelRequest("Chat"_ll));
        }
    });

    m_inputBindingManager.setCallback("toggleEmotePanel"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if ((m_clientStateMachine.getCurrentState() == ApplicationStateId::cInWorld)
            || (m_clientStateMachine.getCurrentState() == ApplicationStateId::cHomeSpace))
        {
            postUIEvent(LLUI::TogglePanelRequest("Emote"_ll));
        }
    });

    m_inputBindingManager.setCallback("toggleUiHeatmapDisplay"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (LLCore::EventQueue* uiEventQueue = getUIEventQueue())
        {
            uiEventQueue->postFunction([]() { ClientUI::MetricsHeatmap::toggleMetricsHeatmap(); });
        }
    });

    m_inputBindingManager.setCallback("toggleDiagnosticsPanel"_ll, [this](const LLInput::InputTriggeredEvent&) {
        postUIEvent(LLUI::TogglePanelRequest("Diagnostics"_ll));
    });

    m_inputBindingManager.setCallback("copySceneUriToClipboard"_ll, [this](const LLInput::InputTriggeredEvent&) {
        copySceneUriToClipboard();
    });

    m_inputBindingManager.setCallback("ToggleApplicationMenu"_ll, [this](const LLInput::InputTriggeredEvent&) {
        getUIEventQueue()->postEvent<LLUI::ToggleApplicationMenuRequest>(getEventQueue());
    });

    m_inputBindingManager.setCallback("openQuests", [this](const LLInput::InputTriggeredEvent&) {
        getUIEventQueue()->postEvent(Quests::MyQuestsShowPanelEvent());
    });
}

//////////////////////////////////////////////////////////////////////////////////
// END Production bindings
//////////////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////////////////
// BEGIN Debug bindings - These will not be available in production
//////////////////////////////////////////////////////////////////////////////////

void ClientApplication::registerDebugBindings()
{
    LLInput::InputBindingGroup* bindings = m_inputBindingManager.getActiveBindingGroup();

    auto* keyboardDeviceMapper = m_inputBindingManager.getDeviceMapper<LLInput::KeyboardDeviceMapper>();
    auto* gamepadDeviceMapper  = m_inputBindingManager.getDeviceMapper<LLInput::GamepadDeviceMapper>();

    using namespace LLInput::KeyCode;

    bindings->bind("reloadUI"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_U}));
    bindings->bind("reloadUIStylesAndWorkspace"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Shift, cKey_U}));
    bindings->bind("reloadAudio"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Alt, cKey_U}));
    bindings->bind("reloadRenderScript"_ll, keyboardDeviceMapper, LLInput::KeyData(cKey_F5));
    bindings->bind("copyWorldDefinitionId"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Alt, cKey_C}));
    bindings->bind("copyCurrentLocation"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Alt, cKey_L}));
    bindings->bind("cycleRenderModeUp"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Shift, cKey_F5}));
    bindings->bind("cycleRenderModeDown"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_F5}));
    bindings->bind("cycleRenderQualityUp"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Shift, cKey_F6}));
    bindings->bind("cycleRenderQualityDown"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_F6}));
    bindings->bind("cycleRenderResolutionUp"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Shift, cKey_F7}));
    bindings->bind("cycleRenderResolutionDown"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_F7}));
    bindings->bind("reloadMaterials"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Alt, cKey_F5}));
    bindings->bind("toggleBoundingBoxes"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_B}));
    bindings->bind("testInventory"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_I}));
    bindings->bind("testEmote"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_P}));
    bindings->bind("testBackpack"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_L}));
    bindings->bind("testNotification"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_F}));
    bindings->bind("testReactionStress"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Alt, cKey_Q}));
    bindings->bind("testPersonaCatalog"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Shift, cKey_T}));
    bindings->bind("testPersonaService"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Shift, cKey_Y}));
    bindings->bind("refreshToken"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_R}));
    bindings->bind("toggleLindenDebugDisplay"_ll, keyboardDeviceMapper, LLInput::KeyData(cKey_H));
    bindings->bind("toggleHavokDebugDisplay"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Shift, cKey_H}));
    bindings->bind("toggleDebugSlowTime"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Shift, cKey_O}));
    bindings->bind("toggleDebugStopTime"_ll, keyboardDeviceMapper, LLInput::KeyData(cKey_O));
    bindings->bind("toggleVisualDebuggerCapture"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Shift, cKey_V}));
    bindings->bind("toggleDebugCamera"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Alt, cKey_F}));
    bindings->bind("screenshot"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Quote}));
    bindings->bind("togglePhysicsDebugDraw"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Alt, cKey_P}));
    bindings->bind("toggleSoundDebugDraw"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Alt, cKey_S}));
    bindings->bind("toggleCollisionGrid"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_Alt, cKey_G}));
    bindings->bind("generateTwitchEvent"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Alt, cKey_T}));

    bindings->bind("testTip0"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_1}));
    bindings->bind("testTip1"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_2}));
    bindings->bind("testTip2"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_3}));
    bindings->bind("testTip3"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_4}));
    bindings->bind("testTip4"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_5}));
    bindings->bind("testTip5"_ll, keyboardDeviceMapper, LLInput::KeyData({cKey_Control, cKey_6}));

    const uint32 gamepadId = 0;

    bindings->bind("increaseCameraZoom"_ll, gamepadDeviceMapper, gamepadId, LLInput::cGamepadButton_DpadUp);
    bindings->bind("decreaseCameraZoom"_ll, gamepadDeviceMapper, gamepadId, LLInput::cGamepadButton_DpadDown);
}

void ClientApplication::registerDebugCallbacks()
{
    m_inputBindingManager.setCallback("reloadRenderScript"_ll, [this](const LLInput::InputTriggeredEvent&) {
        this->requestRenderScriptReload("Triggered via keyboard shortcut");
    });
    m_inputBindingManager.setCallback("cycleRenderModeUp"_ll, [this](const LLInput::InputTriggeredEvent&) {
        const EngineRender::RenderScriptConfig::RenderMode currentMode = getRenderMode();
        setRenderMode((EngineRender::RenderScriptConfig::RenderMode)((currentMode + 1) % EngineRender::RenderScriptConfig::cRenderMode_Count), "Cycle render mode up");
    });
    m_inputBindingManager.setCallback("cycleRenderModeDown"_ll, [this](const LLInput::InputTriggeredEvent&) {
        const EngineRender::RenderScriptConfig::RenderMode currentMode = getRenderMode();
        setRenderMode((EngineRender::RenderScriptConfig::RenderMode)LLCore::Min(EngineRender::RenderScriptConfig::cRenderMode_Count - 1, (currentMode - 1)), "Cycle render mode down");
    });

    m_inputBindingManager.setCallback("cycleRenderQualityUp"_ll, [this](const LLInput::InputTriggeredEvent&) {
        this->m_config->m_graphics.m_renderQuality = ((this->m_config->m_graphics.m_renderQuality + 1) % GraphicsConfig::cRenderQuality_Count);
        requestRenderMaterialsReload("Cycle render quality up");
    });
    m_inputBindingManager.setCallback("cycleRenderQualityDown"_ll, [this](const LLInput::InputTriggeredEvent&) {
        this->m_config->m_graphics.m_renderQuality = LLCore::Min(GraphicsConfig::cRenderQuality_Count - 1, this->m_config->m_graphics.m_renderQuality - 1);
        requestRenderMaterialsReload("Cycle render quality down");
    });
    m_inputBindingManager.setCallback("cycleRenderResolutionUp"_ll, [this](const LLInput::InputTriggeredEvent&) {
        this->m_config->m_graphics.m_renderResolution = ((this->m_config->m_graphics.m_renderResolution + 1) % GraphicsConfig::cRenderResolution_Count);
        requestRenderMaterialsReload("Cycle render resolution up");
    });
    m_inputBindingManager.setCallback("cycleRenderResolutionDown"_ll, [this](const LLInput::InputTriggeredEvent&) {
        this->m_config->m_graphics.m_renderResolution = LLCore::Min(GraphicsConfig::cRenderResolution_Count - 1, this->m_config->m_graphics.m_renderResolution - 1);
        requestRenderMaterialsReload("Cycle render quality down");
    });
    m_inputBindingManager.setCallback("reloadMaterials"_ll, [this](const LLInput::InputTriggeredEvent&) {
        requestRenderMaterialsReload("Triggered via keyboard shortcut");
    });
    m_inputBindingManager.setCallback("toggleBoundingBoxes"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (EngineRender::RenderWorld* renderWorld = this->getRenderWorld())
        {
            using DiagnosticsFlags = EngineRender::GraphicsConfig::ScriptWorldParams::DiagnosticsFlags;
            renderWorld->toggleDiagnosticsFlags((uint)DiagnosticsFlags::cAllBoundingBoxes);
        }
    });
    m_inputBindingManager.setCallback("decreaseCameraZoom"_ll, [this](const LLInput::InputTriggeredEvent&) {
        m_forcedCameraZoom -= 0.125f;
    });
    m_inputBindingManager.setCallback("increaseCameraZoom"_ll, [this](const LLInput::InputTriggeredEvent&) {
        m_forcedCameraZoom += 0.125f;
    });

    m_inputBindingManager.setCallback("refreshToken"_ll, [&](const LLInput::InputTriggeredEvent&) {
        m_authManager->autoRefresh();
    });

#ifdef LLCORE_DEBUG
    m_inputBindingManager.setCallback("toggleDebugSlowTime"_ll, [this](const LLInput::InputTriggeredEvent&) {
        this->setDebugDeltaTimeScale(!this->isDebugDeltaTimeScaled(), 0.1f);
    });

    m_inputBindingManager.setCallback("toggleDebugStopTime"_ll, [this](const LLInput::InputTriggeredEvent&) {
        this->setDebugDeltaTimeForced(!this->isDebugDeltaTimeForced(), 0.0f);
    });
#endif

    m_inputBindingManager.setCallback("toggleVisualDebuggerCapture"_ll, [this](const LLInput::InputTriggeredEvent&) {
        this->toggleVisualDebuggerCapture();
    });

    m_inputBindingManager.setCallback("testPersonaCatalog"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (this->m_authManager->isPersonaLoggedIn())
        {
            LLCore::Uuid                       personaId = this->m_identityManager->getPersona()->getId();
            Identity::PersonaPublicDataRequest request;
            request.authorizeUnderPersona(personaId);
            request.addPersonaIdToFetch(personaId);
            request.respondOnQueue(&this->getEventQueue());
            this->getEventQueue().postEvent(request);
        }
    });
    m_inputBindingManager.setCallback("testPersonaService"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (this->m_authManager->isPersonaLoggedIn())
        {
            LLCore::Uuid                        personaId = this->m_identityManager->getPersona()->getId();
            Identity::PrivatePersonaDataRequest request;
            request.authorizeUnderPersona(personaId);
            request.respondOnQueue(&this->getEventQueue());
            this->getEventQueue().postEvent(request);
        }
    });

#ifdef LLCORE_DEBUG
    m_inputBindingManager.setCallback("testInventory"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if ((m_clientStateMachine.getCurrentState() == cEditMode && !isEditViewModeCharacterEditor()))
        {
            m_inventoryManager->triggerStressTest();
        }
    });

    m_inputBindingManager.setCallback("testEmote"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (m_lacm == nullptr)
        {
            return;
        }

        AgentControllerMessages::AgentPlayAnimation packet;
        {
            packet.m_agentControllerId                 = m_localAgentId;
            packet.m_playAnimPacket[0].m_frame         = getRuntimeWorld()->getCurrentFrame() + 1; // Give one extra frame to load on already cached systems
            packet.m_playAnimPacket[0].m_componentId   = m_lacm->getAgentControllerManager()->getCharacterController()->getAnimationHandle()->getComponentId();
            packet.m_playAnimPacket[0].m_resourceId    = LLResource::ResourceId("fa880bd1629ffd4ecb5c531df72f7633").asUuid();
            packet.m_playAnimPacket[0].m_skeletonType  = (uint8)EngineSimulation::AnimationSkeletonType::cBaseMale2;
            packet.m_playAnimPacket[0].m_animationType = (uint8)EngineSimulation::AnimationType::cFull;
            packet.m_playAnimPacket[0].m_playbackMode  = (uint8)EngineSimulation::AnimationPlaybackMode::cLooping;
            packet.m_playAnimPacket[0].m_playbackSpeed = 1.0f;
        }

        if (m_worldStateManager != nullptr)
        {
            m_worldStateManager->handleMessage(&packet, true);
        }
        else if (m_homeSpace != nullptr)
        {
            m_homeSpace->getWorldStateManager()->handleMessage(&packet, true);
        }
    });

    m_inputBindingManager.setCallback("testBackpack"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (m_lacm == nullptr)
        {
            return;
        }

        LLResource::ResourceId resourceId("ef9bd35106617f3779eead8967fbb05f");
        getEventQueue().postEvent<Engine::UserSpawnItemEvent>(resourceId, false, false, false);
    });


    m_inputBindingManager.setCallback("testNotification"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (m_lacm == nullptr)
        {
            return;
        }

        postUIEvent<ClientServices::LocalAlertEvent>("Testing Notifications"_ll);
    });

    m_inputBindingManager.setCallback("testReactionStress"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (m_lacm == nullptr)
        {
            return;
        }

        LLCore::RandomGenerator rand;
        const int               numTestReactions = 40;
        for (int i = 0; i < numTestReactions; i++)
        {
            AgentControllerMessages::UserReaction msg;
            msg.m_frame             = getRuntimeWorld()->getCurrentFrame();
            msg.m_agentControllerId = m_lacm->getAgentControllerManager()->getAgentControllerId();
            msg.m_orientation       = LLCore::Quaternion(m_lacm->getAgentControllerManager()->getCharacterController()->getTransform().getRotation());
            msg.m_position.setAdd(m_lacm->getCharacterTransform().getTranslation(), LLCore::Vector4((LLCore::Random::GetFloat(rand) - 0.5f) * 5.0f, (LLCore::Random::GetFloat(rand) - 0.5f) * 5.0f, 1.8f));
            msg.m_type = LLCore::ConvertType<LLCore::String>((Engine::DefaultReactionType)(rand.getUint32() % (int32)(Engine::DefaultReactionType::cCount)));

            m_lacm->getAgentControllerManager()->getOutgoingChannel()->sendMessage(msg, false);
        }
    });

    for (int i = 0; i < 6; i++)
    {
        LLCore::String callbackKey("testTip");
        callbackKey.appendFormat("%d", i);
        m_inputBindingManager.setCallback(callbackKey, [this, i](const LLInput::InputTriggeredEvent&) {
            getEventQueue().postEvent<Engine::FireUserReactionEvent>(LLCore::Format("%s_%d", LLCore::ConvertType<LLCore::String>(Engine::DefaultReactionType::cTipping), i));
        });
    }


#endif // LLCORE_DEBUG

    m_inputBindingManager.setCallback("togglePhysicsDebugDraw"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (getRuntimeWorld() != nullptr)
        {
            getRuntimeWorld()->getSimulationWorld()->toggleDebugDraw();
            if (m_config->m_enableAudio)
            {
                EngineAudio::AudioSystem::GetInstance().toggleSpatializationDebugDraw();
            }
        }
    });

    m_inputBindingManager.setCallback("toggleSoundDebugDraw"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if ((getRuntimeWorld() != nullptr) && m_config->m_enableAudio)
        {
            EngineAudio::AudioSystem::GetInstance().setDebugDraw(!EngineAudio::AudioSystem::GetInstance().getDebugDraw());
        }
    });

    m_inputBindingManager.setCallback("copyWorldDefinitionId"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (getRuntimeWorld() != nullptr)
        {
            LLCore::String worldDefinitionId;
            ConvertType(m_worldStateManager->getWorldDefintionId(), &worldDefinitionId);
            LLUI::Clipboard::Copy(worldDefinitionId.asRead());
        }
    });

    m_inputBindingManager.setCallback("copyCurrentLocation"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (getRuntimeWorld() != nullptr && m_lacm != nullptr)
        {
            LLCore::QTransform targetTransform;
            m_lacm->getLastSafeTransformWorldUp(&targetTransform);

            AppCore::SansarExperienceUri currentExperience = m_currentExperienceUri;
            currentExperience.setTargetTransform(targetTransform);
            LLUI::Clipboard::Copy(currentExperience.getUrl().toString().asRead());
        }
    });

    m_inputBindingManager.setCallback("toggleCollisionGrid"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (getRuntimeWorld() != nullptr)
        {
            getRuntimeWorld()->getSimulationWorld()->toggleDebugCollisionGrid();
        }
    });

    m_inputBindingManager.setCallback("generateTwitchEvent"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (getRuntimeWorld() != nullptr)
        {
            Twitch::TwitchEvent event;
            event.m_eventType = Twitch::TwitchEventType::cViewerLove;
            event.m_intensity = 0.5f;
            getEventQueue().postEvent(Twitch::TwitchEventReceivedEvent{event});
        }
    });

    m_inputBindingManager.setCallback("reloadUI"_ll, [this](const LLInput::InputTriggeredEvent&) {
        reloadUIWorkspace();
    });

    m_inputBindingManager.setCallback("reloadUIStylesAndWorkspace"_ll, [this](const LLInput::InputTriggeredEvent&) {
        reloadUIStylesAndWorkspace();
    });

    m_inputBindingManager.setCallback("reloadAudio"_ll, [this](const LLInput::InputTriggeredEvent&) {
        bool canReloadBank = !m_config->m_audio.m_enableFmodStudio; // FMOD will crash if we reload the bank while connected to FMOD studio

        m_clientAudio->reloadConfig(canReloadBank);
        getEventQueue().postEvent<EditAudio::ReloadAudioRequest>();
    });

    m_inputBindingManager.setCallback("toggleLindenDebugDisplay"_ll, [](const LLInput::InputTriggeredEvent&) {
        LLGEMS_SET_DISPLAY_ENABLED_STATE("RenderDebug"_ll, !LLGEMS_GET_DISPLAY_ENABLED_STATE("RenderDebug"_ll));
    });

    m_inputBindingManager.setCallback("toggleHavokDebugDisplay"_ll, [](const LLInput::InputTriggeredEvent&) {
        LLGEMS_SET_DISPLAY_ENABLED_STATE("HavokVdb"_ll, !LLGEMS_GET_DISPLAY_ENABLED_STATE("HavokVdb"_ll));
    });

    m_inputBindingManager.setCallback("toggleDebugCamera"_ll, [this](const LLInput::InputTriggeredEvent&) {
        this->m_resetDebugCameraRequested = !m_usingDebugCamera;
        m_usingDebugCamera ^= true;
    });

    m_inputBindingManager.setCallback("screenshot"_ll, [this](const LLInput::InputTriggeredEvent&) {
        if (rendererRunning())
        {
            auto onScreenshotRequestComplete = [](LLCore::RefPointer<LLImage::ImageRaw>& imageRaw, const LLCore::MTransform& /*cameraTransform*/) {
                LLCore::LogInfo("ClientApplication", "Writing screenshot");

                LLImage::PngWrapper png;
                LLCore::Array<byte> imageData;
                imageData.setCount(imageRaw->getDataSize() + 1024);
                png.writePng(*imageRaw, imageData.begin());
                imageData.setCount(png.getFinalSize());

                // Saving the screenshot to the User Documents folder

                const LLCore::SystemTime currentTime = LLCore::SystemTime::Now();
                LLCore::DateTime         dateTime;
                LLCore::ConvertSystemToDateTime(currentTime, &dateTime, true);

                const LLCore::StringFixed<1024> screenshotName(LLCore::Format("USERDOCUMENTS:sansar-screenshot.%04d-%02d-%02d-%02d-%02d-%02d.png", dateTime.m_year, dateTime.m_month, dateTime.m_day, dateTime.m_hour, dateTime.m_minute, dateTime.m_second));
                const LLCore::Path              screenshotPath(screenshotName);

                LLCore::StringFixed<1024> screenshotNativePath;
                LLCore::CoreNativeFileSystem::GetInstance().getAbsoluteNativePath(screenshotPath, &screenshotNativePath);
                LLCore::NativeFileUtils::SaveRaw(screenshotNativePath.asRead(), imageData.begin(), imageData.getCount());
                LLCore::LogInfo("ClientApplication", "Wrote '", screenshotNativePath, "'");
            };
            // n.b. using window dimensions rather than dimensions of source, because source is conservatively sized (because tiles)
            getEventQueue().postEvent<EngineApplication::ScreenshotRequest>((uint)m_width, (uint)m_height, onScreenshotRequestComplete);
        }
    });

    getEventQueue().addCallback(this, &ClientApplication::handleDebugDeltaTimeResponse);
    getEventQueue().addCallback(this, &ClientApplication::handleVisualDebuggerCaptureResponse);
}

//////////////////////////////////////////////////////////////////////////////////
// END Debug bindings
//////////////////////////////////////////////////////////////////////////////////


void ClientApplication::registerInputBindings()
{
    registerProductionCallbacks();
    registerProductionBindings();

    if (m_config->m_developerMode)
    {
        registerDebugCallbacks();
        registerDebugBindings();
    }
}

void ClientApplication::onWindowFocusLost(const LLWindow::WindowFocusLostEvent&)
{
    if (m_lacm)
    {
        m_lacm->onWindowFocusLost();
    }

    // unconfine the mouse in case it was confined by anything (like hmd mode)
    getWindow()->deconstrainMouse();
}

void ClientApplication::onWindowFocusGained(const LLWindow::WindowFocusGainedEvent&)
{
    // mouse is always confined in hmd mode, so reconfine it now that the window is back in focus
    if (isVrActive() && m_config->m_vr.m_constrainMouseInHmd)
    {
        getWindow()->constrainMouse();
    }
}

void ClientApplication::onScreenshotCaptureRequest(const ClientUI::ScreenshotCaptureRequest& request)
{
    if (rendererRunning())
    {
        auto onScreenshotRequestComplete = [request](LLCore::RefPointer<LLImage::ImageRaw>& imageRaw, const LLCore::MTransform& cameraTransform) {
            request.m_responseQueue->postEvent<ClientUI::ScreenshotCaptureResponse>(imageRaw, cameraTransform);
        };
        // n.b. using window dimensions rather than dimensions of source, because source is conservatively sized (because tiles)
        getEventQueue().postEvent<EngineApplication::ScreenshotRequest>((uint)m_width, (uint)m_height, LLCore::Give(onScreenshotRequestComplete));
    }
}


void ClientApplication::onMarketPlaceCapabilityMapRequest(const ClientUI::MarketPlaceCapabilityMapRequest& request)
{
    request.m_responseQueue->postEvent<ClientUI::MarketPlaceCapabilityMapRequest::Response>(m_remoteConfig->getRemoteConfig().m_marketplaceCapabilityAliases);
}


void ClientApplication::connectToLocalRegionServer(const AppCore::SansarExperienceUri& uri)
{
    LLCORE_ASSERT(m_usingLocalRegionConductor, "Settings incorrect for local region conductor use");
    // Note: in V3 this code doesn't really quite work right anymore, so the URI is being ignored for now
    LLCore::String serverAddress = m_config->m_connection.m_localServer;
    LLCore::Uuid   personaId;
    LLCore::String accountId;
    uint32         sharedRegionSecret;
    uint32         sharedVoiceSecret;
    mem_int        retries    = 0;
    const mem_int  maxRetries = 2;
    do
    {
        sharedRegionSecret = getLocalRegionSecret(serverAddress, &accountId, &personaId);
    } while (sharedRegionSecret == 0 && ++retries < maxRetries);

    //0 for errors
    if (sharedRegionSecret == 0)
    {
        LLCore::LogError("ClientApplication", "Unable to contact local regionserver at ", serverAddress);
        m_clientStateMachine.switchToShutdown();
        return;
    }

    retries = 0;
    do
    {
        sharedVoiceSecret = getLocalVoiceSecret(&personaId);
    } while (sharedVoiceSecret == 0 && ++retries < maxRetries);

    //0 for errors
    if (sharedVoiceSecret == 0)
    {
        LLCore::LogError("ClientApplication", "Unable to contact local voiceserver at ", serverAddress);
    }
    LLCore::Uuid        instance = m_config->m_connection.m_localInstanceId;
    VoiceManager::Cinfo voiceManagerCinfo;
    {
        voiceManagerCinfo.m_allocator            = LLCore::CoreAllocator::GetInstancePtr();
        voiceManagerCinfo.m_streamRouter         = m_streamRouter;
        voiceManagerCinfo.m_appEventQueue        = &getEventQueue();
        voiceManagerCinfo.m_nullUIEventQueue     = getUIEventQueue();
        voiceManagerCinfo.m_useTcp               = m_config->m_networking.m_useTcp;
        voiceManagerCinfo.m_config               = &m_config->m_voice;
        voiceManagerCinfo.m_audioConfig          = &m_config->m_audio;
        voiceManagerCinfo.m_enableSpeechGraphics = m_config->m_enableSpeechGraphics;
        voiceManagerCinfo.m_serverPortOffset     = getServerPortOffset();
        voiceManagerCinfo.m_useV4Socket          = m_config->m_networking.m_useV4Socket;
    }
    m_connectToRegionStateMachine.requestNextState<ConnectingToRegionAndVoiceServers>(m_config->m_account.m_localUserName.asRead(),
                                                                                      accountId,
                                                                                      personaId,
                                                                                      instance,
                                                                                      sharedRegionSecret,
                                                                                      serverAddress,
                                                                                      sharedVoiceSecret,
                                                                                      serverAddress,
                                                                                      voiceManagerCinfo,
                                                                                      0);
}

uint32 ClientApplication::getLocalRegionSecret(LLCore::String regionName, LLCore::String* accountId, LLCore::Uuid* personaId)
{
    const LLCore::StringFixed<256> useUserName      = m_identityManager->getAccount()->getUserName().isEmpty() ? m_config->m_account.m_localUserName : m_identityManager->getAccount()->getUserName();
    const LLCore::StringFixed<256> usePersonaName   = m_identityManager->getPersona()->getName().isEmpty() ? m_config->m_account.m_localPersonaName : m_identityManager->getPersona()->getName();
    const LLCore::StringFixed<256> usePersonaHandle = m_identityManager->getPersona()->getHandle().isEmpty() ? m_config->m_account.m_localPersonaHanlde : m_identityManager->getPersona()->getHandle();
    *accountId                                      = m_identityManager->getAccount()->getAccountId().isEmpty() ? m_config->m_account.m_localAccountId : m_identityManager->getAccount()->getAccountId();
    *personaId                                      = m_identityManager->getPersona()->getId().isNull() ? m_config->m_account.m_localPersonaId : m_identityManager->getPersona()->getId();

    // And a fake avatar appearance
    LLCore::StringFixed<256> useAvatarType;
    if (m_config->m_avatar.m_defaultAvatarResourceId.isProvided())
    { //a bit hacky for debugging purposes.

        Identity::PersonaAppearanceDescriptor descriptor;
        descriptor.m_avatarAssetId = m_config->m_avatar.m_defaultAvatarResourceId;
        useAvatarType              = descriptor.getTomlString();
    }
    else
    {
        RegionCommon::AvatarConfig avatarConfig(m_config->m_avatar.m_defaultAvatarId);
        useAvatarType = avatarConfig.toAppearanceDescriptor();
    }

    LLCore::StringFixed<256> wjson;
    LLJson::JsonWriter       writer;
    LLJson::JsonObject       wroot(&writer);

    wroot.addMember("userName"_ll, useUserName);
    wroot.addMember("personaName"_ll, usePersonaName);
    wroot.addMember("personaHandle"_ll, usePersonaHandle);
    wroot.addMember("avatarType"_ll, useAvatarType);

    // stinson 2017/08/10 : Currently, we do not give the account ID to the region server. Only the persona ID.
    //const LLCore::StringFixed<64> accountIdString(*accountId);
    //wroot.addMember("accountId"_ll, accountIdString);

    const LLCore::StringFixed<64> personaIdString(*personaId);
    wroot.addMember("personaId"_ll, personaIdString);


    LLJson::JsonArray wroles(&writer);
    for (const LLCore::StringFixed<32>* role = m_config->m_account.m_roles.begin();
         role != m_config->m_account.m_roles.end();
         ++role)
    {
        wroles.pushValue(*role);
    }
    wroot.addMember("roles"_ll, &wroles);

    writer.setRoot(&wroot);
    writer.writeToString(&wjson);

    LLHttp::HttpRequest::Ptr httpRequest = LLHttp::HttpRequest::CreateRequest("POST"_ll, "/addUser"_ll, "HTTP/1.1"_ll);
    httpRequest->setBody(wjson);

    LLCore::StringFixed<64> serverAddress;
    int                     x = m_config->m_connection.m_localServer.asRead().find(':');
    if (x != -1)
    {
        LLCore::StringFixed<64> configServerAddress;
        configServerAddress.copy(m_config->m_connection.m_localServer);
        configServerAddress.erase(x);

        serverAddress.format("%s:%d", configServerAddress, 23080 + getServerPortOffset());
    }
    else
    {
        serverAddress.format("%s:%d", m_config->m_connection.m_localServer, 23080 + getServerPortOffset());
    }

    LLHttp::HttpResponse::Ptr response = m_httpManager->sendRequestBlocking(serverAddress, httpRequest);
    if (response->getBody().getCount() > 0)
    {
        LLCore::StringFixed<256> rjson;

        response->getBody(&rjson);

        LLJson::JsonReader reader(rjson);
        LLCORE_ASSERT(reader.isParseSuccessful(), "Unable to parse JSON string");

        LLJson::JsonValue rroot;
        reader.getRoot(&rroot);

        LLCore::StringFixed<64> value;
        LLCORE_ASSERT(rroot.hasMember("secret"), "Missing secret");
        rroot.getMemberValue("secret", &value);

        uint32 secret;
        ConvertType(value, &secret);
        return secret;
    }
    else
    {
        // 0 is error response
        return 0;
    }
}

uint32 ClientApplication::getLocalVoiceSecret(LLCore::Uuid* personaId)
{

    // set the default local persona id to a non-null value
    if (m_config->m_account.m_localPersonaId.isProvided() == false)
    {
        LLCore::ConvertType(Identity::cDefaultLocalPersonaId, &m_config->m_account.m_localPersonaId.asWrite());
    }

    *personaId = m_identityManager->getPersona()->getId().isNull() ? m_config->m_account.m_localPersonaId : m_identityManager->getPersona()->getId();

    LLCore::StringFixed<256> wjson;
    LLJson::JsonWriter       writer;
    LLJson::JsonObject       wroot(&writer);

    const LLCore::StringFixed<64> personaIdString(*personaId);
    wroot.addMember("personaId"_ll, personaIdString);

    LLJson::JsonArray wroles(&writer);
    for (const LLCore::StringFixed<32>* role = m_config->m_account.m_roles.begin();
         role != m_config->m_account.m_roles.end();
         ++role)
    {
        wroles.pushValue(*role);
    }
    wroot.addMember("roles"_ll, &wroles);

    writer.setRoot(&wroot);
    writer.writeToString(&wjson);

    LLHttp::HttpRequest::Ptr httpRequest = LLHttp::HttpRequest::CreateRequest("POST"_ll, "/addUser"_ll, "HTTP/1.1"_ll);
    httpRequest->setBody(wjson);

    LLCore::StringFixed<64> serverAddress;
    int                     x = m_config->m_connection.m_localServer.asRead().find(':');
    if (x != -1)
    {
        LLCore::StringFixed<64> configServerAddress;
        configServerAddress.copy(m_config->m_connection.m_localServer);
        configServerAddress.erase(x);

        serverAddress.format("%s:%d", configServerAddress, 15022 + getServerPortOffset());
    }
    else
    {
        serverAddress.format("%s:%d", m_config->m_connection.m_localServer, 15022 + getServerPortOffset());
    }

    LLHttp::HttpResponse::Ptr response = m_httpManager->sendRequestBlocking(serverAddress, httpRequest);
    if (response->getBody().getCount() > 0)
    {
        LLCore::StringFixed<256> rjson;

        response->getBody(&rjson);

        LLJson::JsonReader reader(rjson);
        LLCORE_ASSERT(reader.isParseSuccessful(), "Unable to parse JSON string");

        LLJson::JsonValue rroot;
        reader.getRoot(&rroot);

        LLCore::StringFixed<64> value;
        LLCORE_ASSERT(rroot.hasMember("secret"), "Missing secret");
        rroot.getMemberValue("secret", &value);

        uint32 secret;
        ConvertType(value, &secret);
        return secret;
    }
    else
    {
        // 0 is error response
        return 0;
    }
}

uint32 ClientApplication::getLocalAccountSecret(LLCore::String* accountId, LLCore::Uuid* personaId)
{
    LLCore::Uuid   usePersonaId;
    LLCore::String useAvatarType;
    LLCore::String useUserName;
    bool           personaProvided = m_config->m_account.m_localPersonaId.isProvided();
    bool           accountProvided = m_config->m_account.m_localAccountId.isProvided();

    if (!personaProvided)
    {
        LLCore::ConvertType(Identity::cDefaultLocalPersonaId, &m_config->m_account.m_localPersonaId.asWrite());
    }

    if (!accountProvided)
    {
        LLCore::ConvertType(Identity::cDefaultLocalPersonaId, &m_config->m_account.m_localAccountId.asWrite());
    }

    if (!(personaProvided && accountProvided) && m_identityManager->getAccount() != nullptr && m_identityManager->getPersona() != nullptr)
    {
        useUserName  = m_identityManager->getPersona()->getName();
        usePersonaId = m_identityManager->getPersona()->getId();
        *accountId   = m_identityManager->getAccount()->getAccountId();
    }
    else
    {
        useUserName  = m_config->m_account.m_localUserName;
        usePersonaId = m_config->m_account.m_localPersonaId;

        *accountId = m_config->m_account.m_localAccountId;
    }

    *personaId = usePersonaId;

    LLCore::StringFixed<256> wjson;
    LLJson::JsonWriter       writer;
    LLJson::JsonObject       wroot(&writer);

    LLCore::StringFixed<64> personaIdString(*personaId);
    LLCore::StringFixed<64> accountIdString(*accountId);

    wroot.addMember("personaId"_ll, personaIdString);
    wroot.addMember("avatarType"_ll, useAvatarType);
    wroot.addMember("accountId"_ll, accountIdString);
    writer.setRoot(&wroot);
    writer.writeToString(&wjson);

    LLHttp::HttpRequest::Ptr httpRequest = LLHttp::HttpRequest::CreateRequest("POST"_ll, "/addUser"_ll, "HTTP/1.1"_ll);
    httpRequest->setBody(wjson);

    LLCore::StringFixed<64> serverAddress;
    int                     x = m_config->m_account.m_kafkaConnectorAddress.asRead().find(':');
    if (x != -1)
    {
        LLCore::StringFixed<64> configServerAddress;
        configServerAddress.copy(m_config->m_account.m_kafkaConnectorAddress);
        configServerAddress.erase(x);

        serverAddress.format("%s:%d", configServerAddress, 15080 + getServerPortOffset());
    }
    else
    {
        serverAddress.format("%s:%d", m_config->m_account.m_kafkaConnectorAddress, 15080 + getServerPortOffset());
    }

    LLHttp::HttpResponse::Ptr response = m_httpManager->sendRequestBlocking(serverAddress, httpRequest);
    if (response->getBody().getCount() > 0)
    {
        LLCore::StringFixed<256> rjson;

        response->getBody(&rjson);

        LLJson::JsonReader reader(rjson);
        LLCORE_ASSERT(reader.isParseSuccessful(), "Unable to parse JSON string");

        LLJson::JsonValue rroot;
        reader.getRoot(&rroot);

        LLCore::StringFixed<64> value;
        LLCORE_ASSERT(rroot.hasMember("secret"), "Missing secret");
        rroot.getMemberValue("secret", &value);

        uint32 secret;
        ConvertType(value, &secret);
        return secret;
    }
    else
    {
        // 0 is error response
        return 0;
    }
}

void ClientApplication::connectToKafkaConnectorAddress(const ClientServices::ServerAddressResponse& kafkaConnectorAddress)
{
    LLCore::EventQueue* eventQueue = &getEventQueue();
    LLCore::String      accountId;
    LLCore::Uuid        personaId;

    ClientServices::ServerAddressResponse::Protocol proto = ClientServices::ServerAddressResponse::Protocol::cUdp;
    if (m_config->m_networking.m_useTcp)
    {
        proto = ClientServices::ServerAddressResponse::Protocol::cTcp;
    }
    LLHttp::HttpUrl kafkaConnectorUrl = kafkaConnectorAddress.asHttpUrl(proto);
    LLCore::LogInfo("ClientApplication", "Region Conductor connecting us to account connector: ", kafkaConnectorUrl.toAddress());

    accountId = m_identityManager->getAccount()->getAccountId();
    personaId = m_identityManager->getPersona()->getId();

    m_inventoryManager->readLocalCacheFile();

    m_accountConnector->connectToKafka(
        accountId,
        personaId,
        kafkaConnectorAddress.m_sharedSecret,
        kafkaConnectorUrl.toAddress(),
        [eventQueue]() {
            eventQueue->postEvent<AccountConnectorState::ConnectedToAccountEvent>();
        },
        m_inventoryManager->getLastKafkaMessageIdSaved());
}

void ClientApplication::onAccountConnectorReconnectResponse(const ClientServices::AccountConnectorResponse& response)
{
    getEventQueue().removeCallback(this, &ClientApplication::onAccountConnectorReconnectResponse);

    if (!response.isOk())
    {
        LLCore::LogError("ClientApplication", "Unable to connect to account inventory, retrying...");
        ClientServices::KafkaDisconnectedEvent disconnectEvent(LLNetwork::Connection::DisconnectReason::cDisconnectReason_DestinationDisconnected);
        getEventQueue().addCallback(this, &ClientApplication::handleUnexpectedKafkaDisconnect);
        getEventQueue().postEvent(disconnectEvent);
        return;
    }

    connectToKafkaConnectorAddress(response.m_kafkaConnectorAddress);

    m_relationshipManager->logout();
    m_relationshipManager->setCurrentUserPersonaId(m_identityManager->getPersona()->getId());

    m_chatManager->logout();
    m_chatManager->setPersonaId(m_identityManager->getPersona()->getId());
}

void ClientApplication::handleUnexpectedKafkaDisconnect(const ClientServices::KafkaDisconnectedEvent& event)
{
    getEventQueue().removeCallback(this, &ClientApplication::handleUnexpectedKafkaDisconnect);
    LLCore::LogInfo("ClientApplication", "Client lost connection to Account Connector, Reason: ", event.m_reason);

    if (m_accountConnectorRetryAttempts < m_maxAccountConnectorRetries && event.m_reason != LLNetwork::Connection::DisconnectReason::cDisconnectReason_ApplicationRequested)
    {
        LLCore::LogInfo("ClientApplication", "Trying to reconnect in ", m_accountConnectorReconnectDelay.asSeconds(), " seconds.  Attempts: ", m_accountConnectorRetryAttempts + 1, " / ", m_maxAccountConnectorRetries);

        if (m_delayedAccountConnectorReconnect.isConstructed())
        {
            m_delayedAccountConnectorReconnect.destruct();
        }

        m_delayedAccountConnectorReconnect.construct(m_accountConnectorReconnectDelay, [this](LLCore::AsyncTimer*) {
            LLCore::CoreAsyncTimerManager::GetInstance().stop(&m_delayedAccountConnectorReconnect.asBase());
            m_accountConnectorRetryAttempts++;
            LLCore::LogInfo("ClientApplication", "Attempting Account Connector Reconnect");
            if (!m_usingLocalRegionConductor)
            {
                getEventQueue().addCallback(this, &ClientApplication::onAccountConnectorReconnectResponse);
                ClientServices::AccountConnectorRequest request;

                if (m_config->m_connection.m_conductor.m_configuration.isProvided())
                {
                    request.useConfiguration(m_config->m_connection.m_conductor.m_configuration);
                }
                if (m_config->m_connection.m_conductor.m_accessGroup.isProvided())
                {
                    request.useAccessGroup(m_config->m_connection.m_conductor.m_accessGroup.asRead().asRead());
                }

                LLCore::StringFixed<32> clientId; // Placeholder
                LLCore::StringFixed<32> clientType       = getConfig().m_enableRenderer ? "sansar-client"_ll : "sansar-headless-client"_ll;
                LLCore::StringFixed<32> clientInstanceId = LLCore::Format("%llu", LLCore::GetProcessId());
                LLCore::StringFixed<32> clientVersion    = m_config->m_appVersion;

                request.setClientData(clientId.asRead(),
                                      clientType.asRead(),
                                      clientInstanceId.asRead(),
                                      clientVersion.asRead(),
                                      m_clientHardwareInfo);

                request.respondOnQueue(&getEventQueue());
                getEventQueue().postEvent(request);
            }
        });

        LLCore::CoreAsyncTimerManager::GetInstance().start(&m_delayedAccountConnectorReconnect.asBase());
    }
}

ClientApplication::~ClientApplication()
{
    LLCore::LogInfo("ClientApplication", "Destruct");

#ifdef CLIENT_USE_FFMPEG
    delete m_streamMuxer;
#endif

    stopStreamer();

    m_ftueTest = nullptr;

    getEventQueue().removeCallbacksWithOwner(this);

    // Stop all application services. This should be done at the beginning of the application destructor.
    m_appServiceRegistry->stopAll();

    m_config->m_isFirstRun = false;
    m_config->saveIfDirty();

    getEventQueue().processEvents();

    m_clientStateMachine.terminate();

    delete m_debugConsoleCommandManager;
    m_debugConsoleCommandManager = nullptr;

    if (isVrActive())
    {
        m_vrSystem->toggleActive(false);
    }

    ClientUI::MetricsHeatmap::destroyMetricsHeatmap();

    delete m_clientHttpManager;
    m_clientHttpManager = nullptr;

    delete m_webFrontend;
    m_webFrontend = nullptr;

    delete m_homeSpace;
    m_homeSpace = nullptr;

    delete m_comfortZoneFilter;
    m_comfortZoneFilter = nullptr;

    delete m_vrVisualizationLayer;
    m_vrVisualizationLayer = nullptr;

    delete m_runtimeCommonAssetManager;
    m_runtimeCommonAssetManager = nullptr;

    if (m_mediaThread)
    {
        m_mediaThread->requestStop();
        m_mediaThread->removeRef();
        m_mediaThread = nullptr;
    }

    delete m_lacm;
    m_lacm = nullptr;

    delete m_chatManager;
    m_chatManager = nullptr;

    delete m_notificationManager;
    m_notificationManager = nullptr;

    LLCORE_ASSERT(m_connectToRegionStateMachine.getCurrentState<DisconnectedFromRegion>(), "Need to disconnect from region before destructing");

    delete m_relationshipManager;
    m_relationshipManager = nullptr;

    // Since m_accountConnector is about to be destroyed, disconnect m_metricsManager from it.
    m_metricsManager->setKafkaManager(nullptr);

    delete m_accountConnector;
    m_accountConnector = nullptr;

    delete m_regionConductor;
    m_regionConductor = nullptr;

    delete m_apiLocatorService;
    m_apiLocatorService = nullptr;

    delete m_streamRouter;
    m_streamRouter = nullptr;

    delete m_gamepadManager;
    m_gamepadManager = nullptr;

    delete m_giftingManager;
    m_giftingManager = nullptr;

    delete m_tippingManager;
    m_tippingManager = nullptr;

    if (m_identityManager != nullptr)
    {
        m_identityManager->setIdentityChangeHandler(nullptr);
    }
    if (m_authManager != nullptr)
    {
        m_authManager->setOAuthTokenChangeHandler(nullptr);
    }

    delete m_loginManager;
    m_loginManager = nullptr;

    delete m_subscriptionManager;
    m_subscriptionManager = nullptr;

    delete m_identityManager;
    m_identityManager = nullptr;

    delete m_authManager;
    m_authManager = nullptr;

    delete m_codeVerifierCache;
    m_codeVerifierCache = nullptr;

    delete m_accountConfigurationManager;
    m_accountConfigurationManager = nullptr;

    delete m_licenseService;
    m_licenseService = nullptr;

    delete m_experienceManager;
    m_experienceManager = nullptr;

    delete m_userProfileManager;
    m_userProfileManager = nullptr;

    delete m_personaCatalog;
    m_personaCatalog = nullptr;

    delete m_codexNotificationsManager;
    m_codexNotificationsManager = nullptr;

    delete m_atlasManager;
    m_atlasManager = nullptr;

    delete m_thumbnailManager;
    m_thumbnailManager = nullptr;

    delete m_supportRequestManager;
    m_supportRequestManager = nullptr;

    delete m_assetImportManager;
    m_assetImportManager = nullptr;

    delete m_inventoryManager;
    m_inventoryManager = nullptr;

    delete m_builtInInventory;
    m_builtInInventory = nullptr;

    delete m_userEmotes;
    m_userEmotes = nullptr;

    delete m_xsensManager;
    m_xsensManager = nullptr;

    if (m_clientMarketplace != nullptr)
    {
        delete m_clientMarketplace;
        m_clientMarketplace = nullptr;
    }

    delete m_marketplace;
    m_marketplace = nullptr;

    delete m_operationQueue;
    m_operationQueue = nullptr;

    delete m_webImageManager;
    m_webImageManager = nullptr;

    delete m_depDupSessionData;
    m_depDupSessionData = nullptr;

    delete m_httpManager;
    m_httpManager = nullptr;

    delete m_compilerClient;
    m_compilerClient = nullptr;

    delete m_scriptCompiler;
    m_scriptCompiler = nullptr;

    if (m_clientAudio != nullptr)
    {
        delete m_clientAudio;
        m_clientAudio = nullptr;
    }

    m_screenshotManager.shutdown();

    if (m_settingsManager != nullptr)
    {
        getEventQueue().removeCallback(this, &ClientApplication::onEngineSettingsChanged);

        delete m_settingsManager;
        m_settingsManager = nullptr;
    }

    shutdownRendering();

    terminateResourceJobs();

    EditWorkspace::Terminate();

    delete m_ftueControlsManager;
    m_ftueControlsManager = nullptr;

    delete m_telnetServer;
    m_telnetServer = nullptr;

    if (m_inputHintController)
    {
        m_inputHintController->cleanupInputHints();
        delete m_inputHintController;
        m_inputHintController = nullptr;
    }

    // The userData and appData must be destroyed before the clientUIManager.
    m_userData = nullptr;
    m_appData  = nullptr;

    // The dataSyncRoot must be destroyed after appData and userData.
    // The dataSyncRoot must be destroyed before clientUIManager.
    m_dataSyncRoot = nullptr;

    // Destroying the clientUIManager also destroys the UIEventQueue.
    delete m_clientUIManager;
    m_clientUIManager = nullptr;

    terminateAssetSystem();

    LLTextureProcessing::Platform::Shutdown();

    //EditEngine::EditParameterDescriptorRegistry::Terminate();
    LLResource::Terminate();

    LLMetrics::MetricsManager::SwapImplementation(m_oldMetricsManager);
    delete m_metricsManager;
    m_metricsManager = nullptr;

    if (m_steamworksManager)
    {
        delete m_steamworksManager;
        m_steamworksManager = nullptr;
    }

    getEventQueue().removeCallback(this, &ClientApplication::handleTelnetEvent);
    getEventQueue().removeCallback(this, &ClientApplication::onSetWindowTitleRequest);
    getEventQueue().removeCallback(this, &ClientApplication::onWindowResized);
    getEventQueue().removeCallback(this, &ClientApplication::onWindowStartResize);
    getEventQueue().removeCallback(this, &ClientApplication::onWindowEndResize);
    getEventQueue().removeCallback(this, &ClientApplication::handleConnectToRegionRequest);
    getEventQueue().removeCallback(this, &ClientApplication::handleEditSceneRequest);
    getEventQueue().removeCallback(this, &ClientApplication::onWindowFocusLost);
    getEventQueue().removeCallback(this, &ClientApplication::onWindowFocusGained);
    getEventQueue().removeCallback(this, &ClientApplication::onVrStateChanged);
    getEventQueue().removeCallback(this, &ClientApplication::onVrCalibrationComplete);
    getEventQueue().removeCallback(this, &ClientApplication::handleSwitchToEditModeRequest);
    getEventQueue().removeCallback(this, &ClientApplication::handleEditModeViewSwitched);
    getEventQueue().removeCallback(this, &ClientApplication::handleSwitchToRegionSelectModeRequest);
    getEventQueue().removeCallback(this, &ClientApplication::handleIsDeveloperModeRequest);
    getEventQueue().removeCallback(this, &ClientApplication::onScreenshotCaptureRequest);
    getEventQueue().removeCallback(this, &ClientApplication::onMarketPlaceCapabilityMapRequest);
    getEventQueue().removeCallback(this, &ClientApplication::onCompiledSceneEvent);
    getEventQueue().removeCallback(this, &ClientApplication::onPublishedSceneEvent);
    getEventQueue().removeCallback(this, &ClientApplication::onRefreshAudioDevicesEvent);
    getEventQueue().removeCallback(this, &ClientApplication::onConnectedToAccountEvent);
    getEventQueue().removeCallback(this, &ClientApplication::handleTeleportToFriendRequest);
    getEventQueue().removeCallback(this, &ClientApplication::handleUnexpectedKafkaDisconnect);
    getEventQueue().removeCallback(this, &ClientApplication::onScriptConsoleBeginRequest);
    getEventQueue().removeCallback(this, &ClientApplication::onScriptConsoleEndRequest);
    getEventQueue().removeCallback(this, &ClientApplication::onApiLocatorLoadedEvent);
    getEventQueue().removeCallback(this, &ClientApplication::onPersonaDataResponse);
    getEventQueue().removeCallback(this, &ClientApplication::handleDebugDeltaTimeResponse);
    getEventQueue().removeCallback(this, &ClientApplication::handleVisualDebuggerCaptureResponse);
    getEventQueue().removeCallback(this, &ClientApplication::handleApplicationMenuToggledEvent);
    getEventQueue().removeCallback(this, &ClientApplication::handleCancelSceneLoadEvent);
    getEventQueue().removeCallback(this, &ClientApplication::handleGlobalKeyPressedEvent);

    getEventQueue().removeCallbacksWithOwner(this);

    // Uninstall all application services. This should be done at the end of the application destructor.
    m_appServiceRegistry->uninstallAll();
}

void ClientApplication::setLoadingPhase(LoadingPhase phase)
{
    if (m_currentLoadingPhase != phase)
    {
        if (LoadingPhase::cWaitingForRegionLoad == phase)
        {
            pauseRenderScriptRestartReload(true);
        }
        else if ((LoadingPhase::cLoaded == phase) || (LoadingPhase::cNone == phase))
        {
            pauseRenderScriptRestartReload(false);
        }



        if (phase == LoadingPhase::cWaitingForRegionLaunch)
        {
            m_currentLoadingPhaseProgress = 0.0f;
            m_currentLoadingDuration.setZero();
        }

        m_currentLoadingPhaseTimer.stop();
        LLCore::Duration duration = m_currentLoadingPhaseTimer.getElapsed();
        m_currentLoadingDuration += duration;
        LLCore::LogInfo<"SceneLoad"_ll_tag>("SceneLoad", m_currentLoadingPhase, " ", duration.asFloat(), " seconds, ", m_currentLoadingDuration, " total");
        logLoadStatistics();

        LLMetrics::RecordMetric(ClientServices::SceneLoadProgressMetricEvent(LLCore::ConvertType<LLCore::StringFixed<128>>(m_config->m_asset.m_http.m_urlTemplate), duration, m_currentLoadingDuration, m_requestedExperienceUri, LLCore::ConvertType<LLCore::StringFixed<32>>(m_currentLoadingPhase)));
        m_currentLoadingPhaseTimer.reset();

        m_currentLoadingPhase = phase;

        if (m_currentLoadingPhase != LoadingPhase::cNone)
        {
            m_currentLoadingPhaseTimer.start();
        }

        resetLoadingPhaseAssetBasis();
    }
}

void ClientApplication::resetLoadingPhaseAssetBasis()
{
    m_currentLoadingPhaseAssetManagerBasis = 0;

    if (m_assetManager != nullptr)
    {
        LLAssetSystem::DedupingAssetReader::Progress ioProgress;
        m_assetManager->getProgress(&ioProgress);
        m_currentLoadingPhaseAssetManagerBasis = ioProgress.m_totalRequests;
    }
}

void ClientApplication::copySceneUriToClipboard()
{
    if (m_clientStateMachine.getCurrentState() == ApplicationStateId::cInWorld)
    {
        AppCore::SansarExperienceUri sansarExperienceUri = m_currentExperienceUri;
        sansarExperienceUri.setInstanceId(m_currentRegionInstanceID);

        LLCore::StringFixed<1024> uriString(sansarExperienceUri);

        LLUI::Clipboard::Copy(uriString.asRead());
    }
}

void ClientApplication::handleCopySceneUriToClipboard(const ClientServices::CopySceneUriToClipboardEvent& event)
{
    copySceneUriToClipboard();
}

void ClientApplication::updateLoadingProgress(LoadingProgress* result)
{
    float nextTotalPercentage = 0.0f;

    result->m_phase = m_currentLoadingPhase;
    result->m_stage = RegionCommon::WorldStateManager::WorldLoadStage::cNone;
    result->m_description.clear();

    float nextIoPercent = 0.0f;
    if (m_assetManager != nullptr)
    {
        LLAssetSystem::DedupingAssetReader::Progress ioProgress;
        m_assetManager->getProgress(&ioProgress);
        if (ioProgress.m_totalRequests > m_currentLoadingPhaseAssetManagerBasis)
        {
            if (ioProgress.m_completedRequests == ioProgress.m_totalRequests)
            {
                nextIoPercent = 1.0f;
            }
            else
            {
                LLCore::LogInfo<"LoadingProgress"_ll_tag>("LoadingProgress", "IO: ", ioProgress.m_completedRequests - m_currentLoadingPhaseAssetManagerBasis, " of ", ioProgress.m_totalRequests - m_currentLoadingPhaseAssetManagerBasis);

                nextIoPercent = (float)(ioProgress.m_completedRequests - m_currentLoadingPhaseAssetManagerBasis + 1) / (float)(ioProgress.m_totalRequests - m_currentLoadingPhaseAssetManagerBasis);
            }
        }
    }

    switch (m_currentLoadingPhase)
    {
        case LoadingPhase::cNone:
            nextTotalPercentage = 0.00f;
            break;
        case LoadingPhase::cWaitingForRegionLaunch:
            nextTotalPercentage   = 0.05f;
            result->m_description = "Launching server";
            break;
        case LoadingPhase::cWaitingForRegionConnection:
            nextTotalPercentage   = 0.07f;
            result->m_description = "Connecting to server";
            break;
        case LoadingPhase::cWaitingForRegionLoad:
        {
            result->m_description     = "Connecting to server";
            float nextPhasePercentage = 0.02f;

            /////////////////////////////////////////////////////////////////////////////////////
            // dig down into the detail of this particular phase and break it down more
            /////////////////////////////////////////////////////////////////////////////////////
            RegionCommon::WorldStateManager* wsm = getWorldStateManager();
            if (wsm != nullptr)
            {
                result->m_stage = wsm->getWorldLoadStage();
                switch (result->m_stage)
                {
                    case RegionCommon::WorldStateManager::WorldLoadStage::cNone:
                    case RegionCommon::WorldStateManager::WorldLoadStage::cError:
                        nextPhasePercentage = 0.00f;
                        break;
                    case RegionCommon::WorldStateManager::WorldLoadStage::cAwaitingDirection:
                        nextPhasePercentage   = 0.01f;
                        result->m_description = "Connecting to server";
                        break;
                    case RegionCommon::WorldStateManager::WorldLoadStage::cLoadingWorldDefinition:
                        nextPhasePercentage   = 0.02f;
                        result->m_description = "Loading scene description";
                        break;
                    case RegionCommon::WorldStateManager::WorldLoadStage::cLoadingWorldScriptResources:
                        nextPhasePercentage   = 0.03f + (nextIoPercent * 0.02f);
                        result->m_description = "Loading scene resources";
                        break;
                    case RegionCommon::WorldStateManager::WorldLoadStage::cLoadingWorldChunk:
                        nextPhasePercentage   = 0.05f + (nextIoPercent * 0.93f);
                        result->m_description = "Loading scene resources";
                        break;
                    case RegionCommon::WorldStateManager::WorldLoadStage::cLoadingObjectScriptResources:
                        nextPhasePercentage   = 0.98f + (nextIoPercent * 0.02f);
                        result->m_description = "Loading scene resources";
                        break;
                    case RegionCommon::WorldStateManager::WorldLoadStage::cFinished:
                        nextPhasePercentage   = 1.00f;
                        result->m_description = "Finishing scene";
                        break;
                }
            }

            nextTotalPercentage = 0.07f + (nextPhasePercentage * 0.83f);
            break;
        }
        case LoadingPhase::cWaitingForDynamicObjects:
            nextTotalPercentage   = 0.90f + (nextIoPercent * 0.07f);
            result->m_description = "Loading avatars";
            break;
        case LoadingPhase::cWaitingForSelfAvatar:
            nextTotalPercentage   = 0.97f + (nextIoPercent * 0.03f);
            result->m_description = "Loading your avatar";
            break;
        case LoadingPhase::cLoaded:
            m_finalizeLoadingPhaseProgressSpeed = LLCore::Max(0.5f, (1.f - m_currentLoadingPhaseProgress) / 0.5f);
            nextTotalPercentage                 = 1.f;
            result->m_description               = "Loading your avatar";

            break;
    }

    const LLCore::Duration frameTimeElapsed = getPrecisionFrameElapsed();

    if (m_currentLoadingPhase == LoadingPhase::cLoaded)
    {
        float adjust = m_finalizeLoadingPhaseProgressSpeed * LLCore::Max(frameTimeElapsed.asFloat(), 0.01f);
        m_currentLoadingPhaseProgress += adjust;

        if (m_currentLoadingPhaseProgress >= 1.f)
        {
            m_clientStateMachine.createdWorld();
            m_currentLoadingPhaseProgress = 1.f;
            setLoadingPhase(LoadingPhase::cNone); // all done

            Engine::RuntimeWorld* runtimeWorld = getRuntimeWorld();
            runtimeWorld->onCreatedWorld();
        }
    }
    else
    {
        constexpr LLCore::Duration cDecayTimeConstant = 1.5_ll_sec;
        constexpr LLCore::Duration cMinProgressTime   = 2_ll_sec;
        const float                interpAmount       = (1.0f - LLCore::Pow(2.0f, (float)(-frameTimeElapsed / cDecayTimeConstant)));
        const float                remainingDistance  = LLCore::Max(0.f, nextTotalPercentage - m_currentLoadingPhaseProgress);
        float                      adjust             = LLCore::Clamp((remainingDistance * interpAmount), 0.f, remainingDistance * (float)(frameTimeElapsed / cMinProgressTime));
        m_currentLoadingPhaseProgress += adjust;
    }

    result->m_totalPercentageComplete = m_currentLoadingPhaseProgress;
    LLCore::LogInfo<"LoadingProgress"_ll_tag>("LoadingProgress", "Current: ", m_currentLoadingPhaseProgress, " Next: ", nextTotalPercentage);
}

void ClientApplication::handleSetRenderModeEvent(const EngineRender::SetRenderModeEvent& event)
{
    BaseType::setRenderMode(event);
}

void ClientApplication::handleRestartRenderScriptEvent(const EngineRender::RestartRenderScriptEvent& event)
{
    if (event.m_needReload)
    {
        requestRenderScriptReload(event.m_reason.asRead());
    }
    else
    {
        requestRenderScriptRestart(event.m_reason.asRead());
    }
}

void ClientApplication::handleMasterInstanceEvent(const MasterInstanceEvent& event)
{
    LLCore::StringFixed<64> instanceId;
    ConvertType(event.m_packet.m_instance, &instanceId);

    if (m_accountConnector != nullptr)
    {
        m_accountConnector->leaveRegion();
        m_accountConnector->enterRegion(instanceId.asRead());
    }
}

void ClientApplication::handleCancelSceneLoadEvent(const ClientServices::CancelSceneLoadEvent& event)
{
    LLCore::Duration currentLoadingPhaseDuration = m_currentLoadingPhaseTimer.getElapsed();
    LLMetrics::RecordMetric(ClientServices::CancelSceneLoadMetricEvent(m_config->m_asset.m_http.m_urlTemplate, currentLoadingPhaseDuration, m_currentLoadingDuration, m_requestedExperienceUri, LLCore::ConvertType<LLCore::String>(m_currentLoadingPhase)));
    queueDisconnectNextFrame();
}

void ClientApplication::toggleHMD()
{
    if ((m_clientStateMachine.getCurrentState() == ApplicationStateId::cInWorld)
        || (m_clientStateMachine.getCurrentState() == cEditMode)
        || (m_clientStateMachine.getCurrentState() == ApplicationStateId::cLoginAccount)
        || (m_clientStateMachine.getCurrentState() == ApplicationStateId::cHomeSpace)
        || (m_clientStateMachine.getCurrentState() == ApplicationStateId::cConnectToRegion))
    {
        if (isVrAvailable() && (m_homeSpace != nullptr || m_worldStateManager != nullptr || m_clientStateMachine.getCurrentState() == cEditMode))
        {
            m_vrSystem->toggleActive(m_clientStateMachine.getCurrentState() == cEditMode);
        }
    }
}

// HACK: remove this when we support global shortcuts
void ClientApplication::handleGlobalKeyPressedEvent(const LLUI::GlobalKeyPressedEvent& event)
{
    switch (event.m_keyCode)
    {
        case LLInput::KeyCode::cKey_Escape:
        {
            if (!m_lacm->isFilmingCameraControlMode() && ((m_clientStateMachine.getCurrentState() == ApplicationStateId::cInWorld)
                || (m_clientStateMachine.getCurrentState() == ApplicationStateId::cEditMode && !isEditViewModeCharacterEditor())
                || (m_clientStateMachine.getCurrentState() == ApplicationStateId::cHomeSpace))
                )
            {
                postUIEvent(LLUI::HidePanelRequest("SettingsDialog"_ll));
                postUIEvent(LLUI::HidePanelRequest("Help"_ll));
                postUIEvent(LLUI::HidePanelRequest("VolumeSlidersDialog"_ll));
                postUIEvent(LLUI::ToggleModalRequest("EscMenu"_ll));
            }
            break;
        }
        case LLInput::KeyCode::cKey_F1:
        {
            if (!m_lacm->isFilmingCameraControlMode())
            {
                postUIEvent(LLUI::HidePanelRequest("SettingsDialog"_ll));
                postUIEvent(LLUI::HidePanelRequest("EscMenu"_ll));
                postUIEvent(LLUI::HidePanelRequest("VolumeSlidersDialog"_ll));
                postUIEvent(LLUI::ToggleModalRequest("Help"_ll));
                getEventQueue().postEvent<ClientAudio::PlayUIAudioEventRequest>(ClientAudio::cEventButtonClick);
                break;
            }
        }
        case LLInput::KeyCode::cKey_F8:
        {
            if (LLCore::EventQueue* uiEventQueue = getUIEventQueue())
            {
                uiEventQueue->postFunction([]() { ClientUI::MetricsHeatmap::toggleMetricsHeatmap(); });
            }
            break;
        }
        case LLInput::KeyCode::cKey_F9:
        {
            toggleHMD();
            break;
        }
        case LLInput::KeyCode::cKey_H:
        {
            getUIEventQueue()->postEvent<LLUI::ToggleAllWorkspacePanelsEvent>();
            break;
        }
        default:
            break;
    }
}

void ClientApplication::handleScreenshotByEditorEvent(const LLUI::ScreenshotByEditorRequest& event)
{
    ClientUI::PushAppletMenuScreenshotButtonEvent pushScreenshot;

    if (event.m_cameraComponentId == LLGems::cInvalidComponentId)
    {
        getUIEventQueue()->postEvent(pushScreenshot);
    }
    else
    {
        // we need to post the camera changes on the App Thread, the UI thread havok memory manager is null!  -rome

        LLGems::ComponentId  componentId(event.m_cameraComponentId);
        Engine::CameraHandle cameraHandle(m_lacm->getAgentControllerManager()->getRuntimeWorld()->getCameraManager()->getComponentById(componentId));
        if (cameraHandle.notNull())
        {
            Engine::ControlMode               scriptControlMode      = Engine::ControlMode::cScriptCamera;
            Engine::UserCameraBehaviorId      scriptCameraBehaviorId = m_lacm->getUserCameraBehaviorIdFromControlMode(scriptControlMode);
            Engine::UserCameraScriptBehavior* scriptCameraBehavior   = static_cast<Engine::UserCameraScriptBehavior*>(m_lacm->getUserCamera()->getBehavior((uint8)scriptCameraBehaviorId));

            if (scriptCameraBehavior != nullptr)
            {
                getEventQueue().addCallback(this, &ClientApplication::handleScreenshotByEditorEvent_RestoreCamera);

                scriptCameraBehavior->setActiveComponent(cameraHandle, (Engine::ScriptCameraControlMode::cCameraOnly));

                if (m_lacm->getControlMode() != scriptControlMode)
                {
                    m_lacm->setControlModeLocked(false);
                    m_lacm->setControlMode(scriptControlMode);
                }
                getUIEventQueue()->postEvent(pushScreenshot);
            }
        }
    }
}
void ClientApplication::handleScreenshotByEditorEvent_RestoreCamera(const LLUI::PanelHiddenEvent& event)
{
    if (event.m_panelName == "ScreenshotEditor")
    {
        getEventQueue().removeCallback(this, &ClientApplication::handleScreenshotByEditorEvent_RestoreCamera);

        if (m_lacm->getControlMode() == Engine::ControlMode::cScriptCamera)
        {
            m_lacm->setPrevControlMode(true);
        }
    }
}

void ClientApplication::handleDiagnosticsEvent(const ClientUI::DiagnosticsEvent& event)
{
    EngineRender::RenderWorld* renderWorld = getRenderWorld();
    if (renderWorld != nullptr)
    {

        switch (event.getEventType())
        {
            case ClientUI::DiagnosticsEvent::EventType::cSensitivityValueChanged:
                renderWorld->setDiagnosticsSensitivity(event.getEventValue().m_float);
                break;
            case ClientUI::DiagnosticsEvent::EventType::cLightingVisibilityValueChanged:
                renderWorld->setDiagnosticsLightingVisibility(event.getEventValue().m_float);
                break;
            case ClientUI::DiagnosticsEvent::EventType::cSelectedModeChanged:
            {
                using RenderMode = EngineRender::RenderScriptConfig::RenderMode;
                LLCORE_ASSERT(event.getEventValue().m_uint32 < RenderMode::cRenderMode_Count, "Invalid value");

                const RenderMode newMode = static_cast<RenderMode>(event.getEventValue().m_uint32);
                BaseType::setRenderMode(newMode, "Diagnostics selected mode changed");
            }
            break;
            case ClientUI::DiagnosticsEvent::EventType::cLightingComplexityModeValueChanged:
            {
                using DiagnosticsFlags = EngineRender::GraphicsConfig::ScriptWorldParams::DiagnosticsFlags;
                switch (event.getEventValue().m_uint32)
                {
                    case ClientUI::cLightingComplexityMode_AllLights:
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cIncludeActiveShadowCasters, true);
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cIncludeNonActiveShadowCasters, true);
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cIncludeNonShadowCasters, true);
                        break;
                    case ClientUI::cLightingComplexityMode_ShadowCasters:
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cIncludeActiveShadowCasters, true);
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cIncludeNonActiveShadowCasters, true);
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cIncludeNonShadowCasters, false);
                        break;
                    case ClientUI::cLightingComplexityMode_ActiveShadowCasters:
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cIncludeActiveShadowCasters, true);
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cIncludeNonActiveShadowCasters, false);
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cIncludeNonShadowCasters, false);
                        break;
                        LLCORE_NO_DEFAULT_CASE("Unimplemented mode value");
                }
            }
            break;
            case ClientUI::DiagnosticsEvent::EventType::cTriangleDensityModeValueChanged:
            {
                using DiagnosticsFlags = EngineRender::GraphicsConfig::ScriptWorldParams::DiagnosticsFlags;
                switch (event.getEventValue().m_uint32)
                {
                    case ClientUI::cTriangleDensityMode_ScreenSpace:
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cWorldSpaceTriangleDensity, false);
                        break;
                    case ClientUI::cTriangleDensityMode_WorldSpace:
                        renderWorld->setOrClearDiagnosticsFlags((uint)DiagnosticsFlags::cWorldSpaceTriangleDensity, true);
                        break;
                        LLCORE_NO_DEFAULT_CASE("Unimplemented mode value");
                }
            }
            break;
                LLCORE_NO_DEFAULT_CASE("Unhandled event type");
        }
    }
}

void ClientApplication::onWindowResized(const LLWindow::WindowResizeEvent& event)
{
    // Forward the event to the UI event queue.
    postUIEvent(event);
}

void ClientApplication::onWindowStartResize(const LLWindow::WindowStartResizeEvent& event)
{
    // Forward the event to the UI event queue.
    postUIEvent(event);
}

void ClientApplication::onWindowEndResize(const LLWindow::WindowEndResizeEvent& event)
{
    // Forward the event to the UI event queue.
    postUIEvent(event);
}

void ClientApplication::handleTelnetEvent(const LLNetwork::TelnetEvent& event)
{
    LLCore::LogInfo("ClientTelnet", LLCore::Format("FROM: %? COMMAND: \"%?\"", event.m_fromSessionId, event.m_command));
    LLCore::StringFixed<512> result;

    const char*    commandPtr = event.m_command.asRead();
    LLCore::String firstWord;
    commandPtr += LLCore::TokenGetString(commandPtr, &firstWord, " ", true);

    if (m_memoryDumpHelper.processCommand(event.m_command.asRead(), &result))
    {
        LLCore::LogInfo("ClientTelnet", result);
    }
    else
    {
        result.copy("UNKNOWN COMMAND: ", event.m_command);
    }

    event.m_telnetServer->send(result.asRead(), event.m_fromSessionId);
}

void ClientApplication::refreshVrState()
{
    if (m_vrSystem)
    {
        postUIEvent<ClientVr::VrStateChangedEvent>(ClientVr::VrStateChangedEvent(m_vrSystem->getCurrentState()));
    }
}

void ClientApplication::flushTextureStreamingQueue()
{
    if (EngineRender::TextureStreamingManager* textureStreamingManager = getTextureStreamingManager(); textureStreamingManager != nullptr)
    {
        textureStreamingManager->flush();
    }
}

// Override the WindowCloseEvent so we can allow EditMode to ask the user if they want to exit with unsaved changes.
void ClientApplication::onWindowCloseEvent(const LLWindow::WindowCloseEvent& inputEvent) // virtual override
{
    if (m_clientStateMachine.getCurrentState() == Client::cEditMode)
    {
        // forestalls forcefully terminating, forgoing forewarning
        // (don't terminate client for not shutting down in a timely manner)
        m_postponeShutdown.set<LLCore::MemorySemantics::cRelease>(true);
        postUIEvent<ClientEditLocal::EditModeShutdownRequest>();
    }
    else
    {
        GraphicalApplicationBase::onWindowCloseEvent(inputEvent);
    }
}

EngineRender::RenderWorld* ClientApplication::getRenderWorld()
{
    EngineRender::RenderWorld* renderWorld = nullptr;

    if (getRuntimeWorld())
    {
        renderWorld = getRuntimeWorld()->getRenderWorld();
    }
    else if (m_clientStateMachine.getRenderWorld())
    {
        renderWorld = m_clientStateMachine.getRenderWorld();
    }
    else if (canRenderHomeSpace())
    {
        renderWorld = m_homeSpace->getWorldStateManager()->getRuntimeWorld()->getRenderWorld();
    }

    return renderWorld;
}

void ClientApplication::onEngineSettingsChanged(Engine::EngineSettingsChanged const& response)
{
    switch (response.m_setting)
    {
        case Engine::EngineSetting::cShadowedAtmosphericsDesktop:
        case Engine::EngineSetting::cShadowedAtmosphericsVR:
        case Engine::EngineSetting::cAmbientOcclusionDesktop:
        case Engine::EngineSetting::cAmbientOcclusionVR:
        case Engine::EngineSetting::cReflectionsDesktop:
        case Engine::EngineSetting::cReflectionsVR:
        case Engine::EngineSetting::cRenderQuality:
        case Engine::EngineSetting::cRenderQualityVR:
        case Engine::EngineSetting::cRenderResolution:
        case Engine::EngineSetting::cRenderResolutionVR:
        case Engine::EngineSetting::cDisplayGamma:
        {
            requestRenderMaterialsReload("Engine settings changed");
            break;
        }
        case Engine::EngineSetting::cUseFSRUpscaler:
        {
            requestRenderMaterialsReload("Engine settings changed");
            requestRenderScriptReload("Toggling RCAS");
            break;
        }
        case Engine::EngineSetting::cMuteMicOnStart:
        {
            m_cachedIsMicMutedState = m_config->m_muteMicOnStart;
            break;
        }
        case Engine::EngineSetting::cFullscreen:
        {
            if (m_mainWindow)
            {
                m_mainWindow->setFullscreen(response.m_value.m_boolValue);
            }
            break;
        }
        case Engine::EngineSetting::cPanelBackgroundColor:
        {
            m_config->m_ui.m_panelBackgroundColor = response.m_value.m_stringValue;
            break;
        }
        case Engine::EngineSetting::cUseDefaultBackgroundColor:
        {
            m_config->m_ui.m_useDefaultBackgroundColor = response.m_value.m_boolValue;
            break;
        }
        case Engine::EngineSetting::cUseVSync:
        {
            if (getWindow())
            {
                getWindow()->setEnableVSync(response.m_value.m_boolValue);
            }
            break;
        }
        case Engine::EngineSetting::cEnableHDR:
        {
            if (getWindow())
            {
                getWindow()->setEnableHDR(response.m_value.m_boolValue);
                requestRenderScriptReload("Toggling HDR");
            }
            break;
        }
        case Engine::EngineSetting::cMirrorVrDisplay:
        {
            // Reload only when VR is active
            // Otherwise, the script already reloads when VR becomes active
            if (isVrActive())
            {
                requestRenderMaterialsReload("Mirror Vr Display setting toggled");
            }
            break;
        }
        case Engine::EngineSetting::cEnableTypingIndicator:
        {
            if (m_worldStateManager)
            {
                m_worldStateManager->enableTypingIndicators(response.m_value.m_boolValue);
            }
            break;
        }
        case Engine::EngineSetting::cEnableAvatarIdentify:
        {
            if (m_lacm)
            {
                m_lacm->setIsAvatarIdentifyAvailable(response.m_value.m_boolValue);
            }
            break;
        }
        case Engine::EngineSetting::cEnableVoiceIndicator:
        {
            if (m_worldStateManager)
            {
                m_worldStateManager->enableVoiceIndicators(response.m_value.m_boolValue);
            }
            break;
        }
        case Engine::EngineSetting::cDesktopCameraHorizontalOffset:
        {
            if (m_thirdPersonCameraBehavior)
            {
                m_thirdPersonCameraBehavior->setHorizontalOffsetRatio(response.m_value.m_floatValue);
            }
            break;
        }
        case Engine::EngineSetting::cThirdPersonControlsKeyboardTurnEnabled:
        {
            if (m_thirdPersonCameraBehavior)
            {
                m_thirdPersonCameraBehavior->setKeyboardTurnEnabled(response.m_value.m_boolValue);
            }
            break;
        }
        case Engine::EngineSetting::cThirdPersonControlsFaceForwardEnabled:
        {
            if (m_thirdPersonCameraBehavior)
            {
                m_thirdPersonCameraBehavior->setFaceForwardEnabled(response.m_value.m_boolValue);
                m_thirdPersonCameraBehavior->activate(false, true);
            }
            if (m_scriptCameraBehavior)
            {
                m_scriptCameraBehavior->setFaceForwardEnabled(response.m_value.m_boolValue);
            }
            break;
        }
        case Engine::EngineSetting::cVrSnapTurnAngleDelta:
        {
            if (m_lacm != nullptr)
            {
                m_lacm->setVrSnapTurnAngleDelta(response.m_value.m_intValue);
            }
            break;
        }
        case Engine::EngineSetting::cDisableMediaPlayback:
        {
            if (m_mediaThread && m_mediaThread->isRunning() && m_config->m_media.m_enabled)
            {
                m_mediaThread->updateConfig(m_config->m_media);

                if (response.m_value.m_boolValue && m_mediaThread->getIsMediaLoaded())
                {
                    m_mediaThread->closeMedia();
                }
                else if (!m_mediaThread->getMediaLocation().isEmpty())
                {
                    m_mediaThread->openMediaLocation(m_mediaThread->getMediaLocation());
                }
            }
            break;
        }
        default:
            break;
    }
}

void ClientApplication::startRendering()
{
    if (m_mainWindow)
    {
        m_mainWindow->setCloseTimeout(10_ll_sec, [this] {
            if (m_postponeShutdown.get<LLCore::MemorySemantics::cAcquire>() == false)
            {
                LLCrashReporter::CrashReporter* crashReporter = getCrashReporter();
                if (crashReporter)
                {
                    crashReporter->clearPreviousRunCrashIndicator();
                    crashReporter->setReportCrashesAsDiagnostic(true);
                }
                //Application did not shut down in a timely manner
                LLCore::LogError("ClientApplication", "Application failed to shut down in 10 seconds, terminating...");
                LLCore::Thread::TerminateUnresponsiveThread(m_config->m_appThread->getThreadId());
            }
        });
    }

    const bool rendererStarted = BaseType::startRenderer(m_resourceStoreManager, m_engineResourceLoader);
    if (rendererStarted == false)
    {
        return;
    }

    if (m_vrSystem != nullptr)
    {
        m_vrSystem->initializeHeadsetManager();
        if (m_config->m_vr.m_defaultVrActive || m_vrSystem->isUserWearingHeadset())
        {
            m_vrSystem->toggleActive(false);
        }
    }

    LLCore::ReconstructInPlace(&m_screenshotManager, &getEventQueue(), &m_graphicsSystem);

    if (m_config->m_media.m_enabled)
    {
        m_mediaThread = new LLMedia::MediaThread(m_config->m_media, LLAudio::AudioSystem::GetInstance().getLoopbackManager());
        m_mediaThread->start();
    }

    getEventQueue().addCallback(this, &ClientApplication::handleSetRenderModeEvent);
    getEventQueue().addCallback(this, &ClientApplication::handleRestartRenderScriptEvent);
    getEventQueue().addCallback(this, &ClientApplication::handleDiagnosticsEvent);
    getEventQueue().addCallback(this, &ClientApplication::handleMasterInstanceEvent);
}

void ClientApplication::shutdownRendering()
{
    if (m_rendererStarted == false)
    {
        return;
    }

    getEventQueue().removeCallback(this, &ClientApplication::handleSetRenderModeEvent);
    getEventQueue().removeCallback(this, &ClientApplication::handleRestartRenderScriptEvent);
    getEventQueue().removeCallback(this, &ClientApplication::handleDiagnosticsEvent);
    getEventQueue().removeCallback(this, &ClientApplication::handleMasterInstanceEvent);

    if (m_clientUIManager)
    {
        LLGraphicsGems::OwnedPointer<LLGraphicsStream::CleanupBuilder> cleanupBuilder = m_streamQueue.buildCleanup();
        m_clientUIManager->terminateUIRenderer(cleanupBuilder);
        m_streamQueue.requestCleanup(LLCore::Give(cleanupBuilder));
    }

    BaseType::shutdownRenderer();
}

void ClientApplication::onGraphicsSystemShutdownComplete()
{
    BaseType::onGraphicsSystemShutdownComplete();

    if (m_clientUIManager)
    {
        BaseType::destroyUiRenderer();
        m_clientUIManager->shutdown();
    }
}

Engine::VrSystemInterface* ClientApplication::createVrSystem()
{
    LLCORE_ASSERT(m_vrSystem == nullptr, "VR System already created");

    {
        const bool forceUseOpenVR = ((m_config->m_runSource.isProvided() && m_config->m_runSource == "steam") || m_config->m_forceOpenVR) && !m_config->m_forceOculus;
        m_vrSystem                = new ClientVr::VrSystem(&getEventQueue(), &m_graphicsSystem, m_config->m_vr.m_lastMeasuredUserHeight, m_config->m_vr.m_rememberCalibration, forceUseOpenVR, m_config->m_forceOculus);
    }

    //Comfort Zone Filter
    {
        ClientVr::ComfortZoneCinfo cinfo;
        cinfo.m_eventQueue          = &getEventQueue();
        cinfo.m_friendComfortDist   = m_config->m_vr.m_friendComfortZoneDistThreshold.asRead();
        cinfo.m_strangerComfortDist = m_config->m_vr.m_strangerComfortZoneDistThreshold.asRead();
        m_comfortZoneFilter         = new ClientVr::ComfortZoneFilter(cinfo);
    }

    return m_vrSystem;
}

void ClientApplication::adjustRenderScriptCustomConfig(EngineRender::RenderScriptConfig* customConfig) const
{
    BaseType::adjustRenderScriptCustomConfig(customConfig);

    customConfig->m_enableEditorFeatures = (m_clientStateMachine.getCurrentState() == ApplicationStateId::cEditMode);
}

void ClientApplication::adjustRenderScriptCustomParams(EngineRender::RenderScriptParams* customParams) const
{
    BaseType::adjustRenderScriptCustomParams(customParams);

    customParams->m_viewSegmentCount = isVrActive() ? 1u : LLCore::Max(m_config->m_camera.m_panoramaSegmentCount, 1u);
}

void ClientApplication::initializeRenderViews(LLGems::ComponentHandle<Engine::CameraComponent> const& cameraComponent, LLGraphicsStream::FrameBuilder* frameBuilder, LLCore::Array<EngineRender::ViewContext>* renderViews)
{
    float const cLodTargetErrorOverFov = 0.001f;

    using FrameContext = BaseType::FrameContext;
    if (cameraComponent)
    {
        EngineRender::ViewContext* renderView = renderViews->insertLast();

        // viewport values are valid for both desktop and VR
        const float viewportWidth  = getViewportWidth();
        const float viewportHeight = getViewportHeight();

        renderView->m_segmentCount        = isVrActive() || m_screenshotManager.isOverridingCamera() ? 1 : LLCore::Max(m_config->m_camera.m_panoramaSegmentCount, 1u);
        renderView->m_renderStreamBuilder = frameBuilder->addRender(/*overlayUI=*/true);
        renderView->m_cameraComponent     = &cameraComponent;

        if (m_screenshotManager.isOverridingCamera())
        {
            //override view with screenshot camera
            m_screenshotManager.populateRenderView(renderView);
            constexpr uint8 mask = (FrameContext::cRenderViewFlag_DisablePresentationToWindow | FrameContext::cRenderViewFlag_DisableLateVRSampling);
            renderView->m_flags  = LLCore::SetBits(renderView->m_flags, mask);
        }
        else if (isVrActive())
        {
            renderView->m_vrTrackingSpaceToWorld.set(m_userCamera.getTrackingSpaceToWorldTransform());
            renderView->m_vrTrackingSpaceToWorld.getColumnRw(0).mul(m_vrSystem->getVrTrackingScale());
            renderView->m_vrTrackingSpaceToWorld.getColumnRw(1).mul(m_vrSystem->getVrTrackingScale());
            renderView->m_vrTrackingSpaceToWorld.getColumnRw(2).mul(m_vrSystem->getVrTrackingScale());

            renderView->m_viewports[0].m_width  = viewportWidth;
            renderView->m_viewports[0].m_height = viewportHeight;

            LLGraphicsStream::CullingInfo& cullingInfo = *renderView->m_renderStreamBuilder.addCullingInfo();
            renderView->m_cameraComponent->asBase()->populateStereoCameraInfo(m_vrSystem->getVrDisplay()->getStaticState(), &cullingInfo.m_camera);
            cullingInfo.m_cameraScale                = m_vrSystem->getVrTrackingScale();
            LLCore::Scalar const fovRadians          = 2.f * LLCore::ATan(m_vrSystem->getVrDisplay()->getStaticState().m_eyeStates[LLVRHeadset::cRightEyeIndex].m_fovPort.m_rightTan);
            cullingInfo.m_lodTargetErrorOverDistance = cLodTargetErrorOverFov * fovRadians.asFloat();
        }
        else if (m_config->m_camera.m_panoramaSegmentCount == 0)
        {
            renderView->m_viewports[0].m_width  = viewportWidth;
            renderView->m_viewports[0].m_height = viewportHeight;
            renderView->m_viewports[0].m_x      = 0.f;
            renderView->m_viewports[0].m_y      = 0.f;

            LLGraphicsStream::CullingInfo& cullingInfo = *renderView->m_renderStreamBuilder.addCullingInfo();
            renderView->m_cameraComponent->asBase()->populateCameraInfo(renderView->m_viewports[0].m_width, renderView->m_viewports[0].m_height, &cullingInfo.m_camera);

            float forcedZoomFactor = LLCore::Exp2(-m_forcedCameraZoom);
            cullingInfo.m_camera.m_leftTan *= forcedZoomFactor;
            cullingInfo.m_camera.m_rightTan *= forcedZoomFactor;
            cullingInfo.m_camera.m_downTan *= forcedZoomFactor;
            cullingInfo.m_camera.m_upTan *= forcedZoomFactor;
            cullingInfo.m_lodTargetErrorOverDistance = cLodTargetErrorOverFov * cameraComponent->getDiagonalFov().asFloat();

            // force cull info to debug camera parameters
            if (m_usingDebugCamera)
            {
                // Use camera component instead of culling info if debug camera is in effect
                // and only if this is the first view
                const mem_int renderViewIndex = renderViews->getIndex(renderView);
                renderView->m_flags           = LLCore::SetOrClearBits(renderView->m_flags, (uint8)FrameContext::cRenderViewFlag_UseCameraComponentTransform, renderViewIndex == 0);

                LLGraphicsGems::Camera& cam = cullingInfo.m_camera;
                cam.m_position              = m_debugCamera.m_position;
                cam.m_forward               = m_debugCamera.m_forward;
                cam.m_up                    = m_debugCamera.m_up;
            }
        }
        else
        {
            for (uint i = 0; i < renderView->m_segmentCount; i++)
            {
                float leftEdge                      = LLCore::RoundToNearest((viewportWidth * (float)i) / (float)renderView->m_segmentCount);
                float rightEdge                     = LLCore::RoundToNearest((viewportHeight * (float)(i + 1)) / (float)renderView->m_segmentCount);
                renderView->m_viewports[i].m_width  = rightEdge - leftEdge;
                renderView->m_viewports[i].m_height = (float)m_height;
                renderView->m_viewports[i].m_x      = leftEdge;
                renderView->m_viewports[i].m_y      = 0.f;

                LLCore::Scalar const           fovRadians  = m_config->m_camera.m_panoramaHorizontalFieldOfViewDegrees * LLCore::ScalarConstants::cRadiansPerDegree; // * LLCore::Exp2(-m_forcedCameraZoom);
                LLGraphicsStream::CullingInfo& cullingInfo = *renderView->m_renderStreamBuilder.addCullingInfo();
                renderView->m_cameraComponent->asBase()->populatePanoramicCameraInfo(viewportWidth, viewportHeight, fovRadians, i, renderView->m_segmentCount, &cullingInfo.m_camera);
                cullingInfo.m_lodTargetErrorOverDistance = cLodTargetErrorOverFov * fovRadians.asFloat();
            }
        }

        renderView->m_renderScriptId = BaseType::getDefaultViewRenderScriptId();
    }
    else
    {
        // will render UI only
        EngineRender::ViewContext* renderView = renderViews->insertLast();
        renderView->m_renderStreamBuilder     = frameBuilder->addRender(true);
        renderView->m_cameraComponent         = nullptr;
        renderView->m_renderScriptId          = nullptr;
        renderView->m_renderStreamBuilder.setAffectedWindowSurface(BaseType::getMainWindowSurfaceHandle());
    }
}

void ClientApplication::initializeScriptViews(EngineRender::ViewContext const* view, LLCore::Array<LLGraphicsScript::View>* scriptViews)
{
    if (view->m_cameraComponent != nullptr)
    {
        BaseType::initializeScriptViews(view, scriptViews);
    }
}

void ClientApplication::runFrame()
{
    using LLProfile::PerformanceStatTimedScope;

    LLPROFILE_AUTO_CPU_FRAME();
    LLPROFILE_AUTO_CPU_MARKER_STATIC("ClientApplication::runFrame");

    LLPROFILE_STAT_INVOKE(ApplicationTiming, Frame, addHeartbeatTimerSample());

    // Force timer resolution to default values. Units are 100ns: 10'000*100ns = 1ms. Fix for broken sound issues.
    LLCore::SetSystemTimerResolution(10'000);

    // save any changes to config (will be throttled)
    {
        LLCore::ThreadTimeoutUntrackedTimeGuard savingConfigGuard;
        m_config->saveIfReady();
    }

    giveTimeToHeartbeatMetric();

    PerformanceStatTimedScope viewUpdateTimeParent("ClientApplication::runFrame_StepView", LLPROFILE_STAT_GET(ApplicationTiming, UpdateView), PerformanceStatTimedScope::cAccumulateParent);

    if (m_telnetServer != nullptr)
    {
        m_telnetServer->giveTime();
    }

    LLCore::Duration highPrecisionFrameElapsed = getPrecisionFrameElapsed();

#ifdef LLCORE_DEBUG
    const LLCore::Scalar deltaTime = (isDebugDeltaTimeForced() ? getDebugDeltaTimeForced() : highPrecisionFrameElapsed.asFloat()) * getDebugDeltaTimeScale();
#else
    const LLCore::Scalar deltaTime = highPrecisionFrameElapsed.asFloat();
#endif

    if (m_vrSystem)
    {
        m_vrSystem->preFrameUpdate(highPrecisionFrameElapsed, m_mainWindow.asBase());
    }

    // give time to systems that generate events we want to process this frame
    {
        PerformanceStatTimedScope viewUpdateTime("ClientApplication::runFrame_InputAndEvents", LLPROFILE_STAT_GET(ApplicationTiming, InputAndEvents));

        m_automation.giveTime();
        m_keyboardInputManager.giveTime();
        m_mouseInputManager.giveTime();
        if (m_clientAudio != nullptr)
        {
            m_clientAudio->giveTime();
        }

        getEventQueue().processEvents();
        if (m_fallbackUiEventQueue.isConstructed())
        {
            m_fallbackUiEventQueue->processEvents();
        }
    }

    m_clientStateMachine.giveTime(highPrecisionFrameElapsed);

    if (m_vrSystem)
    {
        m_vrSystem->giveTime(highPrecisionFrameElapsed, m_userCamera.getTrackingSpaceToWorldTransform());
    }

    Engine::UserCharacter* userCharacter = nullptr;

    Engine::RuntimeWorldStepInfo currentWorldStep;

    // Step the world
    if (m_worldStateManager != nullptr)
    {
        PerformanceStatTimedScope graphUpdateTime("ClientApplication::runFrame_StepWorldSim", LLPROFILE_STAT_GET(ApplicationTiming, StepWorld));
        currentWorldStep = m_worldStateManager->stepSimulation(deltaTime);
        processPendingOperations();
        userCharacter = m_lacm ? m_lacm->getUserCharacter() : userCharacter;
    }
    if (m_homeSpace != nullptr)
    {
        m_homeSpace->stepSimulation(deltaTime);
        userCharacter = m_homeSpace->getUserCharacter();
    }

    if (m_userEmotes != nullptr)
    {
        m_userEmotes->giveTime();
    }

    if (m_inputHintController)
    {
        m_inputHintController->giveTime(m_clientStateMachine.getCurrentState(), userCharacter, deltaTime);
    }

    const Engine::CameraHandle cameraControllerComponent = m_userCamera.getCameraHandle();

    BaseType::FrameContext renderFrameContext;
    if (rendererRunning())
    {
        PerformanceStatTimedScope viewUpdateTimeChild("ClientApplication::runFrame_StepView", LLPROFILE_STAT_GET(ApplicationTiming, UpdateView), PerformanceStatTimedScope::cAccumulateChild);

        renderFrameContext = BaseType::beginBuildingRenderFrame(cameraControllerComponent);
    }

    m_clientStateMachine.buildRenderFrame(highPrecisionFrameElapsed, renderFrameContext);

    if (m_vrVisualizationLayer != nullptr)
    {
        m_vrVisualizationLayer->giveTime(highPrecisionFrameElapsed, m_vrSystem);
    }

    if (m_homeSpace)
    {
        m_homeSpace->step(deltaTime, highPrecisionFrameElapsed, renderFrameContext);
    }
    else if (m_worldStateManager != nullptr)
    {
        PerformanceStatTimedScope graphUpdateTime("ClientApplication::runFrame_StepWorldRender", LLPROFILE_STAT_GET(ApplicationTiming, StepWorld));
        m_worldStateManager->stepRendering(currentWorldStep, renderFrameContext);
    }

    // Update Comfort Zone Filter
    {
        Engine::RuntimeWorld* runtimeWorld = getRuntimeWorld();
        if (!cameraControllerComponent.isNull() && runtimeWorld != nullptr)
        {
            updateComfortZone(cameraControllerComponent.asBase(), runtimeWorld, m_comfortZoneFilter);
        }
    }

    {
        LLPROFILE_AUTO_CPU_MARKER_STATIC("GiveTime_Total");
        PerformanceStatTimedScope graphUpdateTime("ClientApplication::runFrame_Misc", LLPROFILE_STAT_GET(ApplicationTiming, Misc));

        m_appServiceRegistry->giveTimeAll();

        if (m_usingLocalRegionConductor == false)
        {
            m_apiLocatorService->giveTime();
        }

        m_connectToRegionStateMachine.giveTime();

        m_httpManager->giveTime();
        m_operationQueue->giveTime();

        if (m_appData != nullptr)
        {
            m_appData->giveTime();
        }
        if (m_userData != nullptr)
        {
            m_userData->giveTime();
        }

        if (m_assetManager != nullptr)
        {
            m_assetManager->giveTime();
        }
        if (m_accountConnector != nullptr)
        {
            m_accountConnector->giveTime();
        }
        if (m_authManager != nullptr)
        {
            m_authManager->giveTime();
        }
        if (m_personaCatalog != nullptr)
        {
            m_personaCatalog->giveTime();
        }
        if (m_userProfileManager != nullptr)
        {
            m_userProfileManager->giveTime();
        }
        if (m_experienceManager != nullptr)
        {
            m_experienceManager->giveTime();
        }
        if (m_assetImportManager != nullptr)
        {
            m_assetImportManager->giveTime();
        }
        if (m_inventoryManager != nullptr)
        {
            m_inventoryManager->giveTime();
        }
        if (m_thumbnailManager != nullptr)
        {
            m_thumbnailManager->giveTime();
        }
        if (m_loginManager != nullptr)
        {
            m_loginManager->giveTime();
        }
        if (m_chatManager != nullptr)
        {
            m_chatManager->giveTime();
        }
        if (m_clientMarketplace != nullptr)
        {
            m_clientMarketplace->giveTime();
        }

        if (m_steamworksManager != nullptr)
        {
            m_steamworksManager->giveTime();
        }

        // periodic timing functions
        LLCore::PrecisionTime frameTime = getPrecisionFrameTime();
        if ((frameTime - m_lastLogLoadStatistics) >= 60_ll_sec)
        {
            logLoadStatistics();
            m_lastLogLoadStatistics = frameTime;
        }
    }

    {
        PerformanceStatTimedScope viewUpdateTimeChild("ClientApplication::runFrame_StepView", LLPROFILE_STAT_GET(ApplicationTiming, UpdateView), PerformanceStatTimedScope::cAccumulateChild);

        LLCore::MTransform trackingSpaceToWorld = m_userCamera.getTrackingSpaceToWorldTransform();

        LLGraphicsStream::BatchHandle uiBatchHandle;
        LLGraphicsGems::Camera        uiCamera;
        if (cameraControllerComponent)
        {
            cameraControllerComponent->populateCameraInfo(getViewportWidth(), getViewportHeight(), &uiCamera);
        }

        Engine::UserCharacter*                                          character = m_lacm ? m_lacm->getUserCharacter() : nullptr;
        LLCore::Array<LLCore::MTransform, LLInput::WandHand::cNumHands> handTransforms{LLCore::MTransformConstants::cIdentity,
                                                                                       LLCore::MTransformConstants::cIdentity};
        if (character)
        {
            character->getDeviceTransform(Engine::UserCharacterDevice::LeftWand, &handTransforms[(mem_int)Engine::UserCharacterDevice::LeftWand]);
            character->getDeviceTransform(Engine::UserCharacterDevice::RightWand, &handTransforms[(mem_int)Engine::UserCharacterDevice::RightWand]);
        }

        if (m_clientUIManager)
        {
            const LLVRInput::VRInputManager* vrInput = m_vrSystem ? &m_vrSystem->getInputManager() : nullptr;

            m_clientUIManager->giveTime(uiCamera, vrInput, (isVrActive() ? m_vrSystem->getVrHeadsetPose() : LLCore::MTransformConstants::cIdentity), trackingSpaceToWorld, handTransforms, renderFrameContext.m_frameBuilder, &uiBatchHandle);
        }

        if (rendererRunning())
        {
            if (uiBatchHandle.isValid())
            {
                for (EngineRender::ViewContext& renderView : renderFrameContext.m_renderViews)
                {
                    renderView.m_renderStreamBuilder.addBatch(uiBatchHandle);
                }
            }

            if (m_usingDebugCamera)
            {
                m_debugCamera.step();
                LLCore::MTransform transform = LLGraphicsGems::MathUtil::PositionForwardUpToMTransform(m_debugCamera.m_position, m_debugCamera.m_forward, m_debugCamera.m_up);
                // Draw debug frustum being used for culling for visual reference
                cameraControllerComponent->debugDraw(transform);
            }

            if (m_resetDebugCameraRequested)
            {
                m_debugCamera.resetCamera(cameraControllerComponent->getTransform().getTranslation(), cameraControllerComponent->getForwardWorldSpace(), cameraControllerComponent->getUpWorldSpace());
                m_resetDebugCameraRequested = false;
            }

            LLGraphics::Target2d const* screenshotSource = nullptr;
            BaseType::endBuildingRenderFrame(LLCore::Give(renderFrameContext), &screenshotSource);
            m_screenshotManager.update(screenshotSource, cameraControllerComponent.asBase());

            if (screenshotSource != nullptr && m_config->m_enableVideostream)
            {
                m_graphicsSystem.createTarget2dVideoReadback(screenshotSource, 0);
            }
        }
    }

    if (EngineRender::TextureStreamingManager* textureStreamingManager = BaseType::getTextureStreamingManager())
    {
        textureStreamingManager->giveTime();
    }

    if (m_homeSpace)
    {
        m_homeSpace->stepAudio();
    }

    if (m_worldStateManager)
    {
        m_worldStateManager->stepAudio();
    }

    if (m_xsensManager)
    {
        m_xsensManager->giveTime();
    }


    // XInput polling is slow. Poll after rendering has been kicked off at the cost of one frame latency.
    m_gamepadManager->giveTime();

    const bool autoShutdown = (m_config->m_secondsBeforeShutdown > LLCore::Duration::cZero && m_stopwatch.getElapsed() >= m_config->m_secondsBeforeShutdown);

    if (autoShutdown)
    {
        LLCore::LogInfo("ClientApplication", "Shutting down automatically after ", m_config->m_secondsBeforeShutdown, "seconds");
    }

    if (isShutdownRequested() || autoShutdown)
    {
        m_clientStateMachine.switchToShutdown();
    }

    if (m_currentLoadingPhase != LoadingPhase::cNone)
    {
        LoadingProgress progress;
        updateLoadingProgress(&progress);

        if (getUISystem() != nullptr)
        {
            ClientServices::ExperienceLoadingProgressEvent progressEvent;
            progressEvent.m_percentageComplete = progress.m_totalPercentageComplete;
            progressEvent.m_description        = progress.m_description;

            postUIEvent(progressEvent);
        }
    }

    if (m_config->m_window.m_isHdrAvailable != m_lastHDRAvailableState)
    {
        m_lastHDRAvailableState = m_config->m_window.m_isHdrAvailable;
        getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cIsHDRAvailable, m_config->m_window.m_isHdrAvailable);
    }


    // sleep excess frame time
    m_frameSleep                    = LLCore::Duration::FromMilliseconds(LLCore::FloorToInt(1000.0f / static_cast<float>(m_config->m_graphics.m_maxFps)));
    m_frameSleepBackground          = LLCore::Duration::FromMilliseconds(LLCore::FloorToInt(1000.0f / static_cast<float>(m_config->m_graphics.m_maxFpsBackground)));
    m_frameSleepMinimized           = LLCore::Duration::FromMilliseconds(LLCore::FloorToInt(1000.0f / static_cast<float>(m_config->m_graphics.m_maxFpsMinimized)));
    LLCore::Duration frameSleepSlow = getWindow()->getMinimized() ? m_frameSleepMinimized : m_frameSleepBackground;
    bool slowFrames                 = (getWindow()->getMinimized() || !getWindow()->getHasFocus()) && isVrActive() == false;
    frameSleep(isVrActive() ? m_frameSleepVr: slowFrames ? frameSleepSlow : m_frameSleep);
}

// For intercepting shutdown requests made to base class.
void ClientApplication::requestShutdown(uint8 exitValue)
{
    m_exitValue = exitValue;
    m_connectToRegionStateMachine.requestNextState<DisconnectedFromRegion>();
    if (m_clientStateMachine.isInitialized())
    {
        // Give the client state machine a chance to handle the shutdown request.
        m_clientStateMachine.switchToShutdown();
    }
    else
    {
        // The client state machine wasn't initialized, just handle normally.
        BaseType::requestShutdown(exitValue);
    }
}

void ClientApplication::giveTimeToHeartbeatMetric()
{
    LLPROFILE_AUTO_CPU_MARKER_FUNCTION
    if (m_identityManager == nullptr)
    {
        return;
    }

    LLGraphicsRender::MetricsStore::ThreadLocalMetricsContext::Post();

    if (m_heartbeatContext.getElapsed() > m_metricsUpdatePeriod)
    {
        sendHeartbeatMetric();
    }
}

void ClientApplication::sendHeartbeatMetric()
{
    ClientServices::HeartbeatMetricsEvent event;

    event.m_platform = m_clientHardwareInfo.m_osInfo;

    m_clientStateMachine.getCurrentStateName(&event.m_appStateName);
    m_heartbeatContext.stop();

    event.m_averageFrameRate = static_cast<float>(m_heartbeatContext.getSampleRate(LLApplication::EventMetrics::cFrameTime));

    event.m_minFrameRate = static_cast<float>(1.0 / m_heartbeatContext.getMaximum(LLApplication::EventMetrics::cFrameTime));
    event.m_maxFrameRate = static_cast<float>(1.0 / m_heartbeatContext.getMinimum(LLApplication::EventMetrics::cFrameTime));

    if (rendererRunning())
    {
        ClientServices::RenderingMetricsEvent renderingMetrics;

        auto collectMetrics = LLCore::Function([&renderingMetrics](LLGraphicsRender::MetricsContext const* const renderingMetricsContext) {
            using Metric                             = LLGraphicsRender::Metric;
            renderingMetrics.m_primitivesRenderedMin = static_cast<uint64>(renderingMetricsContext->getMinimum(Metric::cPrimitives));
            renderingMetrics.m_primitivesRenderedMax = static_cast<uint64>(renderingMetricsContext->getMaximum(Metric::cPrimitives));
            renderingMetrics.m_primitivesRenderedAvg = static_cast<uint64>(renderingMetricsContext->getMean(Metric::cPrimitives));
            renderingMetrics.m_drawCallsMin          = static_cast<uint64>(renderingMetricsContext->getMinimum(Metric::cDraws));
            renderingMetrics.m_drawCallsMax          = static_cast<uint64>(renderingMetricsContext->getMaximum(Metric::cDraws));
            renderingMetrics.m_drawCallsAvg          = static_cast<uint64>(renderingMetricsContext->getMean(Metric::cDraws));
        });

        LLGraphicsRender::MetricsStore::ThreadLocalMetricsContext::Read(collectMetrics, /*resetContext=*/true);

        LLCore::ReconstructInPlace(&event.m_renderingMetrics, LLCore::Give(renderingMetrics));
    }

    if (m_config->m_enableAudio && rendererRunning())
    {
        event.m_averageRms = static_cast<float>(m_heartbeatContext.getMean(LLAudio::EventMetrics::cRms));
        event.m_peakRms    = static_cast<float>(m_heartbeatContext.getMaximum(LLAudio::EventMetrics::cRms));
    }

    event.m_hmdAvailable      = isVrAvailable();
    event.m_hmdActive         = isVrActive();
    event.m_hasPositionalData = (m_lacm != nullptr);

    // window state
    LLWindow::Window* window = getWindow();
    if (window != nullptr)
    {
        event.m_hasFocus = window->getHasFocus();

        LLWindow::WindowState windowState = window->getMaximized() ? LLWindow::WindowState::cMaximized : (window->getMinimized() ? LLWindow::WindowState::cMinimized : LLWindow::WindowState::cNormal);
        event.m_windowState               = LLCore::ConvertType<LLCore::String>(windowState);
    }

    if (m_lacm != nullptr)
    {
        Engine::ControlMode cameraMode = m_lacm->getControlMode();
        event.m_camera_mode            = LLCore::StringFixed<64>{cameraMode};

        const LLCore::MTransform& character_transform = m_lacm->getCharacterBaseTransform();
        event.m_avatar_position                       = character_transform.getTranslation();
        event.m_avatar_forward                        = character_transform.getRotation().getColumn(1);

        LLCore::MTransform     camera_transform;
        Engine::UserCharacter* character = m_lacm ? m_lacm->getUserCharacter() : nullptr;

        if (character)
        {
            character->getCameraTransform(&camera_transform);
        }
        else
        {
            camera_transform = LLCore::MTransformConstants::cIdentity;
        }

        event.m_camera_position = camera_transform.getTranslation();
        event.m_camera_forward  = camera_transform.getRotation().getColumn(1);
    }
    else
    {
        event.m_camera_mode = LLCore::StringFixed<64>{Engine::ControlMode::cNone};
    }

    if (isVrAvailable())
    {
        const LLVRInput::VRInputSource* vrInputSource = m_vrSystem->getInputManager().getInputSource();
        if (vrInputSource != nullptr)
        {
            vrInputSource->getVendorString(&event.m_hmdVendor);
            vrInputSource->getProductString(&event.m_hmdProduct);

            LLVRHeadset::StaticState staticState;
            vrInputSource->getHeadsetStaticState(&staticState);
            event.m_hmdWidth  = staticState.m_combinedViewport.m_width;
            event.m_hmdHeight = staticState.m_combinedViewport.m_height;
        }
        else
        {
            event.m_hmdVendor  = "no-input-source";
            event.m_hmdProduct = "no-input-source";
        }
    }
    else
    {
        event.m_hmdVendor  = "unavailable";
        event.m_hmdProduct = "unavailable";
    }

    if (m_worldStateManager != nullptr)
    {
        event.m_worldDefintionId = m_worldStateManager->getWorldDefintionId().asUuid();
    }

    if (!m_currentExperienceUri.isEmpty())
    {
        event.m_grid               = m_currentExperienceUri.getGrid();
        event.m_ownerPersonaHandle = m_currentExperienceUri.getPersonaHandle();
        event.m_experienceHandle   = m_currentExperienceUri.getExperienceHandle();
        ConvertType(m_currentExperienceUri.getInstanceId(), &event.m_uriInstanceId);
        ConvertType(m_currentRegionInstanceID, &event.m_experienceInstanceId);
        event.m_sceneInventoryId.clear();
    }
    else if (!m_currentSceneEditUri.isEmpty())
    {
        event.m_grid               = m_currentSceneEditUri.getGrid();
        event.m_ownerPersonaHandle = m_currentSceneEditUri.getPersonaHandle();
        event.m_sceneInventoryId   = m_currentSceneEditUri.getAssetId();
        event.m_experienceInstanceId.clear();
    }
    else
    {
        if (m_apiLocatorService)
        {
            event.m_grid = m_apiLocatorService->getGrid();
        }
        event.m_experienceInstanceId.clear();
    }

    LLMetrics::RecordMetric(event);

    m_heartbeatContext.reset();
    m_heartbeatContext.start();
}

void ClientApplication::onObjectInteractUpdateEvent(RegionCommon::WorldStateManager* wsm, Engine::ObjectId objectId, bool enabled)
{
    if (m_lacm != nullptr)
    {
        m_lacm->onObjectInteractUpdateEvent(objectId, enabled);
    }
}

void ClientApplication::onObjectInteractPromptUpdateEvent(RegionCommon::WorldStateManager* wsm, Engine::ObjectId objectId)
{
    if (m_lacm != nullptr)
    {
        m_lacm->onObjectInteractPromptUpdateEvent(objectId);
    }
}

void ClientApplication::onObjectInteractCreateEvent(RegionCommon::WorldStateManager* wsm, Engine::ObjectId objectId, bool enabled)
{
    if (m_lacm != nullptr)
    {
        m_lacm->onObjectInteractCreateEvent(objectId, enabled);
    }
}

void ClientApplication::onRuntimeInventorySettingsUpdated(RegionCommon::WorldStateManager* wsm)
{
    Engine::RuntimeWorld* runtimeWorld = getRuntimeWorld();
    if (runtimeWorld != nullptr)
    {
        postUIEvent<Engine::RuntimeInventorySettingsUpdateEvent>(runtimeWorld->getRuntimeInventorySettings());
    }
}

void ClientApplication::onSelfAvatarReady(Engine::AgentControllerManager* acm)
{
    LLCore::LogInfo("ClientApplication", "onSelfAvatarReady");

    // when the self-avatar becomes ready, it means it is time to drop the loading screen
    destroyHomeSpace();

    delete m_vrVisualizationLayer;
    m_vrVisualizationLayer = nullptr;

    Engine::RuntimeWorld* runtimeWorld = getRuntimeWorld();
    LLCORE_ASSERT(runtimeWorld != nullptr);

    if (m_vrSystem)
    {
        m_vrVisualizationLayer = new ClientVr::VrVisualizationLayer();

        ClientVr::VrVisualizationLayer::InitInfo initInfo;

        initInfo.m_vrSystem             = m_vrSystem;
        initInfo.m_runtimeWorld         = runtimeWorld;
        initInfo.m_renderWorld          = runtimeWorld->getRenderWorld();
        initInfo.m_resourceStoreManager = m_resourceStoreManager;
        initInfo.m_resourceLoader       = m_engineResourceLoader;
        initInfo.m_assetManager         = m_assetManager;
        initInfo.m_uiWorkspaceManager   = getUISystem()->getWorkspaceManager();
        initInfo.m_isEditMode           = false;

        m_vrVisualizationLayer->init(initInfo);
    }

    createLacm(runtimeWorld, m_vrVisualizationLayer);

    {
        LLCore::RefPointer<ConnectingToRegionAndVoiceServers> regionConnectedState = m_connectToRegionStateMachine.getCurrentState<ConnectingToRegionAndVoiceServers>();
        if (regionConnectedState)
        {
            regionConnectedState->avatarLoadingFinished();
        }
    }

    m_lacm->setAgentControllerManager(acm);
    m_lacmMessageHandler->setLocalAgentControllerManager(m_lacm);

    initUserCamera(runtimeWorld, true);
    m_lacm->setUserCamera(&m_userCamera);

    // If we're already in VR, trigger a state change for initial setup
    if (m_lacm->isVrActive())
    {
        m_lacm->updateVRCalibration();
        m_lacm->onVrStateChanged(m_vrSystem->getCurrentState());
    }

    m_lacm->setControlMode(Engine::ControlMode::cFlyCam);

    requestRenderScriptRestart("Avatar loaded");

    if (m_worldStateManager)
    {
        m_worldStateManager->setLocalAgentControllerManager(m_lacm);
    }

    setSafeToDisplayVrUi(true);

    if (m_config->m_script.m_enableDeveloperCommands)
    {
        Engine::AddIKinemaDebugCommands(m_debugConsoleCommandManager, m_lacm);
    }

    if (m_inputHintController)
    {
        m_inputHintController->setLacm(m_lacm);
    }

    runtimeWorld->onSelfAvatarReady();
    postUIEvent<LLUI::SelfAvatarReadyEvent>();

    m_lacmMessageHandler->applyQueuedMessages();

    //Add emote Complete handlers
    registerEmoteListeners();
}

void ClientApplication::initUserCamera(Engine::RuntimeWorld* runtimeWorld, bool allowsFlyCamera)
{
    LLGems::ComponentId cameraComponentId = Engine::Object::CreateComponentIdFromObjectId(runtimeWorld->allocateObjectIds(1));
    m_userCamera.setDefaultComponent(runtimeWorld->getCameraManager(), cameraComponentId);
    m_userCamera.setVrSystem(m_vrSystem);
    m_userCamera.setPosition(m_config->m_camera.m_cameraPosition);
    m_userCamera.lookAt(m_config->m_camera.m_cameraLookAt, m_config->m_camera.m_cameraUp);
    if (getWindow() != nullptr)
    {
        m_userCamera.setWindowSize(getWindow()->getSize());
    }

    // Register Runtime camera behaviors for new UserCamera system, these are activated by the LACM whenever ControlMode is changed.

    // Free-Fly behavior
    bool                                                  createFlyCamera = allowsFlyCamera;
    LLCore::RefPointer<ConnectingToRegionAndVoiceServers> state           = m_connectToRegionStateMachine.getCurrentState<ConnectingToRegionAndVoiceServers>();

    createFlyCamera &= (state && state->allowFreeCamera()) || runtimeWorld->getEnableFreeCamera();

    if (createFlyCamera)
    {
        Engine::UserCameraFlyBehaviorCInfo flyCInfo;
        {
            if (m_settingsManager != nullptr)
            {
                flyCInfo.m_moveSpeedScaleCounter = m_settingsManager->getEngineSettingValue(Engine::EngineSetting::cFlyCameraMoveSpeed).m_intValue;
            }
            flyCInfo.m_dampingFactor      = m_config->m_camera.m_flyCameraVelocityDampingConstant;
            flyCInfo.m_pitchDampingFactor = m_config->m_camera.m_flyCameraPitchDampingConstant;
            flyCInfo.m_yawDampingFactor   = m_config->m_camera.m_flyCameraYawDampingConstant;
        }

        m_flyCameraBehavior = LLCore::GiveRef(new Engine::UserCameraFlyBehavior(flyCInfo));
        m_userCamera.registerBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cDesktopFly), m_flyCameraBehavior);
    }
    else
    { //The user camera will crash if you unregister when it isn't registered
        //This cleanup is necessary if a previous world allowed fly camera
        if (m_userCamera.hasRegisteredBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cDesktopFly)))
        {
            m_userCamera.unregisterBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cDesktopFly));
        }
    }

    // First Person behavior
    Engine::UserCameraFirstPersonBehaviorCInfo firstPersonCInfo;
    {
        firstPersonCInfo.m_pitchClampPercentage    = 0.95f;
        firstPersonCInfo.m_yawVelocityMultiplier   = m_config->m_camera.m_yawVelocityMultiplier;
        firstPersonCInfo.m_pitchVelocityMultiplier = m_config->m_camera.m_pitchVelocityMultiplier;
    }
    m_userCamera.registerBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cDesktopFirstPerson),
                                  LLCore::GiveRef(new Engine::UserCameraFirstPersonBehavior(firstPersonCInfo)));

    // Third Person behavior
    if (m_config->m_camera.m_enableExperimentalThirdPersonCamera) // R27 over the shoulder prototype
    {
        Engine::UserCameraNewThirdPersonBehaviorCInfo thirdPersonCInfo;
        {
            thirdPersonCInfo.m_pitchClampPercentage   = 1.0f; // allow full pitch movement range
            thirdPersonCInfo.m_minimumForwardDistance = 0.5f;
            thirdPersonCInfo.m_initialPitch           = m_config->m_camera.m_thirdPersonConfig.m_initialPitch * LLCore::ScalarConstants::cRadiansPerDegree;
            thirdPersonCInfo.m_initialForwardDistance = m_config->m_camera.m_thirdPersonConfig.m_initialForwardDistance;
            thirdPersonCInfo.m_verticalDistance       = m_config->m_camera.m_thirdPersonConfig.m_verticalDistance;
            thirdPersonCInfo.m_horizontalOffsetRatio  = m_config->m_camera.m_thirdPersonConfig.m_horizontalOffset;
            thirdPersonCInfo.m_keyboardTurnEnabled    = m_config->m_controls.m_thirdPersonConfig.m_keyboardTurnEnabled;
            thirdPersonCInfo.m_turnStartDelay         = m_config->m_controls.m_thirdPersonConfig.m_turnStartDelay;
            thirdPersonCInfo.m_turnSpeed              = m_config->m_controls.m_thirdPersonConfig.m_turnSpeed;
            thirdPersonCInfo.m_faceForwardEnabled     = m_config->m_controls.m_thirdPersonConfig.m_faceForwardEnabled;
            thirdPersonCInfo.m_smoothingRigidity      = LLCore::Clamp(m_config->m_camera.m_thirdPersonConfig.m_smoothingRigidity, 0.0f, 1.0f);
        }
        m_thirdPersonCameraBehavior = LLCore::GiveRef(new Engine::UserCameraNewThirdPersonBehavior(thirdPersonCInfo));
        m_userCamera.registerBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cDesktopThirdPerson), m_thirdPersonCameraBehavior);
    }
    else
    {
        Engine::UserCameraThirdPersonBehaviorCInfo thirdPersonCInfo;
        {
            thirdPersonCInfo.m_radius                        = 4.f;
            thirdPersonCInfo.m_adjustRadiusBySpeed           = true;
            thirdPersonCInfo.m_pitchClampPercentage          = 0.55f;
            thirdPersonCInfo.m_rootTransformUpOffsetMul      = 1.7f;
            thirdPersonCInfo.m_rootTransformForwardOffsetMul = -0.25f;
            thirdPersonCInfo.m_allowHeadTurning              = false;
        }
        m_userCamera.registerBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cDesktopThirdPerson),
                                      LLCore::GiveRef(new Engine::UserCameraThirdPersonBehavior(thirdPersonCInfo)));
    }

    Engine::UserCameraScriptBehaviorCInfo scriptCameraCInfo;
    {
        scriptCameraCInfo.m_faceForwardEnabled = m_config->m_controls.m_thirdPersonConfig.m_faceForwardEnabled;
    }
    m_scriptCameraBehavior = LLCore::GiveRef(new Engine::UserCameraScriptBehavior(scriptCameraCInfo));
    m_userCamera.registerBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cScriptMode), m_scriptCameraBehavior);

    // VR First Person behavior
    Engine::UserCameraFirstPersonBehaviorCInfo vrFirstPersonCInfo;
    {
        vrFirstPersonCInfo.m_pitchClampPercentage = 0.0f;
    }
    m_userCamera.registerBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cVrFirstPerson),
                                  LLCore::GiveRef(new Engine::UserCameraFirstPersonBehavior(vrFirstPersonCInfo)));

    // VR Third Person behavior
    Engine::UserCameraThirdPersonBehaviorCInfo vrThirdPersonCInfo;
    {
        vrThirdPersonCInfo.m_radius                        = 20.f;
        vrThirdPersonCInfo.m_adjustRadiusBySpeed           = false;
        vrThirdPersonCInfo.m_pitchClampPercentage          = 0.0f;
        vrThirdPersonCInfo.m_forwardAdjustScale            = 0.85f;
        vrThirdPersonCInfo.m_rootTransformUpOffsetMul      = 0.0f;
        vrThirdPersonCInfo.m_rootTransformForwardOffsetMul = 0.25f;
        vrThirdPersonCInfo.m_allowHeadTurning              = true; // Enables tank controls
        vrThirdPersonCInfo.m_useTrackingSpaceForward       = true;
        vrThirdPersonCInfo.m_avoidGeometry                 = true;
    }
    m_userCamera.registerBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cVrThirdPerson),
                                  LLCore::GiveRef(new Engine::UserCameraThirdPersonBehavior(vrThirdPersonCInfo)));

    // VR Room Scale behavior
    Engine::UserCameraVrRoomScaleBehaviorCInfo vrRoomScaleCInfo;
    {
        vrRoomScaleCInfo.m_allowZMovement = false;
    }
    m_userCamera.registerBehavior(static_cast<uint8>(Engine::UserCameraBehaviorId::cVrRoomScale),
                                  LLCore::GiveRef(new Engine::UserCameraVrRoomScaleBehavior(vrRoomScaleCInfo)));
}

bool ClientApplication::isSelf(const LLCore::Uuid& personaId)
{
    if (m_identityManager->getPersona() != nullptr)
    {
        return personaId == m_identityManager->getPersona()->getId();
    }
    return personaId == m_config->m_account.m_localPersonaId;
}

void ClientApplication::destroyLacm()
{
    if (m_homeSpace)
    {
        m_homeSpace->onDestroyLacm();
    }

    if (m_inputHintController)
    {
        m_inputHintController->setLacm(nullptr);
    }

    // Lacm Message Handler when going in/out of the Homespace
    if (m_lacmMessageHandler != nullptr)
    {
        m_lacmMessageHandler->setLocalAgentControllerManager(nullptr);
    }

    // Lacm Message Handler when going in/out of the Homespace
    if (m_lacmMessageHandler != nullptr)
    {
        m_lacmMessageHandler->setLocalAgentControllerManager(nullptr);
    }

    delete m_lacm;
    m_lacm = nullptr;

    setSafeToDisplayVrUi(false);
}

void ClientApplication::createLacm(Engine::RuntimeWorld* runtimeWorld, ClientVr::VrVisualizationLayer* vrVirtualizationLayer)
{
    LLCORE_ASSERT(m_lacm == nullptr, "We already have a LocalAgentControllerManager!");

    m_lacm = new Engine::LocalAgentControllerManager(runtimeWorld, &m_inputBindingManager, m_vrSystem, vrVirtualizationLayer, &getEventQueue(), getUIEventQueue());
    m_lacm->registerControlMapping(LLCore::GiveRef(new Engine::DefaultControlMapping()));
    m_lacm->registerEventMapping(LLCore::GiveRef(new Engine::DefaultEventMapping()));
    m_lacm->setWindow(getWindow());
    m_lacm->setIsAvatarIdentifyAvailable(m_config->m_ui.m_isAvatarIdentifyAvailable);
    m_lacm->setEnableObjectInteractions(m_config->m_ui.m_enableObjectInteractions);
    m_lacm->setEnableGrab(m_config->m_ui.m_enableGrab);
    m_lacm->setThirdPersonListenerOptions(m_config->m_audio.m_listenFromAvatarPosition, m_config->m_audio.m_listenFromAvatarRotation);
    m_lacm->showAvatarInFirstPersonVr(m_config->m_vr.m_showAvatarInFirstPerson);
    m_lacm->setEnableExperimentalThirdPersonCamera(m_config->m_camera.m_enableExperimentalThirdPersonCamera);
    m_lacm->setMouseLookModeEnabled(m_config->m_camera.m_enableMouseLookMode);
    m_lacm->setJumpingEnabled(m_config->m_controls.m_enableJumping);
    m_lacm->setDesktopThrowTurnWithCamera(m_config->m_controls.m_desktopThrowTurnWithCamera);
    m_lacm->setCrouchingEnabled(m_config->m_controls.m_enableCrouching);
    m_lacm->setFlyingEnabled(m_config->m_controls.m_enableFlying);
    m_lacm->setVrSnapTurnAngleDelta(m_config->m_vr.m_vrSnapTurnAngleDelta);
    m_lacm->setEnableToggleRunning(m_config->m_controls.m_enableToggleRunning);
    m_lacm->setXSensEnabled(m_xsensManager);
    m_lacm->setTvModeEnabled(m_config->m_ui.m_tvModeEnabled);
    m_lacm->setVrEnabled(m_config->m_enableVR);

    if (m_config->m_script.m_enableDeveloperCommands)
    {
        m_lacm->createIKRecorder();
    }

    setSafeToDisplayVrUi(true);
}

void ClientApplication::onNamedPipeServerMessage(const byte* buffer, mem_int bufferSize)
{
    // We have seen this case happen in BugSplat. It's unclear how this occurred but this should keep us from crashing.
    LLCORE_ASSERT(getWindow() != nullptr, "Received a NamedPipeServerMessage while getWindow() was nullptr");
    if (getWindow() == nullptr)
    {
        return;
    }

    LLCore::String str;
    str.copy({(const char*)buffer, (int)bufferSize});

    LLGems::ConfigWithIncludes<Config> config;

    LLGems::TomlConfigReader configReader;
    const bool               success = configReader.readString(str, &config);

    if (success && config.m_sceneUri.isProvided())
    {
        AppCore::SansarUriBase::UriType uriType = AppCore::SansarUriBase::FindUriType(config.m_sceneUri);

        if (uriType == AppCore::SansarUriBase::UriType::cExperience || uriType == AppCore::SansarUriBase::UriType::cEvent)
        {
            AppCore::SansarExperienceUri experienceUri;
            bool                         avatarSupported    = false;
            bool                         avatarNeedsUpgrade = true;

            if (experienceUri.parseUrl(config.m_sceneUri))
            {
                if (const Inventory::Item* avatarItem = m_inventoryManager->getItem(m_identityManager->m_personaAppearanceDescriptor.m_avatarInventoryId); avatarItem)
                {
                    avatarSupported    = avatarItem->m_itemEntry.m_capabilities.findFirstOf(EngineCommon::Capabilities::cAvatar2CharacterAsset) != -1;
                    avatarNeedsUpgrade = avatarItem->m_itemEntry.m_capabilities.findFirstOf(EngineCommon::Capabilities::cCharacterAsset) == -1;
                }

                if (experienceUri.getGrid() != m_apiLocatorService->getGrid())
                {
                    LLCore::LogWarning("ClientApplication", "Unable to visit experience, grid does not match");

                    //Notify the user that the experience must be on the current grid.
                    getEventQueue().postEvent(ClientServices::ShowVisitErrorCommand(AppCore::SansarExperienceUri(), LLCore::ConvertType<LLCore::String>(config.m_sceneUri), ClientServices::VisitExperienceError::cFailedCrossGrid, ""_ll));
                }
                else if (m_identityManager->getPersona()->getId().isNull())
                {
                    LLCore::LogInfo("ClientApplication", "Unable to visit experience, not logged in");
                    m_requestedExperienceUri = experienceUri;
                }
                else if (!avatarSupported || avatarNeedsUpgrade)
                {
                    //Notify the user that they need to have a valid avatar before they can visit an experience.
                    getEventQueue().postEvent(ClientServices::ShowVisitErrorCommand(AppCore::SansarExperienceUri(), config.m_sceneUri.asRead().toString(), ClientServices::VisitExperienceError::cFailedNoAvatar, ""_ll));
                }
                else
                {
                    LLCore::RefPointer<ClientServices::CodexActionContext> metricsContext = ClientServices::CodexActionContext::Create(LLCore::Uuid(), 0, LLCore::Uuid(), ClientServices::CodexActionId::cVisit, LLCore::SystemTime::Now(), "scene-uri-from-browser"_ll);
                    ClientServices::VisitExperienceCommand                 command(experienceUri, LLCore::String(config.m_sceneUri.asRead()), ClientServices::VisitMode::cDefault, metricsContext);
                    getEventQueue().postEvent(command);
                }
            }
        }
        else if (uriType == AppCore::SansarUriBase::UriType::cEmpty)
        {
            // Nothing to do here. The sceneUri may contain query params.
        }
        else
        {
            getEventQueue().postEvent(ClientServices::ShowVisitErrorCommand(AppCore::SansarExperienceUri(), config.m_sceneUri.asRead().toString(), ClientServices::VisitExperienceError::cFailedInvalidUri, ""_ll));

            LLCore::LogWarning("ClientApplication", "Unable to parse scene URI: ", config.m_sceneUri);
        }

        parseLoginFromSceneUri(config.m_sceneUri);
    }
    else
    {
        LLCore::LogInfo("ClientApplication", "No scene URI specified");
    }

    getWindow()->show();
}


void ClientApplication::handleTeleportToUri_FromUserManager(const LLCore::String& experienceUriString)
{
    LLCore::RefPointer<ClientServices::CodexActionContext> metricsContext = ClientServices::CodexActionContext::Create(LLCore::Uuid(), 0, LLCore::Uuid(), ClientServices::CodexActionId::cVisit, LLCore::SystemTime::Now(), "teleport-to-uri"_ll);
    getEventQueue().postEvent(ClientServices::VisitExperienceCommand(AppCore::SansarExperienceUri(), experienceUriString, ClientServices::VisitMode::cDefault, metricsContext));
}

void ClientApplication::handleSwitchToEditMode_FromUserManager(EditWorkspace::WorkspaceEditView editView, LLCore::StringRef returnSpawnPoint)
{
    getEventQueue().postEvent<ClientEditLocal::SwitchToEditModeRequest>(editView, returnSpawnPoint);
}

void ClientApplication::setAgentController(const ClientRegionMessages::SetAgentController& packet)
{
    m_localAgentId                      = Engine::AgentControllerId(packet.m_agentControllerId);
    Engine::AgentControllerManager* acm = m_worldStateManager->getAgentController(m_localAgentId);

    onSelfAvatarReady(acm); // once we get told which agent controller is ours, it means that the agent controller is ready, which means our cluster is ready, which means our avatar is ready to start controlling

    // on entering an experience and avatar is ready, get a list of blocked users and block them
    for (const LLCore::Uuid& blockedPersonaId : m_relationshipManager->getBlockedUsers())
    {
        blockUser(blockedPersonaId);
    }

    if (EngineRender::TextureStreamingManager* textureStreamingManager = BaseType::getTextureStreamingManager())
    {
        textureStreamingManager->resumeLoadRequestPrioritization(m_userCamera.getCameraHandle());
    }

    if (m_lacm->getControlMode() != Engine::ControlMode::cScriptCamera)
    {
        Engine::ControlMode cameraControlMode = m_lastControlMode;
        // set last used camera control mode (only if FirstPerson or ThirdPerson)
        if (cameraControlMode != Engine::ControlMode::cThirdPerson && cameraControlMode != Engine::ControlMode::cFirstPerson)
        {
            cameraControlMode = m_config->m_camera.m_cameraModeOverride;
            if (cameraControlMode == Engine::ControlMode::cNone)
            {
                cameraControlMode = Engine::ControlMode::cThirdPerson;
            }
        }
        if (isVrActive())
        {
            cameraControlMode = Engine::ControlMode::cFirstPerson;
        }

        if (m_lacm->getControlMode() != cameraControlMode)
        {
            m_lacm->setControlMode(cameraControlMode, true);
        }
    }
}

void ClientApplication::handleSetAgentController(const ClientRegionMessages::SetAgentController* packet)
{
    LLCore::LogInfo("ClientApplication", "handleSetAgentController");

    const int64 packetFrame = (int64)packet->m_frame;

    m_setAgentControllerBuffer.insertMulti(packetFrame, *packet);
}

void ClientApplication::handleClientKickNotification(const ClientRegionMessages::ClientKickNotification* packet)
{
    LLCore::LogInfo("UserManager", "ClientKickNotification received: ", packet->m_message);

    getEventQueue().postEvent<ClientEditLocal::SwitchToRegionSelectModeRequest>();

    // prepare popup dialog on state change
    m_errorMessage.copy("Connection to server failed due to the error:\n\n", packet->m_message);
}

void ClientApplication::handleClientBanNotification(const KafkaCommon::ClientNotificationEvent& notification)
{
    if (!m_queuedDisconnect && notification.m_type == KafkaCommon::NotificationType::cBannedEvent)
    {
        m_queuedDisconnect = true;

        LLCore::Uuid                  personaId   = m_identityManager->getPersona()->getId();
        ClientLogin::LoginResultEvent loginResult = ClientLogin::LoginResultEvent(ClientLogin::LoginResultEvent::Result::cAccountBlocked, personaId);
        getUIEventQueue()->postEvent(loginResult);

        getEventQueue().postDeferredFunction([this, notification]() -> void {
            setLoadingPhase(LoadingPhase::cNone);
            onLoginDidLogoutEvent();


            m_queuedDisconnect = false;
        });
    }
}

void ClientApplication::handleClientSmiteNotification(const ClientRegionMessages::ClientSmiteNotification* packet)
{
    LLCore::LogInfo("ClientApplication", "ClientSmiteNotification received: ", packet->m_message);
    if (!m_queuedDisconnect)
    {
        m_queuedDisconnect = true;
        // deferring this since we want to disconnect on the next tick
        // disconnect in the middle of runFrame() causes a crash
        getEventQueue().postDeferredFunction([this]() -> void {
            onUserLoggedOut();

            m_clientStateMachine.switchToAtlas();

            m_authManager->logout();

            m_smiteUser = true; // zorba remove this when logout flow is working

            // prepare popup dialog on state change
            m_errorMessage = "You have been logged out of Sansar."_ll;

            m_rememberMe->clearAll();

#if 0 // zorba : implement this when logout is implemented
            disconnectFromRegion();

            onUserLoggedOut();

            // clean up the login credentials
            // force logout
            m_authManager->deferredAccessLogout();
            //requestStateDeferred(ApplicationStateId::cLoginAccount);

            // remove remember me data
            m_rememberMe->clearAll();
#endif
            m_queuedDisconnect = false;
        });
    }
}

void ClientApplication::handleClientRuntimeInventoryUpdatedNotification(const ClientRegionMessages::ClientRuntimeInventoryUpdatedNotification* notification)
{
    if (getUIEventQueue() != nullptr)
    {
        getUIEventQueue()->postEvent<ClientServices::BackpackChangedEvent>(notification->m_message);
    }
}

void ClientApplication::queueDisconnectNextFrame()
{
    if (m_queuedDisconnect)
    {
        return;
    }

    m_queuedReconnect  = false;
    m_queuedDisconnect = true;

    // deferring this since we want to disconnect on the next tick
    // disconnect in the middle of runFrame() causes a crash
    getEventQueue().postDeferredFunction([this]() -> void {
        // loading phase in case disconnect was called during scene load
        setLoadingPhase(LoadingPhase::cNone);
        m_clientStateMachine.switchToAtlas();
        m_queuedDisconnect = false;
    });
}


void ClientApplication::queueReconnectNextFrame()
{
    if (m_queuedReconnect)
    {
        // disconnect if trying to reconnect more than once
        queueDisconnectNextFrame();
        return;
    }

    m_queuedReconnect = true;

    LLCore::QTransform targetTransform;
    m_lacm->getLastSafeTransformWorldUp(&targetTransform);

    // deferring this since we want to reconnect on the next tick
    // since that is what disconnectNextFrame does
    getEventQueue().postDeferredFunction([this, targetTransform]() -> void {
        // loading phase in case disconnect was called during scene load
        setLoadingPhase(LoadingPhase::cNone);

        m_requestedExperienceUri.setInstanceId(LLCore::Uuid{});
        m_requestedExperienceUri.setTargetTransform(targetTransform);

        if (m_clientStateMachine.getCurrentState() == cInWorld)
        {
            // try reconnecting
            m_clientStateMachine.requestState(cConnectToRegion);
        }
        else
        {
            m_clientStateMachine.switchToAtlas();
        }
    });
}

void ClientApplication::handleErrorMessages()
{
    // MINGUS TODO: I think I added this m_errorMessage thing. There has to be a better way of doing this.
    // GAYTON TODO: Error messages can just be posted to the VisitExperienceService when they happen. m_errorMessage will be dead code after that.
    // GAYTON TODO: Why do we need this m_smiteUser bool?
    if (!m_errorMessage.isEmpty())
    {
        const ClientServices::VisitExperienceError error = m_smiteUser ? ClientServices::VisitExperienceError::cSmiteUser : ClientServices::VisitExperienceError::cServerKick;

        getEventQueue().postEvent(ClientServices::ShowVisitErrorCommand(m_currentExperienceUri, ""_ll, error, m_errorMessage));

        m_errorMessage.clear();
    }
}

void ClientApplication::setCurrentExperienceUri(const AppCore::SansarExperienceUri& currentExperienceUri)
{
    m_currentExperienceUri = currentExperienceUri;
    m_metricsManager->setExperienceUri(m_currentExperienceUri);
}

void ClientApplication::onSetWindowTitleRequest(const ClientUI::SetWindowTitleRequest& request)
{
    if (request.m_windowTitle.isEmpty())
    {
        m_mainWindow->setTitle("Sansar"_ll);
    }
    else
    {
        m_mainWindow->setTitle(LLCore::Format("Sansar | %s", request.m_windowTitle));
    }
}

void ClientApplication::handleConnectToRegionRequest(const ClientServices::ConnectToRegionRequest& request)
{
    // The experience in this request will have already been validated to exist and be in the current grid when this function is executed.

    // We are not going to try to respond to this case. We'll ignore the request.
    // This should never occur in practice but we've seen this when doing the infinite teleport test.
    // Ideally, this would be prevented before the request is made.
    if (m_clientStateMachine.getCurrentState() == ApplicationStateId::cConnectToRegion)
    {
        LLCore::LogWarning("ClientApplication", "Attempting to visit an experience while in ConnectToRegion.");
        return;
    }

    getUIEventQueue()->postEvent<ClientScripts::ClearReactionsEvent>();

    if (m_usingLocalRegionConductor)
    {
        if (request.m_experienceUri.isEmpty())
        {
            // if location is empty, we are being told to teleport back to ourself when we have a locally running region server, so go ahead and do it
            m_clientStateMachine.connectToRegion();
        }
        else
        {
            // location switching is not supported w/ local region conductor.
            // DO NOT assert or error - user scripts can request teleports even to clients connected to regions via local region conductor.
            // Log a message and drop the request in this case.
            LLCore::LogInfo("ClientApplication", "Location switching not supported when using local region conductor. Dropping request to switch to location ", request.m_experienceUri);
        }
        return;
    }

    m_requestedExperienceUri = request.m_experienceUri;
    m_currentExperienceId = request.m_experienceId;

    //TODO: Needed V3 to go to the specified place with the region conductor.
    m_clientStateMachine.connectToRegion();
}

void ClientApplication::handleEditSceneRequest(const ClientServices::EditSceneRequest& request)
{
    if (m_clientStateMachine.getCurrentState() != ApplicationStateId::cEditMode)
    {
        m_sceneInventoryItemToEdit          = request.m_inventoryItemId;
        m_experienceAssociatedWithSceneEdit = request.m_experienceId;
        m_editModeView                      = EditWorkspace::WorkspaceEditView::cWorkspaceEditView_Layout;
        m_clientStateMachine.switchToEditor();
    }
}

void ClientApplication::handleSwitchToRegionSelectModeRequest(const ClientEditLocal::SwitchToRegionSelectModeRequest& request)
{
    if (m_usingLocalRegionConductor)
    {
        m_clientStateMachine.switchToShutdown();
    }
    else
    {
        m_clientStateMachine.switchToAtlas();
    }
}

void ClientApplication::handleTeleportToFriendRequest(const ClientServices::TeleportToUserRequest& request)
{
    const AppCore::SansarExperienceUri& requestUri = request.m_experienceUri;

    if (!m_currentExperienceUri.isEmpty() && !requestUri.getTargetPersonaId().isNull() && requestUri.getExperienceHandle() == m_currentExperienceUri.getExperienceHandle() && requestUri.getInstanceId() == m_currentExperienceUri.getInstanceId())
    {
        getEventQueue().postEvent<ClientServices::TeleportToUserInWorldRequest>(requestUri.getTargetPersonaId());
    }
    else
    {
        LLCore::RefPointer<ClientServices::CodexActionContext> metricsContext = ClientServices::CodexActionContext::Create(LLCore::Uuid(), 0, LLCore::Uuid(), ClientServices::CodexActionId::cVisit, LLCore::SystemTime::Now(), "teleport-to-friend"_ll);
        getEventQueue().postEvent(ClientServices::VisitExperienceCommand(requestUri, ""_ll, ClientServices::VisitMode::cDefault, metricsContext));
    }
}

void ClientApplication::registerEmoteListeners()
{
    RegionCommon::WorldStateManager* wsm = m_worldStateManager != nullptr ? m_worldStateManager : (m_homeSpace != nullptr ? m_homeSpace->getWorldStateManager() : nullptr);
    if (!isEditViewModeCharacterEditor() && wsm != nullptr && wsm->getRuntimeWorld() != nullptr)
    {
        wsm->getRuntimeWorld()->getSimulationWorld()->getSimulationEventQueue()->addCallback(this, &ClientApplication::handleAnimationOperationCompleteEvent);
    }
}

void ClientApplication::handlePlayEmote(const Engine::PlayAgentAnimationRequest& request)
{
    RegionCommon::WorldStateManager* wsm = m_worldStateManager != nullptr ? m_worldStateManager : (m_homeSpace != nullptr ? m_homeSpace->getWorldStateManager() : nullptr);
    if (m_lacm != nullptr && m_lacm->getAgentControllerManager() != nullptr && wsm != nullptr && wsm->getRuntimeWorld() != nullptr)
    {
        Engine::AgentControllerManager* acm = m_lacm->getAgentControllerManager();
        bool isSitting = acm->getCharacterController()->isCurrentlySitting();

        AgentControllerMessages::AgentPlayAnimation packet;
        {
            packet.m_agentControllerId                 = m_localAgentId;
            packet.m_playAnimPacket[0].m_frame         = wsm->getRuntimeWorld()->getCurrentFrame() + 1; // Give one extra frame to load on already cached systems
            packet.m_playAnimPacket[0].m_componentId   = acm->getCharacterController()->getAnimationHandle()->getComponentId();
            packet.m_playAnimPacket[0].m_resourceId    = request.m_resourceId.asUuid();
            packet.m_playAnimPacket[0].m_skeletonType  = (uint8)request.m_skeletonType;
            packet.m_playAnimPacket[0].m_animationType = isSitting ? (uint8)EngineSimulation::AnimationType::cPartial_0 : (uint8)EngineSimulation::AnimationType::cFull;
            packet.m_playAnimPacket[0].m_playbackMode  = isSitting ? (uint8)EngineSimulation::AnimationPlaybackMode::cSinglePlay : (uint8)EngineSimulation::AnimationPlaybackMode::cLooping;
            packet.m_playAnimPacket[0].m_playbackSpeed = 1.0f;
        }

        const uint64 cSittingEventId = 1;
        wsm->handleMessage(&packet, true, isSitting ? cSittingEventId : 0);
    }
}

void ClientApplication::handleStopEmote(const Engine::StopAgentAnimationRequest& request)
{
    if (m_lacm != nullptr && m_lacm->getAgentControllerManager() != nullptr)
    {
        Engine::AgentControllerManager*                 acm = m_lacm->getAgentControllerManager();
        AnimationComponentMessages::BehaviorStateUpdate packet;
        {
            auto animHandle = acm->getCharacterController()->getAnimationHandle();

            packet.m_frame       = acm->getRuntimeWorld()->getCurrentFrame();
            packet.m_componentId = animHandle->getComponentId();
            packet.m_internalEventIds.insertLast((uint16)animHandle->getEventId("end_all_user_slot_event"_ll));
        }

        acm->sendBehaviorStateUpdate(&packet);
    }
}

void ClientApplication::handleAnimationOperationCompleteEvent(const EngineCommon::AnimationOperationCompleteEvent& event)
{
    if (m_userEmotes != nullptr)
    {
        m_userEmotes->onEmoteComplete(event.m_resourceId);
    }
}

void ClientApplication::handleIsDeveloperModeRequest(const ClientEditLocal::IsDeveloperModeRequest& request)
{
    request.m_responseQueue->postEvent<ClientEditLocal::IsDeveloperModeResponse>(m_config->m_developerMode);
}

void ClientApplication::handleEditModeViewSwitched(const ClientEditLocal::EditorModeSwitchEvent& event)
{
    m_editModeView = event.m_viewMode;
}

void ClientApplication::handleSwitchToEditModeRequest(const ClientEditLocal::SwitchToEditModeRequest& request)
{
    if (m_editModeView != request.m_workspaceView)
    {
        if (m_clientStateMachine.getCurrentState() == cEditMode)
        {
            //Switches between substates of the editor (e.g.: layout editor, character editor).
            EditModeState* editorState = static_cast<EditModeState*>(m_clientStateMachine.getState(cEditMode));
            editorState->exit();
            m_editModeView = request.m_workspaceView;
            editorState->enter();
        }
        else
        {
            m_editModeView = request.m_workspaceView;
            // When switching to the Character Editor, save the last good position of the avatar into the URI
            if (isEditViewModeCharacterEditor())
            {
                m_requestedExperienceUri.clearTargetTransform();
                if (!request.m_returnSpawnPoint.isEmpty())
                {
                    m_requestedExperienceUri.setTargetSpawnPointName(request.m_returnSpawnPoint);
                }
                else if (m_lacm != nullptr)
                {
                    LLCore::QTransform targetTransform;
                    m_lacm->getLastSafeTransformWorldUp(&targetTransform);
                    m_requestedExperienceUri.setTargetTransform(targetTransform);
                }
            }
            m_clientStateMachine.switchToEditor();
        }
    }

    //Processes wear now product information.
    if (request.m_capabilities.getCount() > 0)
    {
        EditModeState* editorState = static_cast<EditModeState*>(m_clientStateMachine.getState(m_clientStateMachine.getRequestedState()));
        editorState->setPostInitializationUIConfiguration(request.m_productId, request.m_capabilities);
    }
}

bool ClientApplication::isEditViewModeCharacterEditor() const
{
    return isCharacterWorkspaceEditView(m_editModeView);
}

void ClientApplication::startStreamer()
{
    if (m_config->m_streamer.m_enabled)
    {
        m_streamer = new StreamerLauncher(m_config->m_streamer);

        LLCore::String channelName("NOCHANNELSET"_ll);
        {
            if (m_config->m_streamer.m_channel.m_channelName.isChosen())
            {
                channelName = m_config->m_streamer.m_channel.m_channelName;
            }
            else if (m_config->m_streamer.m_channel.m_generated.isChosen())
            {
                LLCore::String experienceId;
                if (m_config->m_streamer.m_channel.m_generated.m_experienceOverride.isProvided())
                {
                    experienceId = m_config->m_streamer.m_channel.m_generated.m_experienceOverride;
                }
                else
                {
                    uint32 hash = LLCore::HashType(m_currentExperienceUri.getExperienceHandle(), m_currentExperienceUri.getPersonaHandle());
                    ConvertToHex(hash, &experienceId);
                }

                channelName = LLCore::Format("%s_%.8s%s", m_config->m_streamer.m_channel.m_generated.m_eventId, experienceId, m_identityManager->getPersona()->getName());
                if (channelName.getLength() > 63)
                {
                    channelName.erase(63);
                }
            }
            else
            {
                LLCore::LogError("Streamer", "No channel was detected for streamer.");
            }
        }
        const uint64* userIdRawData = m_identityManager->getPersona()->getId().getRawData();
        uint32        streamerId    = (uint32)*userIdRawData;
        m_streamer->start(m_mainWindow, channelName, streamerId, m_config);
    }
#ifdef CLIENT_USE_FFMPEG
    if (m_config->m_enableVideostream)
    {
        m_streamMuxer->start();
    }
#endif
}

void ClientApplication::stopStreamer()
{
    if (!m_streamer.isNull())
    {
        m_streamer->stop();
    }
#ifdef CLIENT_USE_FFMPEG
    if (m_streamMuxer != nullptr)
    {
        m_streamMuxer->stop();
    }
#endif
}

void ClientApplication::showLowGraphicsModalIfNeeded()
{
    if (m_showLowGraphicsDialog)
    {
        LLCore::LogInfo("GraphicsCheck", "Show warning for low quality graphics settings");
        postUIEvent<ClientUI::ShowLowGraphicsWarningDialog>();
        m_showLowGraphicsDialog = false;
    }
}

void ClientApplication::processPendingOperations()
{
    auto* runtimeWorld = getRuntimeWorld();
    if (runtimeWorld)
    {
        const int64 currentFrame              = runtimeWorld->getCurrentFrame();
        const auto& processSetAgentController = [this, currentFrame](int64 frame, int64 lastFrameToProcess, const ClientRegionMessages::SetAgentController& packet) {
            if (m_worldStateManager->getAgentController(Engine::AgentControllerId(packet.m_agentControllerId)))
            {
                setAgentController(packet);
            }
            else
            {
                // Try again next frame
                m_setAgentControllerBuffer.insertMulti(currentFrame + 1, packet);
            }
        };

        m_setAgentControllerBuffer.processToFrame(currentFrame, processSetAgentController);
    }
}

void ClientApplication::initializeAssetSystem()
{
    LLCore::LogInfo<"http"_ll_tag>("ClientApplication", "Use IPv4: ", m_config->m_networking.m_useV4Socket);

    // allow for blocking IO during asset system initialization
    LLCore::ThreadTimeoutUntrackedTimeGuard initAssetSystemGuard;

    m_config->m_asset.m_useV4Socket = m_config->m_networking.m_useV4Socket;

    m_assetManager           = new LLAssetSystem::AssetManager(m_config->m_asset);
    m_resourceStoreManager   = new LLResource::ResourceStoreManager();
    m_resourceLoaderJobQueue = new Engine::JobQueue();
    m_resourceLoaderJobQueue->startThreads(1, 4, LLCore::Duration::FromSeconds(10), "ResourceLoader");
    m_engineResourceLoader = new Engine::EngineResourceLoader(m_resourceLoaderJobQueue, m_assetManager, m_resourceStoreManager);

    m_config->m_asset.m_forceWipeCache = false;
    getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cClearCacheCommand, m_config->m_asset.m_forceWipeCache);

    for (LLCore::StringRef resourceTypeToDisable : m_config->m_asset.m_disabledResourceTypes)
    {
        if (LLResource::ResourceTypeInfoRegistry::HasTypeInfo(resourceTypeToDisable))
        {
            LLResource::ResourceStoreBase& resourceStore = LLResource::ResourceTypeInfoRegistry::GetTypeInfo(resourceTypeToDisable).m_getOrCreateResourceStoreFunc(m_resourceStoreManager);
            resourceStore.setLoadsDisabled(true);
        }
        else
        {
            LLCore::LogWarning("ClientApplication", "Failed to disable loading of resource type ", resourceTypeToDisable, ". Resource type unrecognized.");
        }
    }
    if (m_config->m_enableAudio)
    {
        LLCore::Path defaultAudioMaterialFile("RUNTIME:Audio/Material_generic.toml");
        EngineAudio::AudioSystem::GetInstance().configureDefaultMaterial(defaultAudioMaterialFile, m_engineResourceLoader);
    }

    //
    // if we don't do this here we don't have the resource stores properly initialized if we go straight to edit mode
    // and we can't load things like VR UI models.
    //
    {
        const Engine::ClientServerVariant variant = Engine::ClientServerVariant::cCSVariantPcClient;

        m_resourceStoreManager->getOrCreateResourceStore<Engine::WorldDefinition>().setVariant(variant);
        m_resourceStoreManager->getOrCreateResourceStore<Engine::WorldChunkDefinition>().setVariant(variant);
        m_resourceStoreManager->getOrCreateResourceStore<Engine::ClusterDefinition>().setVariant(variant);
    }

    // Don't initialize a Homespace when a local RegionServer is used.
    if (m_config->m_connection.m_useConductor)
    {
        ensureHomeSpace(HomeSpaceScene::cLoadingScreen);
    }

    EditMode::ConfigureCommonAssets(m_config->m_asset.m_sceneEditorCommonAssets.m_config);

    ClientVr::VrVisualizationLayer::ConfigureCommonAssets(m_config->m_asset.m_vrCommonAssets.m_config);
}

void ClientApplication::terminateResourceJobs()
{
    if (m_resourceLoaderJobQueue)
    {
        m_resourceLoaderJobQueue->cancelAllJobs();
        m_resourceLoaderJobQueue->stopThreads(); // blocks until all threads are stopped
    }
    delete m_engineResourceLoader;
    m_engineResourceLoader = nullptr;

    delete m_resourceLoaderJobQueue;
    m_resourceLoaderJobQueue = nullptr;
}

void ClientApplication::clearScriptConsole()
{
    if (LLCore::EventQueue* uiEventQueue = getUIEventQueue())
    {
        uiEventQueue->postEvent<Engine::ScriptConsoleClear>();
    }
}

void ClientApplication::parseLoginFromSceneUri(const LLHttp::HttpUrl& sceneUri)
{
    LLCORE_ASSERT(m_loginManager != nullptr);

    // Ignore empty string, no sceneUri was provided.
    if (sceneUri.isEmpty())
    {
        return;
    }

    LLCore::LogInfo("ClientApplication", "Parsing login from sceneUri:", sceneUri);

    LLCore::Map<LLCore::String, LLCore::String, 0> queryMap;
    sceneUri.getQueryMap(&queryMap);
    const LLCore::String* loginCode            = queryMap.find("code");
    const LLCore::String* loginCsrfTokenString = queryMap.find("state");

    if (loginCode != nullptr && loginCsrfTokenString != nullptr)
    {
        // Found both code and state params.
        m_loginManager->setCodeAccessParams(LLCore::ConvertType<LLCore::String>(sceneUri), *loginCode, *loginCsrfTokenString);
    }
    else if (loginCode == nullptr && loginCsrfTokenString == nullptr)
    {
        // No login params were provided. Continue silently.
    }
    else
    {
        // Found one of code or state params, but not both.
        LLCore::LogWarning("ClientApplication", "Failed to parse login from sceneUri, missing params");
    }
}

void ClientApplication::terminateAssetSystem()
{
    EditMode::ShutdownCommonAssets();
    ClientVr::VrVisualizationLayer::ShutdownCommonAssets();

    delete m_resourceStoreManager;
    m_resourceStoreManager = nullptr;
    delete m_assetManager;
    m_assetManager = nullptr;
}

void ClientApplication::onCompiledSceneEvent(const ClientEditLocal::CompiledSceneEvent& event)
{
    m_cachedSceneMetrics   = event.m_sceneMetrics;
    m_cachedSceneBuildTime = event.m_buildTime;
}

void ClientApplication::onPublishedSceneEvent(const ClientEditLocal::PublishedSceneEvent& event)
{
    if (m_cachedSceneMetrics.m_dataCompiled && !m_currentSceneEditUri.isEmpty())
    {
        ClientServices::PublishSceneMetricsEvent metrics;
        metrics.m_creatorPersonaHandle = m_currentSceneEditUri.getPersonaHandle();
        metrics.m_sceneInventoryId     = m_currentSceneEditUri.getAssetId();
        metrics.m_sceneData            = &m_cachedSceneMetrics;
        metrics.m_buildTime            = m_cachedSceneBuildTime;

        LLMetrics::RecordMetric(metrics);
    }
}

void ClientApplication::onRefreshAudioDevicesEvent(const ClientAudio::RefreshAudioDevicesEvent& event)
{
    if (m_vrSystem)
    {
        LLAudio::AudioSystem::GetInstance().updateDefaultDeviceNames(m_vrSystem->getHeadsetManager());
        LLAudio::AudioSystem::GetInstance().setVrMode(isVrActive(), m_vrSystem->getHeadsetManager());
    }
}

void ClientApplication::onPersonaDataResponse(const Identity::PersonaPublicDataResponse& response)
{
    if (!response.isOk())
    {
        LLCore::LogWarning("Client app", "Error getting persona from catalog");
    }
    else
    {
        for (auto persona : response.m_personas)
        {
            LLCore::LogInfo("Client app", "Received persona ", persona.getName());
        }
    }
}

void ClientApplication::handleApplicationMenuToggledEvent(const LLUI::ApplicationMenuToggledEvent& event)
{
    if (isVrActive())
    {
        if (m_lacm != nullptr)
        {
            m_lacm->setVrAppMenuVisible(event.m_menuVisible);
        }

        const float hapticIntensity = 0.2f;
        const float openDurationMS  = 100.0f;
        const float closeDurationMS = 50.0f;

        const LLInput::WandState* wandState = m_vrSystem->getInputManager().getCurrentState(LLInput::WandHand::cLeft);
        if (wandState != nullptr)
        {
            getEventQueue().postEvent(LLInput::WandVibrationRequest(wandState->m_deviceIndex, hapticIntensity, event.m_menuVisible ? openDurationMS : closeDurationMS));
        }
    }
}

void ClientApplication::setDebugDeltaTimeForced(bool force, LLCore::Scalar_ConstParameter deltaTime)
{
    if (force != isDebugDeltaTimeForced())
    {
        m_debugDeltaTimeForced = force ? deltaTime.asFloat() : -1.0f;

        RegionCommon::DebugServiceRequest::DeltaTimeRequest request(&getEventQueue());
        request.m_clientDeltaTimeForced = m_debugDeltaTimeForced;
        request.m_clientDeltaTimeScale  = m_debugDeltaTimeScale;

        // user manager uses our event queue.
        getEventQueue().postEvent(request);

        LLCore::LogInfo("Client app",
                        "debugDeltaTimeForced: ",
                        isDebugDeltaTimeForced() ? "**true**" : "false",
                        " (",
                        getDebugDeltaTimeForced(),
                        ")");
    }
}

void ClientApplication::setDebugDeltaTimeScale(bool scale, LLCore::Scalar_ConstParameter deltaTimeScale)
{
    if (scale != isDebugDeltaTimeScaled())
    {
        m_debugDeltaTimeScale = scale ? deltaTimeScale.asFloat() : 1.0f;

        RegionCommon::DebugServiceRequest::DeltaTimeRequest request(&getEventQueue());
        request.m_clientDeltaTimeForced = m_debugDeltaTimeForced;
        request.m_clientDeltaTimeScale  = m_debugDeltaTimeScale;
        request.m_requestId             = -1;

        // user manager uses our event queue.
        getEventQueue().postEvent(request);

        LLCore::LogInfo("Client app",
                        "debugDeltaTimeScaled: ",
                        isDebugDeltaTimeScaled() ? "**true**" : "false",
                        " (",
                        getDebugDeltaTimeScale(),
                        ")");
    }
}

void ClientApplication::handleDebugDeltaTimeResponse(const RegionCommon::DebugServiceRequest::DeltaTimeResponse& response)
{
    if (response.isOk())
    {
        // initial request was sent by us
        if (response.m_requestId == -1)
        {
            LLCore::LogInfo("Client app", "regionServer granted time scaling request.");
        }
        // initial request not sent by us
        else
        {
            m_debugDeltaTimeScale  = response.m_serverDeltaTimeScale;
            m_debugDeltaTimeForced = response.m_serverDeltaTimeForced;

            LLCore::LogInfo("Client app",
                            LLCore::Format("Peer %d changed time. debugDeltaTimeScaled: %s (%g) debugDeltaTimeForced: %s (%g)",
                                           response.m_requestId,
                                           isDebugDeltaTimeScaled() ? "*true*" : "false",
                                           getDebugDeltaTimeScale(),
                                           isDebugDeltaTimeForced() ? "**true**" : "false",
                                           getDebugDeltaTimeForced()));
        }
    }
    else
    {
        LLCore::LogInfo("Client app", "regionServer refused time scaling request: ", response.getErrorMessage());
    }
}

void ClientApplication::toggleVisualDebuggerCapture()
{
    if (!m_config->m_vdb.m_enable.asRead())
    {
        return;
    }

    if (EngineSimulation::VisualDebugger* vdb = getRuntimeWorld()->getSimulationWorld()->getVisualDebugger())
    {
        RegionCommon::DebugServiceRequest::VisualDebuggerCaptureRequest captureRequest(&getEventQueue());

        if (vdb->getCaptureState() == EngineSimulation::VisualDebugger::CaptureState::cCapturing)
        {
            captureRequest.m_beginCapture       = false;
            captureRequest.m_startTimeFormatted = m_vdbCaptureStartFormatted;

            const LLCore::String& capturePath = vdb->getCapturePath();

            vdb->endCapture();

            // restore cached viewer set from before capture started
            vdb->setViewers(m_cachedVdbViewers);

            LLCore::LogInfo("Client app", "Finished capture of VDB movie to ", capturePath);
        }
        else
        {
            LLCore::NativeFileSystem& fileSystem = LLCore::CoreNativeFileSystem::GetInstance();
            LLCore::Path              capturePath(captureRequest.GetSavePath());

            if (!fileSystem.isExistingDirectory(capturePath))
            {
                const bool createSucceeded = fileSystem.createDirectory(capturePath);
                if (!createSucceeded)
                {
                    LLCore::String nativeSavePath;
                    fileSystem.getNativePath(capturePath, &nativeSavePath);
                    LLCore::LogWarning("Client app", "Failed creating save directory ", nativeSavePath);
                    return;
                }
            }

            {
                LLCore::StringFixed<32> captureFileName;
                LLCore::String          nativeCapturePath;

                captureRequest.setStartTimeFormattedNow();

                for (const LLCore::String& viewer : m_config->m_vdb.m_captureViewers)
                {
                    captureRequest.m_viewers.insertLast(viewer);
                }

                captureRequest.m_beginCapture = true;

                // must be done after clientCaptureRequest.setStartTimeFormattedNow()
                captureRequest.getClientFileName(&captureFileName);

                capturePath.setFilename(captureFileName);
                fileSystem.getNativePath(capturePath, &nativeCapturePath);

                // cache current set of vdb viewers
                vdb->getViewers(&m_cachedVdbViewers);

                // assign the set we want for capture
                vdb->setViewers(captureRequest.m_viewers);

                vdb->capture(nativeCapturePath.asRead());

                m_vdbCaptureStartFormatted = captureRequest.m_startTimeFormatted;
            }

            LLCore::LogInfo("Client app", "Starting capture of VDB movie to ", vdb->getCapturePath());
        }

        // user manager uses our event queue.
        getEventQueue().postEvent(captureRequest);
    }
    else
    {
        LLCore::LogInfo("Client app", "Visual Debugger not initialized. toggleVisualDebuggerCapture ignored.");
    }
}

void ClientApplication::handleVisualDebuggerCaptureResponse(const RegionCommon::DebugServiceRequest::VisualDebuggerCaptureResponse& response)
{
    if (response.isOk())
    {
        if (response.m_beginCapture)
        {
            LLCore::LogInfo("Client app", "RegionServer begin capture request for ", response.m_startTimeFormatted, " succeeded.");
            return;
        }

        if (response.m_uncompressedSize > 0)
        {
            LLCore::NativeFileSystem& fileSystem = LLCore::CoreNativeFileSystem::GetInstance();
            LLCore::Path              capturePath(response.GetSavePath());

            if (!fileSystem.isExistingDirectory(capturePath))
            {
                fileSystem.createDirectory(capturePath);
            }

            LLCore::StringFixed<64> captureFileName;
            LLCore::String          nativeCapturePath;

            response.getServerFileName(&captureFileName);

            capturePath.setFilename(captureFileName);
            fileSystem.getNativePath(capturePath, &nativeCapturePath);

            if (LLCore::NativeFile* captureFile = fileSystem.openFile(capturePath, LLCore::FileSystem::cOpenMode_WriteReplace))
            {
                LLOodle::Initialize();
                LLCore::Array<byte> hkmData;

                LLOodle::Decompress(response.m_compressedHkmBytes.begin(), response.m_compressedHkmBytes.getCount(), &hkmData);

                mem_int numBytesWritten = 0;
                if (captureFile->write(hkmData.begin(), hkmData.getCount(), &numBytesWritten))
                {
                    LLCore::LogInfo("Client app", "Saved RegionServer VDB movie to ", nativeCapturePath);
                }
                else
                {
                    LLCore::LogWarning("Client app", "Failed saving RegionServer VDB movie to ", nativeCapturePath);
                }
                delete captureFile;
                LLOodle::Terminate();
            }
            else
            {
                LLCore::LogWarning("Client app", "Could not create ", nativeCapturePath, " for saving RegionServer VDB movie.");
            }
        }
        else
        {
            LLCore::LogInfo("Client app", "Local RegionServer acknowledged successful VDB capture for ", response.m_startTimeFormatted);
        }
    }
    else
    {
        LLCore::LogWarning("Client app", "RegionServer VisualDebugger ", response.m_beginCapture ? "begin" : "end", " capture request for ", response.m_startTimeFormatted, " failed : ", response.getErrorMessage());
    }
}

void ClientApplication::onUserLoggedOut()
{
    if (getCrashReporter() != nullptr)
    {
        getCrashReporter()->onUserLoggedOut();
    }

    UpdatePersonaRoot(nullptr);

    m_metricsManager->onUserLoggedOut();
}

void ClientApplication::onUserLoggedIntoAccount(const Identity::Account* account)
{
    LLCORE_ASSERT(account != nullptr);

    if (getCrashReporter() != nullptr)
    {
        LLCore::StringFixed<64> sessionId;
        ConvertType(account->getSessionId(), &sessionId);

        getCrashReporter()->onUserLoggedIntoAccount(account->getAccountId(),
                                                    account->getUserName().asRead(),
                                                    account->getEmail().asRead(),
                                                    sessionId.asRead());
    }

    m_metricsManager->onUserLoggedIntoAccount(account);
}

void ClientApplication::onUserLoggedIntoPersona(const Identity::Persona* persona)
{
    LLCORE_ASSERT(persona != nullptr);

    if (getCrashReporter() != nullptr)
    {
        getCrashReporter()->onUserLoggedIntoPersona(persona->getId(), persona->getHandle().asRead(), persona->getName().asRead());
    }

    UpdatePersonaRoot(persona);

    m_metricsManager->onUserLoggedIntoPersona(persona);
    m_userEmotes->onUserLoggedIntoPersona(persona);
}

void ClientApplication::updateComfortZone(const Engine::CameraComponent* camera, Engine::RuntimeWorld* runtimeWorld, ClientVr::ComfortZoneFilter* filter) const
{
    LLCORE_ASSERT(camera != nullptr && runtimeWorld != nullptr);
    if (m_comfortZoneFilter)
    {
        if (m_lacm && m_lacm->isUsingFirstPersonCamera() && m_localAgentId != Engine::cInvalidAgentControllerId)
        {
            const LLCore::Vector4                    cameraPos        = camera->getTranslation();
            const RegionCommon::AgentControllerList& agentControllers = m_worldStateManager->getAgentControllers();
            for (mem_int i = 0; i < agentControllers.getCount(); i++)
            {
                const Engine::AgentControllerManager* agentController   = agentControllers.at(i).m_agentController;
                const Engine::AgentControllerId       agentControllerId = agentController->getAgentControllerId();
                if (agentControllerId != m_localAgentId && !m_lacm->isAgentBlocked(agentControllerId))
                {
                    LLCore::Uuid personaId;
                    m_worldStateManager->getPersonaIdForAgentControllerId(agentControllerId, &personaId);

                    bool                              isFriend          = m_relationshipManager->isFriend(personaId);
                    EngineSimulation::AnimationHandle character         = agentController->getCharacterController()->getAnimationHandle();
                    Engine::ObjectId                  characterObjectId = Engine::Object::GetObjectIdFromComponentId(character->getComponentId());
                    Engine::Object*                   characterObject   = runtimeWorld->getObjectPointer(characterObjectId);
                    m_comfortZoneFilter->filterObject(characterObject, cameraPos, isFriend, m_heartbeatContext.peekMetric(LLApplication::EventMetrics::cFrameTime).getCount());
                }
            }
        }
        m_comfortZoneFilter->update(runtimeWorld, m_heartbeatContext.peekMetric(LLApplication::EventMetrics::cFrameTime).getCount());
    }
}

void ClientApplication::onScriptConsoleBeginRequest(const Engine::ScriptConsoleBeginRequest& event)
{
    m_accountConnector->subscribeScriptRegionConsole(m_currentRegionInstanceID);
    event.m_responseQueue->postEvent<Engine::ScriptConsoleBeginResponse>(m_currentRegionInstanceID, m_config->m_script.m_enableDeveloperCommands.asRead());
}

void ClientApplication::onScriptConsoleEndRequest(const Engine::ScriptConsoleEndRequest& event)
{
    m_accountConnector->unsubscribeScriptRegionConsole();
}

void ClientApplication::blockUser(const LLCore::Uuid& personaId)
{
    if (m_lacm != nullptr && m_worldStateManager != nullptr)
    {
        LLCore::Array<Engine::AgentControllerManager*> acmList = m_worldStateManager->getAgentControllerForPersonaIds(personaId);
        for (Engine::AgentControllerManager* acm : acmList)
        {
            if (acm != nullptr)
            {
                acm->setLocallyBlocked(true);
                getEventQueue().postEvent(ClientServices::SetMuteUserByPersonaIdRequest(personaId, true));
                EngineSimulation::AnimationHandle character = acm->getCharacterController()->getAnimationHandle();
                m_lacm->blockAgent(acm->getAgentControllerId(), character->getComponentId());

                Engine::RuntimeWorld* runtimeWorld = getRuntimeWorld();
                if (runtimeWorld != nullptr)
                {
                    Engine::ObjectId characterObjectId = Engine::Object::GetObjectIdFromComponentId(character->getComponentId());
                    Engine::Object*  characterObject   = runtimeWorld->getObjectPointer(characterObjectId);
                    characterObject->disableRendering();
                }
            }
        }
    }
}

void ClientApplication::unblockUser(const LLCore::Uuid& personaId)
{
    if (m_lacm != nullptr && m_worldStateManager != nullptr)
    {
        Engine::AgentControllerManager* acm = m_worldStateManager->getAgentControllerForPersonaId(personaId);
        if (acm != nullptr)
        {
            acm->setLocallyBlocked(false);
            getEventQueue().postEvent(ClientServices::SetMuteUserByPersonaIdRequest(personaId, false));
            m_lacm->unblockAgent(acm->getAgentControllerId());

            Engine::RuntimeWorld* runtimeWorld = getRuntimeWorld();
            if (runtimeWorld != nullptr)
            {
                EngineSimulation::AnimationHandle character         = acm->getCharacterController()->getAnimationHandle();
                Engine::ObjectId                  characterObjectId = Engine::Object::GetObjectIdFromComponentId(character->getComponentId());
                Engine::Object*                   characterObject   = runtimeWorld->getObjectPointer(characterObjectId);
                characterObject->enableRendering();
            }
        }
    }
}

void ClientApplication::ensureHomeSpace(HomeSpaceScene scene, LLCore::Function<void()> callback)
{
    if (rendererRunning())
    {
        if (m_homeSpace && m_homeSpace->getHomeSpaceScene() != scene)
        {
            destroyHomeSpace();
        }

        if (!m_homeSpace)
        {
            HomeSpaceSceneConfig config;
            switch (scene)
            {
                case HomeSpaceScene::cHomeSpace:
                {
                    config = m_config->m_homeSpace.m_homeSpaceScene;
                    break;
                }
                case HomeSpaceScene::cCharacterEditor:
                {
                    config = m_config->m_homeSpace.m_dressingRoomScene;
                    break;
                }
                case HomeSpaceScene::cLoadingScreen:
                {
                    config = m_config->m_homeSpace.m_loadingScene;
                    break;
                }
                    LLCORE_NO_DEFAULT_CASE();
            }

            m_homeSpace = new HomeSpace(this, scene, config);
        }
        m_homeSpace->addHomespaceLoadedCallback(callback);
    }
    else if (!callback.isEmpty())
    {
        //homespace will never be available, execute callback immediately
        callback();
    }
}

void ClientApplication::destroyHomeSpace()
{
    if (m_homeSpace)
    {
        delete m_homeSpace;
        m_homeSpace = nullptr;
    }
}

bool ClientApplication::canRenderHomeSpace() const
{
    // not requiring isVrActive() here so that we have a render world during transition from vr -> non-vr
    return m_homeSpace && m_homeSpace->getWorldStateManager() && m_homeSpace->getWorldStateManager()->getRuntimeWorld();
}

void ClientApplication::loadHomeSpaceAvatar(const LLResource::ResourceId& avatarResourceId)
{
    if (m_homeSpace != nullptr)
    {
        m_homeSpace->loadAvatar(avatarResourceId);
    }
}

void ClientApplication::onLoadingSceneReady()
{
    postUIEvent<ClientServices::LoadingSceneReadyEvent>();
}

void ClientApplication::removeAllDebugCommands()
{
    m_debugConsoleCommandManager->removeAllCommands();
}

void ClientApplication::onAgentControllerCreated(RegionCommon::WorldStateManager* wsm, Engine::AgentControllerId agentControllerId, LLCore::Uuid personaId, Engine::ObjectId controlledObjectId)
{
    // users loaded after current user is done loading into an experience, handle them being blocked
    if (m_relationshipManager->isBlocked(personaId))
    {
        blockUser(personaId);
    }
}

void ClientApplication::onLoginDidLogoutEvent()
{
    if (m_homeSpace != nullptr)
    {
        m_homeSpace->removeAvatar();
    }

    m_accountConnector->disconnectFromKafka();

    m_requestedExperienceUri.clear();

    m_userEmotes->logout();
    m_notificationManager->logout();
    m_chatManager->logout();
    m_relationshipManager->logout();
    m_inventoryManager->logout();

    delete m_clientMarketplace;
    m_clientMarketplace = nullptr;

    m_hasBeenInWorld = false;

    m_clientStateMachine.switchToLoginAccount();
}

int ClientApplication::getServerPortOffset() const
{
    if (!m_config->m_connection.m_useConductor)
    {
        return m_config->m_connection.m_localServerPortOffset;
    }
    return 0;
}

void ClientApplication::StartingUp::giveTime()
{
    // this dummy state exists just to do nothing until constructor finishes
    // (shouldn't be necessary once all the relevant clientapplication constructor logic is moved to state machines)
    requestNextState<DisconnectedFromRegion>();
}

ClientApplication::DisconnectedFromRegion::DisconnectedFromRegion()
{
    LLCore::LogInfo("State", "Entering DisconnectedFromRegion");

    getContext().setLoadingPhase(ClientApplication::LoadingPhase::cNone);
    getContext().getEventQueue().addCallbackWithOwner(this, this, &DisconnectedFromRegion::handleVoiceRequestStatus);
}

ClientApplication::DisconnectedFromRegion::~DisconnectedFromRegion()
{
    LLCore::LogInfo("State", "Leaving DisconnectedFromRegion");

    getContext().getEventQueue().removeCallbacksWithOwner(this);
}

void ClientApplication::DisconnectedFromRegion::handleVoiceRequestStatus(const ClientServices::VoiceRequestStatus& event)
{
    ClientServices::VoiceRequestStatus::Response response;

    response.m_isMicMuted          = true;
    response.m_isMicAvailable      = false;
    response.m_isMicMutedFromAdmin = false;

    getContext().getEventQueue().postEvent(response);
    event.getResponseQueue()->postEvent(response);
}

ClientApplication::RequestingRegionAndVoiceServerInfo::RequestingRegionAndVoiceServerInfo()
{
    LLCore::LogInfo("State", "Entering RequestingRegionAndVoiceServerInfo");

    // Connecting to a region marks an end of the first time user flow.
    getContext().m_accountConfigurationManager->clearFirstTimeUser();

    LLCORE_ASSERT(getContext().m_usingLocalRegionConductor || !getContext().m_requestedExperienceUri.getExperienceHandle().isEmpty(), "Attempting to connect to location by URI with empty experience handle");

    getContext().setLoadingPhase(ClientApplication::LoadingPhase::cWaitingForRegionLaunch);

    getContext().getEventQueue().addCallbackWithOwner(this, this, &RequestingRegionAndVoiceServerInfo::onRegionConnectionResponse);

    if (getContext().getUISystem() != nullptr)
    {
        getContext().postUIEvent<LLUI::CreateViewModelByNameRequest>("HudViewModel");
        getContext().postUIEvent<LLUI::CreateViewModelByNameRequest>("ScreenshotEditorViewModel");
        getContext().postUIEvent<LLUI::CreateViewModelByNameRequest>("InWorldRibbonViewModel");
        getContext().postUIEvent<LLUI::CreateViewModelByNameRequest>("DiagnosticsToolboxViewModel");
        getContext().postUIEvent<LLUI::CreateViewModelByNameRequest>("AppletMenuViewModel");
        getContext().postUIEvent<LLUI::CreateViewModelByNameRequest>("Atlas3ViewModel");

        if (!getContext().m_usingLocalRegionConductor)
        {
            getContext().postUIEvent<ClientServices::Atlas3ConnectingToRegionEvent>(getContext().m_requestedExperienceUri);
            getContext().switchUIWorkspace(ClientUI::cWorkspaceNameLoading);
        }
    }

    if (getContext().m_usingLocalRegionConductor)
    {
        getContext().connectToLocalRegionServer(getContext().m_requestedExperienceUri);
    }
    else
    {
        ClientServices::RegionWorldDefinitionRequest wdrequest;
        ClientServices::RegionConnectionRequest      request;

        wdrequest.connectToUri(getContext().m_requestedExperienceUri);
        request.connectToUri(getContext().m_requestedExperienceUri);
        if (getContext().m_config->m_connection.m_conductor.m_configuration.isProvided())
        {
            request.useConfiguration(getContext().m_config->m_connection.m_conductor.m_configuration.asRead());
        }
        if (getContext().m_config->m_connection.m_conductor.m_accessGroup.isProvided())
        {
            wdrequest.useAccessGroup(getContext().m_config->m_connection.m_conductor.m_accessGroup.asRead().asRead());
            request.useAccessGroup(getContext().m_config->m_connection.m_conductor.m_accessGroup.asRead().asRead());
        }
        if (!getContext().m_requestedExperienceUri.getInstanceRole().isEmpty())
        {
            request.useInstanceRole(getContext().m_requestedExperienceUri.getInstanceRole().asRead());
        }
        if (!getContext().m_requestedExperienceUri.getInstanceName().isEmpty())
        {
            request.useInstanceName(getContext().m_requestedExperienceUri.getInstanceName().asRead());
        }
        if (!getContext().m_requestedExperienceUri.getBroadcastChannel().isEmpty())
        {
            request.useBroadcastChannel(getContext().m_requestedExperienceUri.getBroadcastChannel().asRead());
        }
        wdrequest.respondOnQueue(&getContext().getEventQueue());
        request.respondOnQueue(&getContext().getEventQueue());
        getContext().getEventQueue().postEvent(wdrequest);
        getContext().getEventQueue().postEvent(request);

        getContext().ensureHomeSpace(HomeSpaceScene::cLoadingScreen, {&getContext(), &ClientApplication::onLoadingSceneReady});
    }
}

ClientApplication::RequestingRegionAndVoiceServerInfo::~RequestingRegionAndVoiceServerInfo()
{
    LLCore::LogInfo("State", "Leaving RequestingRegionAndVoiceServerInfo");

    getContext().m_inventoryManager->saveLocalCacheFile();
    getContext().getEventQueue().removeCallbacksWithOwner(this);
}

void ClientApplication::RequestingRegionAndVoiceServerInfo::onRegionConnectionResponse(const ClientServices::RegionConnectionResponse& response)
{
    if (response.isOk())
    {
        const LLCore::String& userName  = getContext().m_identityManager->getAccount()->getUserName();
        const LLCore::String& accountId = getContext().m_identityManager->getAccount()->getAccountId();

        LLCore::Uuid personaId = getContext().m_identityManager->getPersona()->getId();

        ClientServices::ServerAddressResponse::Protocol proto = ClientServices::ServerAddressResponse::Protocol::cUdp;
        if (getContext().m_config->m_networking.m_useTcp)
        {
            proto = ClientServices::ServerAddressResponse::Protocol::cTcp;
        }
        LLHttp::HttpUrl        regionServerUrl = response.m_regionServerAddress.asHttpUrl(proto);
        LLHttp::HttpUrl        voiceServerUrl  = response.m_voiceServerAddress.asHttpUrl(proto);
        LLResource::ResourceId worldDefinitionIdHint(response.m_worldDefinitionID);
        LLCore::LogInfo("ClientApplication", "WorldDefinitionId: ", worldDefinitionIdHint);
        LLCore::LogInfo("ClientApplication", "Region Conductor connecting us to region server: ", regionServerUrl.toAddress());
        LLCore::LogInfo("ClientApplication", "Region Conductor connecting us to voice server: ", voiceServerUrl.toAddress());
        getContext().m_requestedExperienceUri.setInstanceId(response.m_instanceID);
        getContext().m_metricsManager->setExperienceUri(getContext().m_requestedExperienceUri);

        VoiceManager::Cinfo voiceManagerCinfo;
        {
            voiceManagerCinfo.m_allocator            = LLCore::CoreAllocator::GetInstancePtr();
            voiceManagerCinfo.m_streamRouter         = getContext().m_streamRouter;
            voiceManagerCinfo.m_appEventQueue        = &getContext().getEventQueue();
            voiceManagerCinfo.m_nullUIEventQueue     = getContext().getUIEventQueue();
            voiceManagerCinfo.m_useTcp               = getContext().m_config->m_networking.m_useTcp;
            voiceManagerCinfo.m_config               = &getContext().m_config->m_voice;
            voiceManagerCinfo.m_audioConfig          = &getContext().m_config->m_audio;
            voiceManagerCinfo.m_enableSpeechGraphics = getContext().m_config->m_enableSpeechGraphics;
        }
        getContext().m_connectToRegionStateMachine.requestNextState<ClientApplication::ConnectingToRegionAndVoiceServers>(userName,
                                                                                                                          accountId,
                                                                                                                          personaId,
                                                                                                                          response.m_instanceID,
                                                                                                                          response.m_regionServerAddress.m_sharedSecret,
                                                                                                                          regionServerUrl.toAddress().asRead(),
                                                                                                                          response.m_voiceServerAddress.m_sharedSecret,
                                                                                                                          voiceServerUrl.toAddress().asRead(),
                                                                                                                          voiceManagerCinfo,
                                                                                                                          response.m_avatarTextureMax);
    }
    else
    {
        getContext().setLoadingPhase(ClientApplication::LoadingPhase::cNone);

        LLCore::LogError("ClientApplication", "Unable to connect to region");
        LLCore::LogError("ClientApplication", "Result: ", response.getServiceResultString(), ", Error Code: ", response.getErrorCode(), ", Message: ", response.getErrorMessage());

        LLCore::String errorMessage;

        if (response.getServiceResult() == ClientHttp::Dep::ServiceResult::cInternalServerError) // All instances are full and we can't make another
        {
            errorMessage = "Could not visit this world because all instances are full.";
        }
        else if (response.getServiceResult() == ClientHttp::Dep::ServiceResult::cServiceUnavailable) // If you ask for a specific instance and it is full
        {
            errorMessage = "Could not visit this world because the specified instance is full.";
        }
        else if (response.getServiceResult() == ClientHttp::Dep::ServiceResult::cNotFound) // specified instance was not found
        {
            errorMessage = "Could not visit this world because the specified instance was not found.";
        }
        else if (response.getServiceResult() == ClientHttp::Dep::ServiceResult::cConflict) // specified instance is no longer running
        {
            errorMessage = "Could not visit this world because the specified instance was not found.";
        }
        else if (response.getServiceResult() == ClientHttp::Dep::ServiceResult::cForbidden) // connection to experience was denied by region conductor
        {
            errorMessage = "Sorry, this world instance is full. Please try visiting again.";
        }
        else
        {
            errorMessage = LLCore::Format("Could not visit world due to the error: %s", response.getErrorMessage());
        }

        getContext().requestStateDeferred(ApplicationStateId::cHomeSpace);

        // Posting deferred event, needs to be handled after the above state change.
        getContext().getEventQueue().postDeferredEvent(ClientServices::ShowVisitErrorCommand(getContext().m_currentExperienceUri,
                                                                                             ""_ll,
                                                                                             ClientServices::VisitExperienceError::cFailedUnknown,
                                                                                             errorMessage));
    }
}

ClientApplication::ConnectingToRegionAndVoiceServers::ConnectingToRegionAndVoiceServers(LLCore::StringRef          userName,
                                                                                        const LLCore::String&      accountId,
                                                                                        const LLCore::Uuid&        personaId,
                                                                                        const LLCore::Uuid&        instanceID,
                                                                                        const uint32               sharedRegionSecret,
                                                                                        LLCore::StringRef          serverAddress,
                                                                                        const uint32               sharedVoiceSecret,
                                                                                        LLCore::StringRef          voiceServerAddress,
                                                                                        const VoiceManager::Cinfo& voiceManagerCinfo,
                                                                                        uint32                     characterTextureMemoryBudget)
    : m_userManager(&getContext(),
                    &getContext().m_inputBindingManager,
                    &getContext().getEventQueue(),
                    getContext().getUIEventQueue(),
                    getContext().m_accountConnector != nullptr,
                    getContext().m_config->m_networking.m_useV4Socket,
                    LLCore::Duration::FromMilliseconds(getContext().m_config->m_networking.m_latencyTesting.m_simulateLatencyMs),
                    getContext().m_config->m_networking.m_latencyTesting.m_packetLatencyStandardDeviation,
                    getContext().m_config->m_networking.m_useTcp)
    , m_voiceManager(voiceManagerCinfo)
    , m_speechGraphicsImplementation(getContext().m_config->m_enableSpeechGraphics, getContext().m_streamRouter)
    , m_loadingRegionStateMachine(this)
{
    LLCore::LogInfo("State", "Entering ConnectingToRegionAndVoiceServers");
    getContext().setLoadingPhase(LoadingPhase::cWaitingForRegionConnection);

    LLCore::StringFixed<64> instanceString(instanceID);

    getContext().m_currentRegionInstanceID = instanceID;

    for (LLCore::StringRef string : getContext().m_config->m_script.m_commandWhitelist)
    {
        m_userManager.addScriptCommandWhitelist(string);
    }

    getContext().m_assetManager->getStatistics(&getContext().m_loadStatisticsStart);

    getContext().ensureHomeSpace(HomeSpaceScene::cLoadingScreen);

    RegionCommon::WorldStateManagerCinfo worldStateManagerCinfo;
    {
        if (getContext().rendererRunning())
        {
            worldStateManagerCinfo.m_renderWorld.m_allocator                 = &getContext().m_graphicsAllocator;
            worldStateManagerCinfo.m_renderWorld.m_graphicsSystem            = &getContext().m_graphicsSystem;
            worldStateManagerCinfo.m_renderWorld.m_graphicsConfig            = getContext().m_config->m_graphics;
            worldStateManagerCinfo.m_renderWorld.m_graphicsSceneConfig       = getContext().m_config->m_graphics.m_scene;
            worldStateManagerCinfo.m_renderWorld.m_initialRenderPipelineType = getContext().getCurrentRenderPipelineType();

            uint32 configSpecifiedTextureBudget = worldStateManagerCinfo.m_renderWorld.m_graphicsSceneConfig.m_characterTextureMemoryBudget;

            if (configSpecifiedTextureBudget == 0)
            {
                // further constrained by region conductor
                worldStateManagerCinfo.m_renderWorld.m_graphicsSceneConfig.m_characterTextureMemoryBudget = characterTextureMemoryBudget;
            }
            else if (characterTextureMemoryBudget != 0)
            {
                // neither local or region conductor config is set to 0 (unlimited), so find minimum and use that
                worldStateManagerCinfo.m_renderWorld.m_graphicsSceneConfig.m_characterTextureMemoryBudget = LLCore::Min(worldStateManagerCinfo.m_renderWorld.m_graphicsSceneConfig.m_characterTextureMemoryBudget, characterTextureMemoryBudget);
            }

            worldStateManagerCinfo.m_renderWorld.m_materialTypeDescriptorCatalog = getContext().getMaterialTypeDescriptorCatalog();
            worldStateManagerCinfo.m_renderWorld.m_textureStreamingManager       = getContext().getTextureStreamingManager();
        }

        worldStateManagerCinfo.m_createAudioWorld               = getContext().m_config->m_enableAudio;
        worldStateManagerCinfo.m_streamRouter                   = getContext().m_streamRouter;
        worldStateManagerCinfo.m_speechGraphicsInterface        = getContext().m_config->m_enableSpeechGraphics ? &m_speechGraphicsImplementation.asBase() : nullptr;
        worldStateManagerCinfo.m_runtimeCommonAssetManager      = getContext().m_runtimeCommonAssetManager;
        worldStateManagerCinfo.m_resourceStoreManager           = getContext().m_resourceStoreManager;
        worldStateManagerCinfo.m_resourceLoader                 = getContext().m_engineResourceLoader;
        worldStateManagerCinfo.m_variant                        = Engine::ClientServerVariant::cCSVariantPcClient;
        worldStateManagerCinfo.m_applicationEventQueue_DoNotUse = &getContext().getEventQueue();
        worldStateManagerCinfo.m_enableVdb                      = getContext().m_config->m_vdb.m_enable;
        worldStateManagerCinfo.m_vdbPort                        = EngineSimulation::VisualDebugger::cClientPort;
        worldStateManagerCinfo.m_mediaThread                    = getContext().m_mediaThread;
        worldStateManagerCinfo.m_sourceIdSpace                  = RegionCommon::SourceIdSpace::cClientSpace;
        worldStateManagerCinfo.m_enableTypingIndicators         = getContext().m_config->m_social.m_enableTypingIndicator;
        worldStateManagerCinfo.m_enableVoiceIndicators          = getContext().m_config->m_social.m_enableVoiceIndicator;
        worldStateManagerCinfo.m_uiAudioConfigManager           = getContext().m_clientAudio ? getContext().m_clientAudio->getConfigManager() : nullptr;
        worldStateManagerCinfo.m_outgoingChannel                = &m_userManager.getClientRegionChannel();
    }

    getContext().m_worldStateManager = new RegionCommon::WorldStateManager(worldStateManagerCinfo);

    RegionCommon::NotifyWorldCreated::AddCallbackWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), this, {this, &ClientApplication::ConnectingToRegionAndVoiceServers::onCreateWorld});
    RegionCommon::NotifyWorldDestroyed::AddCallbackWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), this, {this, &ClientApplication::ConnectingToRegionAndVoiceServers::onDestroyWorld});
    RegionCommon::NotifyWorldFailed::AddCallbackWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), this, {this, &ClientApplication::ConnectingToRegionAndVoiceServers::onWorldCreateFailed});

    RegionCommon::NotifyObjectInteractionUpdate::AddCallbackWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext(), {&getContext(), &ClientApplication::onObjectInteractUpdateEvent});
    RegionCommon::NotifyObjectInteractionPromptUpdate::AddCallbackWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext(), {&getContext(), &ClientApplication::onObjectInteractPromptUpdateEvent});
    RegionCommon::NotifyObjectInteractionCreate::AddCallbackWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext(), {&getContext(), &ClientApplication::onObjectInteractCreateEvent});

    RegionCommon::NotifyRuntimeInventorySettingsUpdated::AddCallbackWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext(), {&getContext(), &ClientApplication::onRuntimeInventorySettingsUpdated});

    m_voiceManager.setWorldStateManager(getContext().m_worldStateManager);
    getContext().getEventQueue().addCallbackWithOwner(this, [this](const RegionServerDisconnectedEvent& event) { getContext().queueReconnectNextFrame(); });
    getContext().getEventQueue().addCallbackWithOwner(this, [this](const VoiceServerDisconnectedEvent& event) { getContext().queueReconnectNextFrame(); });
    getContext().getEventQueue().addCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::handleVoiceRequestStatus);
    getContext().getEventQueue().addCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::onVrStateChanged);
    getContext().getEventQueue().addCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::handlePortalEventRequest);
    getContext().getEventQueue().addCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::onRefreshAudioDevicesEvent);
    getContext().getEventQueue().addCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::onUserTypingEvent);
    getContext().getEventQueue().addCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::onFriendStatusByPersonaIdUpdateEvent);
    getContext().getEventQueue().addCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::onRegionWorldDefinitionResponse);
    getContext().getEventQueue().addCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::handleUserLoggedIntoRegion);

    getContext().m_chatManager->enterRegionChat(instanceString, getContext().m_requestedExperienceUri);

    m_userManager.connectToRegion(userName, sharedRegionSecret, serverAddress, getContext().m_config->m_connection.m_serverConnectTimeout);

    if (getContext().m_config->m_enableAudio)
    {
        EngineAudio::AudioSystem::GetInstance().enableSceneSounds(false);
        if (getContext().m_vrSystem)
        {
            m_voiceManager.setVrMode(getContext().isVrActive());
        }
    }

    if (!getContext().m_usingLocalRegionConductor || 0 != sharedVoiceSecret)
    {
        m_voiceManager.connectToServer(voiceServerAddress, sharedVoiceSecret, instanceID);
    }
    if (getContext().m_accountConnector)
    {
        getContext().m_accountConnector->enterRegion(instanceString);
    }

    m_voiceManager.setChatHandler([this](const LLCore::String& s) {
        Chat::SystemChatReceived localMsg;

        localMsg.m_senderPersonaId = Identity::cVoicePersonaId;
        localMsg.m_messageText     = s;

        getContext().getEventQueue().postEvent(localMsg);
    });

    getContext().m_lacmMessageHandler = new Engine::LocalAgentMessageHandler(&m_userManager.getClientRegionChannel());

    getContext().clearScriptConsole();

    RegionCommon::NotifyAgentControllerCreated::AddCallbackWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext(), {&getContext(), &ClientApplication::onAgentControllerCreated});

    // bind messages the client application handles directly itself to the channel
    m_userManager.getClientRegionChannel().bindCallbackWithOwner(this, &getContext(), &ClientApplication::handleSetAgentController);
    m_userManager.getClientRegionChannel().bindCallbackWithOwner(this, &getContext(), &ClientApplication::handleClientKickNotification);
    m_userManager.getClientRegionChannel().bindCallbackWithOwner(this, &getContext(), &ClientApplication::handleClientSmiteNotification);
    m_userManager.getClientRegionChannel().bindCallbackWithOwner(this, &getContext(), &ClientApplication::handleClientRuntimeInventoryUpdatedNotification);
    m_userManager.getClientRegionChannel().bindCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::handleClientMuteNotification);
    m_userManager.getClientRegionChannel().bindCallbackWithOwner(this, [this](const ClientRegionMessages::InitialChunkSubscribed* packet) { m_initialChunkSubscribed = true; });
    m_userManager.getClientRegionChannel().bindCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::handleClientVoiceBroadcastStartNotification);
    m_userManager.getClientRegionChannel().bindCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::handleClientVoiceBroadcastStopNotification);
    m_userManager.getClientRegionChannel().bindCallbackWithOwner(this, this, &ConnectingToRegionAndVoiceServers::handleClientSetRegionBroadcasted);

    m_toggleMicBinding = getContext().m_inputBindingManager.setCallback("toggleMicrophone"_ll, [this](const LLInput::InputTriggeredEvent&) {
        m_voiceManager.toggleStreamActivation();
    });

    m_pushToTalkBinding = getContext().m_inputBindingManager.setCallback("pushToTalk"_ll, [this](const LLInput::InputEdgeEvent& event) {
        m_voiceManager.setStreamActivation(event.m_isTriggered);
    });
}

void ClientApplication::ConnectingToRegionAndVoiceServers::handleUserLoggedIntoRegion(const UserLoggedIntoRegionEvent& event)
{
    if (event.m_success)
    {
        getContext().setLoadingPhase(ClientApplication::LoadingPhase::cWaitingForRegionLoad);

        LLCore::LogInfo("ClientApplication", "User logged into region");
        getContext().m_latestClientVersionService->setRegionServerVersion(event.m_regionServerVersion);

        getContext().m_relationshipManager->setCurrentUserBlockable(allowBlocking());
    }
    else
    {
        getContext().setLoadingPhase(ClientApplication::LoadingPhase::cNone);

        LLCore::LogError("ClientApplication", "User failed to log into region");

        getContext().requestStateDeferred(ApplicationStateId::cHomeSpace);
    }
}

void ClientApplication::ConnectingToRegionAndVoiceServers::onRegionWorldDefinitionResponse(const ClientServices::RegionWorldDefinitionResponse& response)
{
    if (response.isOk())
    {
        LLResource::ResourceId wdi = LLResource::ResourceId(response.m_worldDefinitionID);
        LLCore::LogInfo("ClientApplication", "WorldDefinition hint: ", wdi);
        hintWorldDefinitionLoad(wdi);
    }
    else
    {
        LLCore::LogError("ClientApplication", "Unable to get WorldDefinition hint");
        LLCore::LogError("ClientApplication", "Result: ", response.getServiceResultString(), ", Error Code: ", response.getErrorCode(), ", Message: ", response.getErrorMessage());
    }
}


void ClientApplication::ConnectingToRegionAndVoiceServers::onCreateWorld(RegionCommon::WorldStateManager* wsm)
{
    LLCore::LogInfo("ClientApplication", "onCreateWorld");

    getContext().setLoadingPhase(LoadingPhase::cWaitingForDynamicObjects);
    m_initialChunkSubscribed = false;
    m_userManager.notifyClientStaticReady();
    m_waitForWorldReadyTimeout = LLCore::PrecisionTime::Now() + getContext().m_config->m_asset.m_dynamicObjectDownloadTimeout;

    // At this point, we have the chunk loaded, so we no longer need to hold onto the hint-futures that were keeping those resources alive from the hint-load
    hintWorldDefinitionLoadClear();
}

void ClientApplication::ConnectingToRegionAndVoiceServers::onWorldCreateFailed(RegionCommon::WorldStateManager* wsm, RegionCommon::WorldStateManagerFailureState failureState)
{
    LLCore::LogInfo("ClientApplication", "onWorldCreateFailed");

    // LLCORE_ASSERT_ALWAYS(false, "Could not load world definition or world chunk definition, terminating");

    getContext().setLoadingPhase(LoadingPhase::cNone); // all done

    // we need to defer this destruction from occurring now, because the destructed objects are on the callstack above us and holding mutex guards
    getContext().getEventQueue().postFunction([this, failureState]() -> void {
        getContext().m_clientStateMachine.switchToAtlas();

        LLCore::String  customErrorMessage;
        LLCore::String  subErrorMessage;
        LLHttp::HttpUrl errorLinkUrl;
        LLCore::String  errorLinkText;
        if (failureState == RegionCommon::WorldStateManagerFailureState::cFailedToLoadChunks)
        {
            customErrorMessage = "Error Loading World:\nPlease check your internet connection and ensure that any installed anti-virus software has an exception for Sansar.\n"_ll;
            subErrorMessage    = "If this problem persists, please contact us"_ll;
            if (getContext().m_webFrontend != nullptr)
            {
                getContext().m_webFrontend->getWebFrontendUrl(ClientServices::OpenWebFrontendUrlId::cReportIssue, &errorLinkUrl);
                errorLinkText = "Sansar Support"_ll;
            }
        }

        // Defer until next frame to allow switch to homespace
        getContext().getEventQueue().postDeferredEvent(ClientServices::ShowVisitErrorCommand(getContext().m_currentExperienceUri,
                                                                                             ""_ll,
                                                                                             ClientServices::VisitExperienceError::cFailedLoadError,
                                                                                             customErrorMessage,
                                                                                             subErrorMessage,
                                                                                             errorLinkUrl,
                                                                                             errorLinkText));
    });
}

void ClientApplication::ConnectingToRegionAndVoiceServers::onVrStateChanged(const ClientVr::VrStateChangedEvent& event)
{
    const bool isEnteringVr = (event.m_state == Engine::VrState::cActive);
    if (getContext().m_config->m_enableAudio)
    {
        m_voiceManager.setVrMode(isEnteringVr);
    }
}

void ClientApplication::ConnectingToRegionAndVoiceServers::handleClientMuteNotification(const ClientRegionMessages::ClientMuteNotification* packet)
{
    LLCore::LogInfo("ClientApplication", "ClientMuteNotification received: ", packet->m_message);

    Chat::SystemChatReceived localMsg;
    localMsg.m_senderPersonaId = Identity::cSystemPersonaId;
    localMsg.m_messageText     = packet->m_message;
    getContext().getEventQueue().postEvent(localMsg);

    getContext().postUIEvent(ClientServices::UserMutedEvent(packet->m_message));
    getContext().postUIEvent<ClientServices::VoiceNotifyMuteChanged>(true);

    m_voiceManager.setMuted(true);
}

void ClientApplication::ConnectingToRegionAndVoiceServers::handleClientVoiceBroadcastStartNotification(const ClientRegionMessages::ClientVoiceBroadcastStartNotification* packet)
{
    LLCore::LogInfo("ClientApplication", "ClientVoiceBroadcastStartNotification received: ", packet->m_message);

    Chat::SystemChatReceived localMsg;
    localMsg.m_senderPersonaId = Identity::cSystemPersonaId;
    localMsg.m_messageText     = packet->m_message;
    getContext().getEventQueue().postEvent(localMsg);

    m_voiceManager.setVoiceBroadcastStart();

    getContext().postUIEvent(ClientServices::VoiceBroadcastStartEvent(packet->m_message));
}

void ClientApplication::ConnectingToRegionAndVoiceServers::handleClientVoiceBroadcastStopNotification(const ClientRegionMessages::ClientVoiceBroadcastStopNotification* packet)
{
    LLCore::LogInfo("ClientApplication", "ClientVoiceBroadcastStartNotification received: ", packet->m_message);

    Chat::SystemChatReceived localMsg;
    localMsg.m_senderPersonaId = Identity::cSystemPersonaId;
    localMsg.m_messageText     = packet->m_message;
    getContext().getEventQueue().postEvent(localMsg);

    m_voiceManager.setVoiceBroadcastStop();

    getContext().postUIEvent(ClientServices::VoiceBroadcastStopEvent(packet->m_message));
}

void ClientApplication::ConnectingToRegionAndVoiceServers::handleClientSetRegionBroadcasted(const ClientRegionMessages::ClientSetRegionBroadcasted* packet)
{
    m_voiceManager.setRegionBroadcasted(packet->m_broadcasted);
}

void ClientApplication::ConnectingToRegionAndVoiceServers::onUserTypingEvent(const Chat::UserTypingEvent& event)
{
    if (getContext().getWorldStateManager())
    {
        getContext().getWorldStateManager()->handleUserTyping(event.m_personaId, event.m_typing);
    }
}
void ClientApplication::ConnectingToRegionAndVoiceServers::onDestroyWorld(RegionCommon::WorldStateManager* wsm)
{
    LLCore::LogInfo("ClientApplication", "onDestroyWorld");

    // this call is perfectly balanced with onCreateWorld call (which is only called on successful world loaded and ready)
    // so undo whatever onCreateWorld does here.

    getContext().clearSceneMetrics();
    if (getContext().m_worldStateManager)
    {
        getContext().m_worldStateManager->setLocalAgentControllerManager(nullptr);
    }

    if (getContext().m_lacm)
    {
        getContext().m_lastControlMode = getContext().m_lacm->getControlMode();
    }

    m_userManager.setLacm(nullptr);
    m_userManager.setInWorld(false);

    getContext().m_userCamera.suspendComponent();
    getContext().destroyLacm();

    if (getContext().m_flyCameraBehavior != nullptr)
    {
        getContext().getEventQueue().postEvent<Engine::EngineSettingsChanged>(Engine::EngineSetting::cFlyCameraMoveSpeed, getContext().m_flyCameraBehavior->getMoveSpeedScaleCounter());
    }

    if (getContext().m_config->m_enableAudio)
    {
        EngineAudio::AudioSystem::GetInstance().enableSceneSounds(true);
    }

    getContext().m_cachedIsMicMutedState = m_voiceManager.isMuted();
    m_voiceManager.clearLocalAgent();

    delete getContext().m_vrVisualizationLayer;
    getContext().m_vrVisualizationLayer = nullptr;
}

void ClientApplication::ConnectingToRegionAndVoiceServers::handlePortalEventRequest(const ClientServices::PortalEventRequest& request)
{
    if (getContext().m_clientStateMachine.getCurrentState() == ApplicationStateId::cInWorld)
    {
        if (request.m_experienceUri.isValid())
        {
            ClientRegionMessages::RequestDropPortal portalMessage;
            portalMessage.m_sansarUriDescription = request.m_experienceDescription;

            switch (AppCore::SansarExperienceUri::FindUriType(request.m_experienceUri.getUrl()))
            {
                case AppCore::SansarUriBase::UriType::cExperience:
                {
                    LLHttp::HttpUrl baseUrl;
                    getContext().m_apiLocatorService->getApiUrl(&baseUrl, "webapi"_ll, "atlas-frontend"_ll, 1);
                    request.m_experienceUri.toWebUrl(baseUrl.toString(), &portalMessage.m_sansarUri);

                    break;
                }

                case AppCore::SansarUriBase::UriType::cEvent:
                {
                    portalMessage.m_sansarUri = LLCore::ConvertType<LLCore::String>(request.m_experienceUri);
                    break;
                }
                    LLCORE_NO_DEFAULT_CASE();
            }

            m_userManager.getClientRegionChannel().sendMessage(portalMessage);
            LLMetrics::RecordMetric(ClientServices::CodexActionMetricEvent(request.m_codexActionContext));
        }
        else
        {
            LLCore::LogError("ClientApplication", "Error parsing experience uri");
        }
    }
}

void ClientApplication::ConnectingToRegionAndVoiceServers::handleVoiceRequestStatus(const ClientServices::VoiceRequestStatus& event)
{
    ClientServices::VoiceRequestStatus::Response response;

    response.m_isMicMuted          = m_isMicMuted;
    response.m_isMicMutedFromAdmin = m_isMicMutedFromAdmin;
    response.m_isMicAvailable      = m_voiceManager.isAvailable();

    getContext().getEventQueue().postEvent(response);
    event.getResponseQueue()->postEvent(response);
}

void ClientApplication::ConnectingToRegionAndVoiceServers::onFriendStatusByPersonaIdUpdateEvent(const ClientServices::FriendStatusByPersonaIdUpdateEvent& event)
{
    //during an experience, if a user status changes, update it

    // todo: we should not blindly send this
    // we should do this on a transition instead
    if (event.isBlocked())
    {
        getContext().blockUser(event.m_personaId);
    }
    else
    {
        getContext().unblockUser(event.m_personaId);
    }

    if (event.isBlockingUnblockableUser())
    {
        const Client::User* user = m_userManager.findUserByPersonaId(event.m_personaId);
        if (user != nullptr)
        {
            LLCore::StringFixed<64> blockNotification;
            blockNotification.appendFormat("%s (@%s) is attempting to block you."_ll, user->getPersonaName(), user->getPersonaHandle());
            getContext().postUIEvent<ClientServices::LocalAlertEvent>(blockNotification);
        }
    }
}

void ClientApplication::ConnectingToRegionAndVoiceServers::onRefreshAudioDevicesEvent(const ClientAudio::RefreshAudioDevicesEvent& event)
{
    m_voiceManager.setVrMode(getContext().isVrActive());
}

bool ClientApplication::ConnectingToRegionAndVoiceServers::allowFreeCamera()
{
    return m_userManager.hasPrivilege("AllowFreeCamera"_ll);
}

bool ClientApplication::ConnectingToRegionAndVoiceServers::enableTpToFreeCamera()
{
    return m_userManager.hasPrivilege("EnableTpToFreeCamera"_ll);
}

bool ClientApplication::ConnectingToRegionAndVoiceServers::allowBlocking()
{
    return !m_userManager.hasPrivilege("Unblockable"_ll);
}

void ClientApplication::ConnectingToRegionAndVoiceServers::enteredInWorld()
{
    m_userManager.setInWorld(true);
    if (m_voiceManager.isAvailable())
    {
        m_voiceManager.syncCachedMuteState(getContext().m_cachedIsMicMutedState);
    }

    if (getContext().m_config->m_enableAudio)
    {
        EngineAudio::AudioSystem::GetInstance().enableSceneSounds(true);
    }
}

void ClientApplication::ConnectingToRegionAndVoiceServers::avatarLoadingFinished()
{
    Engine::AgentControllerManager* acm = getContext().m_worldStateManager->getAgentController(getContext().m_localAgentId);
    m_voiceManager.setLocalAgent(acm, getContext().m_localAgentId);
    m_userManager.setLacm(getContext().m_lacm);
    getContext().setLoadingPhase(LoadingPhase::cLoaded);
}

void ClientApplication::ConnectingToRegionAndVoiceServers::giveTime()
{
    if (m_initialChunkSubscribed && !m_waitForWorldReadyTimeout.isNull())
    {
        if (getContext().m_worldStateManager->getPendingClusterCount() == 0 || LLCore::PrecisionTime::Now() > m_waitForWorldReadyTimeout)
        {
            LLPROFILE_AUTO_CPU_MARKER_STATIC("Wait For World Ready");
            m_waitForWorldReadyTimeout.setNull();

            m_userManager.notifyClientDynamicReady();
            m_initialChunkSubscribed = false;
            getContext().setLoadingPhase(LoadingPhase::cWaitingForSelfAvatar);
        }
    }

    m_userManager.giveTime();
    m_voiceManager.setLocalAudioPosition();
    m_voiceManager.giveTime();

    if (m_voiceManager.isMuted() != m_isMicMuted)
    {
        m_isMicMuted = m_voiceManager.isMuted();
        getContext().postUIEvent<ClientServices::VoiceNotifyMuteChanged>(m_isMicMuted);
    }

    if (m_voiceManager.isAdminMuted() != m_isMicMutedFromAdmin)
    {
        m_isMicMutedFromAdmin = m_voiceManager.isAdminMuted();
        getContext().postUIEvent<ClientServices::VoiceNotifyAdminMuteChanged>(m_isMicMutedFromAdmin);

        // only send the notification if we don't have the megaphone. User gets a separate notification.
        if (!m_voiceManager.isVoiceBroadcast())
        {
            LLCore::StringFixed<64> notification;
            notification.appendFormat(m_isMicMutedFromAdmin ? "A moderator has muted the audience. Your mic is now muted."_ll : "A moderator has unmuted the audience. Your mic has returned to being %s."_ll,
                                      (m_isMicMuted ? "muted"_ll : "unmuted"_ll));
            getContext().postUIEvent(ClientServices::UserMutedEvent(notification, m_isMicMutedFromAdmin));
        }
    }

    m_loadingRegionStateMachine.giveTime();
}

void ClientApplication::ConnectingToRegionAndVoiceServers::hintWorldDefinitionLoad(LLResource::ResourceId worldDefinitionId)
{
    hintWorldDefinitionLoadClear();

    m_hintWorldDefinitionFuture = getContext().m_engineResourceLoader->loadResource<Engine::WorldDefinition>(worldDefinitionId, LLAssetSystem::AssetReader::Priority::cNormal);

    m_hintWorldDefinitionFuture.setCallback([this](const LLCore::Future<LLResource::ResourceLoader::Result<Engine::WorldDefinition>>* signaledFuture) -> void {
        const Engine::WorldDefinition* worldDef = signaledFuture->getValue() ? signaledFuture->getValue()->getHandle().getResource() : nullptr;
        if (worldDef != nullptr)
        {
            m_hintWorldChunkFuture = getContext().m_engineResourceLoader->loadResource<Engine::WorldChunkDefinition>(worldDef->m_worldChunks.at(0), LLAssetSystem::AssetReader::Priority::cNormal);
        }
    });
}
// users loaded after current user is done loading into an experience, handle them being blocked
void ClientApplication::ConnectingToRegionAndVoiceServers::hintWorldDefinitionLoadClear()
{
    m_hintWorldDefinitionFuture.reset();
    m_hintWorldChunkFuture.reset();
}

ClientApplication::ConnectingToRegionAndVoiceServers::~ConnectingToRegionAndVoiceServers()
{
    LLCore::LogInfo("State", "Leaving ConnectingToRegionAndVoiceServers");

    getContext().getEventQueue().removeCallbacksWithOwner(this);


    if (EngineRender::TextureStreamingManager* textureStreamingManager = getContext().BaseType::getTextureStreamingManager())
    {
        textureStreamingManager->suspendLoadRequestPrioritization();
    }

    getContext().getEventQueue().removeCallbacksWithOwner(this);

    m_userManager.getClientRegionChannel().unbindCallbackWithOwner(this);

    hintWorldDefinitionLoadClear(); // make sure the pre-load hint got released

    getContext().m_relationshipManager->setCurrentUserBlockable(true);

    getContext().m_localAgentId = Engine::cInvalidAgentControllerId;
    m_waitForWorldReadyTimeout.setNull();

    if (getContext().m_worldStateManager)
    {
        RegionCommon::NotifyAgentControllerCreated::RemoveCallbacksWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext());
        m_voiceManager.setWorldStateManager(nullptr);

        if (getContext().m_config->m_enableAudio && getContext().rendererRunning())
        {
            EngineAudio::AudioSystem::GetInstance().setDebugDraw(false);
        }

        getContext().m_worldStateManager->prepareToDestruct();
        RegionCommon::NotifyWorldCreated::RemoveCallbacksWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), this);
        RegionCommon::NotifyWorldDestroyed::RemoveCallbacksWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), this);
        RegionCommon::NotifyWorldFailed::RemoveCallbacksWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), this);
        RegionCommon::NotifyObjectInteractionUpdate::RemoveCallbacksWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext());
        RegionCommon::NotifyObjectInteractionPromptUpdate::RemoveCallbacksWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext());
        RegionCommon::NotifyObjectInteractionCreate::RemoveCallbacksWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext());
        RegionCommon::NotifyRuntimeInventorySettingsUpdated::RemoveCallbacksWithOwner(&getContext().m_worldStateManager->getNotificationEventQueue(), &getContext());
        delete getContext().m_worldStateManager;
        getContext().m_worldStateManager = nullptr;
    }

    delete getContext().m_lacmMessageHandler;
    getContext().m_lacmMessageHandler = nullptr;

    getContext().clearScriptConsole();

    m_userManager.disconnectFromRegion();

    LLCORE_ASSERT(getContext().m_chatManager != nullptr, "chat manager is null when attempting to disconnect from region");

    getContext().m_chatManager->clearRegionChat();
    getContext().m_inputBindingManager.removeCallback(m_toggleMicBinding);
    getContext().m_inputBindingManager.removeCallback(m_pushToTalkBinding);

    m_voiceManager.clearChatHandler();
    m_voiceManager.disconnectFromServer();
    m_voiceManager.notifyRegionDisconnected();

    if (getContext().m_accountConnector)
    {
        getContext().m_accountConnector->leaveRegion();
    }
    getContext().m_latestClientVersionService->setRegionServerVersion("Not Connected"_ll);
}

LLAPPLICATION_REGISTER_APPLICATION(ClientApplication, "SansarClient", APPCORE_SANSAR_VERSION_STRING);
LLAPPLICATION_SET_DEFAULT_APPLICATION_NAME("SansarClient");


} // namespace Client
