/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#include "SimulationWorld.h"

#include "BehaviorAttachment.h"
#include "BodyCollisionFilter.h"
#include "FrameSynchronizer.h"
#include "HavokAssetUtil.h"
#include "Picking.h"
#include "SimulationEvents.h"
#include "SourceSpaceCollisionFilter.h"
#include "VisualDebugger.h"

#include "LLCore/UsingMath.h"
#include "LLGems/DebugDisplayManager.h"
#include "LLHavok/HavokUtil.inl"
#include "LLHavok/MathUtils.h"
#include "LLProfile/Profile.h"

#include "AppCore/MathConventions.h"

// For Stats
#include "LLJson/Json.h"

// LLResource
#include "LLResource/Resource.h"

// Components
#include "AnimationComponentManager.h"
#include "CharacterComponentManager.h"
#include "IKBodyComponentManager.h"
#include "KeyframedBoneComponentManager.h"
#include "PhysicsConstraintComponentManager.h"
#include "PoseComponentManager.h"
#include "RigidBodyComponentManager.h"

#if defined(LLCORE_DEBUG) && 0
#define ENGINESIMULATION_DEBUG_DRAW_CAMERACAST
#endif

#if defined(LLCORE_DEBUG) && 0
#define ENGINESIMULATION_DEBUG_DRAW_RAYCASTS
#endif

HAVOK_DISABLE_WARNINGS();

#include "PhysicsCharacterInterface.h"

// Multithreading
#include "Havok/Source/Behavior/Behavior/Multithreaded/hkbBehaviorJobQueueUtils.h"
#include "Havok/Source/Common/Base/Container/Array/hkArray.h"
#include "Havok/Source/Common/Base/System/hkBaseSystem.h"
#include "Havok/Source/Common/Base/Thread/JobQueue/hkJobQueue.h"
#include "Havok/Source/Common/Base/Thread/Pool/hkCpuThreadPool.h"
#include "Havok/Source/Common/Base/Thread/TaskQueue/Default/hkDefaultTaskQueue.h"

// Stepping
#include "Havok/Source/Common/Base/Container/BlockStream/Allocator/Fixed/hkFixedBlockStreamAllocator.h"

// Behavior
#include "Havok/Source/Behavior/Behavior/Character/hkbCharacter.h"
#include "Havok/Source/Behavior/Behavior/Linker/hkbSymbolLinker.h"
#include "Havok/Source/Behavior/Behavior/Utils/hkbGenerateUtils.h"
#include "Havok/Source/Behavior/Behavior/Utils/hkbSceneModifierUtils.h"
#include "Havok/Source/Behavior/Behavior/World/hkbWorld.h"
#include "Havok/Source/Behavior/Behavior/World/hkbWorldCinfo.h"
#include "Havok/Source/Behavior/PhysicsBridge/hkbnpPhysicsInterface.h"

// Physics
#include "Havok/Source/Physics/Physics/Collide/BroadPhase/hknpBroadPhase.h"
#include "Havok/Source/Physics/Physics/Collide/Filter/Constraint/hknpConstraintCollisionFilter.h"
#include "Havok/Source/Physics/Physics/Collide/Shape/Convex/Capsule/hknpCapsuleShape.h"
#include "Havok/Source/Physics/Physics/Dynamics/World/hknpWorld.h"
#include "Physics/Physics/Collide/Filter/Group/hknpGroupCollisionFilter.h"
#include "Physics/Physics/Collide/Query/Collector/hknpAnyHitCollector.h"
#include "Physics/Physics/Collide/Query/Collector/hknpClosestHitCollector.h"
#include "Physics/Physics/Collide/Query/hknpCollisionQuery.h"
#include "Physics/Physics/Collide/Shape/Convex/Sphere/hknpSphereShape.h"
#include "Physics/Physics/Dynamics/World/Events/hknpEventDispatcher.h"
#include "Physics/Physics/Dynamics/World/hknpStepInput.h"
#include "Physics/Physics/Dynamics/World/hknpWorldCinfo.h"

#ifdef ENGINESIMULATION_DEBUG_DRAW_CAMERACAST
#include "Havok/Source/Physics/Physics/Collide/Shape/hknpShapeUtil.h"
#endif

HAVOK_RESTORE_WARNINGS();

// Communications
#include "LLClientServer/Channel.h"
#include "LLCore/EventQueue.h"

// Debug draw
#include "Common/Base/Types/Color/hkColor.h"
#include "Common/Visualize/hkDebugDisplay.h"

// LLProfile headers
#include "LLProfile/PerformanceStatsManager.h"

#include "AppCore/MathConventions.h"

//
//
// this space intentionally left blank (stupid registration macros)
//
//
LLGEMS_DECLARE_DEBUG_LAYER(PhysicsLayer, SimulationGroup);

namespace EngineSimulation
{
LLPROFILE_STAT_DECLARE(EngineSimulation, WorldStep, LLProfile::PerformanceStat::cTimeValueSeconds, 0.010f);

constexpr float SimulationWorld::cDefaultGravityMagnitude;

class UpdateKeyframedObjectsModifier : public hkbSceneModifier
{
    friend SimulationWorld;

public:
    HK_DECLARE_CLASS_ALLOCATOR(HK_MEMORY_CLASS_BEHAVIOR_RUNTIME);

    UpdateKeyframedObjectsModifier(SimulationWorld* simulationWorld)
        : m_simulationWorld(simulationWorld)
        , m_keyframedBoneManager(simulationWorld->getKeyframedBoneManager())
        , m_rigidBodyManager(simulationWorld->getRigidBodyManager())

    {
    }

    // A post modify pass that is run after a previously threaded modifier
    virtual bool modifyPostThreaded(hkbSceneCharacters& sceneCharacters, float timestep, hkArray<CharacterThreadingData>& dataArray) override
    {
        modify(sceneCharacters, timestep);
        return true;
    }

    // Called by the hkbWorld
    virtual void modify(hkbSceneCharacters& sceneCharacters, float timestep) override
    {
        LLCORE_ASSERT(sceneCharacters.m_isPhysicsUpdated == false, "Physics has already been updated.  This scene modifier probably needs to be inserted earlier in the hkbWorld's scene modifier array.");

        // For each keyframed object:

        // Get the transform from the pose, apply it to our transform
        m_keyframedBoneManager->update(sceneCharacters);

        // Apply the delta transform to any rigid we might have
        if (m_rigidBodyManager != nullptr)
        {
            m_rigidBodyManager->driveKeyframed(timestep);
        }
        // callback to listeners
        m_simulationWorld->fireSceneCharacterListeners(sceneCharacters, timestep);
    }

    virtual bool modifyPreThreaded(hkbSceneCharacters& sceneCharacters, float timestep) override
    {
        return true;
    }

public:
protected:
    SimulationWorld*               m_simulationWorld;
    KeyframedBoneComponentManager* m_keyframedBoneManager;
    RigidBodyComponentManager*     m_rigidBodyManager;
};

SimulationWorld::SimulationWorld(const Cinfo& cinfo)
    : m_simulationEventQueue(cinfo.m_simulationEventQueue)
    , m_deltaTime(cinfo.m_deltaTime)
    , m_maxBroadPhaseSize(cinfo.m_broadPhaseSize)
    , m_outgoingChannel(cinfo.m_outgoingChannel)
    , m_broadcastEventQueue(cinfo.m_broadcastEventQueue)
    , m_resourceStoreManager(cinfo.m_resourceStoreManager)
    , m_gravityMagnitude(cinfo.m_gravityMagnitude)
{
    // Threading info
    {
        const EngineSimulation::Internal::ThreadingInfo& threadInfo = EngineSimulation::HavokSystem::GetInstance().getThreadingInfo();
        m_taskQueue                                                 = threadInfo.m_taskQueue;
        m_threadPool                                                = threadInfo.m_threadPool;

        // Add references
        m_taskQueue->addReference();
        m_threadPool->addReference();
    }

    // Frame Synchronization
    {
        FrameSynchronizer::Cinfo fsCinfo;
        fsCinfo.m_outgoingChannel = cinfo.m_outgoingChannel;
        fsCinfo.m_eventQueue      = cinfo.m_broadcastEventQueue;
        fsCinfo.m_deltaTime       = cinfo.m_deltaTime.asFloat();
        m_frameSynchronizer       = new FrameSynchronizer(fsCinfo);
    }

    //
    // Create worlds (order is important)
    //
    const hkVector4& hkUp = MathConvert(AppCore::MathConventions::cWorldUp); // If this line crashes, it is probably due to unaligned memory
    if (cinfo.m_creationFlags.isFlagSet(WorldCreationFlags::cCreatePhysicsWorld))
    {
        // Allocator for simulation caches, used for internal collision code and messaging allocs.
        // We may be able to reduce this size once we have more efficient physics shape gen from simplygon.
        m_persistentAllocator = new hkFixedBlockStreamAllocator(64 * 1024 * 1024);

        // Step local allocator
        m_stepLocalAllocator = new hkFixedBlockStreamAllocator(48 * 1024 * 1024);

        // Create a filter to disable collisions between constrained bodies
        // the groupCollisionFilter portion is a hack to prevent ik traces from colliding with character proxies

        SourceSpaceCollisionFilter* groupFilter = m_layerManager.getCollisionFilter();
        m_constraintFilter                      = new hknpConstraintCollisionFilter(groupFilter);
        m_baseFilter                            = new hknpPairCollisionFilter(m_constraintFilter);

        // Enable/Disable Avatar-Avatar Collision. This should only be done before the world is created
        {
            LayerManager::Layer avatarLayer = LayerManager::Layer::CHARACTER_PROXIES;
            m_layerManager.setCollisionBetweenLayersEnabled(avatarLayer, avatarLayer, cinfo.m_enableAvatarAvatarCollision);
        }

        // Create a physics world
        hknpWorldCinfo info;
        {
            LLCore::ThreadTimeoutUntrackedTimeGuard initializeHavokGuard;

            info.setBroadPhaseSize(cinfo.m_broadPhaseSize);
            info.m_gravity = hkUp;
            info.m_gravity.mul(-m_gravityMagnitude);
            info.m_persistentStreamAllocator = m_persistentAllocator;
            info.m_collisionFilter           = m_baseFilter;
            m_physicsWorld                   = new hknpWorld(info);
        }

        if (cinfo.m_creationFlags.isFlagSet(WorldCreationFlags::cCreateAnimationWorld))
        {
            // Interface to Behavior
            WorldBehaviorPhysicsInterfaceCinfo physicsInterfaceInfo;
            {
                physicsInterfaceInfo.m_animationManager         = m_animationManager;
                physicsInterfaceInfo.m_characterManager         = m_characterComponentManager;
                physicsInterfaceInfo.m_stepLocalStreamAllocator = m_stepLocalAllocator;
                physicsInterfaceInfo.m_threadPool               = m_threadPool;
                physicsInterfaceInfo.m_taskQueue                = m_taskQueue;
                physicsInterfaceInfo.m_world                    = m_physicsWorld;
            }
            m_behaviorPhysicsInterface = new WorldBehaviorPhysicsInterface(physicsInterfaceInfo);
        }

        RigidBodyComponentManager::Cinfo rigidBodyManagerInfo;
        {
            rigidBodyManagerInfo.m_deltaTime         = m_deltaTime.asFloat();
            rigidBodyManagerInfo.m_world             = m_physicsWorld;
            rigidBodyManagerInfo.m_outgoingChannel   = cinfo.m_outgoingChannel;
            rigidBodyManagerInfo.m_eventQueue        = cinfo.m_broadcastEventQueue;
            rigidBodyManagerInfo.m_frameSynchronizer = m_frameSynchronizer;
        }
        m_rigidBodyManager = new RigidBodyComponentManager(rigidBodyManagerInfo);

        m_physicsConstraintManager  = new PhysicsConstraintComponentManager(m_physicsWorld, m_constraintFilter);
        m_characterComponentManager = new CharacterComponentManager(m_physicsWorld, cinfo.m_speechGraphicsInterface);


        m_physicsWorld->m_signals.m_preCollide.subscribe(this, &SimulationWorld::onPreCollide, "SimulationWorld preCollide.");
        m_physicsWorld->m_signals.m_postSolve.subscribe(this, &SimulationWorld::onPostSolve, "SimulationWorld postSolve.");

        m_physicsWorld->m_signals.m_bodyBufferFull.subscribe(this, &SimulationWorld::onBodyBufferFullSignal, "SimulationWorld onBodyBufferFullSignal");
        m_physicsWorld->m_signals.m_motionBufferFull.subscribe(this, &SimulationWorld::onMotionBufferFullSignal, "SimulationWorld onMotionBufferFullSignal");

        if (m_simulationEventQueue != nullptr)
        {
            m_physicsWorld->getEventSignal(hknpEventType::BODY_EXITED_BROAD_PHASE).subscribe(this, &SimulationWorld::onBodyExitedBroadphase, "SimulationWorld onBodyExitedBroadphase");
        }
    }

    // Create global symbol linker
    m_globalSymbolLinker = new hkbGlobalSymbolLinker();

    // Asset loading
    m_assetUtil = new HavokAssetUtil(m_resourceStoreManager, *m_globalSymbolLinker, m_behaviorPhysicsInterface);
    m_globalSymbolLinker->removeReference();

    // Visual debugger sends debugging data to VDB Clients
    VisualDebugger::Cinfo visualDebuggerInfo;
    {
        visualDebuggerInfo.m_capturePath = cinfo.m_vdbCapturePath;
        for (const LLCore::String& viewer : cinfo.m_vdbViewers)
        {
            visualDebuggerInfo.m_viewers.add(viewer);
        }
        visualDebuggerInfo.m_createCameraViewer = true;
    }
    m_visualDebugger = new VisualDebugger(visualDebuggerInfo);

    visualDebuggerInfo.m_createCameraViewer = false;

    if (cinfo.m_creationFlags.isFlagSet(WorldCreationFlags::cCreateAnimationWorld))
    {
        // Create Behavior World
        hkbWorldCinfo info;
        {
            info.m_physicsInterface = m_behaviorPhysicsInterface;
            info.m_up               = hkUp;
            m_behaviorWorld         = new hkbWorld(info);
        }


        // Animation manager
        AnimationComponentManager::AnimationComponentManagerCinfo animationManagerInfo;
        {
            animationManagerInfo.m_assetSystem       = m_assetUtil;
            animationManagerInfo.m_networkEventQueue = cinfo.m_broadcastEventQueue;
            animationManagerInfo.m_callbackQueue     = cinfo.m_callbackQueue;
            animationManagerInfo.m_world             = m_behaviorWorld;
            animationManagerInfo.m_frameSynchronizer = m_frameSynchronizer;
            animationManagerInfo.m_physicsInterface  = m_behaviorPhysicsInterface;
            animationManagerInfo.m_frameDeltaTime    = m_deltaTime;
            animationManagerInfo.m_rigidBodyManager  = m_rigidBodyManager;
            animationManagerInfo.m_simulationWorld   = this;

            if (!cinfo.m_allowFootIK)
            {
                animationManagerInfo.m_characterCapabilitiesFilter &= ~hkbCharacter::CapabilityTypes::FOOT_IK;
            }
            animationManagerInfo.m_optimizeSimulatedBones = cinfo.m_optimizeSimulatedBones;
        }
        m_animationManager = new AnimationComponentManager(animationManagerInfo);

        // Setup keyframed bone manager
        m_keyframedBoneManager           = new KeyframedBoneComponentManager();
        m_updateKeyframedObjectsModifier = new UpdateKeyframedObjectsModifier(this);

        // Insert custom scene modifiers into the Behavior World before the attachment modifier
        // scene characters modifier goes after ik modifiers but before physics is stepped
        m_behaviorWorld->m_sceneModifiers.insertAt(6, m_updateKeyframedObjectsModifier);

        m_poseManager = new PoseComponentManager(m_animationManager);

        m_ikBodyComponentManager = new IKBodyComponentManager(m_animationManager, m_physicsWorld);

        // Sets the updated managers on the physics interface
        m_behaviorPhysicsInterface->setManagers(m_animationManager, m_characterComponentManager, m_rigidBodyManager);

        // Bind Callbacks
        bindSceneCharactersCallback({m_animationManager, &AnimationComponentManager::onSceneCharactersUpdated});
    }

    // Ensure consistent linker
    m_behaviorWorld->setGlobalSymbolLinker(m_globalSymbolLinker);

    // Add to VDB
    m_visualDebugger->addWorld(*this);

    // Start the VDB
    if (cinfo.m_enableVdb)
    {
        m_visualDebugger->serve(cinfo.m_vdbPort);
    }
}

SimulationWorld::~SimulationWorld()
{
    delete m_frameSynchronizer;
    delete m_behaviorAttachmentManager;

    if (m_stepLocalAllocator)
    {
        m_stepLocalAllocator->removeReference();
    }

    if (m_persistentAllocator)
    {
        m_persistentAllocator->removeReference();
    }

    if (m_taskQueue)
    {
        m_taskQueue->removeReference();
    }

    if (m_threadPool)
    {
        m_threadPool->removeReference();
    }

    if (m_visualDebugger)
    {
        m_visualDebugger->shutdown();
        delete m_visualDebugger;
    }

    if (m_updateKeyframedObjectsModifier)
    {
        m_updateKeyframedObjectsModifier->removeReference();
    }

    if (m_behaviorWorld)
    {
        m_behaviorWorld->removeReference();
    }

    // Remove the filter first because the destructor deferences the hknpWorld
    // but doesn't ref count it(!)
    if (m_constraintFilter)
    {
        m_constraintFilter->removeReference();
    }
    if (m_baseFilter)
    {
        m_baseFilter->removeReference();
    }

    if (m_physicsWorld)
    {
        // Unsubscribe from signals
        m_physicsWorld->m_signals.m_bodyBufferFull.unsubscribeAll(this);
        m_physicsWorld->m_signals.m_motionBufferFull.unsubscribeAll(this);

        m_physicsWorld->m_signals.m_preCollide.unsubscribeAll(this);
        m_physicsWorld->m_signals.m_postSolve.unsubscribeAll(this);

        m_physicsWorld->removeReference();
    }

    if (m_behaviorPhysicsInterface)
    {
        m_behaviorPhysicsInterface->removeReference();
    }

    delete m_animationManager;
    delete m_keyframedBoneManager;
    delete m_rigidBodyManager;
    delete m_physicsConstraintManager;
    delete m_characterComponentManager;
    delete m_poseManager;
    delete m_ikBodyComponentManager;


    delete m_assetUtil;
}

float SimulationWorld::getGravityMagnitude() const
{
    return m_gravityMagnitude;
}

void SimulationWorld::setGravityMagnitude(float g)
{
    SimulationMessages::SetWorldGravityMagnitude message;
    message.m_frame     = m_frameSynchronizer->getFrame();
    message.m_magnitude = g;

    handleSetGravityMagnitude(&message);
}

void SimulationWorld::processSimulationMessages()
{
    if (m_gravityMagFrameBuffer.getCount() > 0)
    {
        int64                                               closestFrame = m_gravityMagFrameBuffer.findClosestFrameBelow(m_frameSynchronizer->getFrameToProcess());
        const SimulationMessages::SetWorldGravityMagnitude* message      = m_gravityMagFrameBuffer.find(closestFrame);
        if (message != nullptr)
        {
            processSetGravityMagnitude(message);
            m_gravityMagFrameBuffer.removeUpTo(closestFrame);
        }
    }
}

void SimulationWorld::handleSetGravityMagnitude(const SimulationMessages::SetWorldGravityMagnitude* message)
{
    m_gravityMagFrameBuffer.insertOrReplace(message->m_frame, *message);

    if (m_broadcastEventQueue)
    {
        m_broadcastEventQueue->postDeferredEvent(*message);
    }
}

void SimulationWorld::processSetGravityMagnitude(const SimulationMessages::SetWorldGravityMagnitude* message)
{
    m_gravityMagnitude = message->m_magnitude.asFloat();

    if (m_physicsWorld)
    {
        const hkVector4& hkUp       = MathConvert(AppCore::MathConventions::cWorldUp);
        hkVector4        gravityVec = hkUp * -m_gravityMagnitude;
        m_physicsWorld->setGravity(gravityVec);
    }
}

void SimulationWorld::onBodyBufferFullSignal(hknpWorld* npWorld, hknpBodyManager* npBodyManager)
{
    const uint32 currentCapacity = npBodyManager->getCapacity();
    // increase capacity by 25% when body buffer fills up.
    npBodyManager->relocateBodyBuffer(HK_NULL, currentCapacity + (currentCapacity >> 2));
}

void SimulationWorld::onMotionBufferFullSignal(hknpWorld* npWorld, hknpMotionManager* npMotionManager)
{
    const uint32 currentCapacity = npMotionManager->getCapacity();
    // increase capacity by 25% when motion buffer fills up.
    npMotionManager->relocateMotionBuffer(HK_NULL, currentCapacity + (currentCapacity >> 2));
}

void SimulationWorld::preStep()
{
    // SG pose is only relevant once per frame, not one per sim step.
    // Blending for mute/unmute still happens per sim.
    m_characterComponentManager->updateBehaviorWithSGStream();
}

void SimulationWorld::step()
{
    LLProfile::PerformanceStatTimedScope stepTime("SimulationWorld::step", LLPROFILE_STAT_GET(EngineSimulation, WorldStep));

    LLPROFILE_AUTO_CPU_MARKER_STATIC("SimulationWorld::step");
    HK_TIME_CODE_BLOCK("SimulationWorld", nullptr);
    LLGEMS_DISPLAY_NEXT_SUBFRAME(SimulationGroup);

    m_frameSynchronizer->stepServer();

    firePreSimulationStepListeners();

    //
    // Step
    //
    const hkReal dt = m_deltaTime.asFloat();

    if (m_behaviorWorld)
    {
        processSimulationMessages();

        m_animationManager->firePreStepListeners(m_deltaTime.asFloat());

        // Update the behavior world
        {
            LLPROFILE_AUTO_CPU_MARKER_STATIC("hkbWorld::step");
            m_behaviorWorld->step(dt, m_taskQueue);
        }

        m_characterComponentManager->postBehaviorSGUpdate();

        // If there is no physics world, fire post animation, pre collide callback
        // otherwise, this already happened when Behavior stepped physics
        if (nullptr == m_physicsWorld)
        {
            firePostAnimationPrePhysicsListeners();
        }

        // Update poses
        m_animationManager->step(dt);
        m_animationManager->firePoseListeners();
        m_poseManager->step();
        m_characterComponentManager->step(dt);
        m_ikBodyComponentManager->step();

        m_animationManager->firePostStepListeners(m_deltaTime.asFloat());
    }
    else if (m_physicsWorld)
    {
        if (m_useAVBD && m_avbdBridge)
        {
            // Use AVBD physics solver
            m_avbdBridge->step(dt, m_taskQueue);
        }
        else
        {
            // Use Havok physics solver
            // Create an input structure for each simulation step
            hknpStepInput stepInput;
            {
                stepInput.m_deltaTime                = dt; // use constant time steps of 1/30th of a second
                stepInput.m_stepLocalStreamAllocator = m_stepLocalAllocator;
                stepInput.m_numThreads               = m_threadPool->getNumThreads() + 1; // include the calling thread
            }

            // Perform collision detection.
            m_physicsWorld->stepCollide(stepInput, m_taskQueue);

            // Perform solving and integration.
            m_physicsWorld->stepSolve(m_taskQueue);
        }
    }

    firePostSimulationStepListeners();
}

void SimulationWorld::drawDebugCollisionGridLine(const LLCore::Vector4& start, const LLCore::Vector4& end)
{
#ifdef LLGEMS_DISPLAY_ENABLED
    auto filterInfo = EngineSimulation::LayerManager::SetCollisionFilterLayerInSourceSpaceAll(EngineSimulation::LayerManager::Layer::DYNAMIC_OBJECTS);

    auto collector = EngineSimulation::AllHitsCollector();
    collector.reset();

    QueryResult                hitResult;
    EngineCommon::RayCastQuery ray(LLCore::Segment(start, end), filterInfo);
    castQuery(ray, collector);

    LLGEMS_ON_DEBUG_DISPLAY(m_debugCollisionDisplayIds.insertLast(LLGEMS_DISPLAY_LINE(LLCore::Duration::cForever, start, end, collector.hasHit() ? LLGems::ColorConstants::cRed : LLGems::ColorConstants::cGreen)));

    // draw points at each collision
    LLGEMS_ON_DEBUG_DISPLAY(for (uint32 idx = 0; idx < collector.getNumHits(); idx++) {
        collector.getHit(idx, hitResult);
        m_debugCollisionDisplayIds.insertLast(LLGEMS_DISPLAY_POINT(LLCore::Duration::cForever, hitResult.getPosition(), LLGems::ColorConstants::cRed));
    })
#endif // LLGEMS_DISPLAY_ENABLED
}

void SimulationWorld::toggleDebugCollisionGrid()
{
#ifdef LLGEMS_DISPLAY_ENABLED
    if (m_debugCollisionDisplayIds.isEmpty())
    {
        const float          gridSpacing = 2.f;
        const LLCore::Scalar gridHeight  = 1.f;

        LLCore::Vector4 minExtent, maxExtent;
        getCurrentCollisionExtents(&minExtent, &maxExtent);

        LLCore::Vector4 start(minExtent.getX(), minExtent.getY(), gridHeight);
        LLCore::Vector4 end(minExtent.getX(), maxExtent.getY(), gridHeight);

        // Draw lines across X axis
        for (float x = minExtent.getX().asFloat(); x < maxExtent.getX(); x += gridSpacing)
        {
            start.setX(x);
            end.setX(x);
            drawDebugCollisionGridLine(start, end);
        }

        // Draw lines across Y axis
        start.setX(minExtent.getX());
        end.setX(maxExtent.getX());
        for (float y = minExtent.getY().asFloat(); y < maxExtent.getY(); y += gridSpacing)
        {
            start.setY(y);
            end.setY(y);
            drawDebugCollisionGridLine(start, end);
        }
    }
    else
    {
        for ([[maybe_unused]] int id : m_debugCollisionDisplayIds)
        {
            LLGEMS_REMOVE_DISPLAY_OBJECT(id);
        }
        m_debugCollisionDisplayIds.removeAll();
    }
#endif // LLGEMS_DISPLAY_ENABLED
}

void SimulationWorld::onPreCollide(hknpWorld* world, const hknpStepInput& stepInput)
{
    HK_TIME_CODE_BLOCK("[LindenLab] preCollide", nullptr);

    // Other things (e.g. transform components)
    firePostAnimationPrePhysicsListeners();

    // Order is important. This needs to come after the other listeners
    m_rigidBodyManager->onPreCollide(stepInput);
    m_physicsConstraintManager->onPreCollide(stepInput);
}

void SimulationWorld::onPostSolve(hknpWorld* world)
{
    HK_TIME_CODE_BLOCK("[LindenLab] postSolve", nullptr);

    m_rigidBodyManager->onPostSolve();
}

void SimulationWorld::onBodyExitedBroadphase(const hknpEventHandlerInput& input, const hknpEvent& event)
{
    const hknpBodyExitedBroadPhaseEvent& typedEvent = event.asBodyExitedBroadPhaseEvent();
    BodyHandle*                          body       = getRigidBodyManager()->getBodyHandle(hknpBodyId::toRawInt(typedEvent.m_bodyId));

    ComponentExitedBroadphaseEvent eventOut;
    eventOut.m_componentId = LLGems::cInvalidComponentId;
    bool sendEvent         = false;
    if (body != nullptr)
    {
        eventOut.m_componentId   = (*body)->getComponentId();
        eventOut.m_componentType = ComponentExitedBroadphaseEvent::cComponentType_RigidBody;
        sendEvent                = true;
    }
    else
    {
        // This might be a character proxy
        const AnimationComponent* animationComponent = getAnimationManager()->getComponentByBodyId(hknpBodyId::toRawInt(typedEvent.m_bodyId));
        if (animationComponent != nullptr)
        {
            eventOut.m_componentId   = animationComponent->getComponentId();
            eventOut.m_componentType = ComponentExitedBroadphaseEvent::cComponentType_Animation;
            sendEvent                = true;
        }
    }

    if (sendEvent)
    {
        LLCORE_ASSERT(eventOut.m_componentId != LLGems::cInvalidComponentId, "Body that raised event does not exist.");

        m_simulationEventQueue->postEvent(LLCore::Give(eventOut));
    }
}

void SimulationWorld::fireSceneCharacterListeners(hkbSceneCharacters& sceneCharacters, float timestep)
{
    for (int i = 0; i < m_sceneCharacterListeners.getCount(); i++)
    {
        m_sceneCharacterListeners[i](sceneCharacters, timestep);
    }
    for (int i = 0; i < m_postSceneCharacterListeners.getCount(); i++)
    {
        m_postSceneCharacterListeners[i](timestep);
    }
}

void SimulationWorld::bindSceneCharactersCallback(const SceneCharactersCallback& callbackFunc)
{
    m_sceneCharacterListeners.insertLast(callbackFunc);
}

void SimulationWorld::unbindSceneCharactersCallback(const SceneCharactersCallback& callbackFunc)
{
    const mem_int removeAt = m_sceneCharacterListeners.findFirstOf(callbackFunc);
    if (removeAt >= 0)
    {
        m_sceneCharacterListeners.removeFast(removeAt);
    }
}

void SimulationWorld::bindPostSceneCharactersCallback(const PostSceneCharactersCallback& callbackFunc)
{
    m_postSceneCharacterListeners.insertLast(callbackFunc);
}

void SimulationWorld::unbindPostSceneCharactersCallback(const PostSceneCharactersCallback& callbackFunc)
{
    const mem_int removeAt = m_postSceneCharacterListeners.findFirstOf(callbackFunc);
    if (removeAt >= 0)
    {
        m_postSceneCharacterListeners.removeFast(removeAt);
    }
}

void SimulationWorld::firePostAnimationPrePhysicsListeners()
{
    LLPROFILE_AUTO_CPU_MARKER_FUNCTION
    HK_TIME_CODE_BLOCK("[LindenLab] Post Animation Pre-Physics Listeners", nullptr);

    for (int i = 0; i < m_postAnimationPrePhysicsListeners.getCount(); i++)
    {
        m_postAnimationPrePhysicsListeners[i]();
    }
}

void SimulationWorld::bindPostAnimationPrePhysics(const PostAnimationPrePhysicsCallback& callbackFunc)
{
    LLCORE_ASSERT(m_postAnimationPrePhysicsListeners.findFirstOf(callbackFunc) == -1, "Callback already added.");

    m_postAnimationPrePhysicsListeners.insertLast(callbackFunc);
}


void SimulationWorld::unbindPostAnimationPrePhysics(const PostAnimationPrePhysicsCallback& callbackFunc)
{
    const mem_int index = m_postAnimationPrePhysicsListeners.findFirstOf(callbackFunc);
    if (index > -1)
    {
        m_postAnimationPrePhysicsListeners.removeFast(index);
    }
}

void SimulationWorld::firePreSimulationStepListeners()
{
    LLPROFILE_AUTO_CPU_MARKER_FUNCTION
    HK_TIME_CODE_BLOCK("[LindenLab] Pre Simulation Step Listeners", nullptr);

    for (int i = 0; i < m_preSimulationStepListeners.getCount(); i++)
    {
        m_preSimulationStepListeners[i](getDeltaTime().asFloat(), m_frameSynchronizer->getFrame());
    }
}

void SimulationWorld::bindPreSimulationStepCallback(const PreSimulationStepCallback& callbackFunc, bool insertBefore)
{
    LLCORE_ASSERT(m_preSimulationStepListeners.findFirstOf(callbackFunc) == -1, "Callback already added.");

    if (insertBefore)
    {
        m_preSimulationStepListeners.insertOrdered(0, callbackFunc);
    }
    else
    {
        m_preSimulationStepListeners.insertLast(callbackFunc);
    }
}


void SimulationWorld::unbindPreSimulationStepCallback(const PreSimulationStepCallback& callbackFunc)
{
    const mem_int index = m_preSimulationStepListeners.findFirstOf(callbackFunc);
    if (index > -1)
    {
        m_preSimulationStepListeners.removeFast(index);
    }
}

void SimulationWorld::firePostSimulationStepListeners()
{
    LLPROFILE_AUTO_CPU_MARKER_FUNCTION
    HK_TIME_CODE_BLOCK("[LindenLab] Post Simulation Step Listeners", nullptr);

    for (int i = 0; i < m_postSimulationStepListeners.getCount(); i++)
    {
        m_postSimulationStepListeners[i](getDeltaTime().asFloat(), m_frameSynchronizer->getFrame());
    }
}

void SimulationWorld::bindPostSimulationStepCallback(const PostSimulationStepCallback& callbackFunc)
{
    LLCORE_ASSERT(m_postSimulationStepListeners.findFirstOf(callbackFunc) == -1, "Callback already added.");

    m_postSimulationStepListeners.insertLast(callbackFunc);
}


void SimulationWorld::unbindPostSimulationStepCallback(const PostSimulationStepCallback& callbackFunc)
{
    const mem_int index = m_postSimulationStepListeners.findFirstOf(callbackFunc);
    if (index > -1)
    {
        m_postSimulationStepListeners.removeFast(index);
    }
}

// This is a temporary workaround while we work on the filtering system.  This filter only
// disables collisions between shape casts and character proxies
//
class CharacterProxyCollisionFilter : public hknpCollisionFilter
{
public:
    HK_DECLARE_CLASS_ALLOCATOR(HK_MEMORY_CLASS_PHYSICS);

    CharacterProxyCollisionFilter()
        : hknpCollisionFilter(TYPE_USER) {}
    ~CharacterProxyCollisionFilter() {}

    /// Reduce the list of body pairs passed in through the \a pairs buffer; return the number of pairs left in buffer.
    /// Called on any new overlaps found by the broad phase.
    virtual int filterBodyPairs(const hknpWorld& world, hknpBodyIdPair* pairs, int numPairs) const
    {
        //hknpBodyIdPair* dst = pairs;
        //hknpBodyIdPair* src = pairs;
        //for (int i = 0; i < numPairs; i++)
        //{
        //    if (_isCollisionEnabled(src->m_bodyA, src->m_bodyB))
        //    {
        //        *dst = *src;
        //        dst++;
        //    }
        //    src++;
        //}
        //int newNumPairs = (int)(dst - pairs);
        return numPairs;
    }

    /// Filter collision queries at BROAD PHASE LAYER level.
    /// Called from: world collision queries.
    virtual bool isCollisionEnabled(
        hknpCollisionQueryType::Enum queryType,
        hknpBroadPhaseLayerIndex     layerIndex,
        hkUint64                     userData) const
    {
        return true;
    }

    /// Filter collision queries at BODY level.
    /// Called from: world collision queries.
    virtual bool isCollisionEnabled(
        hknpCollisionQueryType::Enum queryType,
        const hknpQueryFilterData&   queryFilterData,
        const hknpBody&              body) const
    {
        // Check for the character proxy collision layer.
        if (LayerManager::Layer::CHARACTER_PROXIES == LayerManager::GetCollisionFilterLayer(body.m_collisionFilterInfo))
        {
            return false;
        }
        return true;
    }

    /// Filter collision queries at BODY VS BODY level.
    /// Called from: world castShape() and getClosestPoints() queries.
    virtual bool isCollisionEnabled(
        hknpCollisionQueryType::Enum queryType,
        hknpBodyId                   bodyIdA,
        hknpBodyId                   bodyIdB) const
    {
        return true;
    }

    /// Filter collisions at SHAPE VS SHAPE level.
    /// Called from: collision pipeline and collision queries.
    /// If /a targetShapeIsB is TRUE then /a shapeInputB holds data on the target shape and /a shapeInputA holds
    /// data on the query shape (if available).
    virtual bool isCollisionEnabled(
        hknpCollisionQueryType::Enum queryType, bool targetShapeIsB, const FilterInput& shapeInputA, const FilterInput& shapeInputB) const
    {
        return true;
    }
};

Vector4 SimulationWorld::linearCast(Vector4_ConstParameter startingPos, Vector4_ConstParameter endingPos, Scalar_ConstParameter radius, const LLCore::Array<EngineSimulation::BodyId>& ignoredBodyIds) const
{
    Vector4 result = endingPos;

    // Construct the shape cast query
    //
    CharacterProxyCollisionFilter filter;
    BodyCollisionFilter           bodyFilter(&filter, ignoredBodyIds);
    hknpSphereShape*              shape = hknpSphereShape::createSphereShape(hkVector4::getZero(), radius.asFloat());
    hknpShapeCastQuery            query(*shape, MathConvert(startingPos), MathConvert(endingPos));
    {
        query.m_filterData.m_collisionFilterInfo = 0;
        query.m_shapeTagCodec                    = m_physicsWorld->getShapeTagCodec();
        query.m_filter                           = &bodyFilter;
    }

    // Execute
    //
    hknpClosestHitCollector collector;
    m_physicsWorld->castShape(query, hkRotation::getIdentity(), &collector);
    if (collector.hasHit())
    {
        const hknpCollisionResult& hit = collector.getHit();

        hkVector4& r = MathConvert(result);
        r.setInterpolate4(MathConvert(startingPos), MathConvert(endingPos), hkSimdReal(hit.m_fraction));
    }

    shape->removeReference();

    return result;
}

Vector4 SimulationWorld::castCamera(const CameraCollisionQuery& query, Scalar_ConstParameter thickness /*= 0.05f*/) const
{
    LLPROFILE_AUTO_CPU_MARKER_FUNCTION

    // Cast the camera as a pyramid with the camera position as
    // the tip of the pyramid and the near plane as its base.
    Vector4 bestPosition = query.m_cameraFrom;

    // Compute the solid shape we want to use for collision
    //
    hknpShape* pyramidFrustumShape;
    {
        // Setup build configuration - no mass props
        //
        hknpConvexShape::BuildConfig buildConfig;
        {
            // We don't need to bother building the mass properties
            buildConfig.m_buildMassProperties = false;
            // Preserve thickness
            buildConfig.m_shrinkByRadius = false;
        }

        // Construct the pyramid as a convex vertices shape
        //
        hkArray<hkVector4> vertices(5);
        {
            const Scalar tanFov    = LLCore::Tan(query.m_diagonalFovInRadians * ScalarConstants::cHalf);
            const hkReal halfWidth = MathConvert(LLCore::ScalarConstants::cSqrt2 * LLCore::ScalarConstants::cHalf * query.m_nearPlane * tanFov);
            const hkReal y         = query.m_nearPlane.asFloat();
            const hkReal padding   = 0.2f;

            vertices[0].set(0.f, -padding, 0.f);
            vertices[1].set(-halfWidth, y, -halfWidth);
            vertices[2].set(-halfWidth, y, halfWidth);
            vertices[3].set(halfWidth, y, halfWidth);
            vertices[4].set(halfWidth, y, -halfWidth);
        }

        hkStridedVertices stridedVertices(vertices);
        pyramidFrustumShape = hknpConvexShape::createFromVertices(stridedVertices, MathConvert(thickness), buildConfig);
    }

    // Construct the shape query
    //
    BodyCollisionFilter bodyFilter(m_baseFilter, query.ignoredBodyIds);
    hknpShapeCastQuery  shapeCastQuery(*pyramidFrustumShape, MathConvert(query.m_cameraTo), MathConvert(query.m_cameraFrom));
    {
        shapeCastQuery.m_filter                           = &bodyFilter;
        shapeCastQuery.m_filterData.m_collisionFilterInfo = query.m_collisionFilterInfo;
        shapeCastQuery.m_shapeTagCodec                    = m_physicsWorld->getShapeTagCodec();
    }

    // Find transform that describes the camera
    //
    hkRotation cameraOrientation;
    {
        // This isn't the universal camera basis, this is the basis
        // in which the pyramid shape above was constructed, so it is
        // specific to this construction only.
        const Vector4                cameraForward(0.f, 1.f, 0.f);
        const Vector4                cameraUp(0.f, 0.f, 1.f);
        const LLGems::EuclideanBasis originalBasis(cameraForward, cameraUp);

        cameraOrientation = MathConvert(originalBasis.getRotationTo(query.m_cameraBasis));
    }


    // Execute the cast
    //
    hknpClosestHitCollector collector;
    m_physicsWorld->castShape(shapeCastQuery, cameraOrientation, &collector);
    if (collector.hasHit())
    {
        const hknpCollisionResult& hit = collector.getHit();

        hkVector4& hkPosition = MathConvert(bestPosition);
        hkPosition.setInterpolate4(MathConvert(query.m_cameraTo), MathConvert(query.m_cameraFrom), hkSimdReal(hit.m_fraction));
    }

#ifdef ENGINESIMULATION_DEBUG_DRAW_CAMERACAST
    // Draw the camera pyramid
    {
        hkTransform transform;
        {
            transform.setRotation(cameraOrientation);
            transform.setTranslation(MathConvert(bestPosition));
        }

        hkInplaceArray<hkDisplayGeometry*, 1> displayGeometries;
        {
            hknpShapeUtil::buildShapeDisplayGeometries(
                pyramidFrustumShape,
                hknpShape::CONVEX_RADIUS_DISPLAY_NONE,
                displayGeometries);
        }

        // Draw the final position
        HK_DISPLAY_GEOMETRY_WITH_TRANSFORM(displayGeometries, transform, hkColor::AQUAMARINE);

        // Draw the original position
        transform.setTranslation(MathConvert(query.m_cameraFrom));
        HK_DISPLAY_GEOMETRY_WITH_TRANSFORM(displayGeometries, transform, hkColor::BEIGE);
    }
#endif

    // Cleanup
    pyramidFrustumShape->removeReference();

    return bestPosition;
}

LLCore::Vector4 SimulationWorld::castCameraSphere(LLCore::Vector4_ConstParameter startingPos, LLCore::Vector4_ConstParameter endingPos, LLCore::Scalar_ConstParameter radius, uint32 collisionFilterInfo) const
{
    Vector4 result = endingPos;

    hknpSphereShape*   shape = hknpSphereShape::createSphereShape(hkVector4::getZero(), radius.asFloat());
    hknpShapeCastQuery query(*shape, MathConvert(startingPos), MathConvert(endingPos));
    {
        query.m_filterData.m_collisionFilterInfo = collisionFilterInfo;
        query.m_filter                           = m_baseFilter;
        query.m_shapeTagCodec                    = m_physicsWorld->getShapeTagCodec();
    }

    hknpClosestHitCollector collector;
    m_physicsWorld->castShape(query, hkRotation::getIdentity(), &collector);
    if (collector.hasHit())
    {
        const hknpCollisionResult& hit = collector.getHit();

        hkVector4& r = MathConvert(result);
        r.setInterpolate4(MathConvert(startingPos), MathConvert(endingPos), hkSimdReal(hit.m_fraction));
    }

    shape->removeReference();

    return result;
}

void SimulationWorld::startStep()
{
    //
    // Clear all profiling data
    //

    // Reset timer data for this thread
    HavokSystem::GetInstance().ResetMonitorStream();

    // for other threads
    m_threadPool->clearTimerData();
}

void SimulationWorld::finishStep()
{
    LLPROFILE_AUTO_CPU_MARKER_FUNCTION

    // Broadcast animation state
    if (m_broadcastEventQueue)
    {
        if (m_animationManager)
        {
            m_animationManager->broadcastState();
        }

        if (m_rigidBodyManager)
        {
            m_rigidBodyManager->broadcastActiveBodyStates(false);
        }
    }

    //
    // Sync timer data and step visual debugger
    //
    m_visualDebugger->syncTimers(m_threadPool);
    m_visualDebugger->step();
}

int64 SimulationWorld::getCurrentFrame() const
{
    LLCORE_ASSERT(m_frameSynchronizer, "No frame synchronizer to query.");
    return m_frameSynchronizer->getFrame();
}

class CollisionQueryCollector : public hknpCollisionQueryCollector
{
public:
    CollisionQueryCollector()
        : hknpCollisionQueryCollector()
        , m_fraction(HK_REAL_MAX)
        , m_hasHit(false)
    {
        //
    }

    virtual void addHit(const hknpCollisionResult& hit)
    {
        const hknpRayCastQueryResult& queryResult = hit.asRayCast();
        const hkReal                  fraction    = queryResult.getFractionRaw();
        if (fraction < m_fraction)
        {
            m_position = queryResult.getPosition();
            m_normal   = queryResult.getSurfaceNormal();

            m_fraction = fraction;
            m_hasHit   = true;
        }
    }

    virtual bool hasHit() const
    {
        return m_hasHit;
    }

    void getResult(LLCore::Vector4* position, LLCore::Vector4* normal)
    {
        *position = MathConvert(m_position);
        *normal   = MathConvert(m_normal);
    }

private:
    hkVector4 m_position;
    hkVector4 m_normal;

    hkReal m_fraction;
    bool   m_hasHit = false;
};


void SimulationWorld::castQuery(const EngineCommon::RayCastQuery& ray, EngineCommon::HitCollectorInterface& collector) const
{
    auto segment                             = ray.getSegment();
    auto query                               = hknpRayCastQuery(MathConvert(segment.m_start), MathConvert(segment.m_end));
    query.m_filterData.m_collisionFilterInfo = ray.getCollisionFilterInfo();
    query.m_shapeTagCodec                    = m_physicsWorld->getShapeTagCodec();

    BodyCollisionFilter bodyFilter(m_baseFilter, ray.getIgnoredBodyIds());
    query.m_filter = &bodyFilter;

    auto& collectorInterface   = static_cast<HitCollectorHavokInterface&>(collector);
    collectorInterface.m_world = m_physicsWorld;
    m_physicsWorld->castRay(query, collectorInterface.getHkCollector());

#ifdef ENGINESIMULATION_DEBUG_DRAW_RAYCASTS
    collectorInterface.debugDisplayHits(segment);
#endif
}

void SimulationWorld::castQuery(const EngineCommon::ShapeCastQuery& shapeQuery, EngineCommon::HitCollectorInterface& collector) const
{
    hknpShapeCastQuery query;
    query.m_shape = shapeQuery.getShape();
    query.m_ray.setEndPoints(MathConvert(shapeQuery.getSegment().m_start), MathConvert(shapeQuery.getSegment().m_end));
    query.m_filterData.m_collisionFilterInfo = shapeQuery.getCollisionFilterInfo();
    query.m_shapeTagCodec                    = m_physicsWorld->getShapeTagCodec();

    BodyCollisionFilter bodyFilter(m_baseFilter, shapeQuery.getIgnoredBodyIds());
    query.m_filter = &bodyFilter;

    auto& collectorInterface   = static_cast<HitCollectorHavokInterface&>(collector);
    collectorInterface.m_world = m_physicsWorld;
    m_physicsWorld->castShape(query, MathConvert(shapeQuery.getRotation()), collectorInterface.getHkCollector());

#ifdef ENGINESIMULATION_DEBUG_DRAW_RAYCASTS
    collectorInterface.debugDisplayHits(shapeQuery.getSegment());
#endif
}

void SimulationWorld::castQuery(const EngineCommon::ClosestPointsQuery& closestPointsQuery, EngineCommon::HitCollectorInterface& collector) const
{
    hknpClosestPointsQuery query;

    query.m_shape                            = closestPointsQuery.getShape();
    query.m_maxDistance                      = closestPointsQuery.getMaximumDistance();
    query.m_shapeTagCodec                    = m_physicsWorld->getShapeTagCodec();
    query.m_filterData.m_collisionFilterInfo = closestPointsQuery.getCollisionFilterInfo();

    BodyCollisionFilter bodyFilter(m_baseFilter, closestPointsQuery.getIgnoredBodyIds());
    query.m_filter = &bodyFilter;

    auto& collectorInterface   = static_cast<HitCollectorHavokInterface&>(collector);
    collectorInterface.m_world = m_physicsWorld;

    LLCore::MTransform transform(closestPointsQuery.getRotation(), closestPointsQuery.getOrigin());
    m_physicsWorld->getClosestPoints(query, MathConvert(transform), collectorInterface.getHkCollector());
}

void SimulationWorld::getMaxCollisionExtents(LLCore::Vector4* minExtent, LLCore::Vector4* maxExtent)
{
    const LLCore::Scalar aabbExtent = m_maxBroadPhaseSize;
    maxExtent->setMul(aabbExtent, VectorConstants::cHalf);
    minExtent->setNeg(*maxExtent);
}

void SimulationWorld::getCurrentCollisionExtents(LLCore::Vector4* minExtent, LLCore::Vector4* maxExtent)
{
    hkAabb extents;
    m_physicsWorld->m_broadPhase->getExtents(extents);

    *minExtent = MathConvert(extents.m_min);
    *maxExtent = MathConvert(extents.m_max);
}

bool SimulationWorld::capsuleTest(const LLCore::MTransform& transform, const LLCore::Scalar& height, const LLCore::Scalar& radius, int filterInfo, const LLCore::Array<uint32>& ignoredBodyIds)
{
    LLCore::Vector4 foot;
    LLCore::Vector4 head;

    foot.setMul(AppCore::MathConventions::cWorldUp, radius);
    head.setMul(AppCore::MathConventions::cWorldUp, height - radius);

    hknpCapsuleShape* capsule = hknpCapsuleShape::createCapsuleShape(MathConvert(foot), MathConvert(head), radius.asFloat());

    hknpClosestHitCollector collector;
    {
        collector.reset();
    }

    BodyCollisionFilter    bodyFilter(m_baseFilter, ignoredBodyIds);
    hknpClosestPointsQuery query;
    {
        query.m_shape                            = capsule;
        query.m_maxDistance                      = 0.0f;
        query.m_shapeTagCodec                    = m_physicsWorld->getShapeTagCodec();
        query.m_filterData.m_collisionFilterInfo = filterInfo;
        query.m_filter                           = &bodyFilter;
    }

    m_physicsWorld->getClosestPoints(query, MathConvert(transform), &collector);

    capsule->removeReference();

    return collector.m_numHits > 0;
}

bool SimulationWorld::aabbTest(const LLCore::Aabb& aabb, uint32 filterInfo, const LLCore::Array<uint32>& ignoredBodyIds, LLCore::Array<BodyId>* bodiesOut /*= nullptr*/)
{
    BodyCollisionFilter bodyFilter(m_baseFilter, ignoredBodyIds);
    hknpAabbQuery       query;
    {
        query.m_aabb                             = MathConvert(aabb);
        query.m_filterData.m_collisionFilterInfo = filterInfo;
        query.m_shapeTagCodec                    = m_physicsWorld->getShapeTagCodec();
        query.m_filter                           = &bodyFilter;
    }

    hkArray<hknpBodyId> bodies;
    m_physicsWorld->queryAabb(query, bodies);
    if (bodiesOut && bodies.getSize() > 0)
    {
        bodiesOut->copy((BodyId*)&bodies[0], bodies.getSize());
    }
    return bodies.getSize() > 0;
}

bool SimulationWorld::isStaticOrKeyframed(EngineSimulation::BodyId bodyId) const
{
    auto& body = getPhysicsWorld()->getBody(bodyId);
    return body.isStaticOrKeyframed();
}

float SimulationWorld::getBodyVolume(EngineSimulation::BodyId bodyId) const
{
    hkDiagonalizedMassProperties bodyProps;
    if (m_physicsWorld->getBody(bodyId).getShape()->getMassProperties(bodyProps) == HK_SUCCESS)
    {
        return bodyProps.m_volume;
    }
    return 0.f;
}

LLCore::Vector4 SimulationWorld::getBodyCenter(EngineSimulation::BodyId bodyId) const
{
    hkAabb bodyAabb;
    m_physicsWorld->getBodyAabb(bodyId, bodyAabb);

    hkVector4 centerPos = bodyAabb.m_min + bodyAabb.m_max;
    centerPos.mul(0.5f);
    return MathConvert(centerPos);
}

void SimulationWorld::getBodyWorldSpaceAabb(BodyId bodyId, LLCore::Aabb* aabbOut) const
{
    hkAabb aabb;
    m_physicsWorld->getBodyAabb(bodyId, aabb);

    aabbOut->m_min = MathConvert(aabb.m_min);
    aabbOut->m_max = MathConvert(aabb.m_max);
}

LLGems::ComponentId SimulationWorld::getBodyComponentId(EngineSimulation::BodyId bodyId) const
{
    const hknpBody& body = m_physicsWorld->getBody(bodyId);
    return (LLGems::ComponentId)body.m_userData;
}

bool SimulationWorld::isCollisionEnabled(EngineSimulation::BodyId bodyA_id, EngineSimulation::BodyId bodyB_id) const
{
    return getCollisionFilter()->isCollisionEnabled(hknpCollisionQueryType::UNDEFINED, hknpBodyId(bodyA_id), hknpBodyId(bodyB_id));
}

void SimulationWorld::addKeyframedBody(EngineSimulation::BodyId bodyId, LLCore::MTransform* drivingTransform)
{
    m_keyframedBodies.insertLastOrReplace(bodyId, drivingTransform);
}

void SimulationWorld::removeKeyframedBody(EngineSimulation::BodyId bodyId)
{
    auto toRemove = m_keyframedBodies.find(bodyId);
    if (toRemove != nullptr)
    {
        m_keyframedBodies.removeFast(toRemove);
    }
}


void SimulationWorld::getStatsSummary(LLJson::JsonObject* summary) const
{
    //Collision Memory Statistics
    {
        hkMemoryAllocator::MemoryStatistics stats;
        m_persistentAllocator->getMemoryStatistics(stats);
        summary->addMember("sim_col_mem_in_use"_ll, (int64)stats.m_inUse);
        summary->addMember("sim_col_mem_peak"_ll, (int64)stats.m_peakInUse);
        summary->addMember("sim_col_mem_available"_ll, (int64)stats.m_available);
    }

    //Solver Memory Statistics - only the peak is interesting since it is only used during the solve phase
    {
        hkMemoryAllocator::MemoryStatistics stats;
        m_stepLocalAllocator->getMemoryStatistics(stats);
        summary->addMember("sim_solve_mem_peak"_ll, (int64)stats.m_peakInUse);
    }

    // Sim Stats
    summary->addMember("sim_active_rb"_ll, m_physicsWorld->getActiveBodies().getSize());
    summary->addMember("sim_total_rb"_ll, m_physicsWorld->getNumBodies());
}

} // namespace EngineSimulation
