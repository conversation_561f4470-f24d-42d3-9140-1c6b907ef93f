/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#ifndef ENGINESIMULATION_WORLD_H
#define ENGINESIMULATION_WORLD_H

// LLCore
#include "LLCore/Allocator.h"
#include "LLCore/Array.h"
#include "LLCore/Flags.h"
#include "LLCore/Function.h"
#include "LLCore/Math.h"

// EngineCommon
#include "EngineCommon/Picking.h"

// Communications
#include "LLClientServer/Channel.h"
#include "LLCore/EventQueue.h"
#include "LLGems/EuclideanBasis.h"

// EngineSimulation
// Havok
#include "AnimationComponentManager.h"
#include "LayerManager.h"
#include "RigidBodyComponentManager.h"
#include "System.h"
#include "VisualDebugger.h"
#include "AVBDIntegrationBridge.h"

class hkThreadPool;
class hkDefaultTaskQueue;
class hkbnpPhysicsInterface;
class hkFixedBlockStreamAllocator;
class hknpWorld;
class hkbWorld;
class hkbGlobalSymbolLinker;
struct hknpStepInput;
class hknpPairCollisionFilter;
class hknpConstraintCollisionFilter;
struct hknpEvent;
struct hknpEventHandlerInput;
class hknpBodyManager;
class hknpMotionManager;
struct hkbSceneCharacters;

namespace LLResource
{
class ResourceStoreManager;
}

namespace LLJson
{
class JsonObject;
}

namespace EngineCommon
{
class StreamRouter;
}

namespace EngineSimulation
{

class VisualDebugger;
class HavokAssetUtil;
class PhysicsConstraintComponentManager;
class KeyframedBoneComponentManager;
class CharacterComponentManager;
class PoseComponentManager;
class HavokStore;
class UpdateKeyframedObjectsModifier;
class UpdateSceneCharactersModifier;
class WorldBehaviorPhysicsInterface;
class FrameSynchronizer;
class SpeechGraphicsInterface;
class BehaviorAttachmentManager;
class IKBodyComponentManager;

enum class WorldCreationFlags
{
    cCreatePhysicsWorld   = 1,
    cCreateAnimationWorld = 1 << 1
};

class SimulationWorld
{
public:
    static constexpr float cDefaultGravityMagnitude = 9.81f;

    struct Cinfo
    {
        LLCore::String m_vdbCapturePath;

        LLCore::RangeRef<const LLCore::String> m_vdbViewers;

        LLCore::Flags<WorldCreationFlags> m_creationFlags = LLCore::Flags<WorldCreationFlags>(WorldCreationFlags::cCreatePhysicsWorld, WorldCreationFlags::cCreateAnimationWorld);

        LLCore::Duration m_deltaTime;

        float m_broadPhaseSize = 4096.f;

        // Optional event queue by which messages generated on background threads during the step
        // can be passed to higher level systems.
        LLCore::EventQueue* m_simulationEventQueue = nullptr;

        // Optional Channel object. This will be passed to each component manager to give it an opportunity
        // to register handlers for control messages
        LLClientServer::Channel* m_outgoingChannel = nullptr;

        // Optional EventQueue for pushing messages to be broadcast
        LLCore::EventQueue* m_broadcastEventQueue = nullptr;

        // Optional EventQueue for pushing messages outside systems
        LLCore::EventQueue* m_callbackQueue = nullptr;

        // Optional manager for external simulation data streams, e.g. SpeechGraphics
        EngineCommon::StreamRouter*                m_streamRouter            = nullptr;
        EngineSimulation::SpeechGraphicsInterface* m_speechGraphicsInterface = nullptr;

        int m_vdbPort = VisualDebugger::cDefaultPort;

        bool m_enableVdb                   = true;
        bool m_enableAvatarAvatarCollision = false;

        bool m_allowFootIK            = false;
        bool m_optimizeSimulatedBones = true;

        LLResource::ResourceStoreManager* m_resourceStoreManager = nullptr;

        float m_gravityMagnitude = cDefaultGravityMagnitude;
    };

    SimulationWorld(const Cinfo& cinfo);

    ~SimulationWorld();

    // Call should be called before any stepping happens in a frame
    void preStep();

    // Step all worlds
    void step();

    // The RuntimeWorld calls this at the beginning of the frame step.  The SimulationWorld may not actually
    // get stepped this frame.
    void startStep();

    // The RuntimeWorld calls this at the end of the frame step.  This allows us to finish off any
    // visual debugger logic, so we capture timers for the entire step, including rendering.
    void finishStep();

    // Callbacks
    //
    typedef LLCore::Function<void()> PostAnimationPrePhysicsCallback;
    void                             bindPostAnimationPrePhysics(const PostAnimationPrePhysicsCallback& callbackFunc);
    void                             unbindPostAnimationPrePhysics(const PostAnimationPrePhysicsCallback& callbackFunc);

    typedef LLCore::Function<void(LLCore::Scalar_ConstParameter simulationDeltaTime, int64 frame)> PreSimulationStepCallback;
    void                                                                                           bindPreSimulationStepCallback(const PreSimulationStepCallback& callbackFunc, bool insertBefore = false);
    void                                                                                           unbindPreSimulationStepCallback(const PreSimulationStepCallback& callbackFunc);

    typedef LLCore::Function<void(hkbSceneCharacters& sceneCharacters, LLCore::Scalar_ConstParameter simulationDeltaTime)> SceneCharactersCallback;
    typedef LLCore::Function<void(LLCore::Scalar_ConstParameter simulationDeltaTime)>                                      PostSceneCharactersCallback;
    void                                                                                                                   bindSceneCharactersCallback(const SceneCharactersCallback& callbackFunc);
    void                                                                                                                   unbindSceneCharactersCallback(const SceneCharactersCallback& callbackFunc);
    void                                                                                                                   bindPostSceneCharactersCallback(const PostSceneCharactersCallback& callbackFunc);
    void                                                                                                                   unbindPostSceneCharactersCallback(const PostSceneCharactersCallback& callbackFunc);

    typedef LLCore::Function<void(LLCore::Scalar_ConstParameter simulationDeltaTime, int64 frame)> PostSimulationStepCallback;
    void                                                                                           bindPostSimulationStepCallback(const PostSimulationStepCallback& callbackFunc);
    void                                                                                           unbindPostSimulationStepCallback(const PostSimulationStepCallback& callbackFunc);

    // Queries
    //
    struct CameraCollisionQuery
    {
        LLCore::Vector4        m_cameraFrom;
        LLCore::Vector4        m_cameraTo;
        LLGems::EuclideanBasis m_cameraBasis;
        LLCore::Scalar         m_diagonalFovInRadians;
        LLCore::Scalar         m_nearPlane;
        uint32                 m_collisionFilterInfo;

        LLCore::Array<EngineSimulation::BodyId> ignoredBodyIds;
    };

    // Cast the camera near plane represented as a thin box shape
    LLCore::Vector4 castCamera(const CameraCollisionQuery& query, LLCore::Scalar_ConstParameter thickness = 0.05f) const;

    // Cast the camera represented as a sphere
    LLCore::Vector4 castCameraSphere(LLCore::Vector4_ConstParameter startingPos, LLCore::Vector4_ConstParameter endingPos, LLCore::Scalar_ConstParameter radius, uint32 collisionFilterInfo) const;

    // Cast a sphere through the world
    LLCore::Vector4 linearCast(LLCore::Vector4_ConstParameter startingPos, LLCore::Vector4_ConstParameter endingPos, LLCore::Scalar_ConstParameter radius, const LLCore::Array<EngineSimulation::BodyId>& ignoredBodyIds) const;

    // Accessors
    //
    LLCORE_FORCE_INLINE LLCore::Duration getDeltaTime() const { return m_deltaTime; }
    LLCORE_FORCE_INLINE hknpWorld* getPhysicsWorld() const { return m_physicsWorld; }
    LLCORE_FORCE_INLINE hkbWorld* getBehaviorWorld() const { return m_behaviorWorld; }
    LLCORE_FORCE_INLINE LLCore::EventQueue* getSimulationEventQueue() const { return m_simulationEventQueue; }
    LLCORE_FORCE_INLINE HavokAssetUtil* getAssetUtil() const { return m_assetUtil; }
    LLCORE_FORCE_INLINE VisualDebugger* getVisualDebugger() const { return m_visualDebugger; }
    LLCORE_FORCE_INLINE RigidBodyComponentManager* getRigidBodyManager() const { return m_rigidBodyManager; }
    LLCORE_FORCE_INLINE PhysicsConstraintComponentManager* getPhysicsConstraintManager() const { return m_physicsConstraintManager; }
    LLCORE_FORCE_INLINE AnimationComponentManager* getAnimationManager() const { return m_animationManager; }
    LLCORE_FORCE_INLINE KeyframedBoneComponentManager* getKeyframedBoneManager() const { return m_keyframedBoneManager; }
    LLCORE_FORCE_INLINE CharacterComponentManager* getCharacterManager() const { return m_characterComponentManager; }
    LLCORE_FORCE_INLINE PoseComponentManager* getPoseManager() const { return m_poseManager; }
    LLCORE_FORCE_INLINE hknpPairCollisionFilter* getCollisionFilter() const { return m_baseFilter; }
    LLCORE_FORCE_INLINE WorldBehaviorPhysicsInterface* getWorldBehaviorPhysicsInterface() const { return m_behaviorPhysicsInterface; }
    LLCORE_FORCE_INLINE BehaviorAttachmentManager* getBehaviorAttachmentManager() const { return m_behaviorAttachmentManager; }
    LLCORE_FORCE_INLINE IKBodyComponentManager* getIKBodyManager() const { return m_ikBodyComponentManager; }
    LLCORE_FORCE_INLINE const FrameSynchronizer* getFrameSynchronizer() const { return m_frameSynchronizer; }
    LLCORE_FORCE_INLINE FrameSynchronizer* getFrameSynchronizer() { return m_frameSynchronizer; }
    int64                                  getCurrentFrame() const;

    void castQuery(const EngineCommon::RayCastQuery& ray, EngineCommon::HitCollectorInterface& collector) const;
    void castQuery(const EngineCommon::ShapeCastQuery& shapeQuery, EngineCommon::HitCollectorInterface& collector) const;
    void castQuery(const EngineCommon::ClosestPointsQuery& closestPointsQuery, EngineCommon::HitCollectorInterface& collector) const;

    // Wrap castQuery to proved a single way that character specific attributes can be added to the query
    template<typename T_QUERY>
    void characterCastQuery(LLGems::ComponentId characterId, T_QUERY* modifableQuery, EngineCommon::HitCollectorInterface& collector, bool useCharacterIgnoredBodies) const;

    void getMaxCollisionExtents(LLCore::Vector4* minExtent, LLCore::Vector4* maxExtent);
    void getCurrentCollisionExtents(LLCore::Vector4* minExtent, LLCore::Vector4* maxExtent);

    bool capsuleTest(const LLCore::MTransform& transform, const LLCore::Scalar& height, const LLCore::Scalar& radius, int filterInfo, const LLCore::Array<EngineSimulation::BodyId>& ignoredBodyIds);
    bool aabbTest(const LLCore::Aabb& aabb, uint32 filterInfo, const LLCore::Array<EngineSimulation::BodyId>& ignoredBodyIds, LLCore::Array<BodyId>* bodiesOut = nullptr);

    bool                isStaticOrKeyframed(EngineSimulation::BodyId bodyId) const;
    float               getBodyVolume(EngineSimulation::BodyId bodyId) const;
    LLCore::Vector4     getBodyCenter(EngineSimulation::BodyId bodyId) const;
    void                getBodyWorldSpaceAabb(BodyId bodyId, LLCore::Aabb* aabbOut) const;
    LLGems::ComponentId getBodyComponentId(EngineSimulation::BodyId bodyId) const;

    bool isCollisionEnabled(EngineSimulation::BodyId bodyA_id, EngineSimulation::BodyId bodyB_id) const;

    void addKeyframedBody(EngineSimulation::BodyId bodyId, LLCore::MTransform* drivingTransform);
    void removeKeyframedBody(EngineSimulation::BodyId bodyId);

    void                     toggleDebugCollisionGrid();
    LLCORE_FORCE_INLINE void toggleDebugDraw() { m_debugDraw = !m_debugDraw; }
    LLCORE_FORCE_INLINE bool getDebugDraw() const { return m_debugDraw; }

    //Local Interface
    float getGravityMagnitude() const;
    void  setGravityMagnitude(float g);

    //Stats
    void getStatsSummary(LLJson::JsonObject* summary) const;

    void handleSetGravityMagnitude(const SimulationMessages::SetWorldGravityMagnitude* message);

private:
    friend class UpdateKeyframedObjectsModifier;
    friend class UpdateSceneCharactersModifier;
    void processSimulationMessages();

    //Network interface
    void processSetGravityMagnitude(const SimulationMessages::SetWorldGravityMagnitude* message);

    // Havok Physics callbacks
    //
    void onPreCollide(hknpWorld* world, const hknpStepInput& stepInput);
    void onPostSolve(hknpWorld* world);
    void onBodyExitedBroadphase(const hknpEventHandlerInput& input, const hknpEvent& event);

    void firePostAnimationPrePhysicsListeners();
    void firePreSimulationStepListeners();
    void fireSceneCharacterListeners(hkbSceneCharacters& sceneCharacters, float timestep);
    void firePostSimulationStepListeners();

    void drawDebugCollisionGridLine(const LLCore::Vector4& start, const LLCore::Vector4& end);

    void onBodyBufferFullSignal(hknpWorld* npWorld, hknpBodyManager* npBodyManager);
    void onMotionBufferFullSignal(hknpWorld* npWorld, hknpMotionManager* npMotionManager);

private:
    LayerManager                                   m_layerManager;
    LLCore::Array<PostAnimationPrePhysicsCallback> m_postAnimationPrePhysicsListeners;
    LLCore::Array<PreSimulationStepCallback>       m_preSimulationStepListeners;
    LLCore::Array<SceneCharactersCallback>         m_sceneCharacterListeners;
    LLCore::Array<PostSceneCharactersCallback>     m_postSceneCharacterListeners;
    LLCore::Array<PostSimulationStepCallback>      m_postSimulationStepListeners;
    LLCore::EventQueue*                            m_simulationEventQueue;

    // Event Message Handling
    EngineCommon::FramePacketBuffer<SimulationMessages::SetWorldGravityMagnitude> m_gravityMagFrameBuffer;

    LLClientServer::Channel* m_outgoingChannel     = nullptr;
    LLCore::EventQueue*      m_broadcastEventQueue = nullptr;

    FrameSynchronizer* m_frameSynchronizer = nullptr;

    // Requested time step
    LLCore::Duration m_deltaTime;

    // Debugging
    VisualDebugger* m_visualDebugger = nullptr;

    // Loader
    HavokAssetUtil* m_assetUtil = nullptr;

    LLCore::Array<int> m_debugCollisionDisplayIds;
    bool               m_debugDraw = false;

    //
    // Worlds
    //
    hknpWorld*             m_physicsWorld       = nullptr;
    hkbWorld*              m_behaviorWorld      = nullptr;
    hkbGlobalSymbolLinker* m_globalSymbolLinker = nullptr;
    // hclWorld* m_clothWorld;
    // hkaiWorld* m_aiWorld;
    // hkdWorld* m_destructionWorld;

    //
    // Component Managers
    //
    RigidBodyComponentManager*         m_rigidBodyManager          = nullptr;
    PhysicsConstraintComponentManager* m_physicsConstraintManager  = nullptr;
    AnimationComponentManager*         m_animationManager          = nullptr;
    KeyframedBoneComponentManager*     m_keyframedBoneManager      = nullptr;
    CharacterComponentManager*         m_characterComponentManager = nullptr;
    PoseComponentManager*              m_poseManager               = nullptr;
    IKBodyComponentManager*            m_ikBodyComponentManager    = nullptr;


    // Bridge between the two
    WorldBehaviorPhysicsInterface* m_behaviorPhysicsInterface = nullptr;

    // Attachment manager
    BehaviorAttachmentManager* m_behaviorAttachmentManager = nullptr;

    // AVBD Integration
    AVBDIntegrationBridge* m_avbdBridge = nullptr;
    bool m_useAVBD = false;

    float m_maxBroadPhaseSize;

    float m_gravityMagnitude;

    // Allocators for the physics simulation
    hkFixedBlockStreamAllocator* m_persistentAllocator = nullptr;
    hkFixedBlockStreamAllocator* m_stepLocalAllocator  = nullptr;

    // Physics collision filter
    hknpPairCollisionFilter*       m_baseFilter       = nullptr;
    hknpConstraintCollisionFilter* m_constraintFilter = nullptr;

    // Behavior scene modifier
    UpdateKeyframedObjectsModifier*                                    m_updateKeyframedObjectsModifier = nullptr;
    LLCore::MapList<EngineSimulation::BodyId, LLCore::MTransform*, 16> m_keyframedBodies;
    UpdateSceneCharactersModifier*                                     m_sceneCharactersModifier = nullptr;

    //
    // Havok multithreading
    //
    hkDefaultTaskQueue* m_taskQueue  = nullptr;
    hkCpuThreadPool*    m_threadPool = nullptr;

    // LLResource
    LLResource::ResourceStoreManager* m_resourceStoreManager = nullptr;

    friend RigidBodyComponentManager;
};

template<typename T_QUERY>
void SimulationWorld::characterCastQuery(LLGems::ComponentId characterId, T_QUERY* modifableQuery, EngineCommon::HitCollectorInterface& collector, bool useCharacterIgnoredBodies) const
{
    if (useCharacterIgnoredBodies && characterId != LLGems::cInvalidComponentId)
    {
        LLCore::Array<EngineSimulation::BodyId> filteredBodies;
        m_animationManager->getFilteredBodies(characterId, &filteredBodies);
        modifableQuery->addIgnoredBodies(filteredBodies);
    }

    castQuery(*modifableQuery, collector);
}

} // namespace EngineSimulation

#endif // ENGINESIMULATION_WORLD_H
