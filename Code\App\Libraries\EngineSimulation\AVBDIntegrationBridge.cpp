#include "AVBDIntegrationBridge.h"
#include "LLCore/Vector3.h"
#include "LLCore/Vector4.h"
#include "LLCore/MTransform.h"
#include "LLCore/Quaternion.h"
#include <algorithm>

namespace EngineSimulation {

AVBDIntegrationBridge::AVBDIntegrationBridge() 
    : m_enabled(false)
    , m_initialized(false)
    , m_gravity(-9.81f) {
}

AVBDIntegrationBridge::~AVBDIntegrationBridge() {
    // Clean up all AVBD rigid bodies
    for (auto& pair : m_bodyMap) {
        if (pair.second.avbdRigid) {
            m_solver.remove(*pair.second.avbdRigid);
            delete pair.second.avbdRigid;
        }
    }
    m_bodyMap.clear();
    m_reverseMap.clear();
}

void AVBDIntegrationBridge::initialize(float gravity) {
    if (m_initialized) return;
    
    m_gravity = gravity;
    m_solver.clear();
    m_initialized = true;
}

void AVBDIntegrationBridge::addBody(RigidBodyComponent* component) {
    if (!m_initialized || !component) return;

    // Check if already added
    if (m_bodyMap.find(component) != m_bodyMap.end()) {
        return;
    }

    // Create new AVBD rigid body
    Rigid* avbdRigid = new Rigid();
    convertToAVBDRigid(component, *avbdRigid);

    // Add to solver
    m_solver.add(*avbdRigid);

    // Store mapping
    BodyMapping mapping;
    mapping.component = component;
    mapping.avbdRigid = avbdRigid;
    mapping.isActive = true;

    m_bodyMap[component] = mapping;
    m_reverseMap[avbdRigid] = component;
}

void AVBDIntegrationBridge::removeBody(RigidBodyComponent* component) {
    if (!m_initialized) return;

    auto it = m_bodyMap.find(component);
    if (it == m_bodyMap.end()) {
        return;
    }

    // Remove from solver
    m_solver.remove(*it->second.avbdRigid);

    // Clean up
    delete it->second.avbdRigid;
    m_reverseMap.erase(it->second.avbdRigid);
    m_bodyMap.erase(it);
}

void AVBDIntegrationBridge::updateBody(RigidBodyComponent* component) {
    if (!m_initialized) return;

    auto it = m_bodyMap.find(component);
    if (it == m_bodyMap.end()) {
        return;
    }

    // Update the AVBD rigid body with latest data
    convertToAVBDRigid(component, *it->second.avbdRigid);
}

void AVBDIntegrationBridge::step(float deltaTime) {
    if (!m_enabled || !m_initialized) return;

    // Apply gravity to all active bodies
    for (auto& pair : m_bodyMap) {
        if (pair.second.isActive && pair.second.avbdRigid) {
            // Apply gravity as a force
            pair.second.avbdRigid->force[1] += m_gravity * pair.second.avbdRigid->mass;
        }
    }

    // Step the AVBD solver
    m_solver.step(deltaTime);
}

void AVBDIntegrationBridge::syncTransforms() {
    if (!m_enabled || !m_initialized) return;

    // Copy transforms from AVBD back to components
    for (auto& pair : m_bodyMap) {
        if (pair.second.isActive && pair.second.avbdRigid) {
            convertFromAVBDRigid(*pair.second.avbdRigid, pair.second.component);
        }
    }
}

void AVBDIntegrationBridge::convertToAVBDRigid(RigidBodyComponent* component, Rigid& avbdRigid) {
    // Get current transform from component
    LLCore::MTransform transform = component->getTransform();
    LLCore::Vector3 position = transform.getTranslation();
    LLCore::Quaternion rotation = transform.getRotation();

    // Set AVBD rigid body properties
    avbdRigid.position[0] = position.x;
    avbdRigid.position[1] = position.y;
    avbdRigid.position[2] = position.z;
    
    avbdRigid.rotation[0] = rotation.x;
    avbdRigid.rotation[1] = rotation.y;
    avbdRigid.rotation[2] = rotation.z;
    avbdRigid.rotation[3] = rotation.w;
    
    // Get physics properties from component
    avbdRigid.mass = component->getMass();
    avbdRigid.invMass = (avbdRigid.mass > 0.0f) ? 1.0f / avbdRigid.mass : 0.0f;
    
    // Set velocity (if available)
    // Note: This would need to be extended to get actual velocity from component
    avbdRigid.velocity[0] = 0.0f;
    avbdRigid.velocity[1] = 0.0f;
    avbdRigid.velocity[2] = 0.0f;
    
    // Set angular velocity
    avbdRigid.angularVelocity[0] = 0.0f;
    avbdRigid.angularVelocity[1] = 0.0f;
    avbdRigid.angularVelocity[2] = 0.0f;
    
    // Set dimensions based on component scale
    LLCore::Vector3 scale = transform.getScale();
    avbdRigid.radius = std::max(scale.x, std::max(scale.y, scale.z)) * 0.5f; // Approximate radius
}

void AVBDIntegrationBridge::convertFromAVBDRigid(const Rigid& avbdRigid, RigidBodyComponent* component) {
    // Create new transform from AVBD data
    LLCore::MTransform newTransform;
    newTransform.setTranslation(LLCore::Vector3(avbdRigid.position[0], avbdRigid.position[1], avbdRigid.position[2]));
    newTransform.setRotation(LLCore::Quaternion(avbdRigid.rotation[0], avbdRigid.rotation[1], avbdRigid.rotation[2], avbdRigid.rotation[3]));
    
    // Apply the transform to the component
    component->setTransform(newTransform);
}

} // namespace EngineSimulation