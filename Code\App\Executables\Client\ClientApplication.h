/* (C) Linden Research, Inc. All Rights Reserved.
 *
 * Linden Research, Inc. ("Linden Lab") owns (or has the necessary rights to) all information contained herein, all of which is CONFIDENTIAL and PROPRIETARY to Linden Lab. Use of any such information is governed by the Employee Proprietary Information & Inventions Agreement you entered into upon commencement of your employment with Linden Lab. All other use, dissemination or reproduction of this information is strictly prohibited.
 *
 * NOTWITHSTANDING THE FOREGOING, ALL LINDEN LAB SOURCE CODE IS PROVIDED STRICTLY ON AN "AS IS" BASIS. LINDEN LAB MAKES NO WARRANTIES, EXPRESS, IMPLIED, STATUTORY OR OTHERWISE, INCLUDING WITHOUT LIMITATION ANY IMPLIED WARRANTIES OF TITLE, NONINFRINGEMENT, MERCHANTABILITY, ACCURACY, SATISFACTORY QUALITY OR FITNESS FOR A PARTICULAR PURPOSE. */

#pragma once

// current library
#include "AppData.h"
#include "ClientApplicationStates.h" // for AccountConnectorState
#include "ClientAutomation.h"
#include "ClientStateMachine.h"
#include "HomeSpaceTypes.h"
#include "SpeechGraphicsImplementation.h"
#include "UserData.h"
#include "VoiceManager.h"

// other libraries
#include "../AgoraStreamer/StreamerConfig.h"
#include "FtueTest.h"
#include "StreamerLauncher.h"

#include "LLApplication/ConfigWithSave.h"
#include "LLAudio/Configuration.h"
#include "LLCore/Uuid.h"
#include "LLGems/ConfigValidation.h"
#include "LLGraphicalApplication/SimpleCamera.h"
#include "LLMetrics/MetricsContext.h"
#include "LLUI/UIConfigManager.h"
#include "LLUI/UIEvents.h"

#include "AppCore/MemoryDumpHelper.h"
#include "AppCore/SansarUri.h"
#include "Chat/ChatManager.h"
#include "ClientInventory/Inventory.h"     // for Config
#include "ClientLogin/GridStatusService.h" // for Config
#include "ClientLogin/LoginManager.h"      // for Config
#include "ClientLogin/RememberMe.h"        // for Config
#include "ClientLogin/SubscriptionManager.h"
#include "ClientScripts/Compiler.h"
#include "ClientServices/Atlas3.h"
#include "ClientServices/ClientSettings.h"
#include "ClientServices/NotificationManager.h"
#include "ClientServices/PersonaCatalog.h"
#include "ClientServices/RegionConductor.h"
#include "ClientServices/SupportRequestManager.h"
#include "ClientServices/UserEmotes.h"
#include "ClientServices/UserProfileManager.h"
#include "ClientServices/VisitExperienceService.h" // for Config
#include "ClientServices/WorldInstanceManager.h"
#include "EditCommon/EditorConfig.h"
#include "EditEngine/ContentCompiler.h" // for SceneMetricsData
#include "EditWorkspace/AssetUploadManager.h"
#include "EditWorkspace/WorkspaceEvents.h" // for WorkspaceEditView
#include "Engine/AgentControllerInput.h"
#include "Engine/UserCamera.h"
#include "EngineApplication/ScreenshotManager.h"
#include "EngineCommon/SimulationFrameBuffer.h"
#include "EngineCommon/EngineOperationComplete.h"
#include "EngineRenderApplication/EngineRenderApplication.h"
#include "EngineSimulation/SimulationEvents.h"
#include "EngineSimulation/SimulationWorld.h"
#include "EngineSimulation/XSensManager.h"
#include "Identity/AccountConfigurationManager.h"
#include "Identity/Identity.h"
#include "KafkaCommon/ClientKafkaApi.h"
#include "RegionCommon/WorldStateManager.h"
#include "SansarHttp/ApiLocatorService.h"
#include "Streaming/Streaming.h"

namespace Chat
{
struct UserTypingEvent;
}

namespace ClientAudio
{
class ClientAudio;
class RefreshAudioDevicesEvent;
} // namespace ClientAudio

namespace Engine
{
class RuntimeWorld;
class RuntimeCommonAssetManager;
class AgentControllerManager;
class LocalAgentControllerManager;
class LocalAgentMessageHandler;
class EngineStreamRouter;
class UserCameraFlyBehavior;
class UserCameraNewThirdPersonBehavior;
class UserCameraScriptBehavior;
class DebugConsoleCommandManager;
} // namespace Engine

namespace SansarHttp
{
class SansarHttpManager;
}

namespace ClientHttp
{

namespace Dep
{
    class OperationQueue;
}
} // namespace ClientHttp

namespace ClientInventory
{
class MarketplaceManager;
class OrderServiceApi;
} // namespace ClientInventory

namespace ClientLogin
{
class ExperimentConfigApi;
class RemoteConfigApi;
class LatestClientVersionServiceApi;
class GridStatusServiceApi;
class RegistrationMetricsApi;
class GoldFizApi;
class GoogleAnalyticsApi;
class RegistrationServiceApi;
class CodeVerifierCache;
class SubscriptionService;
} // namespace ClientLogin

namespace ClientServices
{
struct CancelSceneLoadEvent;
class ClientMetricsManager;
class CodexNotificationsManager;
class ExperienceManager;
class FtueControlsManager;
class GiftingManager;
class KafkaManager;
class KafkaDisconnectedEvent;
class RelationshipManager;
class ScriptCompileClient;
class ThumbnailManager;
class TippingManager;
class UserBalanceManager;
class UserProfileManager;
class WebFrontend;
class WebImageManager;
struct CopySceneUriToClipboardEvent;

namespace AssetImport
{
    class Manager;
}
} // namespace ClientServices

namespace ClientVr
{
class ComfortZoneFilter;
class VrSystem;
class VrVisualizationLayer;
class VrStateChangedEvent;
} // namespace ClientVr

namespace Engine
{
class ScriptConsoleBeginRequest;
class ScriptConsoleEndRequest;
class VrCalibratedEvent;
} // namespace Engine

namespace ClientUI
{
class ClientUIManager;
class ClientUIViewFactory;
class DiagnosticsEvent;
class MarketPlaceCapabilityMapRequest;
class ScreenshotCaptureRequest;
class SetWindowTitleRequest;
} // namespace ClientUI

namespace ClientEditLocal
{
class SwitchToEditModeRequest;
class CompiledSceneEvent;
class PublishedSceneEvent;
class SwitchToRegionSelectModeRequest;
class IsDeveloperModeRequest;
} // namespace ClientEditLocal

namespace Inventory
{
class BuiltInInventory;
}

namespace LLAppService
{
class AppServiceRegistry;
}

namespace LLDataModel
{
class DataSyncRoot;
}

namespace LLInput
{
class GamepadInputManager;
}

namespace LLMedia
{
class MediaThread;
}

namespace LLMetrics
{
class MetricsManagerInterface;
}

namespace LLNetwork
{
class TelnetEvent;
class TelnetServer;
} // namespace LLNetwork

namespace LLUI
{
class ApplicationMenuToggledEvent;
class GlobalKeyPressedEvent;
} // namespace LLUI

namespace Marketplace
{
class MarketplaceManager;
}

namespace RegionCommon
{
namespace DebugServiceRequest
{
    class DeltaTimeResponse;
    class VisualDebuggerCaptureResponse;
} // namespace DebugServiceRequest
} // namespace RegionCommon


namespace Client
{
class ClientApplicationState;
class AppData;
class EngineSettingsManager;
class FtueQuestController;
class UserData;
class RegionServerDisconnectedEvent;
class UserManager;
class UserInputHintController;
class HomeSpace;
class StreamingMuxer;

class ClientApplication : public EngineRenderApplication::EngineRenderApplicationBase
{
    using BaseType = EngineRenderApplication::EngineRenderApplicationBase;

public:

    struct PerformanceStatsConfig : public LLGems::ConfigBlock<PerformanceStatsConfig>
    {
        Optional<LLCore::Path> m_config             = {{this, "config", {"Path for read-only performance stats budget config", "path"}, &LLGems::ConfigValidation::IsExistingFilePath}};
        Optional<LLCore::Path> m_displayPreferences = {{this, "displayPreferences", {"Path to location for writable performance stats display preferences", "path"}, &LLGems::ConfigValidation::IsFilePath}};
    };

    struct CommonAssetsConfig : public LLGems::ConfigBlock<CommonAssetsConfig>
    {
        Optional<LLCore::Path> m_config = {{this, "config", {"Path for common assets config file", "path"}, &LLGems::ConfigValidation::IsFilePath}};
    };

    struct NetworkingConfig : public LLGems::ConfigBlock<NetworkingConfig>
    {
        Optional<LLClientServer::LatencyTesting> m_latencyTesting = {{this, cInline}};

        Optional<bool>           m_useTcp         = {{this, "useTcp", "use TCP instead of UDP"}, false};
        Optional<bool>           m_useV4Socket    = {{this, "useV4Socket", "Force use of ipv4 sockets"}, false};
        Optional<int>            m_telnetPort     = {{this, "telnetPort", {"listening port for telnet server", "port"}}, 23001};
        Optional<bool>           m_telnetEnabled  = {{this, "telnetEnabled", "enables remote telnet connections to client"}, false};
    };

    struct AssetConfig : public LLGems::ConfigBlock<AssetConfig, LLAssetSystem::AssetManager::Config>
    {
        Optional<LLCore::Path>                              m_importContentExecutable      = {{this, "importContentExecutable", "Path to executable responsible for importing content"}, LLCore::Path("RUNTIME:ImportContent")};
        Optional<EditWorkspace::AssetUploadManager::Config> m_assetUpload                  = {{this, "assetUpload"}};
        Optional<CommonAssetsConfig>                        m_vrCommonAssets               = {{this, "vrCommonAssets", "CommonAssets initialization config"}};
        Optional<CommonAssetsConfig>                        m_sceneEditorCommonAssets      = {{this, "sceneEditorAssets", "Scene Editor Common assets initialization config"}};
        Optional<CommonAssetsConfig>                        m_runtimeCommonAssets          = {{this, "runtimeAssets", "Runtime Common assets initialization config"}};
        Multiple<LLCore::StringFixed<32>>                   m_disabledResourceTypes        = {{this, {"disabledResources", "disabledResource"}, {"Don't load resources of this type", "resource type"}}};
        Optional<LLCore::Duration>                          m_dynamicObjectDownloadTimeout = {{this, "dynamicObjectDownloadTime", "time to wait for dynamic objects to load whie loading scene"}, 30_ll_sec};
    };

    struct ConnectionConfig : public LLGems::ConfigBlock<ConnectionConfig>
    {
        Optional<bool>                                    m_useConductor          = {{this, "useConductor", "Use conductor to determine which server to connect to"}, true};
        Optional<LLCore::Uuid>                            m_localInstanceId       = {{this, "localInstanceId", {"Local regionserver instance id to report to kafkaconnector", "UUID string"}}};
        Optional<ClientServices::RegionConductor::Config> m_conductor             = {{this, "conductor"}};
        Optional<LLCore::StringFixed<24>>                 m_localServer           = {{this, "localServer", {"Connect to this specific server", "IP address"}}, "127.0.0.1"};
        Optional<int>                                     m_localServerPortOffset = {{this, "localServerPortOffset", "Offset the default port for server connections"}, 0};
        Optional<LLCore::Duration>                        m_serverConnectTimeout  = {{this, "serverConnectTimeout", {"Duration to wait while connecting to region server", "seconds"}}, 60_ll_sec};
    };

    struct AccountConfig : public LLGems::ConfigBlock<AccountConfig, Identity::AccountConfigurationManager::Config>
    {
        Optional<Identity::IdentityManager::Config> m_identity              = {{this, "identity"}};
        Optional<LLCore::StringFixed<24>>           m_kafkaConnectorAddress = {{this, "kafkaConnectorAddress", {"Connect to this specific KafkaConnector", "IP:port"}}, ""};
        Optional<LLCore::StringFixed<64>>           m_localAccountId        = {{this, "localAccountId", {"Use this accountId when connecting locally", "Tilia defined identifier string"}}, Identity::cDefaultLocalPersonaId};
        Optional<LLCore::Uuid>                      m_localPersonaId        = {{this, "localPersonaId", {"Use this personaId when connecting locally", "UUID string"}}, Identity::cDefaultLocalPersonaId};
        Optional<LLCore::StringFixed<32>>           m_localUserName         = {{this, "localUserName", {"Use this user name when connecting locally", "userName"}}, "NotLoggedIn"};
        Optional<LLCore::StringFixed<32>>           m_localPersonaName      = {{this, "localPersonaName", {"Use this persona name when connecting locally", "personaName"}}, "Generic Local Linden"};
        Optional<LLCore::StringFixed<32>>           m_localPersonaHanlde    = {{this, "localPersonaHandle", {"Use this persona handle when connecting locally", "personaHandle"}}, "generic-local-linden"};
        Multiple<LLCore::StringFixed<32>>           m_roles                 = {{this, "roles", "Use these roles when connecting locally"}};
        Optional<bool>                              m_guestModeEnabled      = {{this, {"guestModeEnabled", "guestMode"}, "Enable guest access mode"}, false};
    };

    struct VdbConfig : public LLGems::ConfigBlock<VdbConfig>
    {
        Optional<bool>                                   m_enable         = {{this, "enable"}, false};
        Multiple<LLCore::StringFixed<32>, AnyAmount, 16> m_captureViewers = {{this, "captureViewers"}};
    };

    struct VrConfig : public LLGems::ConfigBlock<VrConfig>
    {
        Optional<bool>  m_defaultVrActive                  = {{this, "startActive", "Start application with VR enabled"}, false};
        Optional<float> m_hmdPixelRate                     = {{this, "hmdPixelRate", {"Amount of over/undersampling of image contents for hmd display", "multiplier"}, LLGems::ConfigValidation::MakeRangeValidator(0.5f, 2.f)}, 1.f};
        Optional<bool>  m_constrainMouseInHmd              = {{this, "constrainMouseInHmd", "Constrain the mouse inside the window when in HMD mode"}, true};
        Optional<float> m_strangerComfortZoneDistThreshold = {{this, "strangerComfortZoneDistThreshold"}, 0.0f};
        Optional<float> m_friendComfortZoneDistThreshold   = {{this, "friendComfortZoneDistThreshold"}, 0.0f};
        Optional<bool>  m_showAvatarInFirstPerson          = {{this, "showAvatarInFirstPerson"}, true};
        Optional<bool>  m_rememberCalibration              = {{this, "rememberCalibration"}, true};
        Optional<float> m_lastMeasuredUserHeight           = {{this, "lastMeasuredUserHeight"}, 0.0f};
        Optional<bool>  m_displayHeightInMeters            = {{this, "displayHeightInMeters"}, false};
        Optional<int>   m_vrSnapTurnAngleDelta             = {{this, "vrSnapTurnAngleDelta"}, 0};
    };

    struct SocialConfig : public LLGems::ConfigBlock<SocialConfig>
    {
        Optional<bool> m_enableTypingIndicator       = {{this, "enableTypingIndicator", "Show typing indicator"}, true};
        Optional<bool> m_enableVoiceIndicator        = {{this, "enableVoiceIndicator", "Show voice indicator"}, true};
        Optional<bool> m_enableVisualEmoteTriggering = {{this, "enableVisualEmoteTriggering", "Enables use of the emotes panel"}, true};
    };

    struct ThirdPersonCameraConfig : public LLGems::ConfigBlock<ThirdPersonCameraConfig>
    {
        Optional<float> m_initialForwardDistance = {{this, "initialForwardDistance", "Initial forward distance from the avatar (meters)"}, 1.75f};
        Optional<float> m_verticalDistance       = {{this, "verticalDistance", "Vertical distance from avatar's head (meters)"}, 0.25f};
        Optional<float> m_horizontalOffset       = {{this, "horizontalOffset", "Horizontal offset from the avatar (screen units -1 <-> 1)"}, 0.65f};
        Optional<float> m_smoothingRigidity      = {{this, "smoothingRigidity", "Determines how rigidly the camera follows the avatar (1 - very rigid, 0 - loose)"}, 0.8f};
        Optional<float> m_initialPitch           = {{this, "initialPitch", "Initial camera pitch (degrees)"}, -10.0f};
    };

    struct CameraConfig : public LLGems::ConfigBlock<CameraConfig>
    {
        Optional<Engine::ControlMode>     m_cameraModeOverride                   = {{this, "cameraMode"}, Engine::ControlMode::cNone};
        Macro                             m_flyCamera                            = {{this, "flyCamera"}, [](CameraConfig* config) { config->m_cameraModeOverride = Engine::ControlMode::cFlyCam; }};
        Macro                             m_firstPersonCamera                    = {{this, "firstPersonCamera"}, [](CameraConfig* config) { config->m_cameraModeOverride = Engine::ControlMode::cFirstPerson; }};
        Macro                             m_thirdPersonCamera                    = {{this, "thirdPersonCamera"}, [](CameraConfig* config) { config->m_cameraModeOverride = Engine::ControlMode::cThirdPerson; }};
        Optional<LLCore::Vector4>         m_cameraPosition                       = {{this, "cameraPosition", "This is the default camera position for the application (Including VR login, home space, and loading)"}, 0.0f, 0.0f, 2.0f, 1.0f};
        Optional<LLCore::Vector4>         m_cameraLookAt                         = {{this, "cameraLookAt"}, -7.5f, 45.f, 90.f, 1.0f};
        Optional<LLCore::Vector4>         m_cameraUp                             = {{this, "cameraUp"}, 20.f, 0.f, .5f, 0.0f};
        Optional<uint>                    m_panoramaSegmentCount                 = {{this, "panoramaSegmentCount", "Number of planar projections to be stitched (0 to disable)"}, 0};
        Optional<float>                   m_panoramaHorizontalFieldOfViewDegrees = {{this, "panoramaHorizontalFieldOfViewDegrees", "Horizontal field of view for panorama rendering, in degrees"}, 178.f};
        Optional<float>                   m_firstPersonDefaultCameraHeight       = {{this, "firstPersonCameraHeight"}, 1.7f};
        Optional<float>                   m_yawVelocityMultiplier                = {{this, "yawVelocityMultiplier"}, 1.0f};
        Optional<float>                   m_pitchVelocityMultiplier              = {{this, "pitchVelocityMultiplier"}, 1.0f};
        Optional<float>                   m_flyCameraVelocityDampingConstant     = {{this, "flyCameraVelocityDampingConstant", "Spring constant for damping movement"}, 5.0f};
        Optional<float>                   m_flyCameraPitchDampingConstant        = {{this, "flyCameraPitchDampingConstant", "Spring constant for damping pitch"}, 5.0f};
        Optional<float>                   m_flyCameraYawDampingConstant          = {{this, "flyCameraYawDampingConstant", "Spring constant for damping yaw"}, 5.0f};
        Optional<int>                     m_flyCameraMoveSpeedScaleCounter       = {{this, "flyCameraMoveSpeedScaleCounter"}, 0};
        Optional<bool>                    m_enableExperimentalThirdPersonCamera  = {{this, "enableExperimentalThirdPersonCamera"}, true};
        Optional<bool>                    m_enableMouseLookMode                  = {{this, "enableMouseLookMode"}, true};
        Optional<ThirdPersonCameraConfig> m_thirdPersonConfig                    = {{this, "thirdPerson", "Third person camera initialization config."}};
    };

    struct ThirdPersonControlsConfig : public LLGems::ConfigBlock<ThirdPersonControlsConfig>
    {
        Optional<bool>  m_keyboardTurnEnabled = {{this, "keyboardTurnEnabled"}, true};
        Optional<bool>  m_faceForwardEnabled  = {{this, "faceForwardEnabled"}, false};
        Optional<float> m_turnStartDelay      = {{this, "turnStartDelay"}, 0.0f};
        Optional<float> m_turnSpeed           = {{this, "turnSpeed"}, 0.03f};
    };

    struct ControlsConfig : public LLGems::ConfigBlock<ControlsConfig>
    {
        Optional<ThirdPersonControlsConfig> m_thirdPersonConfig          = {{this, "thirdPerson", "Third person controls configuration."}};
        Optional<bool>                      m_enableToggleRunning        = {{this, "enableToggleRunning"}, true};
        Optional<bool>                      m_enableJumping              = {{this, "enableJumping"}, true};
        Optional<bool>                      m_desktopThrowTurnWithCamera = {{this, "desktopThrowTurnWithCamera"}, true};
        Optional<bool>                      m_enableCrouching            = {{this, "enableCrouching"}, true};
        Optional<bool>                      m_enableFlying               = {{this, "enableFlying"}, true};
    };

    struct AvatarConfig : public LLGems::ConfigBlock<AvatarConfig>
    {
        Optional<LLResource::ResourceId> m_defaultAvatarResourceId = {{this, "defaultAvatarResourceId", "ResourceId for avatar for local region"}};
        Optional<int>                    m_defaultAvatarId         = {{this, "defaultAvatarId", "The four avatar descriptor values: face, body, hair, clothing"}, 1};
    };

    struct EditConfig : public LLGems::ConfigBlock<EditConfig, EditCommon::EditorConfig>
    {
        //Edit mode
        Optional<EditWorkspace::WorkspaceEditView> m_mode = {{this, "mode"}};
        // todo - Layout Editor does not currently work through command line.
        //Macro                                    m_layout    = {{this, "layout"}, [](EditConfig* config) { config->m_mode = EditWorkspace::WorkspaceEditView::cWorkspaceEditView_Layout; }};
        Macro m_character = {{this, "character"}, [](EditConfig* config) { config->m_mode = EditWorkspace::WorkspaceEditView::cWorkspaceEditView_CharacterLookBook; }};
    };

    struct ScriptConfig : public LLGems::ConfigBlock<ScriptConfig>
    {
        Optional<bool>                                   m_debugConsole            = {{this, "debugConsole"}, true};
        Optional<bool>                                   m_enableDeveloperCommands = {{this, "enableDeveloperCommands", "Enable developer commands in the debug console"}, false};
        Multiple<LLCore::StringFixed<32>, AnyAmount, 32> m_commandWhitelist        = {{this, "commandWhitelist", "Input binding commands that can be used by scripts."}};
    };


    struct RegionDbConfig : public LLGems::ConfigBlock<RegionDbConfig>
    {
        Multiple<LLCore::StringFixed<32>, AtLeast<1>> m_regionList = {{this, "regionList"}, {"en-US"_ll}};
    };

    struct TutorialConfig : public LLGems::ConfigBlock<TutorialConfig>
    {
        Optional<bool>             m_enableUserInputHints   = {{this, "enableUserInputHints"}, true};
        Optional<LLCore::Path>     m_userInputHintsConfig   = {{this, "userInputHintConfig", {"Path for user input hints config file", "path"}, &LLGems::ConfigValidation::IsFilePath}};
        Optional<LLCore::Path>     m_userInputHintsProgress = {{this, "userInputHintProgress", {"Path for user input hints progess file", "path"}, &LLGems::ConfigValidation::IsFilePath}};
        Optional<bool>             m_resetUserInputHints    = {{this, "resetUserInputHints"}, false};
        Mandatory<LLHttp::HttpUrl> m_ftueExperience         = {{this, "ftueExperience"}};
    };

    struct HomeSpaceConfig : public LLGems::ConfigBlock<HomeSpaceConfig>
    {
        Mandatory<HomeSpaceSceneConfig>    m_homeSpaceScene    = {{this, "homeSpaceScene"}};
        Mandatory<HomeSpaceSceneConfig>    m_dressingRoomScene = {{this, "dressingRoomScene"}};
        Mandatory<HomeSpaceSceneConfig>    m_loadingScene      = {{this, "loadingScene"}};
        Mandatory<LLCore::StringFixed<32>> m_socialHub         = {{this, {"socialHub", "nexus"}}};
    };

    struct UIConfig : public LLGems::ConfigBlock<UIConfig, LLUI::UIConfig>
    {
        Optional<bool> m_enableUi                      = {{this, {"enableUI", "enabled", "enable"}}, true};
        Optional<bool> m_isAvatarIdentifyAvailable     = {{this, "isAvatarIdentifyAvailable", "Show the AvatarIdentifyUI in World"}, true};
        Optional<bool> m_showNewCharacterEditorButtons = {{this, "showCharacterEditorButtons", "Show the AppLauncher buttons for the new character Editor"}, false};
        Optional<bool> m_enableObjectInteractions      = {{this, "enableObjectInteractions", "Enable the ability to interact with scripted objects"}, true};
        Optional<bool> m_enableGrab                    = {{this, "enableGrab", "Enable the ability to pick up objects"}, true};

        Optional<bool>         m_enableToastNotifications      = {{this, "enableToastNotifications", "Enable message notifications"}, true};
        Optional<LLCore::Path> m_heatmapDataFile               = {{this, "heatmapDataFile", {"Path for displaying UI heatmap data", "heatmapData"}, &LLGems::ConfigValidation::IsExistingFilePath}};
        Optional<bool>         m_streamingMode                 = {{this, "streamingMode"}, false};
        Optional<bool>         m_inputHintsEnabled             = {{this, "inputHintsEnabled"}, true};
        Optional<bool>         m_allowScriptedShowWorldDetails = {{this, "allowScriptedShowWorldDetails"}, true};
        Optional<bool>         m_tvModeEnabled                 = {{this, {"tvModeEnabled", "tvMode"}, "Sets the UI to TV mode layout"}, false};
        Optional<bool>         m_gameCastModeEnabled          = {{this, {"gameCastModeEnabled", "gameCastMode"}, "Sets the UI to game cast mode layout"}, false};

        Optional<LLCore::String> m_panelBackgroundColor = {{this, "uiColor", "Sets the UI panel color (hex format)"}, "#EE2D3940"};
        Optional<bool>           m_useDefaultBackgroundColor = {{this, "useDefaultBackgroundColor", "Use the default background color for the UI"}, true};
    };

    //various automated tests to run
    struct TestConfig : public LLGems::ConfigBlock<TestConfig, BaseType::Config>
    {
        Optional<bool> m_ftue = {{this, "ftue"}, false};
    };

    struct XSensConfig : public LLGems::ConfigBlock<XSensConfig>
    {
        Optional<bool>  m_active = {{this, "active"}, false};
        Optional<int32> m_port   = {{this, "port"}, 9763};
    };

    struct VideoStreamConfig : public LLGems::ConfigBlock<VideoStreamConfig>
    {
        Optional<bool> m_enabled = {{this, "enabled"}, false};
    };


    struct ClientConfig : public LLGems::ConfigBlock<ClientConfig, BaseType::Config>
    {
        Optional<AppData::Config>  m_appData    = {{this, "appData"}};
        Optional<UserData::Config> m_userData   = {{this, "userData"}};
        Optional<bool>             m_isFirstRun = {{this, "firstRun", " This will be set to false when client has successfully shut down first time. "}, true}; // default is true since first time application is run, this field wont have been modified

        Optional<LLHttp::HttpUrl>          m_sceneUri  = {{this, {"sceneUri", "sceneurl", "uri", "url", cPositional}, "URI of a scene to which to connect"}};
        Optional<LLCore::StringFixed<128>> m_runSource = {{this, "runSource", "Source from which Sansar is run from"}};

        Optional<ClientServices::ClientStartWorld> m_defaultStartWorld = {{this, "defaultStartWorld", "Where the player starts after logging in"}, ClientServices::ClientStartWorld::cNexus};

        // hack to workaround miscalculated window dimensions...remove once issues have been fixed
        Optional<bool> m_ignoreWindowPositionAndSize = {{this, "ignoreWindowPositionAndSize"}, false};

        Optional<Inventory::InventoryManager::Config>            m_inventory                  = {{this, "inventory"}};
        Optional<ClientServices::AtlasManager::Config>           m_atlasManager               = {{this, "atlas"}};
        Optional<ClientServices::SupportRequestManager::Config>  m_supportRequestManager      = {{this, "support"}};
        Optional<ClientServices::PersonaCatalog::Config>         m_personaCatalog             = {{this, "personaCatalog"}};
        Optional<SansarHttp::ApiLocatorService::Config>          m_apiService                 = {{this, "apiLocatorService"}};
        Optional<ClientLogin::HttpGridStatusService::Config>     m_gridStatusService          = {{this, "gridStatusService"}};
        Optional<ClientLogin::RememberMe::Config>                m_rememberMe                 = {{this, "rememberMe"}};
        Optional<LLMedia::Media::Config>                         m_media                      = {{this, "media", "Web content and other media parameters"}};
        Optional<RegionDbConfig>                                 m_regions                    = {{this, "region", "List of available localization regions"}};
        Optional<NetworkingConfig>                               m_networking                 = {{this, "networking"}};
        Optional<AssetConfig>                                    m_asset                      = {{this, "asset"}};
        Optional<ConnectionConfig>                               m_connection                 = {{this, {"connection", cInline}}};
        Optional<ClientLogin::LoginManager::Config>              m_login                      = {{this, "login"}};
        Optional<Chat::ChatManager::Config>                      m_chat                       = {{this, "chat"}};
        Optional<AccountConfig>                                  m_account                    = {{this, {"account", "accountConfig", cInline}, "Account configuration"}};
        Optional<PerformanceStatsConfig>                         m_performanceStats           = {{this, "performanceStats", "Performance Stats initialization and display configs"}};
        Optional<bool>                                           m_logFrameTiming             = {{this, "logFrameTiming"}, false};
        Optional<LLCore::Duration>                               m_metricsUpdatePeriod        = {{this, "metricsUpdatePeriod", "time between metrics snapshots", LLGems::ConfigValidation::MakeRangeValidator(5_ll_sec, LLCore::Duration::cForever)}, 60_ll_sec};
        Optional<LLCore::FileReference>                          m_stateLogFilePath           = {{this, "stateLogFilePath"}};
        Optional<LLCore::Duration>                               m_secondsBeforeShutdown      = {{this, "secondsBeforeShutdown"}, LLCore::Duration::cZero};
        Optional<bool>                                           m_shutdownOnRegionDisconnect = {{this, "shutdownOnRegionDisconnect"}, false};
        Optional<VdbConfig>                                      m_vdb                        = {{this, "vdb"}};
        Optional<VrConfig>                                       m_vr                         = {{this, "vr"}};
        Optional<SocialConfig>                                   m_social                     = {{this, "social"}};
        Optional<LLAudio::AudioConfig>                           m_audio                      = {{this, "audio"}};
        Optional<UIConfig>                                       m_ui                         = {{this, "ui"}};
        Optional<XSensConfig>                                    m_xsens                      = {{this, "xsens"}};
        Optional<CameraConfig>                                   m_camera                     = {{this, "camera"}};
        Optional<ControlsConfig>                                 m_controls                   = {{this, "controls"}};
        Optional<AvatarConfig>                                   m_avatar                     = {{this, "avatar"}};
        Optional<VoiceConfig>                                    m_voice                      = {{this, "voice"}};
        Optional<EditConfig>                                     m_edit                       = {{this, "editor"}};
        Optional<ScriptConfig>                                   m_script                     = {{this, "script"}};
        Optional<ClientScripts::Compiler::Config>                m_scriptCompilerConfig       = {{this, "scriptCompiler", "Options for the script compiler"}};
        Optional<bool>                                           m_developerMode              = {{this, {"developer", "developerKeybindings"}, "Enable developer features, including extended keybindings for debugging purposes"}, false};
        Optional<uint32>                                         m_editCommandDelay           = {{this, "editCommandDelay", "Time (in milliseconds) to delay processing of edit commands, to test server latency"}, 0};
        Optional<bool>                                           m_useLocalEditServer         = {{this, "useLocalEditServer"}, false};
        Optional<bool>                                           m_enableSteamLogin           = {{this, "enableSteamLogin"}, true};
        Optional<uint32>                                         m_steamAppId                 = {{this, "steamAppId"}, 0}; //lokii: might not need this, but putting this in case we do..
        Optional<TutorialConfig>                                 m_tutorial                   = {{this, "tutorial"}};
        Optional<bool>                                           m_enableHomeSpace            = {{this, "enableHomeSpace", "Enable home space.  Disabling shuts client down when home space would have been shown."}, true};
        Optional<HomeSpaceConfig>                                m_homeSpace                  = {{this, "homeSpace"}};
        Optional<bool>                                           m_experimentalViveBinding    = {{this, "experimentalViveBinding"}, true};
        Optional<ClientServices::VisitExperienceService::Config> m_visitExperienceService     = {{this, "visitExperience"}};
        Optional<bool>                                           m_enableSpeechGraphics       = {{this, "enableSpeechGraphics", "enable speech graphics"}, true};
        Optional<bool>                                           m_enableAudio                = {{this, "enableAudio", "enable audio"}, true};
        Optional<bool>                                           m_muteMicOnStart             = {{this, "muteMic", "mute microphone on client start"}, true};
        Optional<TestConfig>                                     m_test                       = {{this, "test", "which automated tests to run"}};
        Optional<bool>                                           m_hasDiscreteRam             = {{this, "hasDiscreteRam", "stores whether a card with dedicated graphics was found"}, true};
        Optional<bool>                                           m_hasLimitedDiscreteRam      = {{this, "hasLimitedDiscreteRam", "stores whether a card with limited VRAM was found"}, false};

        Optional<AgoraStreamer::StreamerLaunchConfig> m_streamer = {{this, {"streamer", "streaming"}}};

        Optional<ClientAutomation::Config> m_automation = {{this, "automation"}};

        // This overrides the "headless" Macro of GraphicalApplicationBase::Config.
        Macro m_runHeadlessOverride = {{this, "headless", "Run without audio or video"},
                                       [](ClientConfig* config) {
                                           // Call the "headless" Macro of GraphicalApplicationBase::Config.
                                           config->m_runHeadless.set();

                                           if (config->m_asset.m_disabledResourceTypes.isProvided() == false)
                                           {
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("GeometryResource-Resource"_ll);
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("TextureMip-Resource"_ll);
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("Texture-Resource"_ll);
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("SpeechGraphicsCharacter-Resource"_ll);
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("SpeechGraphicsAlgorithm-Resource"_ll);
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("Sound-Resource"_ll);
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("AudioGraph-Resource"_ll);
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("AudioMaterial-Resource"_ll);
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("Bank-Resource"_ll);
                                               config->m_asset.m_disabledResourceTypes.asWrite().insertLast("Environment-Resource"_ll);
                                           }
                                           // In headless mode, autoLogin should default to true.
                                           if (!config->m_login.m_autoLogin.isProvided()) config->m_login.m_autoLogin = true;

                                           // In headless mode, visiting an experience should never prompt.
                                           if (!config->m_visitExperienceService.m_isPromptingEnabled.isProvided()) config->m_visitExperienceService.m_isPromptingEnabled = false;

                                           // In headless mode, entering home space should shutdown instead.
                                           if (!config->m_enableHomeSpace.isProvided()) config->m_enableHomeSpace = false;

                                           // In headless mode, don't use speechgraphics
                                           if (!config->m_enableSpeechGraphics.isProvided()) config->m_enableSpeechGraphics = false;

                                           if (config->m_enableAudio.isProvided() == false) config->m_enableAudio = false;
                                           if (config->m_enableVR.isProvided() == false) config->m_enableVR = false;
                                       }};

        Macro m_noAudio = {{this, "noAudio", "Run without audio enabled"},
                           [](ClientConfig* config) {
                               if (config->m_enableAudio.isProvided() == false) config->m_enableAudio = false;
                           }};
    };

    using Config = LLApplication::ConfigWithSave<ClientConfig>;

    static bool Configure(const LLCore::CommandLineArguments& args, LLCore::Allocator* configAllocator, Config* config);


    ClientApplication(Config*);
    virtual ~ClientApplication();

    virtual void runFrame() override;

    void giveTimeToHeartbeatMetric();
    void sendHeartbeatMetric();

    LLUI::UISystem*     getUISystem();
    LLCore::EventQueue* getUIEventQueue();
    void                createSessionDurationLimitTimer();
    void                switchUIWorkspace(uint32 workspaceHash, uint32 workspaceFlags = 0);
    void                reloadUIWorkspace();
    void                reloadUIStylesAndWorkspace();
    void                setSafeToDisplayVrUi(bool safeToDisplay);
    void                enteredInWorld();
    void                exitedInWorld();

    template<typename T_EVENT, typename... T_PARAMS>
    void postUIEvent(T_PARAMS&&... params)
    {
        if (LLCore::EventQueue* uiEventQueue = getUIEventQueue())
        {
            uiEventQueue->postEvent<T_EVENT>(static_cast<T_PARAMS&&>(params)...);
        }
    }

    template<typename T_EVENT>
    void postUIEvent(T_EVENT&& event)
    {
        if (LLCore::EventQueue* uiEventQueue = getUIEventQueue())
        {
            uiEventQueue->postEvent(LLCore::Give(event));
        }
    }

    void flushTextureStreamingQueue();

    const Config& getConfig() { return *m_config; }

    void logLoadStatistics() const;

    void processPendingOperations();

    virtual void onWindowCloseEvent(const LLWindow::WindowCloseEvent& inputEvent) override;

    LLCore::Scalar getDebugDeltaTimeForced() const { return LLCore::Scalar(m_debugDeltaTimeForced); }
    bool           isDebugDeltaTimeForced() const { return m_debugDeltaTimeForced >= 0.0f; }
    void           setDebugDeltaTimeForced(bool force, LLCore::Scalar_ConstParameter deltaTime);

    LLCore::Scalar getDebugDeltaTimeScale() const { return (m_debugDeltaTimeScale < 0.0f) ? LLCore::ScalarConstants::cOne : LLCore::Scalar(m_debugDeltaTimeScale); }
    bool           isDebugDeltaTimeScaled() const { return getDebugDeltaTimeScale() != LLCore::ScalarConstants::cOne; }
    void           setDebugDeltaTimeScale(bool scale, LLCore::Scalar_ConstParameter deltaTimeScale);

    void onUserLoggedOut();
    void onUserLoggedIntoAccount(const Identity::Account* account);
    void onUserLoggedIntoPersona(const Identity::Persona* persona);

    void handleErrorMessages();

    void handleTeleportToUri_FromUserManager(const LLCore::String& experienceUriString);
    void handleSwitchToEditMode_FromUserManager(EditWorkspace::WorkspaceEditView editView, LLCore::StringRef returnSpawnPoint);

    // Emote
    void registerEmoteListeners();

    void                                setCurrentExperienceUri(const AppCore::SansarExperienceUri& currentExperienceUri);
    const AppCore::SansarExperienceUri& getRequestedExperienceUri() const { return m_requestedExperienceUri; };

    //Edit Mode
    bool isEditViewModeCharacterEditor() const;

    void startStreamer();
    void stopStreamer();

    void showLowGraphicsModalIfNeeded();

    // Allows the client to connect to a secondary region server running
    // on one box by offsetting the bound port from the base value.
    // Only enabled when connecting to a local region server.
    int getServerPortOffset() const;

public:
    enum class LoadingPhase
    {
        cNone,
        cWaitingForRegionLaunch,
        cWaitingForRegionConnection,
        cWaitingForRegionLoad,
        cWaitingForDynamicObjects,
        cWaitingForSelfAvatar,
        cLoaded,
    };

    class LoadingProgress
    {
    public:
        LoadingPhase            m_phase;
        float                   m_totalPercentageComplete;
        LLCore::StringFixed<64> m_description;

        RegionCommon::WorldStateManager::WorldLoadStage m_stage; // stage only meaningful during RegionLoad phase.
    };

    void updateLoadingProgress(LoadingProgress* result);
    void setLoadingPhase(LoadingPhase phase);

protected:
    void onWindowResized(const LLWindow::WindowResizeEvent& event);
    void onWindowStartResize(const LLWindow::WindowStartResizeEvent& event);
    void onWindowEndResize(const LLWindow::WindowEndResizeEvent& event);

    void handleTelnetEvent(const LLNetwork::TelnetEvent& event);
    void handleSetRenderModeEvent(const EngineRender::SetRenderModeEvent& event);
    void handleRestartRenderScriptEvent(const EngineRender::RestartRenderScriptEvent& event);
    void handleDiagnosticsEvent(const ClientUI::DiagnosticsEvent& event);
    void handleMasterInstanceEvent(const MasterInstanceEvent& event);
    void handleCancelSceneLoadEvent(const ClientServices::CancelSceneLoadEvent& event);
    void handleGlobalKeyPressedEvent(const LLUI::GlobalKeyPressedEvent& event);
    void handleScreenshotByEditorEvent(const LLUI::ScreenshotByEditorRequest& event);
    void handleScreenshotByEditorEvent_RestoreCamera(const LLUI::PanelHiddenEvent& event);

    void toggleHMD();

    uint32 getLocalAccountSecret(LLCore::String* accountId, LLCore::Uuid* personaId);
    uint32 getLocalRegionSecret(LLCore::String regionName, LLCore::String* accountId, LLCore::Uuid* personaId);
    uint32 getLocalVoiceSecret(LLCore::Uuid* personaId);
    void   connectToLocalRegionServer(const AppCore::SansarExperienceUri& uri);

    virtual void onNamedPipeServerMessage(const byte* buffer, mem_int bufferSize) override;

    // WorldStateManager notification handlers
    void onAgentControllerCreated(RegionCommon::WorldStateManager* wsm, Engine::AgentControllerId agentControllerId, LLCore::Uuid personaId, Engine::ObjectId controlledObjectId);
    void onObjectInteractUpdateEvent(RegionCommon::WorldStateManager* wsm, Engine::ObjectId objectId, bool enabled);
    void onObjectInteractPromptUpdateEvent(RegionCommon::WorldStateManager* wsm, Engine::ObjectId objectId);
    void onObjectInteractCreateEvent(RegionCommon::WorldStateManager* wsm, Engine::ObjectId objectId, bool enabled);
    void onRuntimeInventorySettingsUpdated(RegionCommon::WorldStateManager* wsm);

    inline RegionCommon::WorldStateManager* getWorldStateManager() { return m_worldStateManager; }
    inline Engine::RuntimeWorld*            getRuntimeWorld() { return m_worldStateManager ? m_worldStateManager->getRuntimeWorld() : nullptr; }

    void onSelfAvatarReady(Engine::AgentControllerManager* acm);
    bool isSelf(const LLCore::Uuid& personaId);

    void destroyLacm();
    void createLacm(Engine::RuntimeWorld* runtimeWorld, ClientVr::VrVisualizationLayer* vrVirtualizationLayer);
    void initUserCamera(Engine::RuntimeWorld* runtimeWorld, bool allowsFlyCamera);

    void updateComfortZone(const Engine::CameraComponent* camera, Engine::RuntimeWorld* runtimeWorld, ClientVr::ComfortZoneFilter* filter) const;
    void refreshVrState();

    void blockUser(const LLCore::Uuid& personaId);
    void unblockUser(const LLCore::Uuid& personaId);

    void ensureHomeSpace(HomeSpaceScene scene, LLCore::Function<void()> callback = []() {});
    void destroyHomeSpace();
    bool canRenderHomeSpace() const;
    void loadHomeSpaceAvatar(const LLResource::ResourceId& avatarResourceId);
    void onLoadingSceneReady();

    // EngineRenderApplicationBase overrides
    EngineRender::RenderWorld*        getRenderWorld() override final;
    LLResource::ResourceStoreManager* getResourceStoreManager() override final { return m_resourceStoreManager; }
    void                              initializeRenderViews(LLGems::ComponentHandle<Engine::CameraComponent> const& cameraComponent, LLGraphicsStream::FrameBuilder* frameBuilder, LLCore::Array<EngineRender::ViewContext>* renderViews) override final;
    void                              initializeScriptViews(EngineRender::ViewContext const* view, LLCore::Array<LLGraphicsScript::View>* scriptViews) override final;
    Engine::VrSystemInterface*        createVrSystem() override final;
    void                              adjustRenderScriptCustomConfig(EngineRender::RenderScriptConfig* customConfig) const override final;
    void                              adjustRenderScriptCustomParams(EngineRender::RenderScriptParams* customParams) const override final;
    void                              onGraphicsSystemShutdownComplete() override;

    void startRendering();
    void shutdownRendering();

    void onEngineSettingsChanged(Engine::EngineSettingsChanged const& response);

    void onApiLocatorLoadedEvent(const SansarHttp::ApiLocatorLoadedEvent& event);

    void registerInputBindings();

    void registerProductionBindings();
    void registerProductionCallbacks();

    void registerDebugBindings();
    void registerDebugCallbacks();

    void initializeAssetSystem();
    void terminateAssetSystem();
    void terminateResourceJobs();
    void clearScriptConsole();

    void parseLoginFromSceneUri(const LLHttp::HttpUrl& sceneUriString);

    template<typename... T_FUNCTION_ARGS>
    void registerVrModeOnlyCallback(const LLCore::String& eventName, T_FUNCTION_ARGS&&... args);
    template<typename T_EVENT>
    void registerVrModeOnlyCallbackImpl(const LLCore::String& eventName, LLCore::Function<void(const T_EVENT&)>&& callback);
    template<typename... T_FUNCTION_ARGS>
    void registerVrModeOnlyOneTimeCallback(const LLCore::String& eventName, T_FUNCTION_ARGS&&... args);
    template<typename T_EVENT>
    void registerVrModeOnlyOneTimeCallbackImpl(const LLCore::String& eventName, LLCore::Function<void(const T_EVENT&)>&& callback);

    void handleSetAgentController(const ClientRegionMessages::SetAgentController* packet);
    void setAgentController(const ClientRegionMessages::SetAgentController& packet);

    void handleClientKickNotification(const ClientRegionMessages::ClientKickNotification* packet);
    void handleClientSmiteNotification(const ClientRegionMessages::ClientSmiteNotification* packet);
    void handleClientBanNotification(const KafkaCommon::ClientNotificationEvent& notification);
    void handleClientRuntimeInventoryUpdatedNotification(const ClientRegionMessages::ClientRuntimeInventoryUpdatedNotification* notification);

    void handleUnexpectedKafkaDisconnect(const ClientServices::KafkaDisconnectedEvent& event);

    void queueDisconnectNextFrame();
    void queueReconnectNextFrame();

    void requestErrorMessageDeferred(const LLCore::String& message) { m_errorMessage = message; }

    void handleEditModeViewSwitched(const ClientEditLocal::EditorModeSwitchEvent& event);
    void handleSwitchToEditModeRequest(const ClientEditLocal::SwitchToEditModeRequest& request);
    // Atlas Location switching event handler
    void handleConnectToRegionRequest(const ClientServices::ConnectToRegionRequest& request);
    void handleEditSceneRequest(const ClientServices::EditSceneRequest& request);

    void handleSwitchToRegionSelectModeRequest(const ClientEditLocal::SwitchToRegionSelectModeRequest& request);

    // Teleport to friend handling; does different work if you are in same world and instance vs different world or instance
    void handleTeleportToFriendRequest(const ClientServices::TeleportToUserRequest& request);

    // Handle playing an emote on the lacm avatar
    void handlePlayEmote(const Engine::PlayAgentAnimationRequest& request);
    void handleStopEmote(const Engine::StopAgentAnimationRequest& request);
    void handleAnimationOperationCompleteEvent(const EngineCommon::AnimationOperationCompleteEvent& event);

    //developer mode event handler
    void handleIsDeveloperModeRequest(const ClientEditLocal::IsDeveloperModeRequest& request);

    // for use by deferred login logic (not going through login UI)
    void onPersonaDataResponse(const Identity::PersonaPublicDataResponse& response);
    void onVrStateChanged(const ClientVr::VrStateChangedEvent& event);
    void onVrCalibrationComplete(const Engine::VrCalibratedEvent& event);
    void setMouseInputEnabled(bool);

    void onWindowFocusLost(const LLWindow::WindowFocusLostEvent& event);
    void onWindowFocusGained(const LLWindow::WindowFocusGainedEvent& event);

    void onScreenshotCaptureRequest(const ClientUI::ScreenshotCaptureRequest& request);
    void onMarketPlaceCapabilityMapRequest(const ClientUI::MarketPlaceCapabilityMapRequest& request);

    void onAccountConnectorReconnectResponse(const ClientServices::AccountConnectorResponse& response);
    void onConnectedToAccountEvent(const AccountConnectorState::ConnectedToAccountEvent& event);
    void onCompiledSceneEvent(const ClientEditLocal::CompiledSceneEvent& event);
    void onPublishedSceneEvent(const ClientEditLocal::PublishedSceneEvent& event);
    void onRefreshAudioDevicesEvent(const ClientAudio::RefreshAudioDevicesEvent& event);
    void handleApplicationMenuToggledEvent(const LLUI::ApplicationMenuToggledEvent& event);

    void connectToKafkaConnectorAddress(const ClientServices::ServerAddressResponse& kafkaConnectorAddress);

    void clearSceneMetrics() { m_cachedSceneMetrics = EditEngine::SceneMetricsData(); }

    void handleDebugDeltaTimeResponse(const RegionCommon::DebugServiceRequest::DeltaTimeResponse& response);
    void handleVisualDebuggerCaptureResponse(const RegionCommon::DebugServiceRequest::VisualDebuggerCaptureResponse& response);

    void toggleVisualDebuggerCapture();

    void onScriptConsoleBeginRequest(const Engine::ScriptConsoleBeginRequest& event);
    void onScriptConsoleEndRequest(const Engine::ScriptConsoleEndRequest& event);

    void onSetWindowTitleRequest(const ClientUI::SetWindowTitleRequest& request);

    uint8 m_exitValue = 0;

    void requestShutdown(uint8 exitValue = 0) override;

    void removeAllDebugCommands();

    LLMetrics::Context<LLApplication::EventMetrics, LLAudio::EventMetrics> m_heartbeatContext;

    void onLoginDidLogoutEvent();

    static void UpdatePersonaRoot(const Identity::Persona* persona);

    // ClientApplicationStates are friends as they need access to all the managers; they are classes merely to help encapsulate state-specific data.
    // These are all defined in ClientApplicationStates.cpp
    friend class ClientStateMachine;
    friend class ClientApplicationState;
    friend class StartState;
    friend class ContactServicesState;
    friend class LoginAccountState;
    friend class CreatePersonaState;
    friend class ConnectToRegionState;
    friend class ShutdownState;
    friend class AccountConnectorState;
    friend class HomeSpaceState;
    friend class InWorldState;
    friend class EditModeState;

    friend class EditServerProxy;

    friend class HomeSpace;
    friend class FtueQuestController;

    struct StartingUp : public LLGems::State<ClientApplication>
    {
        void giveTime() override;
    };

    struct DisconnectedFromRegion : public LLGems::State<ClientApplication>
    {
        DisconnectedFromRegion();
        ~DisconnectedFromRegion();
        void handleVoiceRequestStatus(const ClientServices::VoiceRequestStatus& event);
    };

    struct RequestingRegionAndVoiceServerInfo : public LLGems::State<ClientApplication>
    {
        RequestingRegionAndVoiceServerInfo();

        void onRegionConnectionResponse(const ClientServices::RegionConnectionResponse& response);

        ~RequestingRegionAndVoiceServerInfo();
    };

    struct ConnectingToRegionAndVoiceServers : public LLGems::State<ClientApplication>
    {
        ConnectingToRegionAndVoiceServers(LLCore::StringRef          userName,
                                          const LLCore::String&      accountId,
                                          const LLCore::Uuid&        personaId,
                                          const LLCore::Uuid&        instanceID,
                                          const uint32               sharedRegionSecret,
                                          LLCore::StringRef          serverAddress,
                                          const uint32               sharedVoiceSecret,
                                          LLCore::StringRef          voiceServerAddress,
                                          const VoiceManager::Cinfo& voiceManagerCinfo,
                                          uint32                     characterTextureMemoryBudget);

        void giveTime() override;

        void onCreateWorld(RegionCommon::WorldStateManager* wsm);
        void onWorldCreateFailed(RegionCommon::WorldStateManager* wsm, RegionCommon::WorldStateManagerFailureState failureState);
        void onDestroyWorld(RegionCommon::WorldStateManager* wsm);

        void handleUserLoggedIntoRegion(const UserLoggedIntoRegionEvent& event);
        void onRegionWorldDefinitionResponse(const ClientServices::RegionWorldDefinitionResponse& response);
        void handlePortalEventRequest(const ClientServices::PortalEventRequest& request);
        void handleVoiceRequestStatus(const ClientServices::VoiceRequestStatus& event);
        void onRefreshAudioDevicesEvent(const ClientAudio::RefreshAudioDevicesEvent& event);
        void handleClientMuteNotification(const ClientRegionMessages::ClientMuteNotification* packet);
        void handleClientVoiceBroadcastStartNotification(const ClientRegionMessages::ClientVoiceBroadcastStartNotification* packet);
        void handleClientVoiceBroadcastStopNotification(const ClientRegionMessages::ClientVoiceBroadcastStopNotification* packet);
        void handleClientSetRegionBroadcasted(const ClientRegionMessages::ClientSetRegionBroadcasted* packet);
        void onUserTypingEvent(const Chat::UserTypingEvent& event);
        void onFriendStatusByPersonaIdUpdateEvent(const ClientServices::FriendStatusByPersonaIdUpdateEvent& event);

        bool allowFreeCamera();
        bool enableTpToFreeCamera();
        bool allowBlocking();
        void enteredInWorld();
        void avatarLoadingFinished();

        void hintWorldDefinitionLoad(LLResource::ResourceId worldDefinitionId);
        void hintWorldDefinitionLoadClear();

        void onVrStateChanged(const ClientVr::VrStateChangedEvent& event);

        ~ConnectingToRegionAndVoiceServers();

    public:
        struct LoadingRegion : public LLGems::State<ConnectingToRegionAndVoiceServers>
        {
        };

    private:
        LLGems::StateMachine<LoadingRegion>                                              m_loadingRegionStateMachine;
        UserManager                                                                      m_userManager;
        VoiceManager                                                                     m_voiceManager;
        LLCore::DeferredConstruction<SpeechGraphicsImplementation>                       m_speechGraphicsImplementation;
        LLInput::CallbackToken*                                                          m_toggleMicBinding;
        LLInput::CallbackToken*                                                          m_pushToTalkBinding;
        bool                                                                             m_initialChunkSubscribed = false;
        LLCore::PrecisionTime                                                            m_waitForWorldReadyTimeout;
        LLCore::Future<LLResource::ResourceLoader::Result<Engine::WorldDefinition>>      m_hintWorldDefinitionFuture;
        LLCore::Future<LLResource::ResourceLoader::Result<Engine::WorldChunkDefinition>> m_hintWorldChunkFuture;
        bool                                                                             m_isMicMuted          = false;
        bool                                                                             m_isMicMutedFromAdmin = false;
    };

    LLGems::StateMachine<StartingUp> m_connectToRegionStateMachine;

    ClientStateMachine m_clientStateMachine;
    inline void        requestState(ApplicationStateId nextState) { m_clientStateMachine.requestState(nextState); }
    inline void        requestStateDeferred(ApplicationStateId nextState) { m_clientStateMachine.requestStateDeferred(nextState); }

    struct Graphics
    {
        LLCore::RefPointer<LLGraphicsResource::PackageData const> m_commonPackage;
        LLCore::RefPointer<LLGraphicsResource::PackageData const> m_uiPackage;
        LLCore::RefPointer<LLGraphics::WindowSurface>             m_mainWindowSurface;
    };

    ClientAutomation m_automation;

    // Manager objects which live for the life of the client can be put in the appData.
    // GridStatus and RemoteConfig are good examples.
    LLCore::OwnedPointer<AppData> m_appData;

    // Manager objects which live for the life of the login session can be put in the userData.
    // This object is created as a response to completing login.
    // This object is destroyed as a response to logout.
    LLCore::OwnedPointer<UserData> m_userData;

    // Data models.
    LLCore::OwnedPointer<ClientLogin::RememberMe>   m_rememberMe;
    LLCore::OwnedPointer<LLDataModel::DataSyncRoot> m_dataSyncRoot;

    // The AppServiceRegistry instance followed by all application level services.
    // The services should be listed here in the order in which they are allocated and installed.
    LLCore::OwnedPointer<LLAppService::AppServiceRegistry>           m_appServiceRegistry;
    LLCore::OwnedPointer<ClientLogin::GridStatusServiceApi>          m_gridStatusService;
    LLCore::OwnedPointer<ClientLogin::LatestClientVersionServiceApi> m_latestClientVersionService;
    LLCore::OwnedPointer<ClientLogin::RegistrationServiceApi>        m_registrationService;
    LLCore::OwnedPointer<ClientLogin::RemoteConfigApi>               m_remoteConfig;
    LLCore::OwnedPointer<ClientLogin::GoldFizApi>                    m_goldFizService;
    LLCore::OwnedPointer<ClientLogin::GoogleAnalyticsApi>            m_googleAnalytics;
    LLCore::OwnedPointer<ClientLogin::ExperimentConfigApi>           m_experimentConfig;
    LLCore::OwnedPointer<ClientLogin::RegistrationMetricsApi>        m_registrationMetrics;
    LLCore::OwnedPointer<ClientInventory::OrderServiceApi>           m_orderService;
    LLCore::OwnedPointer<ClientServices::VisitExperienceServiceApi>  m_visitExperienceService;
    LLCore::OwnedPointer<ClientLogin::SubscriptionService>           m_subscriptionService;

    // The Managers should be listed here, after services.
    LLCore::OwnedPointer<ClientServices::WorldInstanceManager> m_worldInstanceManager;

    void resetLoadingPhaseAssetBasis();

    LLAssetSystem::AssetManager::Statistics m_loadStatisticsStart;
    LoadingPhase                            m_currentLoadingPhase = LoadingPhase::cNone;
    float                                   m_currentLoadingPhaseProgress;
    float                                   m_finalizeLoadingPhaseProgressSpeed;
    LLCore::Duration                        m_currentLoadingDuration;
    int                                     m_currentLoadingPhaseAssetManagerBasis = 0;
    LLCore::PrecisionStopwatch              m_currentLoadingPhaseTimer;

    LLHttp::HttpManager*                        m_httpManager       = nullptr;
    SansarHttp::DeprecatedDuplicateSessionData* m_depDupSessionData = nullptr;
    SansarHttp::SansarHttpManager*              m_clientHttpManager = nullptr;

    ClientHttp::Dep::OperationQueue* m_operationQueue = nullptr;

    Config*                                m_config;
    LLCore::HardwareInfo::HardwareSnapshot m_clientHardwareInfo;

    LLInput::GamepadInputManager*         m_gamepadManager            = nullptr;
    ClientVr::VrSystem*                   m_vrSystem                  = nullptr;
    ClientVr::VrVisualizationLayer*       m_vrVisualizationLayer      = nullptr;
    ClientVr::ComfortZoneFilter*          m_comfortZoneFilter         = nullptr;
    ClientServices::RelationshipManager*  m_relationshipManager       = nullptr;
    Chat::ChatManager*                    m_chatManager               = nullptr;
    ClientServices::NotificationManager*  m_notificationManager       = nullptr;
    ClientServices::KafkaManager*         m_accountConnector          = nullptr;
    ClientServices::ClientMetricsManager* m_metricsManager            = nullptr;
    ClientServices::UserEmotes*           m_userEmotes                = nullptr;
    LLMetrics::MetricsManagerInterface*   m_oldMetricsManager         = nullptr;
    RegionCommon::WorldStateManager*      m_worldStateManager         = nullptr;
    Engine::RuntimeCommonAssetManager*    m_runtimeCommonAssetManager = nullptr;
    Engine::LocalAgentControllerManager*  m_lacm                      = nullptr;
    Engine::LocalAgentMessageHandler*     m_lacmMessageHandler        = nullptr;
    Engine::AgentControllerId             m_localAgentId              = Engine::cInvalidAgentControllerId;
    ClientServices::RegionConductor*      m_regionConductor           = nullptr;
    Engine::UserCamera                    m_userCamera;
    Engine::ControlMode                   m_lastControlMode = Engine::ControlMode::cNone;

    EngineSettingsManager*     m_settingsManager = nullptr;
    ClientUI::ClientUIManager* m_clientUIManager = nullptr;
    // For use when UI is disabled, yet other systems are expecting a valid ui event queue
    LLCore::DeferredConstruction<LLCore::EventQueue> m_fallbackUiEventQueue;

    LLCore::RefPointer<Engine::UserCameraFlyBehavior>            m_flyCameraBehavior;
    LLCore::RefPointer<Engine::UserCameraNewThirdPersonBehavior> m_thirdPersonCameraBehavior;
    LLCore::RefPointer<Engine::UserCameraScriptBehavior>         m_scriptCameraBehavior;

    // ClientServices
    SansarHttp::ApiLocatorService*             m_apiLocatorService         = nullptr;
    ClientServices::AtlasManager*              m_atlasManager              = nullptr;
    ClientServices::ThumbnailManager*          m_thumbnailManager          = nullptr;
    ClientServices::SupportRequestManager*     m_supportRequestManager     = nullptr;
    ClientInventory::LicenseService*           m_licenseService            = nullptr;
    ClientInventory::MarketplaceManager*       m_clientMarketplace         = nullptr;
    ClientServices::PersonaCatalog*            m_personaCatalog            = nullptr;
    ClientServices::UserProfileManager*        m_userProfileManager        = nullptr;
    ClientServices::ExperienceManager*         m_experienceManager         = nullptr;
    ClientLogin::CodeVerifierCache*            m_codeVerifierCache         = nullptr;
    ClientLogin::AuthenticationManager*        m_authManager               = nullptr;
    ClientLogin::SubscriptionManager*          m_subscriptionManager       = nullptr;
    ClientLogin::LoginManager*                 m_loginManager              = nullptr;
    ClientServices::FtueControlsManager*       m_ftueControlsManager       = nullptr;
    ClientServices::GiftingManager*            m_giftingManager            = nullptr;
    ClientServices::TippingManager*            m_tippingManager            = nullptr;
    ClientScripts::Compiler*                   m_scriptCompiler            = nullptr;
    ClientServices::ScriptCompileClient*       m_compilerClient            = nullptr;
    ClientServices::AssetImport::Manager*      m_assetImportManager        = nullptr;
    ClientServices::WebFrontend*               m_webFrontend               = nullptr;
    ClientServices::WebImageManager*           m_webImageManager           = nullptr;
    ClientServices::CodexNotificationsManager* m_codexNotificationsManager = nullptr;
    EngineSimulation::XSensManager*            m_xsensManager              = nullptr;

    Identity::IdentityManager*             m_identityManager             = nullptr;
    Identity::AccountConfigurationManager* m_accountConfigurationManager = nullptr;

    Inventory::InventoryManager* m_inventoryManager = nullptr;

    Inventory::BuiltInInventory* m_builtInInventory = nullptr;


    Marketplace::MarketplaceManager* m_marketplace = nullptr;

    EditEngine::SceneMetricsData m_cachedSceneMetrics;
    LLCore::Duration             m_cachedSceneBuildTime;

    LLNetwork::TelnetServer*  m_telnetServer = nullptr;
    AppCore::MemoryDumpHelper m_memoryDumpHelper;

    bool  m_isRegionSwitching = false;
    uint8 m_renderScriptState = 0;
    bool  m_showLowGraphicsDialog = false;

    bool m_lastHDRAvailableState = false;

    LLCore::Duration m_frameSleep;
    LLCore::Duration m_frameSleepVr;
    LLCore::Duration m_frameSleepBackground;
    LLCore::Duration m_frameSleepMinimized;

    // hack: m_hasBeenInWorld drives what should happen after login such as initial UI setup
    // ex. "only show MyQuests on initial login" --  it is needed as the user may transition from
    // login/connection states to the Character Editor before entering SelectRegion/InWorld if
    // avatar upgrade or selection is needed
    bool m_hasBeenInWorld = false;

    EditWorkspace::WorkspaceEditView m_editModeView = EditWorkspace::WorkspaceEditView::cWorkspaceEditView_Undefined;


    // asset/resource related stuff
    LLAssetSystem::AssetManager*      m_assetManager           = nullptr;
    Engine::JobQueue*                 m_resourceLoaderJobQueue = nullptr;
    LLResource::ResourceStoreManager* m_resourceStoreManager   = nullptr;
    Engine::EngineResourceLoader*     m_engineResourceLoader   = nullptr;

    // Stream routing
    // @TODO: We probably want multiple of these at the World level, but we cannot do this unless we move the
    // voice manager to the same level
    Engine::EngineStreamRouter* m_streamRouter = nullptr;
#ifdef CLIENT_USE_FFMPEG
    StreamingMuxer* m_streamMuxer = nullptr;
#endif

    LLCore::PrecisionStopwatch                                                m_stopwatch;
    EngineCommon::FramePacketBuffer<ClientRegionMessages::SetAgentController> m_setAgentControllerBuffer;

    // command-line sansar URI
    AppCore::SansarExperienceUri m_requestedExperienceUri;
    AppCore::SansarSceneEditUri  m_requestedSceneEditUri;

    AppCore::SansarExperienceUri m_webTrackedExperienceUri; // only populated on first run of new sansar installation and if user wanted to visit an experience on web

    // If we failed to parse the command-line scene URI, this will be set to true. We will then notify the user after login.
    bool m_commandLineSceneUriError = false;

    // TODO: Make sure this gets cleared when we leave an experience!
    LLCore::Uuid m_currentExperienceId;
    LLCore::Uuid m_currentRegionInstanceID;

    // store this for metrics
    AppCore::SansarExperienceUri m_currentExperienceUri;
    AppCore::SansarSceneEditUri  m_currentSceneEditUri;

    LLCore::Duration m_metricsUpdatePeriod;

    bool m_usingLocalRegionConductor = false;

    // State-switching init data
    LLCore::Uuid m_sceneInventoryItemToEdit;
    LLCore::Uuid m_experienceAssociatedWithSceneEdit;

    EngineSimulation::SimulationWorld::PreSimulationStepCallback m_preSimulationCallback;

    LLGraphicalApplication::SimpleCamera m_debugCamera;
    bool                                 m_usingDebugCamera          = false;
    bool                                 m_resetDebugCameraRequested = false;
    float                                m_forcedCameraZoom          = 0.f; // debugging aid

    EngineApplication::ScreenshotManager m_screenshotManager;

    LLSteamworks::SteamworksManager* m_steamworksManager = nullptr;

    // A media thread is tied to dullahan/CEF on Windows, which can only be initialized once
    LLMedia::MediaThread*     m_mediaThread = nullptr;
    ClientAudio::ClientAudio* m_clientAudio = nullptr;

    float m_debugDeltaTimeForced = -1.0f;
    float m_debugDeltaTimeScale  = -1.0f;

    LLCore::PrecisionTime m_lastLogLoadStatistics;

    LLCore::StringFixed<16>       m_vdbCaptureStartFormatted;
    LLCore::Array<LLCore::String> m_cachedVdbViewers;

    HomeSpace* m_homeSpace = nullptr;

    // error messages
    LLCore::StringFixed<256> m_errorMessage;
    bool                     m_smiteUser;
    bool                     m_queuedDisconnect;
    bool                     m_queuedReconnect;

    // delayed reconnection attempts when account connector is lost
    int                                              m_accountConnectorRetryAttempts  = 0;
    int                                              m_maxAccountConnectorRetries     = 10;
    LLCore::Duration                                 m_accountConnectorReconnectDelay = 5_ll_sec;
    LLCore::DeferredConstruction<LLCore::AsyncTimer> m_delayedAccountConnectorReconnect;

    // session duration limiting
    LLCore::DeferredConstruction<LLCore::AsyncTimer> m_sessionDurationLimitTimer;
    LLCore::DeferredConstruction<LLCore::AsyncTimer> m_shutdownDelayTimer;

    UserInputHintController* m_inputHintController = nullptr;
    LLCore::AtomicBool       m_postponeShutdown{false};

    KafkaCommon::ClientKafkaApi m_dummyKafkaApi;

    Engine::DebugConsoleCommandManager* m_debugConsoleCommandManager = nullptr;

    // Mic
    // Muted state should persist across world visits.
    bool m_cachedIsMicMutedState = true;
    LLCore::OwnedPointer<StreamerLauncher> m_streamer;

    //Test Objects
    LLCore::RefPointer<FtueTest> m_ftueTest;

    //event for instance sharing
public:
    struct CopySceneUriToClipboardEvent
    {
    };

private:
    void copySceneUriToClipboard();
    void handleCopySceneUriToClipboard(const ClientServices::CopySceneUriToClipboardEvent& event);
    void checkForLowGraphics();
};

LLCORE_CONVERT_ENUM_AS_STRING_ORDERED(ClientApplication::LoadingPhase, "LoadingPhaseNone", "LoadingPhaseRegionLaunch", "LoadingPhaseRegionConnection", "LoadingPhaseRegionLoad", "LoadingPhaseDynamicObjects", "LoadingPhaseSelfAvatar", "LoadingPhaseLoaded");


} // namespace Client
