﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClInclude Include="SwapChain.h" />
    <ClInclude Include="Device.h" />
    <ClInclude Include="GraphicsPipelineState.h" />
    <ClInclude Include="Buffer.h" />
    <ClInclude Include="VertexArray.h" />
    <ClInclude Include="D3D11\D3D11Buffer.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11CommandQueue.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Device.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Framebuffer.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11GraphicsCommandList.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11GraphicsPipelineState.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11SwapChain.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11VertexArray.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLGraphicsPipelineState.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLSwapChain.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLVertexArray.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLBuffer.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLCommandQueue.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLDevice.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLFramebuffer.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLGraphicsCommandList.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\Win\GLWinSwapChain.h">
      <Filter>GL\Win</Filter>
    </ClInclude>
    <ClInclude Include="GL\Ios\GLIosSwapChain.h">
      <Filter>GL\Ios</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLPlatform.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalBuffer.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalCommandQueue.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalDevice.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalFramebuffer.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalGpuRefCounted.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalGpuTracker.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalGraphicsCommandList.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalGraphicsPipelineState.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalSwapChain.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalVertexArray.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Buffer.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12CommandQueue.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Device.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Framebuffer.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12GraphicsCommandList.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12GraphicsPipelineState.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12SwapChain.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12VertexArray.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalPlatform.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Platform.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="CommandAllocator.h" />
    <ClInclude Include="Metal\MetalCommandAllocator.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLCommandAllocator.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11CommandAllocator.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12CommandAllocator.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12GpuRefCounted.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12RenderTargetView.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12GpuTracker.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12RootSignature.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11RootSignature.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalRootSignature.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLRootSignature.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="ComputePipelineState.h" />
    <ClInclude Include="D3D11\D3D11ComputePipelineState.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ComputePipelineState.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="Portable.h" />
    <ClInclude Include="GL\GLRenderTargetView.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLComputePipelineState.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalComputePipelineState.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalRenderTargetView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11RenderTargetView.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="Texture2d.h" />
    <ClInclude Include="D3D11\D3D11Texture2d.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Platform.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalTexture2d.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLTexture2d.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Texture2d.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11DepthStencilView.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12DepthStencilView.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLDepthStencilView.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalDepthStencilView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12DescriptorHeap.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11DescriptorHeap.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLDescriptorHeap.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalDescriptorHeap.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11SamplerState.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11ShaderResourceView.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="SamplerState.h" />
    <ClInclude Include="ShaderResourceView.h" />
    <ClInclude Include="D3D12\D3D12ShaderResourceView.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12SamplerState.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLSamplerState.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLShaderResourceView.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalShaderResourceView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalSamplerState.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Resource.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLResource.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Resource.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalResource.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11UnorderedAccessView.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLUnorderedAccessView.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalUnorderedAccessView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12UnorderedAccessView.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11ResourceCommandList.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLResourceCommandList.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ResourceCommandList.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalResourceCommandList.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Texture3d.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Texture3d.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLTexture3d.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalTexture3d.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Typedefs.h" />
    <ClInclude Include="Framebuffer.h" />
    <ClInclude Include="GpuResource.h" />
    <ClInclude Include="D3D12\D3D12CommandSignature.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalCommandSignature.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLCommandSignature.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11CommandSignature.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="CommandSignature.h" />
    <ClInclude Include="GL\Linux\GLLinuxSwapChain.h">
      <Filter>GL\Linux</Filter>
    </ClInclude>
    <ClInclude Include="DescriptorHeap.h" />
    <ClInclude Include="D3D11\D3D11PipelineState.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12PipelineState.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLPipelineState.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalPipelineState.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11QueryHeap.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12Fence.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLFence.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Fence.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalFence.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLQueryHeap.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12QueryHeap.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalQueryHeap.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ConstantBufferView.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalConstantBufferView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLConstantBufferView.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11ConstantBufferView.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLGpuTracker.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLGpuRefCounted.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="StatKeeper.h" />
    <ClInclude Include="D3D11\D3D11ResourceBarrier.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ResourceBarrier.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="GL\GLResourceBarrier.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalResourceBarrier.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="ResourceBarrier.h" />
    <ClInclude Include="D3D12\D3D12Bundle.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="Bundle.h" />
    <ClInclude Include="GL\GLBundle.h">
      <Filter>GL</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11Bundle.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="D3D11\D3D11GpuTracker.h">
      <Filter>D3D11</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalBuffer.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalBundle.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalCommandAllocator.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalCommandQueue.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalCommandSignature.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalComputePipelineState.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalConstantBufferView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalDepthStencilView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalDescriptorHeap.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalDevice.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalFence.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalFramebuffer.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalGpuRefCounted.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalGpuTracker.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalGraphicsCommandList.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalGraphicsPipelineState.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalPipelineState.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalPlatform.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalQueryHeap.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalRenderTargetView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalResource.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalResourceBarrier.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalResourceCommandList.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalRootSignature.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalSamplerState.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalShaderResourceView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalSwapChain.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalTexture2d.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalTexture3d.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalUnorderedAccessView.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="Metal\MetalVertexArray.h">
      <Filter>Metal</Filter>
    </ClInclude>
    <ClInclude Include="D3D12\D3D12ResourceBarrier.h" />
    <ClInclude Include="D3D12\D3D12ResourceBarrier.h">
      <Filter>D3D12</Filter>
    </ClInclude>
    <ClInclude Include="Texture3d.h" />
    <ClInclude Include="HDR.h" />
    <ClInclude Include="HDRHelpers.h" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="GL">
      <UniqueIdentifier>{bdbdc6f5-b44a-4964-bdf8-d1314768e44e}</UniqueIdentifier>
    </Filter>
    <Filter Include="GL\Win">
      <UniqueIdentifier>{be6267d3-ab13-4619-96f3-9c5a8a28c042}</UniqueIdentifier>
    </Filter>
    <Filter Include="D3D11">
      <UniqueIdentifier>{4c1e9f1d-14f2-4d28-b50d-cc439034d84f}</UniqueIdentifier>
    </Filter>
    <Filter Include="GL\Ios">
      <UniqueIdentifier>{60c25194-9dff-4df8-9495-aef73f28843f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Metal">
      <UniqueIdentifier>{35ce0d29-dddd-4be3-aaeb-b9d3dbe1b3d7}</UniqueIdentifier>
    </Filter>
    <Filter Include="D3D12">
      <UniqueIdentifier>{bdf07785-7cf2-44e1-81de-15038ef6c560}</UniqueIdentifier>
    </Filter>
    <Filter Include="GL\Linux">
      <UniqueIdentifier>{ff3a57fb-765c-4a12-96a6-d4d1785587f9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D3D11\D3D11Buffer.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11CommandQueue.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Device.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Framebuffer.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11GraphicsCommandList.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11GraphicsPipelineState.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11SwapChain.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11VertexArray.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLGraphicsPipelineState.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLVertexArray.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLBuffer.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLCommandQueue.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLDevice.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLFramebuffer.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLGraphicsCommandList.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\Win\GLWinSwapChain.cpp">
      <Filter>GL\Win</Filter>
    </ClCompile>
    <None Include="GL\Ios\GLIosSwapChain.mm">
      <Filter>GL\Ios</Filter>
    </None>
    <ClCompile Include="D3D12\D3D12Buffer.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12CommandQueue.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Device.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Framebuffer.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12GraphicsCommandList.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12GraphicsPipelineState.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12SwapChain.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12VertexArray.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12CommandAllocator.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12GpuRefCounted.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12RenderTargetView.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12GpuTracker.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12RootSignature.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11ComputePipelineState.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12ComputePipelineState.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Platform.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11RenderTargetView.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Texture2d.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Platform.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLSwapChain.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLTexture2d.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Texture2d.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLPlatform.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <None Include="Metal\MetalPlatform.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalBuffer.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalCommandQueue.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalDevice.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalFramebuffer.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalGpuRefCounted.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalGpuTracker.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalGraphicsCommandList.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalGraphicsPipelineState.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalSwapChain.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalVertexArray.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalTexture2d.mm">
      <Filter>Metal</Filter>
    </None>
    <ClCompile Include="D3D11\D3D11DepthStencilView.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12DepthStencilView.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLDepthStencilView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <None Include="Metal\MetalDepthStencilView.mm">
      <Filter>Metal</Filter>
    </None>
    <ClCompile Include="D3D12\D3D12DescriptorHeap.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11DescriptorHeap.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLDescriptorHeap.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11SamplerState.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11ShaderResourceView.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12ShaderResourceView.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12SamplerState.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLShaderResourceView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLSamplerState.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLRenderTargetView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="Portable.cpp" />
    <ClCompile Include="D3D12\D3D12Resource.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLResource.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Resource.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11UnorderedAccessView.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLUnorderedAccessView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12UnorderedAccessView.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLComputePipelineState.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11RootSignature.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLRootSignature.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11ResourceCommandList.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLResourceCommandList.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12ResourceCommandList.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLCommandAllocator.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Texture3d.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Texture3d.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLTexture3d.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12CommandSignature.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLCommandSignature.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11CommandSignature.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="GL\Linux\GLLinuxSwapChain.cpp">
      <Filter>GL\Linux</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11PipelineState.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12PipelineState.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLPipelineState.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11QueryHeap.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12Fence.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLFence.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Fence.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12QueryHeap.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLQueryHeap.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D12\D3D12ConstantBufferView.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLConstantBufferView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11ConstantBufferView.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLGpuTracker.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLGpuRefCounted.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11CommandAllocator.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="StatKeeper.cpp" />
    <ClCompile Include="D3D12\D3D12Bundle.cpp">
      <Filter>D3D12</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11Bundle.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="D3D11\D3D11GpuTracker.cpp">
      <Filter>D3D11</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLBuffer.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLBundle.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLCommandAllocator.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLCommandQueue.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLCommandSignature.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLComputePipelineState.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLConstantBufferView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLDepthStencilView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLDescriptorHeap.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLDevice.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLFence.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLFramebuffer.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLGpuRefCounted.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLGpuTracker.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLGraphicsCommandList.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLGraphicsPipelineState.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLPipelineState.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLPlatform.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLQueryHeap.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLRenderTargetView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLResource.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLResourceCommandList.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLRootSignature.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLSamplerState.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLShaderResourceView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLTexture2d.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLTexture3d.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLUnorderedAccessView.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLVertexArray.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="GL\GLGlewWrapper.cpp">
      <Filter>GL</Filter>
    </ClCompile>
    <ClCompile Include="HDR.cpp" />
    <ClCompile Include="HDRHelpers.cpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Portable.inl" />
    <None Include="Metal\MetalBuffer.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalBundle.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalCommandAllocator.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalCommandQueue.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalCommandSignature.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalComputePipelineState.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalConstantBufferView.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalDepthStencilView.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalDescriptorHeap.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalDevice.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalFence.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalFramebuffer.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalGpuRefCounted.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalGpuTracker.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalGraphicsCommandList.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalGraphicsPipelineState.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalPipelineState.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalPlatform.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalRenderTargetView.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalResource.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalResourceCommandList.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalRootSignature.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalSamplerState.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalShaderResourceView.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalSwapChain.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalTexture2d.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalTexture3d.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalUnorderedAccessView.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="Metal\MetalVertexArray.mm">
      <Filter>Metal</Filter>
    </None>
    <None Include="..\..\..\..\Runtime\d3dcompiler_47.dll">
      <Filter>D3D11</Filter>
    </None>
  </ItemGroup>
</Project>