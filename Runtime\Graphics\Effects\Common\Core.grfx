-- caution, changes to this source file might require changes to app code
targets = import("Targets.grfx")
blendSettings = import("BlendSettings.grfx")
samplers = import("Samplers.grfx")

local variant = current_variant()

Highlight_Depth               = target_signature { Colors = targets.RasterLDR_Depth.Colors, Depth = targets.JustDepth.Depth }
Gbuffer1Variant_Depth         = target_signature { Colors = (variant == 'High' and targets.Gbuffer1High_Depth.Colors         or targets.Gbuffer1Low_Depth.Colors), Depth = targets.JustDepth.Depth }

CameraInfo = frame_parameters {
    HeadToWorld = float4x4(),
    HeadToWorldHistory = float4x4(),
    WorldToHead = float4x4(),
    LeftEyeToHead = float4x4(),
    RightEyeToHead = float4x4(),
    LeftHeadToEye = float4x4(),
    RightHeadToEye = float4x4(),
    ClipToLeftEye = float4x4(),
    ClipToRightEye = float4x4(),
    LeftEyeToClip = float4x4(),
    RightEyeToClip = float4x4(),
    LeftEyeToClipHistory = float4x4(),
    RightEyeToClipHistory = float4x4(),

    LeftEyeToHeadOffset = float(0),
    RightEyeToHeadOffset = float(0),

    RenderViewportOffset = float2(1,1),
    RenderViewportSize = float2(1,1),
    RcpRenderViewportSize = float2(1,1),
    DisplayViewportOffset = float2(0,0),
    DisplayViewportSize = float2(1,1),
    RcpDisplayViewportSize = float2(1,1),
    RenderTargetSize = float2(1,1),
    RcpRenderTargetSize = float2(1,1),
    DisplayTargetSize = float2(1,1),
    RcpDisplayTargetSize = float2(1,1),
    TemporalJitter = float2(0,0),
    GlobalMipBias = float(0),
    FrameNumber = uint(1),
}

WorldInfo = frame_parameters {
    WorldTimeSeconds = float(0),
    WorldDeltaSeconds = float(0),
    EnableDistanceFading = bool(1),
    FrameNumber = uint(1),
}

DiagnosticsInfo = frame_parameters {
    LightingVisibility = float(0),
    Sensitivity = float(1),
    Flags = uint(0),
}

ShadowProjectionInfo = frame_parameters {
    LocalToWorld = float4x4(),
    LocalToClip = float4x4(),
    CascadeOrigin = float3(),
    CascadeFactor = float(),
    CascadeTexelSnapAnchor = float2(),
    CascadeResolution = float(),
    CascadeCount = uint(),
    LocalSpaceOffsetToViewer = float3(),
}

StaticModelInfo = draw_parameters {
    LocalToWorld = float4x4(),
    LocalToWorldHistory = float4x4(),
    WorldToLocal = float4x4(),
    MinDrawDistance = float(0),
    MaxDrawDistance = float(999999999),
    InitialTime = float(0),
    Flags = uint(0),
}

RiggedModelInfo = draw_parameters {
    LocalToWorld = float4x4(),
    LocalToWorldHistory = float4x4(),
    WorldToLocal = float4x4(),
    MinDrawDistance = float(0),
    MaxDrawDistance = float(50),
    InitialTime = float(0),
    Flags = uint(0),
    -- above this line must match StaticModelInfo exactly (engine runtime assumes this) --
    Pose = buffer(float4),
    PoseHistory = buffer(float4),
}

ModelHighlightInfo = draw_parameters {
    Color = float4(1,1,1,1),
}

HighlightParams = frame_parameters {
    Mask = texture2d(float4),
}

SubsurfaceParams = frame_parameters {
    SubsurfaceIrradiance = texture2d(float4),
    ExitAlbedo = texture2d(float4),
}

MediaParams = frame_parameters {
    MediaTexture = texture2d(float4)
}

SkyParams = frame_parameters {
    Cubemap = texturecube(float4),
    Rotation = float4x4(),
    Brightness = float(1),
}

BackbufferSignature = target_signature {
    Colors = {PixelFormat.R10G10B10A2Unorm},
}

DebugGeometryEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Motion_Depth,
    StageParameters = { CameraInfo = CameraInfo },
}

DebugGeometryBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = { DebugGeometryEffectType },
}

DebugGeometryProgramCommon = partial(graphics_program) {
    Parameters = {CameraInfo = CameraInfo},
    Samplers = {},
    PixelOutput = DebugGeometryEffectType.TargetSignature,
    CullMode = CullMode.Back,
    BlendState = blend_state { TargetSettings = {blend_setting {BlendEnable = false}} },
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.GreaterEqual },
}

DebugStaticGeometryProgram = graphics_program {
    DebugGeometryProgramCommon,
    Parameters = {ModelInfo = StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "DebugStaticGeometry",
    PixelShader = "DebugStaticGeometry",
}

DebugSkinnedGeometryProgram = graphics_program {
    DebugGeometryProgramCommon,
    Parameters = {ModelInfo = RiggedModelInfo},
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "DebugSkinnedGeometry",
    PixelShader = "DebugSkinnedGeometry",
}

DebugStaticGeometryEffect = graphics_effect {
    Type = DebugGeometryEffectType,
    Program = DebugStaticGeometryProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}

DebugSkinnedGeometryEffect = graphics_effect {
    Type = DebugGeometryEffectType,
    Program = DebugSkinnedGeometryProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}

HighlightMaskEffectType = graphics_effect_type {
    TargetSignature = Highlight_Depth,
    StageParameters = { CameraInfo = CameraInfo },
}
HighlightEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Depth,
    StageParameters = { CameraInfo = CameraInfo, HighlightParams = HighlightParams },
}
HighlightBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = { HighlightMaskEffectType, HighlightEffectType },
}
HighlightMaskProgramCommon = partial(graphics_program) {
    Parameters = {CameraInfo = CameraInfo},
    PixelOutput = Highlight_Depth,
    CullMode = CullMode.None,
    BlendState = blendSettings.States.Disabled,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal },
}
HighlightProgramCommon = partial(graphics_program) {
    Parameters = {CameraInfo = CameraInfo, HighlightParams = HighlightParams, ModelHighlightInfo = ModelHighlightInfo },
    Samplers = { LinearSampler = samplers.LinearBlackBorder },
    PixelOutput = targets.Luminance_Depth,
    CullMode = CullMode.None,
    BlendState = blendSettings.States.PremultipliedOver,
    DepthStencilState = depth_stencil_state { DepthFunction = ComparisonFunction.Equal },
}
HighlightMaskStaticProgram = graphics_program {
    HighlightMaskProgramCommon,
    Parameters = {ModelInfo = StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "HighlightMaskStatic",
    PixelShader = "HighlightMaskStatic",
}
HighlightStaticProgram = graphics_program {
    HighlightProgramCommon,
    Parameters = {ModelInfo = StaticModelInfo},
    VertexInput = vertex_input { position = float3 },
    VertexShader = "HighlightStatic",
    PixelShader = "HighlightStatic",
}
HighlightSmoothStaticProgram = graphics_program {
    HighlightProgramCommon,
    Parameters = {ModelInfo = StaticModelInfo},
    VertexInput = vertex_input { position = float3, tangent = half4, bitangent = half4 },
    VertexShader = "HighlightStatic",
    PixelShader = "HighlightStatic",
    Macros = { "SMOOTH" },
}
HighlightMaskSkinnedProgram = graphics_program {
    HighlightMaskProgramCommon,
    Parameters = { ModelInfo = RiggedModelInfo },
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "HighlightMaskSkinned",
    PixelShader = "HighlightMaskSkinned",
}
HighlightSkinnedProgram = graphics_program {
    HighlightProgramCommon,
    Parameters = { ModelInfo = RiggedModelInfo },
    VertexInput = vertex_input { position = float3, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "HighlightSkinned",
    PixelShader = "HighlightSkinned",
}
HighlightSmoothSkinnedProgram = graphics_program {
    HighlightProgramCommon,
    Parameters = { ModelInfo = RiggedModelInfo },
    VertexInput = vertex_input { position = float3, tangent = half4, bitangent = half4, blendWeights = byte4_unorm, blendIndices = byte4 },
    VertexShader = "HighlightSkinned",
    PixelShader = "HighlightSkinned",
    Macros = { "SMOOTH" },
}
HighlightMaskStaticEffect = graphics_effect {
    Type = HighlightMaskEffectType,
    Program = HighlightMaskStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
HighlightStaticEffect = graphics_effect {
    Type = HighlightEffectType,
    Program = HighlightStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo', HighlightParams = 'HighlightParams'},
}
HighlightSmoothStaticEffect = graphics_effect {
    Type = HighlightEffectType,
    Program = HighlightSmoothStaticProgram,
    Arguments = {CameraInfo = 'CameraInfo', HighlightParams = 'HighlightParams'},
}
HighlightMaskSkinnedEffect = graphics_effect {
    Type = HighlightMaskEffectType,
    Program = HighlightMaskSkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo'},
}
HighlightSkinnedEffect = graphics_effect {
    Type = HighlightEffectType,
    Program = HighlightSkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo', HighlightParams = 'HighlightParams'},
}
HighlightSmoothSkinnedEffect = graphics_effect {
    Type = HighlightEffectType,
    Program = HighlightSmoothSkinnedProgram,
    Arguments = {CameraInfo = 'CameraInfo', HighlightParams = 'HighlightParams'},
}

WidgetFrameParams = frame_parameters {
    Exposure = buffer(float),
}

WidgetGeometryBehindEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Motion_Depth,
    StageParameters = { CameraInfo = CameraInfo, WidgetFrameParams = WidgetFrameParams },
}

WidgetGeometryEffectType = graphics_effect_type {
    TargetSignature = targets.Luminance_Motion_Depth,
    StageParameters = { CameraInfo = CameraInfo, WidgetFrameParams = WidgetFrameParams },
}

WidgetGeometryBin = render_bin {
    RenderOrder = RenderOrder.Performance,
    GraphicsEffectTypes = { WidgetGeometryBehindEffectType, WidgetGeometryEffectType },
}

-- Missing material types will default to these...

ErrorStaticGeometryEffectTable = effect_table {
    BinSelections = {
        Default = render_bins { DebugGeometryBin },
    },
    GraphicsEffects = { DebugStaticGeometryEffect },
    DrawParameters = { Model = StaticModelInfo, ModelHighlightInfo = ModelHighlightInfo },
}

ErrorSkinnedGeometryEffectTable = effect_table {
    BinSelections = {
        Default = render_bins { DebugGeometryBin },
    },
    GraphicsEffects = { DebugSkinnedGeometryEffect },
    DrawParameters = { Model = RiggedModelInfo, ModelHighlightInfo = ModelHighlightInfo },
}
