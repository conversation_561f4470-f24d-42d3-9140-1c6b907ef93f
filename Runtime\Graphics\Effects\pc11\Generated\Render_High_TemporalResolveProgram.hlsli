//
// Generated by GraphicsBuild. Do not modify.
//

#if !defined(INCLUDED_Render_High_TemporalResolveProgram) && defined(COMPILING_Render_High_TemporalResolveProgram)
#define INCLUDED_Render_High_TemporalResolveProgram


// Effect-provided macros
//

#define FAT_GBUFFER

// Accessor macros (for HLSL/GLSL/Metal shader source compatibility)
//

#define CameraInfo(member)                          CameraInfo_##member
#define TemporalResolveParams(member)               TemporalResolveParams_##member
#define VertexInput(member)                         VertexInput_.member
#define PixelOutput(member)                         PixelOutput_.member

// Samplers
//

SamplerState                        LinearSampler                       : register(s0);

SamplerState                        LinearWrapSampler                   : register(s1);

// Parameter blocks
//

//     Type Core::CameraInfo
//

cbuffer                             CameraInfo                          : register(b0)
{
    float4x4                        CameraInfo(HeadToWorld);
    float4x4                        CameraInfo(HeadToWorldHistory);
    float4x4                        CameraInfo(WorldToHead);
    float4x4                        CameraInfo(LeftEyeToHead);
    float4x4                        CameraInfo(RightEyeToHead);
    float4x4                        CameraInfo(LeftHeadToEye);
    float4x4                        CameraInfo(RightHeadToEye);
    float4x4                        CameraInfo(ClipToLeftEye);
    float4x4                        CameraInfo(ClipToRightEye);
    float4x4                        CameraInfo(LeftEyeToClip);
    float4x4                        CameraInfo(RightEyeToClip);
    float4x4                        CameraInfo(LeftEyeToClipHistory);
    float4x4                        CameraInfo(RightEyeToClipHistory);
    float                           CameraInfo(LeftEyeToHeadOffset);
    float                           CameraInfo(RightEyeToHeadOffset);
    float2                          CameraInfo(RenderViewportOffset);
    float2                          CameraInfo(RenderViewportSize);
    float2                          CameraInfo(RcpRenderViewportSize);
    float2                          CameraInfo(DisplayViewportOffset);
    float2                          CameraInfo(DisplayViewportSize);
    float2                          CameraInfo(RcpDisplayViewportSize);
    float2                          CameraInfo(RenderTargetSize);
    float2                          CameraInfo(RcpRenderTargetSize);
    float2                          CameraInfo(DisplayTargetSize);
    float2                          CameraInfo(RcpDisplayTargetSize);
    float2                          CameraInfo(TemporalJitter);
    float                           CameraInfo(GlobalMipBias);
    uint                            CameraInfo(FrameNumber);
};

//     Type Render::TemporalResolveParams
//

cbuffer                             TemporalResolveParams               : register(b1)
{
    bool                            TemporalResolveParams(Cut);
};
Buffer<float>                       TemporalResolveParams(ExposureInfo) : register(t0);
Texture2D<float4>                   TemporalResolveParams(History)      : register(t1);
Texture2D<float4>                   TemporalResolveParams(Source)       : register(t2);
Texture2D<float2>                   TemporalResolveParams(Motion)       : register(t3);
Texture2D<float2>                   TemporalResolveParams(Depth)        : register(t4);

// Vertex inputs
//

struct                              VertexInput
{
};

// Pixel outputs
//

struct                              PixelOutput
{
    float4                          color0          : SV_Target0;
};


#endif
