﻿#pragma checksum "UserProductDetailsControl.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "ACC5557DB02F0296BE414D9F71EA3B2E475B9F44352F68EB0118B463E2E4BC6D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LindenLab;
using Microsoft.Expression.Interactivity.Core;
using Microsoft.Expression.Interactivity.Input;
using Microsoft.Expression.Interactivity.Layout;
using Microsoft.Expression.Interactivity.Media;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Interactivity;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace LindenLab {
    
    
    /// <summary>
    /// UserProductDetailsControl
    /// </summary>
    public partial class UserProductDetailsControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 10 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.UserProductDetailsControl Root;
        
        #line default
        #line hidden
        
        
        #line 74 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ProductDetailsModal;
        
        #line default
        #line hidden
        
        
        #line 82 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LindenLab.RoundedCornerImage ProductImage;
        
        #line default
        #line hidden
        
        
        #line 121 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ProductListingDetails;
        
        #line default
        #line hidden
        
        
        #line 174 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel BuySection;
        
        #line default
        #line hidden
        
        
        #line 183 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BuyButton;
        
        #line default
        #line hidden
        
        
        #line 193 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RedeliverButton;
        
        #line default
        #line hidden
        
        
        #line 202 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button WearNowButton;
        
        #line default
        #line hidden
        
        
        #line 222 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditListing;
        
        #line default
        #line hidden
        
        
        #line 233 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PricePanel;
        
        #line default
        #line hidden
        
        
        #line 253 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Price;
        
        #line default
        #line hidden
        
        
        #line 346 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProductDescription;
        
        #line default
        #line hidden
        
        
        #line 374 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ProductSpecificationsTable;
        
        #line default
        #line hidden
        
        
        #line 397 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CategoryLabel;
        
        #line default
        #line hidden
        
        
        #line 406 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProductCategory;
        
        #line default
        #line hidden
        
        
        #line 432 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TagsLabel;
        
        #line default
        #line hidden
        
        
        #line 474 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProductNameLabel;
        
        #line default
        #line hidden
        
        
        #line 480 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProductName;
        
        #line default
        #line hidden
        
        
        #line 494 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SellerNameLabel;
        
        #line default
        #line hidden
        
        
        #line 500 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SellerName;
        
        #line default
        #line hidden
        
        
        #line 519 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MinResaleLabel;
        
        #line default
        #line hidden
        
        
        #line 553 "UserProductDetailsControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MinResalePrice;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/App;component/ui/marketplace/userproductdetailscontrol.xaml", System.UriKind.Relative);
            
            #line 1 "UserProductDetailsControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.Root = ((LindenLab.UserProductDetailsControl)(target));
            return;
            case 2:
            this.ProductDetailsModal = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.ProductImage = ((LindenLab.RoundedCornerImage)(target));
            return;
            case 4:
            this.ProductListingDetails = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.BuySection = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 6:
            this.BuyButton = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.RedeliverButton = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.WearNowButton = ((System.Windows.Controls.Button)(target));
            return;
            case 9:
            this.EditListing = ((System.Windows.Controls.Button)(target));
            return;
            case 10:
            this.PricePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.Price = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ProductDescription = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ProductSpecificationsTable = ((System.Windows.Controls.Grid)(target));
            return;
            case 14:
            this.CategoryLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.ProductCategory = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TagsLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.ProductNameLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.ProductName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.SellerNameLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.SellerName = ((System.Windows.Controls.Button)(target));
            return;
            case 21:
            this.MinResaleLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.MinResalePrice = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

